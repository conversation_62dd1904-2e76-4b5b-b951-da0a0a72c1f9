<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\NewBuilder;
use App\Models\Architect;
use Illuminate\Support\Facades\DB;
use App\Models\Message;
use Illuminate\Support\Facades\Auth;
use App\Models\Attachment;
use App\Models\Inspector;
use App\Models\McdStaff;
use App\Models\BuilderStaff;
use App\Models\MessageUserStar;

class EmailMessageController extends Controller
{
    public function index()
    {
        $builderCount = User::where('role_id', 2)
            ->select('email', 'name', DB::raw('"User" as source'))
            ->union(
                BuilderStaff::select('email', 'staff_name as name', DB::raw('"BuilderStaff" as source'))
            )
            ->distinct('email')
            ->count();

        $architectCount = DB::table('users')
            ->where('role_id', 4)
            ->select('email', 'name', DB::raw('"User" as source'))
            ->union(
                DB::table('architects')
                    ->where('role_id', 3)
                    ->select('email', 'name', DB::raw('"Architect" as source'))
            )
            ->distinct('email')
            ->count();

        $mcdCount = User::where('role_id', 3)
            ->select('email', 'name', DB::raw('"User" as source'))
            ->union(
                McdStaff::select('email', 'staff_name as name', DB::raw('"McdStaff" as source'))
            )
            ->distinct('email')
            ->count();

        $inspectorCount = Inspector::count();

        $superadminCount = User::where('role_id', 1)
            ->select('email', 'name', DB::raw('"User" as source'))
            ->distinct('email')
            ->count();

        $userId = auth()->guard('staff_mcd')->check()
            ? auth()->guard('staff_mcd')->id()
            : (auth()->guard('staff_builder')->check()
                ? auth()->guard('staff_builder')->id()
                : Auth::id());

        $guard = auth()->guard('staff_mcd')->check()
            ? 'staff_mcd'
            : (auth()->guard('staff_builder')->check()
                ? 'staff_builder'
                : 'web');

        $inboxMessages = Message::with([
            'senderInspectorRelation',
            'senderUserRelation',
            'senderMcdStaffRelation',
            'senderBuilderStaffRelation',
            'receiverInspectorRelation',
            'receiverUserRelation',
            'receiverMcdStaffRelation',
            'receiverBuilderStaffRelation',
            'attachments',
            'userStars'
        ])
            ->where(function ($query) use ($userId, $guard) {
                if ($guard === 'staff_mcd') {
                    $query->where('receiver_id_mcd_staff', $userId);
                } elseif ($guard === 'staff_builder') {
                    $query->where('receiver_id_builder_staff', $userId);
                } else {
                    $query->where('receiver_id', $userId);
                }
            })
            ->whereNull('deleted_at')
            ->get()
            ->map(function ($message) {
                $message->status = 'inbox';
                return $message;
            });

        $outboxMessages = Message::with([
            'senderInspectorRelation',
            'senderUserRelation',
            'senderMcdStaffRelation',
            'senderBuilderStaffRelation',
            'receiverInspectorRelation',
            'receiverUserRelation',
            'receiverMcdStaffRelation',
            'receiverBuilderStaffRelation',
            'attachments',
            'userStars'
        ])
            ->where(function ($query) use ($userId, $guard) {
                if ($guard === 'staff_mcd') {
                    $query->where('sender_id_mcd_staff', $userId);
                } elseif ($guard === 'staff_builder') {
                    $query->where('sender_id_builder_staff', $userId);
                } else {
                    $query->where('sender_id', $userId);
                }
            })
            ->whereNull('deleted_at')
            ->get()
            ->map(function ($message) {
                $message->status = 'outbox';
                return $message;
            });

        $starredMessages = Message::with([
            'senderInspectorRelation',
            'senderUserRelation',
            'senderMcdStaffRelation',
            'senderBuilderStaffRelation',
            'receiverInspectorRelation',
            'receiverUserRelation',
            'receiverMcdStaffRelation',
            'receiverBuilderStaffRelation',
            'attachments',
            'userStars'
        ])
            ->whereHas('userStars', function ($query) use ($userId, $guard) {
                if ($guard === 'staff_mcd') {
                    $query->where('mcd_staff_id', $userId)->where('is_starred', true);
                } elseif ($guard === 'staff_builder') {
                    $query->where('builder_staff_id', $userId)->where('is_starred', true);
                } else {
                    $query->where('user_id', $userId)->where('is_starred', true);
                }
            })
            ->whereNull('deleted_at')
            ->where(function ($query) use ($userId, $guard) {
                if ($guard === 'staff_mcd') {
                    $query->where('sender_id_mcd_staff', $userId)
                          ->orWhere('receiver_id_mcd_staff', $userId);
                } elseif ($guard === 'staff_builder') {
                    $query->where('sender_id_builder_staff', $userId)
                          ->orWhere('receiver_id_builder_staff', $userId);
                } else {
                    $query->where('sender_id', $userId)
                          ->orWhere('receiver_id', $userId);
                }
            })
            ->get()
            ->map(function ($message) {
                $message->status = 'starred';
                return $message;
            });

        $trashMessages = Message::onlyTrashed()
            ->with([
                'senderInspectorRelation',
                'senderUserRelation',
                'senderMcdStaffRelation',
                'senderBuilderStaffRelation',
                'receiverInspectorRelation',
                'receiverUserRelation',
                'receiverMcdStaffRelation',
                'receiverBuilderStaffRelation',
                'attachments',
                'userStars'
            ])
            ->where(function ($query) use ($userId, $guard) {
                if ($guard === 'staff_mcd') {
                    $query->where('sender_id_mcd_staff', $userId)
                          ->orWhere('receiver_id_mcd_staff', $userId);
                } elseif ($guard === 'staff_builder') {
                    $query->where('sender_id_builder_staff', $userId)
                          ->orWhere('receiver_id_builder_staff', $userId);
                } else {
                    $query->where('sender_id', $userId)
                          ->orWhere('receiver_id', $userId);
                }
            })
            ->get()
            ->map(function ($message) {
                $message->status = 'trash';
                return $message;
            });

        $messages = collect()
            ->merge($inboxMessages)
            ->merge($outboxMessages)
            ->merge($starredMessages)
            ->merge($trashMessages)
            ->unique('id');

        return view('BuilderWebsiteDashboard.Communication.index', compact(
            'builderCount',
            'architectCount',
            'mcdCount',
            'inspectorCount',
            'superadminCount',
            'messages'
        ));
    }

    public function store(Request $request)
    {
        try {
            if (!Auth::check() && !auth()->guard('staff_mcd')->check() && !auth()->guard('staff_builder')->check()) {
                \Log::error('No authenticated user found.');
                return response()->json(['success' => false, 'message' => 'User not authenticated.'], 401);
            }

            $request->validate([
                'receiver_email' => [
                    'required',
                    'email',
                    function ($attribute, $value, $fail) {
                        try {
                            $existsInUsers = User::where('email', $value)->exists();
                            $existsInMcdStaff = McdStaff::where('email', $value)->exists();
                            if (!$existsInUsers && !$existsInMcdStaff) {
                                $fail('The selected email does not exist in the system.');
                            }
                        } catch (\Exception $e) {
                            \Log::error('Validation error for receiver_email: ' . $e->getMessage());
                            $fail('An error occurred while validating the email.');
                        }
                    },
                ],
                'body' => 'required|string',
                'attachments.*' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:204800',
            ]);

            $receiver = User::where('email', $request->receiver_email)->first();
            if (!$receiver) {
                $receiver = McdStaff::where('email', $request->receiver_email)->first();
                if (!$receiver) {
                    \Log::error('McdStaff not found for email: ' . $request->receiver_email);
                    throw new \Exception('Receiver not found.');
                }
            }

            $messageData = [
                'subject' => 'Message Sent to MCD',
                'body' => $request->body,
                'attachment_count' => $request->hasFile('attachments') ? count($request->file('attachments')) : 0,
            ];

            $guard = auth()->guard('staff_mcd')->check() ? 'staff_mcd' : (auth()->guard('staff_builder')->check() ? 'staff_builder' : 'web');
            $senderId = auth()->guard('staff_mcd')->check()
                ? auth()->guard('staff_mcd')->id()
                : (auth()->guard('staff_builder')->check()
                    ? auth()->guard('staff_builder')->id()
                    : Auth::id());

            if ($guard === 'staff_mcd') {
                $messageData['sender_id_mcd_staff'] = $senderId;
            } elseif ($guard === 'staff_builder') {
                $messageData['sender_id_builder_staff'] = $senderId;
            } else {
                $messageData['sender_id'] = $senderId;
            }

            if ($receiver instanceof User) {
                $messageData['receiver_id'] = $receiver->id;
            } elseif ($receiver instanceof McdStaff) {
                $messageData['receiver_id_mcd_staff'] = $receiver->id;
            }

            $message = Message::create($messageData);

            if ($request->hasFile('attachments')) {
                $destinationPath = public_path('attachments');
                if (!file_exists($destinationPath)) {
                    mkdir($destinationPath, 0755, true);
                }
                if (!is_writable($destinationPath)) {
                    throw new \Exception('Attachments directory is not writable.');
                }
                foreach ($request->file('attachments') as $file) {
                    if ($file->isValid()) {
                        $filename = uniqid() . '.' . $file->getClientOriginalExtension();
                        $file->move($destinationPath, $filename);
                        Attachment::create([
                            'message_id' => $message->id,
                            'file_path' => 'attachments/' . $filename,
                            'file_name' => $file->getClientOriginalName(),
                        ]);
                    } else {
                        throw new \Exception('Invalid file uploaded.');
                    }
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Your message has been sent to the MCD Communication Panel successfully.'
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed: ' . implode(', ', $e->errors()[array_key_first($e->errors())]),
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Error in store: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while sending the message: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function storeBuilder(Request $request)
    {
        try {
            if (!Auth::check() && !auth()->guard('staff_mcd')->check() && !auth()->guard('staff_builder')->check()) {
                \Log::error('No authenticated user found.');
                return response()->json(['success' => false, 'message' => 'User not authenticated.'], 401);
            }

            $request->validate([
                'receiver_email' => [
                    'required',
                    'email',
                    function ($attribute, $value, $fail) {
                        try {
                            $existsInUsers = User::where('email', $value)->exists();
                            $existsInBuilderStaff = BuilderStaff::where('email', $value)->exists();
                            if (!$existsInUsers && !$existsInBuilderStaff) {
                                $fail('The selected email does not exist in the system.');
                            }
                        } catch (\Exception $e) {
                            \Log::error('Validation error for receiver_email: ' . $e->getMessage());
                            $fail('An error occurred while validating the email.');
                        }
                    },
                ],
                'body' => 'required|string',
                'attachments.*' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:204800',
            ]);

            $receiver = User::where('email', $request->receiver_email)->first();
            if (!$receiver) {
                $receiver = BuilderStaff::where('email', $request->receiver_email)->first();
                if (!$receiver) {
                    \Log::error('BuilderStaff not found for email: ' . $request->receiver_email);
                    throw new \Exception('Receiver not found.');
                }
            }

            $messageData = [
                'subject' => 'Message Sent to Builder',
                'body' => $request->body,
                'attachment_count' => $request->hasFile('attachments') ? count($request->file('attachments')) : 0,
            ];

            $guard = auth()->guard('staff_mcd')->check() ? 'staff_mcd' : (auth()->guard('staff_builder')->check() ? 'staff_builder' : 'web');
            $senderId = auth()->guard('staff_mcd')->check()
                ? auth()->guard('staff_mcd')->id()
                : (auth()->guard('staff_builder')->check()
                    ? auth()->guard('staff_builder')->id()
                    : Auth::id());

            if ($guard === 'staff_mcd') {
                $messageData['sender_id_mcd_staff'] = $senderId;
            } elseif ($guard === 'staff_builder') {
                $messageData['sender_id_builder_staff'] = $senderId;
            } else {
                $messageData['sender_id'] = $senderId;
            }

            if ($receiver instanceof User) {
                $messageData['receiver_id'] = $receiver->id;
            } elseif ($receiver instanceof BuilderStaff) {
                $messageData['receiver_id_builder_staff'] = $receiver->id;
            }

            $message = Message::create($messageData);

            if ($request->hasFile('attachments')) {
                $destinationPath = public_path('attachments');
                if (!file_exists($destinationPath)) {
                    mkdir($destinationPath, 0755, true);
                }
                if (!is_writable($destinationPath)) {
                    throw new \Exception('Attachments directory is not writable.');
                }
                foreach ($request->file('attachments') as $file) {
                    if ($file->isValid()) {
                        $filename = uniqid() . '.' . $file->getClientOriginalExtension();
                        $file->move($destinationPath, $filename);
                        Attachment::create([
                            'message_id' => $message->id,
                            'file_path' => 'attachments/' . $filename,
                            'file_name' => $file->getClientOriginalName(),
                        ]);
                    } else {
                        throw new \Exception('Invalid file uploaded.');
                    }
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Your message has been sent to the Builder Communication Panel successfully.'
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed: ' . implode(', ', $e->errors()[array_key_first($e->errors())]),
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Error in storeBuilder: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while sending the message: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function storeEngineer(Request $request)
    {
        try {
            if (!Auth::check() && !auth()->guard('staff_mcd')->check() && !auth()->guard('staff_builder')->check()) {
                \Log::error('No authenticated user found.');
                return response()->json(['success' => false, 'message' => 'User not authenticated.'], 401);
            }

            $request->validate([
                'receiver_email' => [
                    'required',
                    'email',
                    function ($attribute, $value, $fail) {
                        try {
                            $existsInUsers = User::where('email', $value)->exists();
                            $existsInArchitects = Architect::where('email', $value)->exists();
                            if (!$existsInUsers && !$existsInArchitects) {
                                $fail('The selected email does not exist in the system.');
                            }
                        } catch (\Exception $e) {
                            \Log::error('Validation error for receiver_email: ' . $e->getMessage());
                            $fail('An error occurred while validating the email.');
                        }
                    },
                ],
                'body' => 'required|string',
                'attachments.*' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:204800',
            ]);

            $receiver = User::where('email', $request->receiver_email)->first();
            if (!$receiver) {
                $receiver = Architect::where('email', $request->receiver_email)->first();
                if (!$receiver) {
                    \Log::error('Architect not found for email: ' . $request->receiver_email);
                    throw new \Exception('Receiver not found.');
                }
            }

            $messageData = [
                'subject' => 'Message Sent to Engineer',
                'body' => $request->body,
                'attachment_count' => $request->hasFile('attachments') ? count($request->file('attachments')) : 0,
            ];

            $guard = auth()->guard('staff_mcd')->check() ? 'staff_mcd' : (auth()->guard('staff_builder')->check() ? 'staff_builder' : 'web');
            $senderId = auth()->guard('staff_mcd')->check()
                ? auth()->guard('staff_mcd')->id()
                : (auth()->guard('staff_builder')->check()
                    ? auth()->guard('staff_builder')->id()
                    : Auth::id());

            if ($guard === 'staff_mcd') {
                $messageData['sender_id_mcd_staff'] = $senderId;
            } elseif ($guard === 'staff_builder') {
                $messageData['sender_id_builder_staff'] = $senderId;
            } else {
                $messageData['sender_id'] = $senderId;
            }

            if ($receiver instanceof User) {
                $messageData['receiver_id'] = $receiver->id;
            } elseif ($receiver instanceof Architect) {
                $messageData['receiver_id_architect'] = $receiver->id;
            }

            $message = Message::create($messageData);

            if ($request->hasFile('attachments')) {
                $destinationPath = public_path('attachments');
                if (!file_exists($destinationPath)) {
                    mkdir($destinationPath, 0755, true);
                }
                if (!is_writable($destinationPath)) {
                    throw new \Exception('Attachments directory is not writable.');
                }
                foreach ($request->file('attachments') as $file) {
                    if ($file->isValid()) {
                        $filename = uniqid() . '.' . $file->getClientOriginalExtension();
                        $file->move($destinationPath, $filename);
                        Attachment::create([
                            'message_id' => $message->id,
                            'file_path' => 'attachments/' . $filename,
                            'file_name' => $file->getClientOriginalName(),
                        ]);
                    } else {
                        throw new \Exception('Invalid file uploaded.');
                    }
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Your message has been sent to the Engineer Communication Panel successfully.'
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed: ' . implode(', ', $e->errors()[array_key_first($e->errors())]),
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Error in storeEngineer: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while sending the message: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function storeSuperadmin(Request $request)
    {
        try {
            if (!Auth::check() && !auth()->guard('staff_mcd')->check() && !auth()->guard('staff_builder')->check()) {
                \Log::error('No authenticated user found.');
                return response()->json(['success' => false, 'message' => 'User not authenticated.'], 401);
            }

            $request->validate([
                'receiver_email' => [
                    'required',
                    'email',
                    function ($attribute, $value, $fail) {
                        try {
                            $existsInUsers = User::where('email', $value)->where('role_id', 1)->exists();
                            if (!$existsInUsers) {
                                $fail('The selected email does not belong to a superadmin.');
                            }
                        } catch (\Exception $e) {
                            \Log::error('Validation error for receiver_email: ' . $e->getMessage());
                            $fail('An error occurred while validating the email.');
                        }
                    },
                ],
                'body' => 'required|string',
                'attachments.*' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:204800',
            ]);

            $receiver = User::where('email', $request->receiver_email)->where('role_id', 1)->first();
            if (!$receiver) {
                \Log::error('Superadmin not found for email: ' . $request->receiver_email);
                throw new \Exception('Receiver not found.');
            }

            $messageData = [
                'subject' => 'Message Sent to Superadmin',
                'body' => $request->body,
                'attachment_count' => $request->hasFile('attachments') ? count($request->file('attachments')) : 0,
            ];

            $guard = auth()->guard('staff_mcd')->check() ? 'staff_mcd' : (auth()->guard('staff_builder')->check() ? 'staff_builder' : 'web');
            $senderId = auth()->guard('staff_mcd')->check()
                ? auth()->guard('staff_mcd')->id()
                : (auth()->guard('staff_builder')->check()
                    ? auth()->guard('staff_builder')->id()
                    : Auth::id());

            if ($guard === 'staff_mcd') {
                $messageData['sender_id_mcd_staff'] = $senderId;
            } elseif ($guard === 'staff_builder') {
                $messageData['sender_id_builder_staff'] = $senderId;
            } else {
                $messageData['sender_id'] = $senderId;
            }

            if ($receiver instanceof User) {
                $messageData['receiver_id'] = $receiver->id;
            }

            $message = Message::create($messageData);

            if ($request->hasFile('attachments')) {
                $destinationPath = public_path('attachments');
                if (!file_exists($destinationPath)) {
                    mkdir($destinationPath, 0755, true);
                }
                if (!is_writable($destinationPath)) {
                    throw new \Exception('Attachments directory is not writable.');
                }
                foreach ($request->file('attachments') as $file) {
                    if ($file->isValid()) {
                        $filename = uniqid() . '.' . $file->getClientOriginalExtension();
                        $file->move($destinationPath, $filename);
                        Attachment::create([
                            'message_id' => $message->id,
                            'file_path' => 'attachments/' . $filename,
                            'file_name' => $file->getClientOriginalName(),
                        ]);
                    } else {
                        throw new \Exception('Invalid file uploaded.');
                    }
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Your message has been sent to the Superadmin Communication Panel successfully.'
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed: ' . implode(', ', $e->errors()[array_key_first($e->errors())]),
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Error in storeSuperadmin: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while sending the message: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function storeInspector(Request $request)
    {
        try {
            if (!Auth::check() && !auth()->guard('staff_mcd')->check() && !auth()->guard('staff_builder')->check()) {
                \Log::error('No authenticated user found.');
                return response()->json(['success' => false, 'message' => 'User not authenticated.'], 401);
            }

            $request->validate([
                'receiver_email' => [
                    'required',
                    'email',
                    function ($attribute, $value, $fail) {
                        try {
                            if (!Inspector::where('email', $value)->exists()) {
                                $fail('The selected email does not exist in the system.');
                            }
                        } catch (\Exception $e) {
                            \Log::error('Validation error for receiver_email: ' . $e->getMessage());
                            $fail('An error occurred while validating the email.');
                        }
                    },
                ],
                'body' => 'required|string',
                'attachments.*' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:204800',
            ]);

            $receiver = Inspector::where('email', $request->receiver_email)->first();
            if (!$receiver) {
                \Log::error('Inspector not found for email: ' . $request->receiver_email);
                throw new \Exception('Receiver not found.');
            }

            $messageData = [
                'subject' => 'Message Sent to Inspector',
                'body' => $request->body,
                'receiver_id_inspector' => $receiver->id,
                'attachment_count' => $request->hasFile('attachments') ? count($request->file('attachments')) : 0,
            ];

            $guard = auth()->guard('staff_mcd')->check() ? 'staff_mcd' : (auth()->guard('staff_builder')->check() ? 'staff_builder' : 'web');
            $senderId = auth()->guard('staff_mcd')->check()
                ? auth()->guard('staff_mcd')->id()
                : (auth()->guard('staff_builder')->check()
                    ? auth()->guard('staff_builder')->id()
                    : Auth::id());

            if ($guard === 'staff_mcd') {
                $messageData['sender_id_mcd_staff'] = $senderId;
            } elseif ($guard === 'staff_builder') {
                $messageData['sender_id_builder_staff'] = $senderId;
            } else {
                $messageData['sender_id'] = $senderId;
            }

            $message = Message::create($messageData);

            if ($request->hasFile('attachments')) {
                $destinationPath = public_path('attachments');
                if (!file_exists($destinationPath)) {
                    mkdir($destinationPath, 0755, true);
                }
                if (!is_writable($destinationPath)) {
                    throw new \Exception('Attachments directory is not writable.');
                }
                foreach ($request->file('attachments') as $file) {
                    if ($file->isValid()) {
                        $filename = uniqid() . '.' . $file->getClientOriginalExtension();
                        $file->move($destinationPath, $filename);
                        Attachment::create([
                            'message_id' => $message->id,
                            'file_path' => 'attachments/' . $filename,
                            'file_name' => $file->getClientOriginalName(),
                        ]);
                    } else {
                        throw new \Exception('Invalid file uploaded.');
                    }
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Your message has been sent to the Inspector Communication Panel successfully.'
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed: ' . implode(', ', $e->errors()[array_key_first($e->errors())]),
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Error in storeInspector: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while sending the message: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function reply(Request $request)
    {
        try {
            if (!Auth::check() && !auth()->guard('staff_mcd')->check() && !auth()->guard('staff_builder')->check()) {
                \Log::error('No authenticated user found.');
                return response()->json(['success' => false, 'message' => 'User not authenticated.'], 401);
            }

            $request->validate([
                'message_id' => 'required|exists:messages,id',
                'body' => 'required|string',
                'attachments.*' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:204800',
            ]);

            $originalMessage = Message::findOrFail($request->message_id);

            $messageData = [
                'subject' => 'RE: ' . ($originalMessage->subject ?? 'Reply'),
                'body' => $request->body,
                'parent_id' => $originalMessage->id,
                'attachment_count' => $request->hasFile('attachments') ? count($request->file('attachments')) : 0,
            ];

            $guard = auth()->guard('staff_mcd')->check() ? 'staff_mcd' : (auth()->guard('staff_builder')->check() ? 'staff_builder' : 'web');
            $senderId = auth()->guard('staff_mcd')->check()
                ? auth()->guard('staff_mcd')->id()
                : (auth()->guard('staff_builder')->check()
                    ? auth()->guard('staff_builder')->id()
                    : Auth::id());

            if ($guard === 'staff_mcd') {
                $messageData['sender_id_mcd_staff'] = $senderId;
            } elseif ($guard === 'staff_builder') {
                $messageData['sender_id_builder_staff'] = $senderId;
            } else {
                $messageData['sender_id'] = $senderId;
            }

            if ($originalMessage->sender_id == $senderId) {
                $messageData['receiver_id'] = $originalMessage->receiver_id;
                $messageData['receiver_id_inspector'] = $originalMessage->receiver_id_inspector;
                $messageData['receiver_id_mcd_staff'] = $originalMessage->receiver_id_mcd_staff;
                $messageData['receiver_id_builder_staff'] = $originalMessage->receiver_id_builder_staff;
                $messageData['receiver_id_architect'] = $originalMessage->receiver_id_architect;
            } else {
                $messageData['receiver_id'] = $originalMessage->sender_id;
                $messageData['receiver_id_inspector'] = $originalMessage->sender_id_inspector;
                $messageData['receiver_id_mcd_staff'] = $originalMessage->sender_id_mcd_staff;
                $messageData['receiver_id_builder_staff'] = $originalMessage->sender_id_builder_staff;
                $messageData['receiver_id_architect'] = $originalMessage->sender_id_architect;
            }

            $reply = Message::create($messageData);

            if ($request->hasFile('attachments')) {
                $destinationPath = public_path('attachments');
                if (!file_exists($destinationPath)) {
                    mkdir($destinationPath, 0755, true);
                }
                if (!is_writable($destinationPath)) {
                    throw new \Exception('Attachments directory is not writable.');
                }
                foreach ($request->file('attachments') as $file) {
                    if ($file->isValid()) {
                        $filename = uniqid() . '.' . $file->getClientOriginalExtension();
                        $file->move($destinationPath, $filename);
                        Attachment::create([
                            'message_id' => $reply->id,
                            'file_path' => 'attachments/' . $filename,
                            'file_name' => $file->getClientOriginalName(),
                        ]);
                    } else {
                        throw new \Exception('Invalid file uploaded.');
                    }
                }
            }

            return response()->json(['success' => true, 'message' => 'Reply sent successfully.']);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed: ' . implode(', ', $e->errors()[array_key_first($e->errors())]),
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Error in reply: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while sending the reply: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function inspector()
    {
        $inspectors = Inspector::select('id', 'first_name', 'last_name', 'email', 'created_at')->get();
        return view('BuilderWebsiteDashboard.Communication.inspectors', compact('inspectors'));
    }

    public function superadmins()
    {
        $superadmins = User::where('role_id', 1)
            ->select('id', 'name', 'email', 'created_at')
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'created_at' => $user->created_at,
                    'source' => 'User',
                ];
            });
        return view('BuilderWebsiteDashboard.Communication.superadmins', compact('superadmins'));
    }

    public function builders()
    {
        $users = User::where('role_id', 2)->get(['id', 'name', 'email', 'created_at']);
        $builderStaff = BuilderStaff::get(['id', 'staff_name as name', 'email', 'created_at']);
        $builders = $users->merge($builderStaff)->map(function ($user) {
            return [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'created_at' => $user->created_at,
                'source' => $user instanceof User ? 'User' : 'BuilderStaff',
            ];
        });
        return view('BuilderWebsiteDashboard.Communication.builders', compact('builders'));
    }

    public function architects()
    {
        $architects = DB::table('users')
            ->where('role_id', 4)
            ->select(
                'id',
                'name',
                'email',
                DB::raw('created_at AS created_at'),
                DB::raw('"User" AS source')
            )
            ->union(
                DB::table('architects')
                    ->where('role_id', 3)
                    ->select(
                        'id',
                        'name',
                        'email',
                        DB::raw('created_at AS created_at'),
                        DB::raw('"Architect" AS source')
                    )
            )
            ->distinct('email')
            ->get();
        return view('BuilderWebsiteDashboard.Communication.architects', compact('architects'));
    }

    public function mcd()
    {
        $users = User::where('role_id', 3)->get(['id', 'name', 'email', 'created_at']);
        $mcdStaff = McdStaff::get(['id', 'staff_name as name', 'email', 'created_at']);
        $mcd = $users->merge($mcdStaff)->map(function ($user) {
            return [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'created_at' => $user->created_at,
                'type' => $user instanceof User ? 'user' : 'mcd_staff',
            ];
        });
        return view('BuilderWebsiteDashboard.Communication.mcd', compact('mcd'));
    }

    public function showAttachments($id)
    {
        try {
            $message = Message::with('attachments')->findOrFail($id);
            $attachments = $message->attachments->map(function ($file) {
                return [
                    'file_type' => $file->file_type ?? mime_content_type(public_path($file->file_path)) ?? 'application/octet-stream',
                    'url' => asset($file->file_path),
                    'name' => $file->file_name ?? basename($file->file_path),
                ];
            });
            return response()->json(['attachments' => $attachments], 200);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json(['error' => 'Message not found'], 404);
        } catch (\Exception $e) {
            \Log::error('Error in showAttachments: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to load attachments'], 500);
        }
    }

    public function message()
    {
        $userId = Auth::id();

        $inboxMessages = Message::with([
            'senderInspectorRelation',
            'senderUserRelation',
            'senderMcdStaffRelation',
            'senderBuilderStaffRelation',
            'receiverInspectorRelation',
            'receiverUserRelation',
            'receiverMcdStaffRelation',
            'receiverBuilderStaffRelation',
            'attachments',
            'userStars'
        ])
            ->where('receiver_id', $userId)
            ->whereNull('deleted_at')
            ->get()
            ->map(function ($message) {
                $message->status = 'inbox';
                return $message;
            });

        $outboxMessages = Message::with([
            'senderInspectorRelation',
            'senderUserRelation',
            'senderMcdStaffRelation',
            'senderBuilderStaffRelation',
            'receiverInspectorRelation',
            'receiverUserRelation',
            'receiverMcdStaffRelation',
            'receiverBuilderStaffRelation',
            'attachments',
            'userStars'
        ])
            ->where('sender_id', $userId)
            ->whereNull('deleted_at')
            ->get()
            ->map(function ($message) {
                $message->status = 'outbox';
                return $message;
            });

        $starredMessages = Message::with([
            'senderInspectorRelation',
            'senderUserRelation',
            'senderMcdStaffRelation',
            'senderBuilderStaffRelation',
            'receiverInspectorRelation',
            'receiverUserRelation',
            'receiverMcdStaffRelation',
            'receiverBuilderStaffRelation',
            'attachments',
            'userStars'
        ])
            ->whereHas('userStars', function ($query) use ($userId) {
                $query->where('user_id', $userId)->where('is_starred', true);
            })
            ->whereNull('deleted_at')
            ->where(function ($query) use ($userId) {
                $query->where('sender_id', $userId)
                      ->orWhere('receiver_id', $userId);
            })
            ->get()
            ->map(function ($message) {
                $message->status = 'starred';
                return $message;
            });

        $trashMessages = Message::onlyTrashed()
            ->with([
                'senderInspectorRelation',
                'senderUserRelation',
                'senderMcdStaffRelation',
                'senderBuilderStaffRelation',
                'receiverInspectorRelation',
                'receiverUserRelation',
                'receiverMcdStaffRelation',
                'receiverBuilderStaffRelation',
                'attachments',
                'userStars'
            ])
            ->where(function ($query) use ($userId) {
                $query->where('sender_id', $userId)
                      ->orWhere('receiver_id', $userId);
            })
            ->get()
            ->map(function ($message) {
                $message->status = 'trash';
                return $message;
            });

        $messages = collect()
            ->merge($inboxMessages)
            ->merge($outboxMessages)
            ->merge($starredMessages)
            ->merge($trashMessages)
            ->unique('id');

        return view('BuilderWebsiteDashboard.Communicationmessages.index', compact('messages'));
    }

    public function star(Request $request)
    {
        try {
            $request->validate([
                'message_id' => 'required|exists:messages,id',
            ]);

            $message = Message::findOrFail($request->message_id);

            $guard = auth()->guard('staff_mcd')->check() ? 'staff_mcd' : (auth()->guard('staff_builder')->check() ? 'staff_builder' : 'web');
            $userId = auth()->guard('staff_mcd')->check()
                ? auth()->guard('staff_mcd')->id()
                : (auth()->guard('staff_builder')->check()
                    ? auth()->guard('staff_builder')->id()
                    : Auth::id());

            $isAuthorized = false;
            if ($guard === 'staff_mcd') {
                $isAuthorized = $message->sender_id_mcd_staff == $userId || $message->receiver_id_mcd_staff == $userId;
            } elseif ($guard === 'staff_builder') {
                $isAuthorized = $message->sender_id_builder_staff == $userId || $message->receiver_id_builder_staff == $userId;
            } else {
                $isAuthorized = $message->sender_id == $userId || $message->receiver_id == $userId;
            }

            if (!$isAuthorized) {
                return response()->json(['success' => false, 'message' => 'Unauthorized action.'], 403);
            }

            $starData = ['message_id' => $message->id];
            if ($guard === 'staff_mcd') {
                $starData['mcd_staff_id'] = $userId;
            } elseif ($guard === 'staff_builder') {
                $starData['builder_staff_id'] = $userId;
            } else {
                $starData['user_id'] = $userId;
            }

            $star = MessageUserStar::where($starData)->first();

            if ($star) {
                $star->update(['is_starred' => !$star->is_starred]);
                $isStarred = $star->is_starred;
            } else {
                $starData['is_starred'] = true;
                MessageUserStar::create($starData);
                $isStarred = true;
            }

            return response()->json(['success' => true, 'is_starred' => $isStarred]);
        } catch (\Exception $e) {
            \Log::error('Error in star: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while toggling star status: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function delete($id)
    {
        try {
            $message = Message::findOrFail($id);

            $userId = Auth::id();
            if ($message->sender_id == $userId || $message->receiver_id == $userId ||
                $message->sender_id_mcd_staff == $userId || $message->receiver_id_mcd_staff == $userId ||
                $message->sender_id_builder_staff == $userId || $message->receiver_id_builder_staff == $userId) {
                $message->delete();
                return redirect()->back()->with('success', 'Message moved to trash.');
            }

            return redirect()->back()->with('error', 'Unauthorized action.');
        } catch (\Exception $e) {
            \Log::error('Error in delete: ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred while deleting the message.');
        }
    }
}