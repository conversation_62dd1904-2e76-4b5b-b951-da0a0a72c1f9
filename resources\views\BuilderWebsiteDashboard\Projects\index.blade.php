@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4>Project Insight > Projects for review</h4>
                    </div>
                    <div class="card-body">
                        @if ($projects->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="table-1">
                                    <thead class="bg-gray-200 text-gray-800">
                                        <tr>
                                            <th>Sr No</th>
                                            <th>Project Name</th>
                                            <th>Trade</th>
                                            <th>City</th>
                                            <th>Engineer Name</th>
                                            <th>Approval Status</th>
                                            <th>Inspector</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($projects as $key => $project)
                                            <tr>
                                                <td class="text-center">{{ $key + 1 }}</td>
                                                <td>{{ $project->project_name }}</td>
                                                <td>
                                                    @php
                                                        $trades = json_decode($project->trades_involved, true);
                                                    @endphp
                                                    @if (is_array($trades) && count($trades))
                                                        {{ implode(', ', array_filter($trades)) }}
                                                    @else
                                                        N/A
                                                    @endif
                                                </td>
                                                <td>{{ $project->city ?? 'N/A' }}</td>
                                                <td>{{ $project->engineer_name ?? 'N/A' }}</td>
                                                <td>
                                                    @if($project->approval_status === 'Pending')
                                                        <span class="badge badge-success">Pending</span>
                                                    @elseif($project->approval_status === 'Violation')
                                                        <span class="badge badge-warning">Violation</span>
                                                    @elseif($project->approval_status === 'Approval')
                                                        @if(!is_null($project->inspector_id))
                                                            <span class="badge badge-info">
                                                                {{ $project->inspector?->name ?? 'Inspector Assigned' }}
                                                            </span>
                                                            @if(!is_null($project->inspector_assigned_at))
                                                                <br>
                                                                <small class="text-muted">
                                                                    Assigned on {{ \Carbon\Carbon::parse($project->inspector_assigned_at)->format('d M Y h:i A') }}
                                                                </small>
                                                            @endif
                                                        @else
                                                            <span class="badge badge-secondary">Inspector not assigned</span>
                                                        @endif
                                                    @else
                                                        <span class="badge badge-secondary">Pending</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if ($project->inspector)
                                                        {{ $project->inspector->first_name . ' ' . $project->inspector->last_name }}
                                                    @else
                                                        <button type="button" class="btn btn-orange" data-toggle="modal" data-target="#assignInspectorModal{{ $project->id }}">
                                                            Assign Inspector
                                                        </button>
                                                    @endif
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="dropdown">
                                                            <button class="btn btn-primary btn-sm dropdown-toggle" type="button" id="dropdownMenuButton{{ $project->id }}" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                                View
                                                            </button>
                                                            <div class="dropdown-menu" aria-labelledby="dropdownMenuButton{{ $project->id }}">
                                                                <a class="dropdown-item" href="{{ route('myprojects.show', $project->id) }}">View Documents</a>
                                                                {{-- <a class="dropdown-item" href="#" data-toggle="modal" data-target="#rejectProjectModal{{ $project->id }}">Reject</a>
                                                                <a class="dropdown-item" href="#" data-toggle="modal" data-target="#objectionProjectModal{{ $project->id }}">Violation</a> --}}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <!-- Assign Inspector Modal -->
                                            <div class="modal fade" id="assignInspectorModal{{ $project->id }}" tabindex="-1" role="dialog" aria-labelledby="assignInspectorModalLabel{{ $project->id }}" aria-hidden="true">
                                                <div class="modal-dialog" role="document">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="assignInspectorModalLabel{{ $project->id }}">Assign Inspector to {{ $project->project_name }}</h5>
                                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                <span aria-hidden="true">×</span>
                                                            </button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <div id="assignFeedback{{ $project->id }}" class="alert" style="display: none;"></div>
                                                            <form id="assignInspectorForm{{ $project->id }}" method="POST" action="{{ route('myprojects.assign', $project->id) }}">
                                                                @csrf
                                                                <div class="form-group">
                                                                    <label for="inspector_id" class="text-dark">Select Inspector</label>
                                                                    <select name="inspector_id" id="inspector_id" class="form-control" required>
                                                                        <option value="">Select an Inspector</option>
                                                                        @foreach ($inspectors as $inspector)
                                                                            <option value="{{ $inspector->id }}">{{ $inspector->first_name }} {{ $inspector->last_name }}</option>
                                                                        @endforeach
                                                                    </select>
                                                                </div>
                                                                <div class="form-group mt-3">
                                                                    <button type="submit" class="btn btn-primary">Assign Inspector</button>
                                                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                                                </div>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- Reject Project Modal -->
                                            <div class="modal fade" id="rejectProjectModal{{ $project->id }}" tabindex="-1" role="dialog" aria-labelledby="rejectProjectModalLabel{{ $project->id }}" aria-hidden="true">
                                                <div class="modal-dialog" role="document">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="rejectProjectModalLabel{{ $project->id }}">Reject Project: {{ $project->project_name }}</h5>
                                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                <span aria-hidden="true">×</span>
                                                            </button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <div id="rejectFeedback{{ $project->id }}" class="alert" style="display: none;"></div>
                                                            <form id="rejectProjectForm{{ $project->id }}" method="POST" action="{{ route('builder.projects.updateStatus', $project->id) }}">
                                                                @csrf
                                                                @method('PATCH')
                                                                <input type="hidden" name="status" value="Rejected">
                                                                <p>Are you sure you want to reject this project?</p>
                                                                <div class="form-group mt-3">
                                                                    <button type="submit" class="btn btn-danger">Reject Project</button>
                                                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                                                </div>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- Objection Project Modal -->
                                            <div class="modal fade" id="objectionProjectModal{{ $project->id }}" tabindex="-1" role="dialog" aria-labelledby="objectionProjectModalLabel{{ $project->id }}" aria-hidden="true">
                                                <div class="modal-dialog" role="document">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="objectionProjectModalLabel{{ $project->id }}">Raise Violation: {{ $project->project_name }}</h5>
                                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                <span aria-hidden="true">×</span>
                                                            </button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <div id="objectionFeedback{{ $project->id }}" class="alert" style="display: none;"></div>
                                                            <form id="objectionProjectForm{{ $project->id }}" method="POST" action="{{ route('builder.projects.updateStatus', $project->id) }}">
                                                                @csrf
                                                                @method('PATCH')
                                                                <input type="hidden" name="status" value="Violation">
                                                                <div class="form-group">
                                                                    <label for="objection_comment" class="text-dark">Violation Comment</label>
                                                                    <textarea name="objection_comment" id="objection_comment" class="form-control" rows="4" placeholder="Enter objection details..." required></textarea>
                                                                </div>
                                                                <div class="form-group mt-3">
                                                                    <button type="submit" class="btn btn-info">Submit Violation</button>
                                                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                                                </div>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="alert alert-info text-center">
                                <i class="fa fa-info-circle mr-2"></i>
                                No projects found.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('style')
    <style>
        .btn-sm {
            padding: 0.25rem 0.5rem;
        }
        .badge {
            font-size: 0.8rem;
            padding: 0.3em 0.6em;
        }
        .table th, .table td {
            vertical-align: middle;
        }
        .form-control {
            height: calc(1.5em + 0.75rem + 2px);
        }
        .status-select {
            width: 120px;
        }
        .dropdown-toggle::after {
            margin-left: 0.5em;
        }
        .dropdown-menu {
            min-width: 150px;
        }
        .btn-orange {
            background-color: orange;
            border-color: orange;
            color: white;
        }
        .btn-orange:hover {
            background-color: darkorange;
            border-color: darkorange;
        }
    </style>
@endpush

@push('script')
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Success Message from Session
            @if(session('success'))
                Swal.fire({
                    title: 'Success!',
                    text: '{{ session('success') }}',
                    icon: 'success',
                    showConfirmButton: false,
                    timer: 4000,
                    timerProgressBar: true,
                    position: 'top-end',
                    toast: true,
                    customClass: {
                        popup: 'swal2-popup-custom',
                        title: 'swal2-title-custom',
                        content: 'swal2-content-custom'
                    }
                });
            @endif
        });
    </script>
    <script>
        $(document).ready(function() {
            // Assign Inspector Form Submission
            $('form[id^="assignInspectorForm"]').on('submit', function(e) {
                e.preventDefault();
                var form = $(this);
                var feedback = form.closest('.modal-body').find('.alert');
                $.ajax({
                    url: form.attr('action'),
                    type: 'POST',
                    data: form.serialize(),
                    dataType: 'json',
                    success: function(response) {
                        if (response.success && response.redirect) {
                            feedback.removeClass('alert-danger').addClass('alert-success').text(
                                response.message).show();
                            setTimeout(function() {
                                form.closest('.modal').modal('hide');
                                window.location.href = response.redirect;
                            }, 2000);
                        } else {
                            feedback.removeClass('alert-success').addClass('alert-danger').text(
                                'Unexpected response from server.').show();
                        }
                    },
                    error: function(xhr) {
                        var errorMsg = 'An error occurred while assigning the inspector.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMsg = xhr.responseJSON.message;
                        } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                            errorMsg = Object.values(xhr.responseJSON.errors).flat().join(' ');
                        }
                        feedback.removeClass('alert-success').addClass('alert-danger').text(
                            errorMsg).show();
                    }
                });
            });

            // Reject Project Form Submission
            $('form[id^="rejectProjectForm"]').on('submit', function(e) {
                e.preventDefault();
                var form = $(this);
                var feedback = form.closest('.modal-body').find('.alert');
                $.ajax({
                    url: form.attr('action'),
                    type: 'POST',
                    data: form.serialize(),
                    dataType: 'json',
                    success: function(response) {
                        if (response.success && response.redirect) {
                            feedback.removeClass('alert-danger').addClass('alert-success').text(
                                response.message).show();
                            setTimeout(function() {
                                form.closest('.modal').modal('hide');
                                window.location.href = response.redirect;
                            }, 2000);
                        } else {
                            feedback.removeClass('alert-success').addClass('alert-danger').text(
                                'Unexpected response from server.').show();
                        }
                    },
                    error: function(xhr) {
                        var errorMsg = 'An error occurred while rejecting the project.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMsg = xhr.responseJSON.message;
                        } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                            errorMsg = Object.values(xhr.responseJSON.errors).flat().join(' ');
                        }
                        feedback.removeClass('alert-success').addClass('alert-danger').text(
                            errorMsg).show();
                    }
                });
            });

            // Objection Project Form Submission
            $('form[id^="objectionProjectForm"]').on('submit', function(e) {
                e.preventDefault();
                var form = $(this);
                var feedback = form.closest('.modal-body').find('.alert');
                $.ajax({
                    url: form.attr('action'),
                    type: 'POST',
                    data: form.serialize(),
                    dataType: 'json',
                    success: function(response) {
                        if (response.success && response.redirect) {
                            feedback.removeClass('alert-danger').addClass('alert-success').text(
                                response.message).show();
                            setTimeout(function() {
                                form.closest('.modal').modal('hide');
                                window.location.href = response.redirect;
                            }, 2000);
                        } else {
                            feedback.removeClass('alert-success').addClass('alert-danger').text(
                                'Unexpected response from server.').show();
                        }
                    },
                    error: function(xhr) {
                        var errorMsg = 'An error occurred while submitting the objection.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMsg = xhr.responseJSON.message;
                        } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                            errorMsg = Object.values(xhr.responseJSON.errors).flat().join(' ');
                        }
                        feedback.removeClass('alert-success').addClass('alert-danger').text(
                            errorMsg).show();
                    }
                });
            });

            // Clear feedback when modals are closed
            $('.modal').on('hidden.bs.modal', function() {
                $(this).find('.alert').hide().removeClass('alert-success alert-danger').text('');
                $(this).find('form')[0].reset();
            });
        });
    </script>
@endpush