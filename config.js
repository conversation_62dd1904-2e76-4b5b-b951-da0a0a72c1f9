// Configuration settings for the PDF annotation tool
export const config = {
  // API Configuration
  api: {
    baseUrl: import.meta.env.VITE_API_URL || 'http://localhost:3001',
    timeout: 30000, // 30 seconds
  },
  
  // File Upload Configuration
  upload: {
    maxFileSize: 50 * 1024 * 1024, // 50MB
    allowedTypes: ['application/pdf'],
  },
  
  // Canvas Configuration
  canvas: {
    maxPages: 100,
    defaultScale: 1.5,
    minScale: 0.5,
    maxScale: 3.0,
  },
  
  // Annotation Configuration
  annotations: {
    maxUndoSteps: 50,
    defaultBrushSize: 2,
    defaultTextSize: 20,
    colors: {
      default: '#000000',
      options: ['#000000', '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF']
    }
  },
  
  // UI Configuration
  ui: {
    debounceTime: 500,
    errorDisplayTime: 5000,
    resizeThreshold: 10,
  }
} 