<?php

use App\Http\Controllers\AverageReviewTimeController;
use App\Http\Controllers\McdSupportTicketController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\ComplianceController;
use App\Http\Controllers\PermitNumberController;

use App\Http\Controllers\ReviewProcessController;
use App\Http\Controllers\RolesController;

use App\Http\Controllers\BuilderProjectController;
use App\Http\Controllers\McdStaffController;
use App\Http\Controllers\McdStaffRoleController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\RuleRegulationController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\MilestoneController;
use App\Http\Controllers\MunicipalController;
use App\Http\Controllers\DeptReviewController;

use App\Http\Controllers\ProjectMunicipalController;

use App\Http\Controllers\InspectorController;
use App\Http\Controllers\BuilderWebsiteProject;

use App\Http\Controllers\PhysicalInspectionController;
use App\Http\Controllers\RolePermissionController;
use App\Http\Controllers\EmailMessageController;

use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\AssignPermitController;
use App\Http\Controllers\GuidelineController;
use App\Http\Controllers\PDFProxyController;
use App\Http\Controllers\AnnotatedPdfController;





Route::get('/inspector/create', [InspectorController::class, 'create'])->name('inspectors.create');
Route::post('/inspectors', [InspectorController::class, 'store'])->name('inspectors.store');




Route::middleware('auth:web,staff_mcd')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Password update route with multi-guard support
    Route::put('/password', [\App\Http\Controllers\Auth\PasswordController::class, 'update'])->name('password.update');
    
    Route::get('/dashboard', function () {
        return view('welcome');
    })->name('dashboard');

    Route::get('/staff-dashboard', function () {
        return view('welcome'); // Or a different view like 'staff.dashboard'
    })->name('staff.dashboard');
   
  
    
    Route::get('/support-tickets', [McdSupportTicketController::class, 'index'])->name('support_tickets.index');
    Route::get('/support-tickets/create', [McdSupportTicketController::class, 'create'])->name('support_tickets.create');
    Route::post('/support-tickets/store', [McdSupportTicketController::class, 'store'])->name('support_tickets.store');
    Route::get('/support-tickets/{id}/edit', [McdSupportTicketController::class, 'edit'])->name('support_tickets.edit');
    Route::put('/support-tickets/{id}', [McdSupportTicketController::class, 'update'])->name('support_tickets.update');
    Route::delete('/support-tickets/{id}', [McdSupportTicketController::class, 'destroy'])->name('support_tickets.destroy');
    Route::patch('/support-tickets/{id}/status', [McdSupportTicketController::class, 'updateStatus'])->name('support_tickets.updateStatus');

    Route::prefix('staff-management')->group(function () {
        Route::get('/index', [McdStaffController::class, 'index'])->name('mcd-staff.index');
    
        Route::get('/edit/{id}', [McdStaffController::class, 'edit'])->name('mcd-staff.edit');
        Route::put('/update/{id}', [McdStaffController::class, 'update'])->name('mcd-staff.update');
        Route::delete('/destroy/{id}', [McdStaffController::class, 'destroy'])->name('mcd-staff.destroy');
    });

    Route::prefix('roles')->group(function () {

        
        Route::get('/', [RolesController::class, 'index'])->name('roles.index');
        Route::post('/send-invite-role', [RolesController::class, 'sendInvite'])->name('roles_mcd.send-invite');
        Route::post('/roles/update-status', [RolesController::class, 'updateStatus'])->name('roles.update-status');
        Route::get('/edit/{id}', [RolesController::class, 'edit'])->name('roles.edit');
        Route::put('/update/{id}', [RolesController::class, 'update'])->name('roles.update');
        Route::delete('/destroy/{id}', [RolesController::class, 'destroy'])->name('roles.destroy');
        Route::get('/create', [RolesController::class, 'create'])->name('roles.create');
        Route::post('/store', [RolesController::class, 'store'])->name('roles.store');  
    });

 Route::prefix('permissions')->group(function () {

    Route::get('/permission-roles/{id}/assign-permissions', [RolePermissionController::class, 'assignPermissions'])->name('mcd-permission-roles.assign-permissions');
    Route::put('/permission-roles/{id}/store-permissions', [RolePermissionController::class, 'storePermissions'])->name('mcd-permission-roles.store-permissions');
    Route::get('/permission-roles/{id}/edit-permissions', [RolePermissionController::class, 'editPermissions'])->name('mcd-permission-roles.edit-permissions');

    Route::get('/', [RolePermissionController::class, 'index'])->name('permissions.index');
    Route::get('/roles/{role}/permissions', [RolePermissionController::class, 'edit'])->name('role_permissions.edit');
    Route::put('/roles/{role}/permissions', [RolePermissionController::class, 'update'])->name('role_permissions.update');
    Route::put('/staff-permissions/update', [RolePermissionController::class, 'updateStaffPermissions'])->name('staff_permissions.update');
});




    Route::prefix('department-management')->group(function () {
        Route::get('/staff-roles/{id}/assign-permissions', [McdStaffRoleController::class, 'assignPermissions'])->name('mcd-staff-roles.assign-permissions');
        Route::put('/staff-roles/{id}/store-permissions', [McdStaffRoleController::class, 'storePermissions'])->name('mcd-staff-roles.store-permissions');
        Route::get('/create', [McdStaffRoleController::class, 'create'])->name('mcd-staff-roles.create');
        Route::get('/index', [McdStaffRoleController::class, 'index'])->name('mcd-staff-roles.index');
        Route::post('/store', [McdStaffRoleController::class, 'store'])->name('mcd-staff-roles.store');
        Route::get('/edit/{id}', [McdStaffRoleController::class, 'edit'])->name('mcd-staff-roles.edit');
        Route::put('/update/{id}', [McdStaffRoleController::class, 'update'])->name('mcd-staff-roles.update');
        Route::delete('/destroy/{id}', [McdStaffRoleController::class, 'destroy'])->name('mcd-staff-roles.destroy');
        Route::post('/staff-roles/update-status', [McdStaffRoleController::class, 'updateStatus'])->name('staff-roles.update-status');
        Route::post('/send-invite', [McdStaffRoleController::class, 'sendInvite'])->name('mcd-staff-roles.send-invite');

    });

    Route::get('/inspectors', [InspectorController::class, 'index'])->name('inspectors.index');

    Route::get('/inspectors/{inspector}', [InspectorController::class, 'show'])->name('inspectors.show');
    Route::get('/inspectors/{inspector}/edit', [InspectorController::class, 'edit'])->name('inspectors.edit');
    Route::put('/inspectors/{inspector}', [InspectorController::class, 'update'])->name('inspectors.update');
    Route::delete('/inspectors/{inspector}', [InspectorController::class, 'destroy'])->name('inspectors.destroy');
    Route::post('/inspectors/invite', [InspectorController::class, 'invite'])->name('inspectors.invite');


    // Notification routes
    Route::get('/admin/notifications/create', [NotificationController::class, 'create'])->name('notifications.create');
    Route::post('/admin/notifications', [NotificationController::class, 'store'])->name('notifications.store');
    Route::get('/admin/notifications', [NotificationController::class, 'index'])->name('notifications.index');
    Route::get('/admin/notifications/{notification}', [NotificationController::class, 'show'])->name('notifications.show');
    Route::delete('/admin/notifications/{notification}', [NotificationController::class, 'destroy'])->name('notifications.destroy');


    Route::get('/compliances', [ComplianceController::class, 'index'])->name('compliances.index');

    // Show create form
    Route::get('/compliances/create', [ComplianceController::class, 'create'])->name('compliances.create');
    Route::post('/compliances', [ComplianceController::class, 'store'])->name('compliances.store');
    // Show edit form
    Route::get('/compliances/{id}/edit', [ComplianceController::class, 'edit'])->name('compliances.edit');

    Route::put('/compliances/{id}', [ComplianceController::class, 'update'])->name('compliances.update');
    Route::delete('/compliances/{id}', [ComplianceController::class, 'destroy'])->name('compliances.destroy');


    /// permit numbver
    Route::get('permit-numbers', [PermitNumberController::class, 'index'])->name('permit-numbers.index');
    Route::get('assign-permit-numbers', [PermitNumberController::class, 'assignIndex'])->name('assign-permit-numbers.index');
    Route::put('/permit-numbers-update/{id}', [PermitNumberController::class, 'updatePermitnumber'])->name('update.permit-number');
    Route::post('/permit-number-assign-store/assign/{project}', [PermitNumberController::class, 'assignStore'])->name('assign.permit-number');



    Route::get('permit-numbers/create', [PermitNumberController::class, 'create'])->name('permit-numbers.create');
    Route::post('permit-numbers', [PermitNumberController::class, 'store'])->name('permit-numbers.store');
    Route::get('/permit-numbers/{id}/edit', [PermitNumberController::class, 'edit'])->name('permit-numbers.edit');
    Route::put('permit-numbers/{id}', [PermitNumberController::class, 'update'])->name('permit-numbers.update');
    Route::delete('permit-numbers/{id}', [PermitNumberController::class, 'destroy'])->name('permit-numbers.destroy');


    Route::prefix('staff-management')->group(function () {
        Route::get('/index', [McdStaffController::class, 'index'])->name('mcd-staff.index');
        Route::get('/create', [McdStaffController::class, 'create'])->name('mcd-staff.create');
        Route::post('/store', [McdStaffController::class, 'store'])->name('mcd-staff.store');
        Route::get('/edit/{id}', [McdStaffController::class, 'edit'])->name('mcd-staff.edit');
        Route::put('/update/{id}', [McdStaffController::class, 'update'])->name('mcd-staff.update');
        Route::delete('/destroy/{id}', [McdStaffController::class, 'destroy'])->name('mcd-staff.destroy');
        Route::post('/builder-staff/{id}/status', [McdStaffController::class, 'updateStatus'])->name('mcd-staff.update.status');
        Route::get('/thankyou',[McdStaffController::class,'thankyou'])->name('mcd.thankyou.page');
        Route::get('/already-submitted', function () {
                return view('modules.mcd_staff.alreadyadded');
            })->name('mcd.already.submitted.page');

             Route::get('/765765yhdt', function () {
                return view('modules.mcd_staff.inspectorthankyou');
            })->name('mcd.inspector.thankyou.page');
    });

    Route::prefix('projects-for-review')->group(function () {
        Route::get('/create', [BuilderProjectController::class, 'add'])->name('project.create');
        Route::get('/index', [BuilderProjectController::class, 'index'])->name('project.index');
        Route::post('/store', [BuilderProjectController::class, 'store'])->name('project.store');
        Route::get('/edit/{id}', [BuilderProjectController::class, 'edit'])->name('project.edit');
        Route::put('/update/{id}', [BuilderProjectController::class, 'update'])->name('project.update');
        Route::delete('/destroy/{id}', [BuilderProjectController::class, 'destroy'])->name('project.destroy');
        Route::post('/project/{id}/status', [BuilderProjectController::class, 'updateStatus'])->name('project.status.update');  
    });

    Route::get('/MyProject-index', [BuilderWebsiteProject::class, 'index'])->name('myprojects.index');
    Route::get('/MyProject-view/{id}',[BuilderWebsiteProject::class, 'show'])->name('myprojects.show');
    Route::get('/project-details/{id}', [BuilderWebsiteProject::class, 'showStatus'])->name('myprojects.show.status');

    Route::match(['get', 'post'], '/MyProject-assign/{id}', [BuilderWebsiteProject::class, 'assign'])->name('myprojects.assign');
    Route::get('/all-Projects-review', [BuilderWebsiteProject::class, 'nonPendingProjects'])->name('myprojects.non_pending');
    Route::get('/all-Projects-backlogs', [BuilderWebsiteProject::class, 'backlogsAnalysis'])->name('myprojects.backlogs');


 

    Route::patch('builder/projects/{id}/status', [BuilderWebsiteProject::class, 'updateStatus'])->name('builder.projects.updateStatus');


    Route::prefix('total-projects-review-')->group(function () {
        Route::get('/index', [BuilderProjectController::class, 'index1'])->name('total-project-review.index');
    });


    Route::prefix('my-muncipal')->group(function () {
        Route::get('/Municipals', [MunicipalController::class, 'index'])->name('Municipals.index');
        Route::get('/Municipals/create', [MunicipalController::class, 'create'])->name('Municipals.create');
        Route::post('/Municipals', [MunicipalController::class, 'store'])->name('Municipals.store');
        Route::get('/Municipals/{municipal}', [MunicipalController::class, 'show'])->name('Municipals.show');
        Route::get('/Municipals/{municipal}/edit', [MunicipalController::class, 'edit'])->name('Municipals.edit');
        Route::put('/Municipals/{municipal}', [MunicipalController::class, 'update'])->name('Municipals.update');
        Route::delete('/Municipals/{municipal}', [MunicipalController::class, 'destroy'])->name('Municipals.destroy');
        Route::get('/project-approval', [BuilderProjectController::class, 'projectApproval'])->name('munciple.project_approval');
        Route::get('/project-objection', [BuilderProjectController::class, 'projectObjection'])->name('munciple.project_objection');
    });
    //reveiwprocess steps
    Route::get('/review/processes/view', [ReviewProcessController::class, 'index'])->name('review_processes.index');
    Route::get('/review-processes/create', [ReviewProcessController::class, 'create'])->name('review_processes.create');
    Route::post('/review-processes', [ReviewProcessController::class, 'store'])->name('review_processes.store');
    Route::get('/review-processes/{reviewProcess}/edit', [ReviewProcessController::class, 'edit'])->name('review_processes.edit');
    Route::put('/review-processes/{reviewProcess}', [ReviewProcessController::class, 'update'])->name('review_processes.update');
    Route::delete('/review-processes/{reviewProcess}', [ReviewProcessController::class, 'destroy'])->name('review_processes.destroy');


//DepartmentalReview commissionreview
Route::get('/deptreview-index', [DeptReviewController::class, 'index'])->name('deptreview.index');
Route::get('/commissionreview', [DeptReviewController::class, 'commissionreview'])->name('commissionreview');
Route::get('/deptreview-show/{id}', [DeptReviewController::class, 'showreview'])->name('deptreview.showreview');
Route::get('/planningcommission-show/{id}', [DeptReviewController::class, 'showReviewPlanning'])->name('planningcommission');
// Route::get('projectdocument/{id}', [DeptReviewController::class, 'showprojectdocument'])->name('showprojectdocument');
Route::get('/projectdocument/{id}/{department?}/{step_index?}', [DeptReviewController::class, 'showprojectdocument'])->name('showprojectdocument');
Route::get('/review/process-complete/{project}/{department}/{step}', [DeptReviewController::class, 'process_complete'])->name('process.complete');
Route::post('/projects/{project}/documents-upload', [DeptReviewController::class, 'uploadDocuments'])->name('project.documents.upload');
Route::post('/projects/{project}/assign-department', [DeptReviewController::class, 'assignDepartment'])
    ->name('assign.department');
    Route::post('/review-documents/update-status-pc', [DeptReviewController::class, 'updateProStatusPc'])->name('review-documents.updateStatuspc');
Route::post('/projects/{project}/assign-department', [DeptReviewController::class, 'assignDepartment'])->name('assign.department');
Route::get('/planningcommission/process-complete/{project}/{department}/{step}', [DeptReviewController::class, 'process_complete_pc'])->name('planningcommission.process.complete');
Route::get('/projectdocument/{id}/{department?}/{step_index?}', [DeptReviewController::class, 'showprojectdocumentpc'])->name('showprojectdocumentpc');
Route::get('/review-history/page', [DeptReviewController::class, 'showReviewHistoryPage'])->name('review.history.page');
Route::get('/review-history/page/pc', [DeptReviewController::class, 'showReviewHistoryPagePc'])->name('review.history.page.pc');

Route::post('/project/{id}/documents/remark-pc', [DeptReviewController::class, 'addRemarkPc'])->name('project.documents.remark.pc');



Route::get('/deptreview/pdf-editor/{id}/{field}', [DeptReviewController::class, 'pdfEditor'])->name('deptreview.pdfEditor');
Route::post('/projects/{projectId}/annotations', [DeptReviewController::class, 'saveAnnotations'])->name('project.annotations.save');
Route::post('project/{projectId}/documents/remark', [DeptReviewController::class, 'addRemark'])->name('project.documents.remark');
Route::post('/projects/{id}/status', [DeptReviewController::class, 'updateProjectStatus'])->name('projects.status.update');
Route::post('/review-documents/update-status', [DeptReviewController::class, 'updateProStatus'])->name('review-documents.updateStatus');


//guidlines
Route::get('/guidelines', [GuidelineController::class, 'index'])->name('guidelines.index');
Route::get('/guidelines/create', [GuidelineController::class, 'create'])->name('guidelines.create');
Route::post('/guidelines', [GuidelineController::class, 'store'])->name('guidelines.store');
Route::get('/guidelines/{guideline}', [GuidelineController::class, 'show'])->name('guidelines.show');
Route::get('/guidelines/{guideline}/edit', [GuidelineController::class, 'edit'])->name('guidelines.edit');
Route::put('/guidelines/{guideline}', [GuidelineController::class, 'update'])->name('guidelines.update');
Route::delete('/guidelines/{guideline}', [GuidelineController::class, 'destroy'])->name('guidelines.destroy');

//physical inspection

Route::get('/physical-inspection', [PhysicalInspectionController::class, 'index'])->name('physicalinspection.index');
Route::get('/physical-inspectionsreport', [PhysicalInspectionController::class, 'showreport'])->name('physicalinspection.report');
Route::get('/physical-inspection/detailreport/{id}', [PhysicalInspectionController::class, 'detailreport'])->name('physical-inspection.detailreport');
Route::get('complianceflag', [PhysicalInspectionController::class, 'complianceflag'])->name('physical-inspection.complianceflag');
Route::get('/physicalinspection/download/{id}', [PhysicalInspectionController::class, 'downloadReport'])->name('physicalinspection.download');


// List all project municipals
Route::get('/project_municipals', [ProjectMunicipalController::class, 'index'])->name('project_municipals.index');

// Show create form
Route::get('/project_municipals/create', [ProjectMunicipalController::class, 'create'])->name('project_municipals.create');

// Store new project municipal
Route::post('/project_municipals', [ProjectMunicipalController::class, 'store'])->name('project_municipals.store');

// Show edit form
Route::get('/project_municipals/{projectMunicipal}/edit', [ProjectMunicipalController::class, 'edit'])->name('project_municipals.edit');

// Update project municipal
Route::put('/project_municipals/{projectMunicipal}', [ProjectMunicipalController::class, 'update'])->name('project_municipals.update');

// Delete project municipal
Route::delete('/project_municipals/{projectMunicipal}', [ProjectMunicipalController::class, 'destroy'])->name('project_municipals.destroy');

Route::prefix('rules')->name('rules.')->group(function () {
    Route::get('/', [RuleRegulationController::class, 'index'])->name('index'); // List all rules
    Route::get('/create', [RuleRegulationController::class, 'create'])->name('create'); // Show create form
    Route::post('/', [RuleRegulationController::class, 'store'])->name('store'); // Store new rule
    Route::get('/{rule}', [RuleRegulationController::class, 'show'])->name('show'); // Show single rule
    Route::get('/{rule}/edit', [RuleRegulationController::class, 'edit'])->name('edit'); // Show edit form
    Route::put('/{rule}', [RuleRegulationController::class, 'update'])->name('update'); // Update rule
    Route::delete('/{rule}', [RuleRegulationController::class, 'destroy'])->name('destroy'); // Delete rule
  

});
//message
Route::get('/communication-message', [EmailMessageController::class, 'index'])->name('communication.index');
Route::get('/communication/superadmins', [EmailMessageController::class, 'superadmins'])->name('communication.superadmins');
Route::get('/communication/builders', [EmailMessageController::class, 'builders'])->name('communication.builders');
Route::get('/communication/architects', [EmailMessageController::class, 'architects'])->name('communication.architects');
Route::get('/communication/mcd', [EmailMessageController::class, 'mcd'])->name('communication.mcd');
Route::get('/communication/inspector', [EmailMessageController::class, 'inspector'])->name('communication.inspector');
Route::post('/communication/inspector', [EmailMessageController::class, 'storeInspector'])->name('messages.store-inspector');
Route::get('/communication/messageswithall', [EmailMessageController::class, 'message'])->name('messages.index');
Route::post('/messages/store', [EmailMessageController::class, 'store'])->name('messages.store');
Route::post('/messages/builder-store', [EmailMessageController::class, 'storeBuilder'])->name('messages.store-builder');
Route::post('/messages/engineer-store', [EmailMessageController::class, 'storeEngineer'])->name('messages.store-engineer');
Route::post('/messages/superadmin-store', [EmailMessageController::class, 'storeSuperadmin'])->name('messages.storeSuperadmin');

Route::get('/messages/{id}/attachments', [EmailMessageController::class, 'showAttachments']);
Route::delete('/messages/{id}', [EmailMessageController::class, 'delete'])->name('messages.delete');
Route::get('/messages/{id}/attachments', [EmailMessageController::class, 'showAttachments']);
Route::post('/messages/reply', [EmailMessageController::class, 'reply'])->name('messages.reply');
Route::post('/messages/star', [EmailMessageController::class, 'star'])->name('messages.star');





Route::get('/documents/{filename}', function ($filename) {
    $filePath = storage_path('app/public/documents/' . $filename);
    if (file_exists($filePath)) {
        return response()->file($filePath);
    }
    abort(404, 'File nahi mila');
});


Route::prefix('review-times')->group(function () {
    Route::get('/create', [AverageReviewTimeController::class, 'create'])->name('average-review-times.create');
    Route::get('/index', [AverageReviewTimeController::class, 'index'])->name('average-review-times.index');
    Route::post('/store', [AverageReviewTimeController::class, 'store'])->name('average-review-times.store');
    Route::get('/edit/{averageReviewTime}', [AverageReviewTimeController::class, 'edit'])->name('average-review-times.edit');
    Route::put('/update/{averageReviewTime}', [AverageReviewTimeController::class, 'update'])->name('average-review-times.update');
    Route::delete('/destroy/{averageReviewTime}', [AverageReviewTimeController::class, 'destroy'])->name('average-review-times.destroy');
});
//permit by department
Route::prefix('project-department-permits')->group(function () {
    Route::get('/', [AssignPermitController::class, 'index'])->name('project-department-permits.index');
    Route::get('/create', [AssignPermitController::class, 'create'])->name('project-department-permits.create');
    Route::post('/', [AssignPermitController::class, 'store'])->name('project-department-permits.store');
    Route::get('/{id}/edit', [AssignPermitController::class, 'edit'])->name('project-department-permits.edit');
    Route::put('/{id}', [AssignPermitController::class, 'update'])->name('project-department-permits.update');
    Route::delete('/{id}', [AssignPermitController::class, 'destroy'])->name('project-department-permits.destroy');
    Route::get('/assign', [AssignPermitController::class, 'assignIndex'])->name('project-department-permits.assign');
    Route::post('/assign/{project}', [AssignPermitController::class, 'assignStore'])->name('project-department-permits.assign.store');
     Route::get('/departments/{projectId}', [AssignPermitController::class, 'getProjectDepartments'])->name('project-department-permits.departments');
});


// Prefix 'project' ke saath group banaya
Route::prefix('project')->group(function () {
    // List all milestones
    Route::get('/milestones', [MilestoneController::class, 'index'])
        ->name('project.milestones.index');

    // Show create form
    Route::get('/milestones/create', [MilestoneController::class, 'create'])
        ->name('project.milestones.create');

    // Store new milestone
    Route::post('/milestones', [MilestoneController::class, 'store'])
        ->name('project.milestones.store');

    // Show single milestone
    Route::get('/milestones/{milestone}', [MilestoneController::class, 'show'])
        ->name('project.milestones.show');

    // Show edit form
    Route::get('/milestones/{milestone}/edit', [MilestoneController::class, 'edit'])
        ->name('project.milestones.edit');

    // Update milestone
    Route::put('/milestones/{milestone}', [MilestoneController::class, 'update'])
    ->name('project.milestones.update');



    Route::delete('/milestones/{milestone}/delete', [MilestoneController::class, 'destroy'])
    ->name('project.milestones.destroy');
    Route::post('/project/milestones/{milestone}/update-status', [MilestoneController::class, 'updateStatus'])
    ->name('project.milestones.update.status');

});

//middleware end
});


    Route::get('/create', [McdStaffController::class, 'create'])->name('mcd-staff.create');
    Route::post('/store', [McdStaffController::class, 'store'])->name('mcd-staff.store');

require __DIR__ . '/auth.php';

Route::get('/proxy-pdf', [PDFProxyController::class, 'servePDF'])->name('proxy.pdf');

// Debug route for user tracking
Route::get('/debug-user-tracking', function () {
    $userId = null;
    $userRole = 'unknown';
    $userName = 'Unknown User';

    if (auth()->guard('staff_mcd')->check()) {
        $userId = auth()->guard('staff_mcd')->id();
        $user = auth()->guard('staff_mcd')->user();
        $userRole = $user->role->name ?? 'mcd_staff';
        $userName = $user->staff_name ?? 'MCD Staff';
        $guardType = 'staff_mcd';
    } elseif (auth()->guard('web')->check()) {
        $userId = auth()->guard('web')->id();
        $user = auth()->guard('web')->user();
        $userRole = 'admin'; // Web users are admin by default
        $userName = $user->name ?? 'Admin User';
        $guardType = 'web';
    } elseif (auth()->guard('staff_builder')->check()) {
        $userId = auth()->guard('staff_builder')->id();
        $user = auth()->guard('staff_builder')->user();
        $userRole = $user->role->name ?? 'builder_staff';
        $userName = $user->staff_name ?? 'Builder Staff';
        $guardType = 'staff_builder';
    } else {
        $guardType = 'none';
    }

    return response()->json([
        'authenticated' => auth()->check(),
        'guard_type' => $guardType,
        'user_id' => $userId,
        'user_role' => $userRole,
        'user_name' => $userName,
        'sample_url' => "http://localhost:3001/?file=test.pdf&projectId=15&departmentId=8&reviewStep=Design%20Submission&userId={$userId}&userRole=" . urlencode($userRole) . "&userName=" . urlencode($userName)
    ]);
});


