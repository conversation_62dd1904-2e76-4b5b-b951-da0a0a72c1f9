<?php
namespace App\Http\Controllers;

use App\Models\Municipal;
use Illuminate\Http\Request;

class MunicipalController extends Controller
{
    public function index()
    {
        $Municipals = Municipal::all();
        return view('modules.munciple.index', compact('Municipals'));
    }

    public function create()
    {
        return view('modules.munciple.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:55',
            'description' => 'required',
            'location' => 'required|string|max:55',
            'status' => 'required|in:pending,in_progress,resolved',
            'priority' => 'required|in:low,medium,high',
            'reported_date' => 'required|date',
            'assigned_to' => 'nullable|string|max:55',
            'budget' => 'nullable|numeric',
            'resolution_date' => 'nullable|date',
        ]);

        Municipal::create($request->all());
        $data['status'] = 'pending';
        return redirect()->route('Municipals.index')->with('success', 'Municipal created successfully.');
    }
    public function edit(Municipal $municipal)
    {
        return view('modules.munciple.edit', compact('municipal'));
    }

    public function update(Request $request, Municipal $municipal)
    {
        $request->validate([
            'title' => 'required|string|max:55',
            'description' => 'required',
            'location' => 'required|string|max:55',
            'status' => 'required|in:pending,in_progress,resolved',
            'priority' => 'required|in:low,medium,high',
            'reported_date' => 'required|date',
            'assigned_to' => 'nullable|string|max:55',
            'budget' => 'nullable|numeric',
            'resolution_date' => 'nullable|date',
        ]);

        $municipal->update($request->all());
        $data['status'] = 'pending';
        return redirect()->route('Municipals.index')->with('success', 'Municipal updated successfully.');
    }
   

   
   

    public function destroy(Municipal $Municipal)
    {
        $Municipal->delete();
        return redirect()->route('Municipals.index')->with('success', 'Municipal deleted successfully.');
    }
}
