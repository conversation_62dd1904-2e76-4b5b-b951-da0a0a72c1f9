<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    // public function up(): void
    // {
    //     Schema::create('average_review_times', function (Blueprint $table) {
    //         $table->id();
    //         $table->foreignId('builder_project_id')->constrained()->onDelete('cascade');
    //         $table->string('project_name');
    //         $table->integer('days'); // Changed from integer 'time' to date
    //         $table->date('end_date'); // Changed from integer 'time' to date
    //         $table->timestamps();
    //     });
    // }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('average_review_times');
    }
};
