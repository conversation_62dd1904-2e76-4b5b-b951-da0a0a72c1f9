<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMilestonesTable extends Migration
{
    public function up()
    {
        Schema::create('milestones', function (Blueprint $table) {
            $table->id(); // Auto-incrementing primary key
            $table->string('name'); // Milestone Name
            $table->date('start_date'); // Start Date
            $table->date('end_date'); // End Date
            $table->date('actual_completion_date')->nullable(); // Actual Completion Date (nullable)
            $table->enum('status', ['Active', 'Inactive']); // Status as enum with "Active" and "Inactive"
            $table->string('assigned_to'); // Assigned To
            $table->integer('progress_percentage')->default(0); // Progress Percentage (0-100)
            $table->string('dependencies')->nullable(); // Dependencies (optional)
            $table->timestamps(); // created_at and updated_at
        });
    }

    public function down()
    {
        Schema::dropIfExists('milestones');
    }
}
