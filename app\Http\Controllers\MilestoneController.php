<?php

namespace App\Http\Controllers;

use App\Models\Milestone;
use Illuminate\Http\Request;

class MilestoneController extends Controller
{
    public function index()
    {
        $milestones = Milestone::all();
        return view('modules.milestones.index', compact('milestones'));
    }

    public function create()
    {
        return view('modules.milestones.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'status' => 'required|in:Active,Inactive', // Ensure only valid enum values
            'assigned_to' => 'required|string',
            'progress_percentage' => 'required|integer|min:0|max:100',
            'dependencies' => 'nullable|string',
        ]);

        Milestone::create($request->all());
        return redirect()->route('project.milestones.index')->with('success', 'Milestone created successfully.');
    }

    // public function show(Milestone $milestone)
    // {
    //     return view('modules.milestones.show', compact('milestone'));
    // }

    public function edit(Milestone $milestone)
    {
        return view('modules.milestones.edit', compact('milestone'));
    }

    public function update(Request $request, Milestone $milestone)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'status' => 'required|in:Active,Inactive', // Ensure only valid enum values
            'assigned_to' => 'required|string',
            'progress_percentage' => 'required|integer|min:0|max:100',
            'dependencies' => 'nullable|string',
        ]);

        $milestone->update($request->all());
        return redirect()->route('project.milestones.index')->with('success', 'Milestone updated successfully.');
    }

    public function destroy(Milestone $milestone)
    {
        $milestone->delete();
        return redirect()->route('project.milestones.index')->with('success', 'Milestone deleted successfully.');
    }

    public function updateStatus(Request $request, Milestone $milestone)
    {
        $request->validate([
            'status' => 'required|in:Active,Inactive'
        ]);

        $milestone->update([
            'status' => $request->status
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Status updated successfully'
        ]);
    }
}
