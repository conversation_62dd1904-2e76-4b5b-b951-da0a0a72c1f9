<?php
namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class Inspector extends Authenticatable
{
    use HasApiTokens;

    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone',
        'designation',
        'department',
        'address',
        'status',
        'inspector_id',
        'image',
        'password', // If password updates are allowed
    ];

    public function role()
    {
        return $this->belongsTo(McdStaffRole::class, 'department');
    }
}