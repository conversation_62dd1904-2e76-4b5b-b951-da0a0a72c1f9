<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PhysicalInspectionReport extends Model
{
    protected $fillable = [
        'project_id', 'inspectors', 'report_images',
        'note', 'violation_note', 'report_status',
        'potential_violations', 'inspection_done_at',
    ];

    protected $casts = [
        'inspectors' => 'array',
        'report_images' => 'array',
        'potential_violations' => 'array',
        'inspection_done_at' => 'datetime',
    ];

    public function project()
    {
        return $this->belongsTo(WebsiteBuilderProject::class, 'project_id');
    }
}