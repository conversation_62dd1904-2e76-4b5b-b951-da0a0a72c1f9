<?php $__env->startSection('content'); ?>
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4>Physical Inspection > Active Inspections</h4>
                    </div>
                    <div class="card-body">
                        <!-- Department Filter Form -->
                        <form method="GET" action="#" class="mb-3">
                            <label for="tradeFilter">Filter by Department:</label>
                            <select name="trade" id="tradeFilter" class="form-control w-25" onchange="this.form.submit()">
                                <option value="">All Departments</option>
                                <?php $__currentLoopData = $departments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $id => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($id); ?>" <?php echo e(request('trade') == $id ? 'selected' : ''); ?>><?php echo e($name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </form>

                        <?php if($projects->count() > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="table-1">
                                    <thead class="bg-gray-200 text-gray-800">
                                        <tr>
                                            <th>Sr No</th>
                                            <th>Project Name</th>
                                            <th>Address</th>
                                            <th>Scope of Work</th>
                                            <th>Department(Trade)</th>
                                            <th>Date Range</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td class="text-center"><?php echo e($key + 1); ?></td>
                                                <td>
                                                    <?php if($project->project_name): ?>
                                                        <a href="<?php echo e(route('myprojects.show.status', ['id' => $project->id])); ?>">
                                                            <?php echo e($project->project_name); ?>

                                                        </a>
                                                    <?php else: ?>
                                                        N/A
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php echo e($project->project_address ?? 'N/A'); ?>,
                                                    <?php echo e($project->city ?? ''); ?>,
                                                    <?php echo e($project->state ?? ''); ?> <?php echo e($project->zip ?? ''); ?>

                                                </td>
                                                <td><?php echo e($project->scope_of_work ?? 'N/A'); ?></td>
                                                <td>
                                                    <?php if(!empty($project->department_names)): ?>
                                                        <?php echo e(implode(', ', $project->department_names)); ?>

                                                    <?php else: ?>
                                                        N/A
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php echo e($project->expected_start_date ? \Carbon\Carbon::parse($project->expected_start_date)->format('d M Y') : ' n/a'); ?>

                                                    -
                                                    <?php echo e($project->expected_end_date ? \Carbon\Carbon::parse($project->expected_end_date)->format('d M Y') : ' n/a'); ?>

                                                </td>
                                                <td>
                                                    <?php
                                                        if ($project->physicalInspectionReportnew) {
                                                            $statusText = $project->physicalInspectionReportnew->report_status === '2' ? 'Pending' : 'Unknown';
                                                            $badgeClass = $project->physicalInspectionReportnew->report_status === '2' ? 'warning' : 'secondary';
                                                        } else {
                                                            $statusText = 'Pending';
                                                            $badgeClass = 'warning';
                                                        }
                                                    ?>
                                                    <span class="badge bg-<?php echo e($badgeClass); ?>">
                                                        <?php echo e($statusText); ?>

                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info text-center">
                                <i class="fa fa-info-circle mr-2"></i>
                                No active inspections found.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('style'); ?>
    <style>
        .btn-sm {
            padding: 0.25rem 0.5rem;
        }
        .badge {
            font-size: 0.8rem;
            padding: 0.3em 0.6em;
        }
        .table th, .table td {
            vertical-align: middle;
        }
        .table-responsive {
            overflow-x: auto;
        }
        .form-control.w-25 {
            width: 25%;
            display: inline-block;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap4.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#table-1').DataTable({
                responsive: true,
                paging: true,
                searching: true,
                ordering: true,
                bLengthChange: false,
                language: {
                    search: "<i class='ti-search'></i>",
                    searchPlaceholder: 'Quick Search',
                    paginate: {
                        next: "<i class='ti-arrow-right'></i>",
                        previous: "<i class='ti-arrow-left'></i>",
                    },
                },
            });
        });
    </script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('mcdpanel.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\mcdconstructions\resources\views/BuilderWebsiteDashboard/physicalinspection/activeinspection.blade.php ENDPATH**/ ?>