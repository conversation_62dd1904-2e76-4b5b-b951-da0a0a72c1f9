@extends('mcdpanel.layouts.master')
@php
    $user = Auth::guard('staff_mcd')->user();
    $permissions = $user && $user->role ? $user->role->permissions : [];
    $isAdmin = Auth::guard('web')->check();
    $hasWritePermission = $isAdmin || (isset($permissions['plannining_commission_review']['write']) && $permissions['plannining_commission_review']['write'] == 1);
    $isApproved = isset($existingDocument) && $existingDocument->status == 2;
@endphp

@section('content')
<div class="main-content">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Combined Project Details + Review Info Card -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Project: {{ $project->project_name ?? 'N/A' }}</h4>
                    <a href="{{ route('commissionreview') }}" class="btn btn-primary btn-sm">Back to Planning Commission Review</a>
                </div>
                <div class="card-body">
                    {{-- @if ($department && $stepName) --}}
                        <div class="mb-3">
                            <h5>Reviewing Department (Trade): <strong class="text-info">{{ $department ?? 'All Departments' }}</strong></h5>
                            <h6>Step: <strong class="text-success">{{ $stepName ?? 'Step #' . ($step_index + 1) }}</strong></h6>
                        </div>
                    {{-- @endif --}}

                    <h5 class="mb-3">Project Information</h5>
                    <table class="table table-bordered mb-4">
                        <tbody>
                            <tr>
                                <th width="35%">Project Name</th>
                                <td>{{ $project->project_name ?? 'N/A' }}</td>
                            </tr>
                            <tr>
                                <th>Address</th>
                                <td>{{ $project->project_address ?? 'N/A' }}, {{ $project->city ?? 'N/A' }}, {{ $project->state ?? 'N/A' }}, {{ $project->zip ?? 'N/A' }}</td>
                            </tr>
                            <tr>
                                <th>Scope of Work</th>
                                <td>{{ $project->scope_of_work ?? 'N/A' }}</td>
                            </tr>
                            <tr>
                                <th>Departments (Trades)</th>
                                <td>
                                    @if (!empty($project->department_names))
                                        {{ implode(', ', $project->department_names) }}
                                    @else
                                        N/A
                                    @endif
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Document Upload Card -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Documents (PDFs / Images)</h5>
                    @if ($department && $stepName && $department_id)
                        <!-- Status Update Form -->
                        {{-- <form action="{{ $hasWritePermission ? route('review-documents.updateStatuspc') : 'javascript:void(0)' }}" 
                            method="POST"
                            class="d-flex align-items-center mb-3"
                            @if (!$hasWritePermission) onsubmit="showAccessDenied(); return false;" @endif>
                            
                            @csrf
                            <input type="hidden" name="project_id" value="{{ $project->id }}">
                            <input type="hidden" name="department_id" value="{{ $department_id }}">
                            <input type="hidden" name="review_step" value="{{ $stepName }}">

                            <label for="status" class="me-2 fw-bold mb-0">Review Status:</label>
                            
                            <select name="status" id="status" class="form-select form-select-sm w-auto me-2" 
                                @if (!$hasWritePermission) disabled @endif>
                                <option value="0" {{ isset($existingDocument) && $existingDocument->status == 0 ? 'selected' : '' }}>Pending</option>
                                <option value="1" {{ isset($existingDocument) && $existingDocument->status == 1 ? 'selected' : '' }}>Violation</option>
                                <option value="2" {{ isset($existingDocument) && $existingDocument->status == 2 ? 'selected' : '' }}>Approved</option>
                                <option value="3" {{ isset($existingDocument) && $existingDocument->status == 3 ? 'selected' : '' }}>Rejected</option>
                            </select>

                            @if ($hasWritePermission)
                                <button type="submit" class="btn btn-sm btn-success">Update Status</button>
                            @else
                                <button type="button" class="btn btn-sm btn-success" onclick="showAccessDenied()">Update Status</button>
                            @endif
                        </form> --}}

                        <form action="{{ $hasWritePermission && !$isApproved ? route('review-documents.updateStatuspc') : 'javascript:void(0)' }}" 
                            method="POST"
                            class="d-flex align-items-center mb-3"
                            @if (!$hasWritePermission || $isApproved) onsubmit="showAccessDenied(); return false;" @endif>

                            @csrf
                            <input type="hidden" name="project_id" value="{{ $project->id }}">
                            <input type="hidden" name="department_id" value="{{ $department_id }}">
                            <input type="hidden" name="review_step" value="{{ $stepName }}">

                            <label for="status" class="me-2 fw-bold mb-0">Review Status:</label>

                            <select name="status" id="status" class="form-select form-select-sm w-auto me-2"
                                @if (!$hasWritePermission || $isApproved) disabled @endif>
                                <option value="0" {{ isset($existingDocument) && $existingDocument->status == 0 ? 'selected' : '' }}>Pending</option>
                                <option value="1" {{ isset($existingDocument) && $existingDocument->status == 1 ? 'selected' : '' }}>Violation</option>
                                <option value="2" {{ isset($existingDocument) && $existingDocument->status == 2 ? 'selected' : '' }}>Approved</option>
                                <option value="3" {{ isset($existingDocument) && $existingDocument->status == 3 ? 'selected' : '' }}>Rejected</option>
                            </select>

                            <button type="submit"
                                class="btn btn-sm btn-success"
                                @if (!$hasWritePermission || $isApproved) type="button" onclick="showAccessDenied()" disabled @endif>
                                Update Status
                            </button>
                        </form>
                    @endif
                </div>
                <div class="card-body">
                    @php
                        $reviewDocs = \App\Models\ProjectReviewDocumentPc::where('project_id', $project->id)
                            ->where('department_id', $department_id ?? 0)
                            ->where('review_step', $stepName ?? '')
                            ->get()
                            ->groupBy('document_type');
                    @endphp

                    <table class="table table-bordered table-hover align-middle">
                        <thead class="table-light">
                            <tr>
                                <th>Document Type</th>
                                <th>Current File</th>
                                <th>Status Check</th>
                                <th>Remarks</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if ($allDocuments->isEmpty())
                                <tr>
                                    <td colspan="4" class="text-center text-muted">No documents or images available for this project.</td>
                                </tr>
                            @else
                                @foreach ($allDocuments as $doc)
                                    <tr>
                                        <td>{{ $doc->file_type_display }}</td>

                                        <!-- Show document or image -->
                                        <td>
                                            @php
                                                $ext = strtolower(pathinfo($doc->file_path, PATHINFO_EXTENSION));
                                                $baseUrl = $doc->file_type === 'project_image'
                                                    ? 'http://localhost/builderwebsite/public/profile_images/'
                                                    : 'http://localhost/builderwebsite/public/project_documents/';
                                                $url = $baseUrl . basename($doc->file_path);
                                                $hasRemark = !empty($reviewDocs[$doc->file_type]) && count($reviewDocs[$doc->file_type]) > 0;
                                                $btnClass = $hasRemark ? 'btn-primary' : 'btn-outline-primary';
                                            @endphp

                                            @if (in_array($ext, ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg']))
                                                <a href="{{ $url }}" target="_blank"
                                                   class="btn btn-sm {{ $btnClass }} mb-1">
                                                    <i class="fa fa-file-image"></i> View Image
                                                </a>
                                            @elseif ($ext === 'pdf')
                                                @php
                                                    $userId = null;
                                                    $userRole = 'unknown';
                                                    $userName = 'Unknown User';

                                                    if (auth()->guard('staff_mcd')->check()) {
                                                        $userId = auth()->guard('staff_mcd')->id();
                                                        $user = auth()->guard('staff_mcd')->user();
                                                        $userRole = $user->role->name ?? 'mcd_staff';
                                                        $userName = $user->staff_name ?? 'MCD Staff';
                                                    } elseif (auth()->guard('web')->check()) {
                                                        $userId = auth()->guard('web')->id();
                                                        $user = auth()->guard('web')->user();
                                                        $userRole = 'admin';
                                                        $userName = $user->name ?? 'Admin User';
                                                    } elseif (auth()->guard('staff_builder')->check()) {
                                                        $userId = auth()->guard('staff_builder')->id();
                                                        $user = auth()->guard('staff_builder')->user();
                                                        $userRole = $user->role->name ?? 'builder_staff';
                                                        $userName = $user->staff_name ?? 'Builder Staff';
                                                    }
                                                @endphp
                                                <a href="http://localhost:3001/?file={{ $url }}&projectId={{ $project->id }}&departmentId={{ $department_id }}&reviewStep={{ $stepName }}&userId={{ $userId }}&userRole={{ $userRole }}&userName={{ urlencode($userName) }}" target="_blank"
                                                   class="btn btn-sm btn-warning mb-1">
                                                    <i class="fa fa-edit"></i> Edit PDF
                                                </a>
                                                <a href="{{ $url }}" target="_blank"
                                                   class="btn btn-sm {{ $btnClass }} mb-1">
                                                    <i class="fa fa-file-pdf"></i> View PDF
                                                </a>
                                            @else
                                                <a href="{{ $url }}" target="_blank"
                                                   class="btn btn-sm {{ $btnClass }} mb-1">
                                                    View File
                                                </a>
                                            @endif

                                            @if ($hasWritePermission)
                                                <button type="button" class="btn btn-sm btn-outline-success mt-1"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#remarkModal"
                                                    data-field="{{ $doc->file_type }}"
                                                    data-label="{{ $doc->file_type_display }}">
                                                    Remark
                                                </button>
                                            @else
                                                <button type="button" class="btn btn-sm btn-outline-success mt-1"
                                                    onclick="showAccessDenied()">
                                                    Remark
                                                </button>
                                            @endif
                                        </td>

                                        <!-- Status Check -->
                                        <td>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox"
                                                       {{ $hasRemark ? 'checked' : '' }}>
                                            </div>
                                        </td>

                                        <!-- Remarks -->
                                        <td>
                                            <div class="mt-2">
                                                @if (!empty($reviewDocs[$doc->file_type]) && count($reviewDocs[$doc->file_type]) > 0)
                                                    @foreach ($reviewDocs[$doc->file_type] as $reviewDoc)
                                                        <div class="mb-1">
                                                            {{ $reviewDoc->remarks ?? 'N/A' }}
                                                        </div>
                                                    @endforeach
                                                @else
                                                    <div class="mb-1 text-muted">N/A</div>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            @endif
                        </tbody>
                    </table>

                    <!-- Review Steps Table -->
                    {{-- @if ($departments)
                        <h5 class="mt-4">Review Steps</h5>
                        @foreach ($departments as $dept)
                            <h6 class="text-info">{{ $dept['name'] }}</h6>
                            <table class="table table-bordered table-hover align-middle mb-4">
                                <thead class="table-light">
                                    <tr>
                                        <th>Step</th>
                                        <th>Status</th>
                                        <th>Remarks</th>
                                        <th>Attachment</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($dept['steps'] as $index => $step)
                                        <tr>
                                            <td>{{ $step['step'] }}</td>
                                            <td>{{ $step['status'] }}</td>
                                            <td>{{ $step['remarks'] ?: 'N/A' }}</td>
                                            <td>
                                                @if ($step['attachment'])
                                                    <a href="{{ asset('https://xcrinogroup.com/builderwebsite/public/' . $step['attachment']) }}" target="_blank" class="btn btn-sm btn-outline-primary">View Attachment</a>
                                                @else
                                                    <span class="text-muted">None</span>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        @endforeach
                    @endif --}}
                </div>
            </div>

            <!-- Annotated PDFs Section -->
            @if(isset($annotatedPdfs) && $annotatedPdfs->count() > 0)
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-pdf text-danger me-2"></i>
                        Annotated PDFs ({{ $annotatedPdfs->count() }})
                    </h5>
                </div>
                <div class="card-body">
                    <div class="accordion" id="annotatedPdfsAccordionPc">
                        @foreach ($annotatedPdfs as $index => $pdf)
                            <div class="card mb-2">
                                <div class="card-header" id="annotatedHeadingPc{{ $index }}">
                                    <h6 class="mb-0">
                                        <button class="btn btn-link text-start w-100 text-decoration-none"
                                                type="button"
                                                data-bs-toggle="collapse"
                                                data-bs-target="#annotatedCollapsePc{{ $index }}"
                                                aria-expanded="{{ $index === 0 ? 'true' : 'false' }}"
                                                aria-controls="annotatedCollapsePc{{ $index }}">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <i class="fas fa-file-pdf text-danger me-2"></i>
                                                    <strong>Annotated PDF #{{ $index + 1 }}</strong>
                                                    - Uploaded by {{ $pdf->uploader_role_display }} ({{ $pdf->uploader_name }})
                                                    - {{ $pdf->created_at->format('Y-m-d H:i') }}
                                                </div>
                                                <i class="fas fa-chevron-down"></i>
                                            </div>
                                        </button>
                                    </h6>
                                </div>
                                <div id="annotatedCollapsePc{{ $index }}"
                                     class="collapse {{ $index === 0 ? 'show' : '' }}"
                                     aria-labelledby="annotatedHeadingPc{{ $index }}"
                                     data-bs-parent="#annotatedPdfsAccordionPc">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>Document Type:</strong> {{ $pdf->document_type ?? 'N/A' }}</p>
                                                <p><strong>Review Step:</strong> {{ $pdf->review_step ?? 'N/A' }}</p>
                                                <p><strong>Uploaded By:</strong> {{ $pdf->uploader_role_display }} ({{ $pdf->uploader_name }})</p>
                                                <p><strong>Upload Date:</strong> {{ $pdf->created_at->format('Y-m-d H:i:s') }}</p>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="d-flex flex-column gap-2">
                                                    <!-- View Annotated PDF Button -->
                                                    <a href="{{ asset($pdf->annotated_pdf_path) }}"
                                                       target="_blank"
                                                       class="btn btn-primary btn-sm">
                                                        <i class="fas fa-eye me-1"></i> View Annotated PDF
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif

            <!-- Remark Modal -->
            <div class="modal fade" id="remarkModal" tabindex="-1" aria-labelledby="remarkModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <form method="POST" action="{{ route('project.documents.remark.pc', $project->id) }}">
                            @csrf
                            <div class="modal-header">
                                <h5 class="modal-title" id="remarkModalLabel">Add Remark for <span id="modalDocumentLabel"></span></h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <input type="hidden" name="department_id" value="{{ $department_id ?? '' }}">
                                <input type="hidden" name="review_step" value="{{ $stepName ?? '' }}">
                                <input type="hidden" name="document_type" id="modalDocumentType">

                                <div class="mb-3">
                                    <label for="remark" class="form-label">Remark</label>
                                    <textarea class="form-control" id="remark" name="remark" rows="4" maxlength="255" required></textarea>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                <button type="submit" class="btn btn-primary">Submit Remark</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('script')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        if (typeof bootstrap === 'undefined') {
            console.error('Bootstrap JavaScript is not loaded.');
            return;
        }

        var remarkModal = document.getElementById('remarkModal');
        if (remarkModal) {
            remarkModal.addEventListener('show.bs.modal', function (event) {
                var button = event.relatedTarget;
                var field = button.getAttribute('data-field');
                var label = button.getAttribute('data-label');

                var modalTitle = remarkModal.querySelector('#modalDocumentLabel');
                var modalDocumentType = remarkModal.querySelector('#modalDocumentType');

                if (modalTitle && modalDocumentType) {
                    modalTitle.textContent = label;
                    modalDocumentType.value = field;
                } else {
                    console.error('Modal elements not found.');
                }
            });
        } else {
            console.error('Remark modal element not found.');
        }

        // Annotated PDFs Accordion functionality
        var annotatedAccordionPc = document.getElementById('annotatedPdfsAccordionPc');
        if (annotatedAccordionPc) {
            // Add click event listeners to accordion buttons
            var accordionButtons = annotatedAccordionPc.querySelectorAll('[data-bs-toggle="collapse"]');
            accordionButtons.forEach(function(button) {
                button.addEventListener('click', function() {
                    var chevron = this.querySelector('.fa-chevron-down, .fa-chevron-up');
                    if (chevron) {
                        // Toggle chevron direction
                        setTimeout(function() {
                            if (chevron.classList.contains('fa-chevron-down')) {
                                chevron.classList.remove('fa-chevron-down');
                                chevron.classList.add('fa-chevron-up');
                            } else {
                                chevron.classList.remove('fa-chevron-up');
                                chevron.classList.add('fa-chevron-down');
                            }
                        }, 100);
                    }
                });
            });

            // Handle collapse events to reset chevrons
            var collapseElements = annotatedAccordionPc.querySelectorAll('.collapse');
            collapseElements.forEach(function(collapseEl) {
                collapseEl.addEventListener('hidden.bs.collapse', function() {
                    var button = document.querySelector('[data-bs-target="#' + this.id + '"]');
                    if (button) {
                        var chevron = button.querySelector('.fa-chevron-up');
                        if (chevron) {
                            chevron.classList.remove('fa-chevron-up');
                            chevron.classList.add('fa-chevron-down');
                        }
                    }
                });

                collapseEl.addEventListener('shown.bs.collapse', function() {
                    var button = document.querySelector('[data-bs-target="#' + this.id + '"]');
                    if (button) {
                        var chevron = button.querySelector('.fa-chevron-down');
                        if (chevron) {
                            chevron.classList.remove('fa-chevron-down');
                            chevron.classList.add('fa-chevron-up');
                        }
                    }
                });
            });
        }

        function showAccessDenied() {
            Swal.fire({
                icon: 'error',
                title: 'Access Denied',
                text: 'You do not have permission to perform this Planning Commission Review.'
            });
        }
    });
</script>
@endpush