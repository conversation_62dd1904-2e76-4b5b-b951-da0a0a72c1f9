@extends('mcdpanel.layouts.master')

@section('content')
<div class="main-content">
    <section class="section">
        <div class="section-body">
            <div class="row">
                <div class="col-12 col-md-12 col-lg-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4>Municipal Management > Add</h4>
                        </div>

                        <form action="{{ route('Municipals.store') }}" method="POST">
                            @csrf
                            <div class="card-body">
                                <!-- Basic Details -->
                                <h5 class="section-title">📋 Basic Details</h5>
                                <hr>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Title<span class="text-danger">*</span></label>
                                            <input type="text" name="title" class="form-control @error('title') is-invalid @enderror" value="{{ old('title') }}" required>
                                            @error('title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Location<span class="text-danger">*</span></label>
                                            <input type="text" name="location" class="form-control @error('location') is-invalid @enderror" value="{{ old('location') }}" required>
                                            @error('location')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label>Description<span class="text-danger">*</span></label>
                                            <textarea name="description" class="form-control @error('description') is-invalid @enderror" required>{{ old('description') }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Status & Priority -->
                                <h5 class="section-title">⚙️ Status & Priority</h5>
                                <hr>
                                <div class="row">
                                    <!-- Hidden Status Field -->
                                    <input type="hidden" name="status" value="pending">
                                    
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Priority<span class="text-danger">*</span></label>
                                            <select name="priority" class="form-control @error('priority') is-invalid @enderror" required>
                                                <option value="low" {{ old('priority') == 'low' ? 'selected' : '' }}>Low</option>
                                                <option value="medium" {{ old('priority') == 'medium' ? 'selected' : '' }}>Medium</option>
                                                <option value="high" {{ old('priority') == 'high' ? 'selected' : '' }}>High</option>
                                            </select>
                                            @error('priority')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Dates & Budget -->
                                <h5 class="section-title">📅 Dates & Budget</h5>
                                <hr>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Reported Date<span class="text-danger">*</span></label>
                                            <input type="date" name="reported_date" class="form-control @error('reported_date') is-invalid @enderror" value="{{ old('reported_date') }}" required>
                                            @error('reported_date')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Resolution Date</label>
                                            <input type="date" name="resolution_date" class="form-control @error('resolution_date') is-invalid @enderror" value="{{ old('resolution_date') }}">
                                            @error('resolution_date')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Budget (in INR)</label>
                                            <input type="number" step="0.01" name="budget" class="form-control @error('budget') is-invalid @enderror" value="{{ old('budget') }}">
                                            @error('budget')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Additional Details -->
                                <h5 class="section-title">📎 Additional Details</h5>
                                <hr>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Assigned To</label>
                                            <input type="text" name="assigned_to" class="form-control @error('assigned_to') is-invalid @enderror" value="{{ old('assigned_to') }}">
                                            @error('assigned_to')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="card-footer text-right">
                                <button type="submit" class="btn btn-success mr-1">Save</button>
                                <a href="{{ route('Municipals.index') }}" class="btn btn-secondary">Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Custom Styling -->
    <style>
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
    </style>
    
@endsection