@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4>Project Management > Milestones</h4>
                        <a href="{{ route('project.milestones.create') }}" class="btn btn-primary">
                            <i class="fa fa-plus"></i> Add New Milestone
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="table-1">
                                <thead>
                                    <tr>
                                        <th class="text-center">Sr No</th>
                                        <th>Name</th>
                                        <th>Start Date</th>
                                        <th>End Date</th>
                                        <th>Status</th>
                                        <th>Assigned To</th>
                                        <th>Progress</th>
                                        <th class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($milestones as $key => $milestone)
                                        <tr>
                                            <td class="text-center">{{ $key + 1 }}</td>
                                            <td>{{ $milestone->name }}</td>
                                            <td>{{ $milestone->start_date }}</td>
                                            <td>{{ $milestone->end_date }}</td>
                                            <td>
                                                <label class="switch">
                                                    <input type="checkbox" class="status-toggle" 
                                                           data-id="{{ $milestone->id }}" 
                                                           {{ $milestone->status === 'Active' ? 'checked' : '' }}>
                                                    <span class="slider round"></span>
                                                </label>
                                            </td>
                                            <td>{{ $milestone->assigned_to }}</td>
                                            <td>{{ $milestone->progress_percentage }}%</td>
                                            <td class="text-center">
                                                <a href="{{ route('project.milestones.edit', $milestone) }}" 
                                                   class="btn btn-sm btn-primary mr-1" title="Edit">
                                                    <i class="fa fa-edit"></i>
                                                </a>
                                                <form action="{{ route('project.milestones.destroy', $milestone) }}" 
                                                      method="POST" class="d-inline delete-form">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger delete-btn" 
                                                            title="Delete" data-name="{{ $milestone->name }}">
                                                        <i class="fa fa-trash"></i>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="8" class="text-center">No milestones found</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('style')
    <style>
        .switch {
            position: relative;
            display: inline-block;
            width: 25px;
            height: 14px;
        }
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #dc3545;
            transition: .4s;
            border-radius: 14px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 10px;
            width: 10px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #28a745;
        }
        input:checked + .slider:before {
            transform: translateX(11px);
        }
    </style>
@endpush

@push('script')
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Success Message
            @if(session('success'))
                Swal.fire({
                    title: 'Success!',
                    text: '{{ session('success') }}',
                    icon: 'success',
                    showConfirmButton: false,
                    timer: 4000,
                    timerProgressBar: true,
                    position: 'top-end',
                    toast: true
                });
            @endif

            // Delete Confirmation
            document.querySelectorAll('.delete-btn').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const form = this.closest('form');
                    const milestoneName = this.getAttribute('data-name');

                    Swal.fire({
                        title: 'Are you sure?',
                        text: `You are about to delete "${milestoneName}". This action cannot be undone!`,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#3085d6',
                        confirmButtonText: 'Yes, delete it!'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            form.submit();
                        }
                    });
                });
            });

            // Status Toggle Functionality
            // document.querySelectorAll('.status-toggle').forEach(toggle => {
            //     toggle.addEventListener('change', function() {
            //         let milestoneId = this.getAttribute('data-id');
            //         let newStatus = this.checked ? 'Active' : 'Inactive';

            //         fetch("{{ route('project.milestones.update.status', '') }}/" + milestoneId, {
            //             method: 'POST',
            //             headers: {
            //                 'Content-Type': 'application/json',
            //                 'X-CSRF-TOKEN': '{{ csrf_token() }}'
            //             },
            //             body: JSON.stringify({
            //                 status: newStatus
            //             })
            //         })
            //         .then(response => response.json())
            //         .then(data => {
            //             if (data.success) {
            //                 Swal.fire({
            //                     title: 'Success!',
            //                     text: 'Status updated successfully',
            //                     icon: 'success',
            //                     toast: true,
            //                     position: 'top-end',
            //                     showConfirmButton: false,
            //                     timer: 3000
            //                 });
            //             } else {
            //                 throw new Error(data.message || 'Failed to update status');
            //             }
            //         })
            //         .catch(error => {
            //             console.error('Error:', error);
            //             Swal.fire({
            //                 title: 'Error!',
            //                 text: 'Failed to update status',
            //                 icon: 'error',
            //                 toast: true,
            //                 position: 'top-end',
            //                 showConfirmButton: false,
            //                 timer: 3000
            //             });
            //             // Revert the toggle state on error
            //             this.checked = !this.checked;
            //         });
            //     });
            // });


            document.querySelectorAll('.status-toggle').forEach(toggle => {
    toggle.addEventListener('change', function() {
        let milestoneId = this.getAttribute('data-id');
        let newStatus = this.checked ? 'Active' : 'Inactive';

        let url = "{{ route('project.milestones.update.status', ['milestone' => 'ID']) }}".replace('ID', milestoneId);

        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                status: newStatus
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                Swal.fire({
                    title: 'Success!',
                    text: 'Status updated successfully',
                    icon: 'success',
                    toast: true,
                    position: 'top-end',
                    showConfirmButton: false,
                    timer: 3000
                });
            } else {
                throw new Error(data.message || 'Failed to update status');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                title: 'Error!',
                text: 'Failed to update status',
                icon: 'error',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000
            });
            // Revert the toggle state on error
            this.checked = !this.checked;
        });
    });
});
        });
    </script>
@endpush