
@extends('mcdpanel.layouts.master')

@section('content')
<body class="bg-[#f6f7f9]">

  <!-- Header -->

  <!-- Main Content Wrapper -->
  <div class="py-6">
    <div class="max-w-[1560px] w-[90%] md:w-[80%] mx-auto flex gap-6 flex-col lg:flex-row">

      <!-- Sidebar -->
     
      <!-- Main Projects Area -->
      <div class="flex-1 flex flex-col gap-6">
        <div class="bg-gray-100 flex items-center justify-center ">
          <div class=" w-full bg-white shadow-md flex flex-col">
              <!-- Header with user info -->
              <div class="flex items-center p-4 gap-2">
                  <div class="relative">
                      <div class="w-12 h-12 bg-gray-300 rounded-full overflow-hidden flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                      </div>
                      <div class="absolute bottom-0 right-0 w-3 h-3 bg-gray-400 border-2 border-white rounded-full"></div>
                  </div>
                  <div class="ml-3 flex-1">
                      <h3 class="  text-[#373B53] font-poppins font-black text-[18px]">Antony</h3>
                      <p class="text-[14px] font-semibold font-poppins text-gray-500 flex items-center">
                          <span>Offline</span>
                          <span class="mx-1">•</span>
                          <span>Last seen 3 hours ago</span>
                      </p>
                  </div>
                  <div class="flex space-x-3">
                      <button class="text-gray-400">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                          </svg>
                      </button>
                      <button class="text-gray-400">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                      </button>
                      <button class="text-gray-400">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 rotate-90" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                        </svg>
                      </button>
                      
                  </div>
              </div>
      
              <!-- Chat area -->
              <div class="flex-1 overflow-y-auto p-4 space-y-4">
                  <!-- Incoming message -->
                  <div class="flex flex-col items-start">
                      <div class=" border border-[#DADADA] rounded-full px-4 py-2 max-w-xs">
                          <p class=" text-[#373B53] text-[16px] font-poppins font-normal">Hi Alex! What's Up?</p>
                      </div>
                      <span class="text-xs text-gray-500 font-normal font-poppins mt-1">Yesterday 14:25 PM</span>
                  </div>
      
                  <!-- Outgoing message -->
                  <div class="flex flex-col items-end">
                      <div class="bg-btnclifford rounded-full px-6 py-3 max-w-lg font-poppins font-normal text-[14px] text-[#FFFFFF]"> 
                          <p class="">Oh, hello! All perfectly.</p>
                          <p class="">I work, study and know this wonderful world!</p>
                      </div>
                      <span class="text-xs text-gray-500 mt-1 font-normal font-poppins ">Yesterday 14:31 PM</span>
                  </div>
      
                  <!-- Voice message -->
                  <div class="flex flex-col items-start w-full px-4 sm:px-6 lg:px-8">
                    <div class="flex flex-col sm:flex-row items-center gap-4 border border-[#292F4C] rounded-full px-4 py-2 w-full max-w-md w-full ">
                      <button class="flex items-center justify-center w-12 h-12 rounded-full bg-[#F94D1D] focus:outline-none shrink-0 ">
                        <img src="assets/image/voiceplay.png" alt="Play Voice">
                      </button>
                  
                      <div class="flex-1 flex flex-col sm:flex-row sm:items-center justify-between">
                        <div class="flex items-center flex-wrap   space-x-px py-2 overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300">
                          <!-- Equalizer bars -->
                          <div class=" w-1 h-1 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-3 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-5 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-7 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-10 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-9 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-8 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-6 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-4 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-2 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-1 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-2 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-4 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-6 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-7 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-5 bg-[#F94D1D] rounded"></div>
                         
                          <div class=" w-1 h-6 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-4 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-2 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-1 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-2 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-4 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-6 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-7 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-5 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-6 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-4 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-2 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-1 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-2 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-4 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-6 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-7 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-5 bg-[#F94D1D] rounded"></div>
                           <div class=" w-1 h-6 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-4 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-2 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-1 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-2 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-4 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-6 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-7 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-5 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-2 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-4 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-6 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-7 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-5 bg-[#F94D1D] rounded"></div>
                           <div class=" w-1 h-6 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-4 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-2 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-1 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-2 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-4 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-6 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-7 bg-[#F94D1D] rounded"></div>
                          <div class=" w-1 h-5 bg-[#F94D1D] rounded"></div>
                        </div>
                        <p class="text-gray-600 text-sm lg:text-right sm:ml-4 mt-2 sm:mt-0 min-w-fit text-center">01:24</p>
                      </div>
                    </div>
                    <span class="text-xs text-gray-500 mt-2 sm:mt-1 font-poppins font-normal ">Yesterday 15:25 PM</span>
                  </div>
                  
      
                  <!-- Outgoing message -->
                  <div class="flex flex-col items-end">
                      <div class="bg-btnclifford rounded-full px-6 py-2 max-w-sm font-poppins font-normal text-[14px] text-[#FFFFFF]">
                          <p class="">I remember everything mate. See you later 🤘</p>
                      </div>
                      <span class="text-xs text-gray-500 mt-1 font-normal font-poppins">Today 06:18 AM</span>
                  </div>
              </div>
      
              <!-- Input area -->
              <div class="p-4">
                <div class="relative flex items-center">
                  <textarea
                    rows="8"
                    cols="30"
                    placeholder="Type a message here..."
                    class="flex-1 bg-[#FFF9F7] border border-transparent rounded-2xl py-3 px-5 focus:outline-none text-sm text-gray-600 placeholder-gray-400 resize-none"
                  ></textarea>
                  <button
                    class="absolute right-2 bottom-2  text-white  focus:outline-none transition duration-150"
                  >
                    <img src="assets/image/sendButton.png" alt="Send">
                  </button>
                </div>
              </div>
              
              
          </div>
        </div>
   
          
          </div>

  
          

          <!-- Project Card -->
  
          
       
        
          
          
        
          
          
       
              
        
         
         
  
          
        </div>
      </div>
    </div>
</div>

</body>

