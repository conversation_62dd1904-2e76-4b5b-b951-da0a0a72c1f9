<?php
    $user = Auth::guard('staff_mcd')->user();
    $permissions = $user && $user->role ? $user->role->permissions : [];
    $isAdmin = Auth::guard('web')->check();
    $hasWritePermission = $isAdmin || (isset($permissions['review_processes']['write']) && $permissions['review_processes']['write'] == 1);
?>

<?php $__env->startSection('content'); ?>
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4>Projects > Review Processes</h4>
                        
                        <?php if($hasWritePermission): ?>
                            <a href="<?php echo e(route('review_processes.create')); ?>" class="btn btn-primary">
                                Create Review Process Steps
                            </a>
                        <?php else: ?>
                            <button type="button" class="btn btn-primary" onclick="showAccessDenied()">
                                Create Review Process Steps
                            </button>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <!-- Filter Buttons -->
                        <div class="mb-3">
                            
                            
                        </div>

                        <?php if($reviewProcesses->count() > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="table-1">
                                    <thead class="bg-gray-200 text-gray-800">
                                        <tr>
                                            <th>Sr No</th>
                                            <th>Review Level</th>
                                            <th>Departments</th>
                                            <th>Review Steps</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $reviewProcesses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $reviewProcess): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td class="text-center"><?php echo e($key + 1); ?></td>
                                                <td><?php echo e($reviewProcess->review_level ?? 'N/A'); ?></td>
                                                <td>
                                                    <?php
                                                        $departments = $reviewProcess->department;
                                                    ?>
                                                    <?php if(is_array($departments) && count($departments)): ?>
                                                        <?php echo e(implode(', ', array_filter($departments))); ?>

                                                    <?php else: ?>
                                                        N/A
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php
                                                        $steps = $reviewProcess->review_steps;
                                                    ?>
                                                    <?php if(is_array($steps) && count($steps)): ?>
                                                        <?php echo e(implode(', ', array_filter($steps))); ?>

                                                    <?php else: ?>
                                                        N/A
                                                    <?php endif; ?>
                                                </td>
                                                
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                    <?php if($hasWritePermission): ?>
                                                        <a href="<?php echo e(route('review_processes.edit', $reviewProcess->id)); ?>" class="btn btn-primary btn-sm mr-2" title="Edit">
                                                            <i class="fa fa-edit"></i>
                                                        </a>
                                                        <button type="button" class="btn btn-danger btn-sm" data-toggle="modal" data-target="#deleteReviewProcessModal<?php echo e($reviewProcess->id); ?>" title="Delete">
                                                            <i class="fa fa-trash"></i>
                                                        </button>
                                                    <?php else: ?>
                                                        <button type="button" class="btn btn-primary btn-sm mr-2" onclick="showAccessDenied()" title="Edit">
                                                            <i class="fa fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-danger btn-sm" onclick="showAccessDenied()" title="Delete">
                                                            <i class="fa fa-trash"></i>
                                                        </button>
                                                    <?php endif; ?>

                                                    </div>
                                                </td>
                                            </tr>
                                            <!-- Delete Review Process Modal -->
                                            <div class="modal fade" id="deleteReviewProcessModal<?php echo e($reviewProcess->id); ?>" tabindex="-1" role="dialog" aria-labelledby="deleteReviewProcessModalLabel<?php echo e($reviewProcess->id); ?>" aria-hidden="true">
                                                <div class="modal-dialog" role="document">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="deleteReviewProcessModalLabel<?php echo e($reviewProcess->id); ?>">Delete Review Process</h5>
                                                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                <span aria-hidden="true">×</span>
                                                            </button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <form id="deleteReviewProcessForm<?php echo e($reviewProcess->id); ?>" method="POST" action="<?php echo e(route('review_processes.destroy', $reviewProcess->id)); ?>">
                                                                <?php echo csrf_field(); ?>
                                                                <?php echo method_field('DELETE'); ?>
                                                                <p>Are you sure you want to delete this review process?</p>
                                                                <div class="form-group mt-3">
                                                                    <button type="submit" class="btn btn-danger">Delete</button>
                                                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                                                </div>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info text-center">
                                <i class="fa fa-info-circle mr-2"></i>
                                No review processes found.
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('style'); ?>
    <style>
        .btn-sm {
            padding: 0.25rem 0.5rem;
        }
        .badge {
            font-size: 0.8rem;
            padding: 0.3em 0.6em;
        }
        .table th, .table td {
            vertical-align: middle;
        }
        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }
        .btn-secondary {
            background-color: #6c757d;
            border-color: #6c757d;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        $(document).ready(function() {
            // Success Message from Session
            <?php if(session('success')): ?>
                Swal.fire({
                    title: 'Success!',
                    text: '<?php echo e(session('success')); ?>',
                    icon: 'success',
                    showConfirmButton: false,
                    timer: 4000,
                    timerProgressBar: true,
                    position: 'top-end',
                    toast: true,
                    customClass: {
                        popup: 'swal2-popup-custom',
                        title: 'swal2-title-custom',
                        content: 'swal2-content-custom'
                    }
                });
            <?php endif; ?>

            // Error Message from Session
            <?php if(session('error')): ?>
                Swal.fire({
                    title: 'Error!',
                    text: '<?php echo e(session('error')); ?>',
                    icon: 'error',
                    showConfirmButton: false,
                    timer: 4000,
                    timerProgressBar: true,
                    position: 'top-end',
                    toast: true,
                    customClass: {
                        popup: 'swal2-popup-custom',
                        title: 'swal2-title-custom',
                        content: 'swal2-content-custom'
                    }
                });
            <?php endif; ?>
        });

        function showAccessDenied() {
        Swal.fire({
            icon: 'error',
            title: 'Access Denied',
            text: 'You do not have permission to modify the Review Process.'
        });
    }
    </script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('mcdpanel.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\mcdconstructions\resources\views/BuilderWebsiteDashboard/ReviewProcess/index.blade.php ENDPATH**/ ?>