@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4>Physical Inspection > Active Inspections</h4>
                    </div>
                    <div class="card-body">
                        <!-- Department Filter Form -->
                        <form method="GET" action="#" class="mb-3">
                            <label for="tradeFilter">Filter by Department:</label>
                            <select name="trade" id="tradeFilter" class="form-control w-25" onchange="this.form.submit()">
                                <option value="">All Departments</option>
                                @foreach($departments as $id => $name)
                                    <option value="{{ $id }}" {{ request('trade') == $id ? 'selected' : '' }}>{{ $name }}</option>
                                @endforeach
                            </select>
                        </form>

                        @if ($projects->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="table-1">
                                    <thead class="bg-gray-200 text-gray-800">
                                        <tr>
                                            <th>Sr No</th>
                                            <th>Project Name</th>
                                            <th>Address</th>
                                            <th>Scope of Work</th>
                                            <th>Department(Trade)</th>
                                            <th>Date Range</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($projects as $key => $project)
                                            <tr>
                                                <td class="text-center">{{ $key + 1 }}</td>
                                                <td>
                                                    @if ($project->project_name)
                                                        <a href="{{ route('myprojects.show.status', ['id' => $project->id]) }}">
                                                            {{ $project->project_name }}
                                                        </a>
                                                    @else
                                                        N/A
                                                    @endif
                                                </td>
                                                <td>
                                                    {{ $project->project_address ?? 'N/A' }},
                                                    {{ $project->city ?? '' }},
                                                    {{ $project->state ?? '' }} {{ $project->zip ?? '' }}
                                                </td>
                                                <td>{{ $project->scope_of_work ?? 'N/A' }}</td>
                                                <td>
                                                    @if (!empty($project->department_names))
                                                        {{ implode(', ', $project->department_names) }}
                                                    @else
                                                        N/A
                                                    @endif
                                                </td>
                                                <td>
                                                    {{ $project->expected_start_date ? \Carbon\Carbon::parse($project->expected_start_date)->format('d M Y') : ' n/a' }}
                                                    -
                                                    {{ $project->expected_end_date ? \Carbon\Carbon::parse($project->expected_end_date)->format('d M Y') : ' n/a' }}
                                                </td>
                                                <td>
                                                    @php
                                                        if ($project->physicalInspectionReportnew) {
                                                            $statusText = $project->physicalInspectionReportnew->report_status === '2' ? 'Pending' : 'Unknown';
                                                            $badgeClass = $project->physicalInspectionReportnew->report_status === '2' ? 'warning' : 'secondary';
                                                        } else {
                                                            $statusText = 'Pending';
                                                            $badgeClass = 'warning';
                                                        }
                                                    @endphp
                                                    <span class="badge bg-{{ $badgeClass }}">
                                                        {{ $statusText }}
                                                    </span>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="alert alert-info text-center">
                                <i class="fa fa-info-circle mr-2"></i>
                                No active inspections found.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('style')
    <style>
        .btn-sm {
            padding: 0.25rem 0.5rem;
        }
        .badge {
            font-size: 0.8rem;
            padding: 0.3em 0.6em;
        }
        .table th, .table td {
            vertical-align: middle;
        }
        .table-responsive {
            overflow-x: auto;
        }
        .form-control.w-25 {
            width: 25%;
            display: inline-block;
        }
    </style>
@endpush

@push('scripts')
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap4.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#table-1').DataTable({
                responsive: true,
                paging: true,
                searching: true,
                ordering: true,
                bLengthChange: false,
                language: {
                    search: "<i class='ti-search'></i>",
                    searchPlaceholder: 'Quick Search',
                    paginate: {
                        next: "<i class='ti-arrow-right'></i>",
                        previous: "<i class='ti-arrow-left'></i>",
                    },
                },
            });
        });
    </script>
@endpush