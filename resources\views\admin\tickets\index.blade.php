<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Support Tickets</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-4">
        <h1 class="text-3xl font-bold mb-6">Support Tickets</h1>
        <table class="w-full border-collapse border border-gray-300">
            <thead>
                <tr class="bg-gray-200">
                    <th class="border border-gray-300 p-2">ID</th>
                    <th class="border border-gray-300 p-2">Subject</th>
                    <th class="border border-gray-300 p-2">Priority</th>
                    <th class="border border-gray-300 p-2">Status</th>
                    <th class="border border-gray-300 p-2">Created At</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($tickets as $ticket)
                    <tr>
                        <td class="border border-gray-300 p-2">{{ $ticket->id }}</td>
                        <td class="border border-gray-300 p-2">{{ $ticket->subject }}</td>
                        <td class="border border-gray-300 p-2">{{ ucfirst($ticket->priority) }}</td>
                        <td class="border border-gray-300 p-2">{{ ucfirst($ticket->status) }}</td>
                        <td class="border border-gray-300 p-2">{{ $ticket->created_at->format('Y-m-d H:i') }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</body>
</html>