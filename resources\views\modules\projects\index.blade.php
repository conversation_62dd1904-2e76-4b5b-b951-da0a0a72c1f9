@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4>Project Insights > Projects For Review</h4>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="table-1">
                                <thead>
                                    <tr>
                                        <th class="text-center">Sr No</th>
                                        <th>Name</th>
                                        <th>Start Date</th>
                                        <th>End Date</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($projects as $key => $project)
                                        @if($project->status === 'pending') <!-- Safeguard to ensure only pending -->
                                            <tr>
                                                <td class="text-center">{{ $key + 1 }}</td>
                                                <td>{{ $project->name }}</td>
                                                <td>{{ $project->start_date }}</td>
                                                <td>{{ $project->end_date }}</td>
                                                <td>
                                                    <div class="status-wrapper dropdown" data-id="{{ $project->id }}">
                                                        <span class="badge status-badge dropdown-toggle
                                                            {{ $project->status == 'pending' ? 'badge-warning' : 
                                                               ($project->status == 'approved' ? 'badge-success' : 'badge-danger') }}"
                                                            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                            {{ ucfirst($project->status) }}
                                                        </span>
                                                        <div class="dropdown-menu">
                                                            <a class="dropdown-item status-option" href="#" data-status="pending">Pending</a>
                                                            <a class="dropdown-item status-option" href="#" data-status="approved">Approved</a>
                                                            <a class="dropdown-item status-option" href="#" data-status="rejected">Rejected</a>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endif
                                    @empty
                                        <tr>
                                            <td colspan="5" class="text-center">No pending projects found</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('style')
    <style>
        .btn-sm { padding: 0.25rem 0.5rem; }
        .mr-1 { margin-right: 0.25rem; }
        .status-badge {
            cursor: pointer;
            font-size: 0.9rem;
            padding: 0.35rem 0.75rem;
            text-transform: capitalize;
        }
        .status-wrapper {
            position: relative;
            display: inline-block;
        }
        .dropdown-menu {
            min-width: 120px;
        }
        .dropdown-item:hover {
            background-color: #f8f9fa;
        }
    </style>
@endpush

@push('script')
  
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        $(document).ready(function() {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            @if(session('success'))
                Swal.fire({
                    title: 'Success!',
                    text: '{{ session('success') }}',
                    icon: 'success',
                    showConfirmButton: false,
                    timer: 4000,
                    timerProgressBar: true,
                    position: 'top-end',
                    toast: true,
                    customClass: {
                        popup: 'swal2-popup-custom',
                        title: 'swal2-title-custom',
                        content: 'swal2-content-custom'
                    }
                });
            @endif

            $('.status-option').on('click', function(e) {
                e.preventDefault();
                const wrapper = $(this).closest('.status-wrapper');
                const projectId = wrapper.data('id');
                const newStatus = $(this).data('status');
                const badge = wrapper.find('.status-badge');

                // Store original status for reversion on error
                const originalStatus = badge.text().toLowerCase();

                badge.text(newStatus.charAt(0).toUpperCase() + newStatus.slice(1));
                badge.removeClass('badge-warning badge-success badge-danger')
                     .addClass(newStatus === 'pending' ? 'badge-warning' : 
                               newStatus === 'approved' ? 'badge-success' : 'badge-danger');

                $.ajax({
                    url: '{{ route("project.status.update", ":id") }}'.replace(':id', projectId),
                    method: 'POST',
                    data: { status: newStatus },
                    success: function(data) {
                        if (data.success) {
                            Swal.fire({
                                title: 'Success!',
                                text: data.message,
                                icon: 'success',
                                toast: true,
                                position: 'top-end',
                                timer: 3000,
                                showConfirmButton: false
                            });
                            // Optionally remove the row if status changes from pending
                            if (newStatus !== 'pending') {
                                wrapper.closest('tr').fadeOut(500, function() {
                                    $(this).remove();
                                });
                            }
                        }
                    },
                    error: function(xhr) {
                        let errorMessage = xhr.responseJSON?.message || 'Unknown error occurred';
                        Swal.fire({
                            title: 'Error!',
                            text: 'Failed to update status: ' + errorMessage,
                            icon: 'error',
                            toast: true,
                            position: 'top-end',
                            timer: 3000,
                            showConfirmButton: false
                        });
                        // Revert to original status on error
                        badge.text(originalStatus.charAt(0).toUpperCase() + originalStatus.slice(1));
                        badge.removeClass('badge-warning badge-success badge-danger')
                             .addClass(originalStatus === 'pending' ? 'badge-warning' : 
                                       originalStatus === 'approved' ? 'badge-success' : 'badge-danger');
                    }
                });
            });
        });
    </script>

    <style>
        .swal2-popup-custom {
            border-radius: 8px;
            padding: 15px;
            background: #ffffff;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
        }
        .swal2-title-custom {
            color: #28a745;
            font-weight: bold;
            font-size: 18px;
        }
        .swal2-content-custom {
            font-size: 14px;
            color: #555;
        }
    </style>
@endpush