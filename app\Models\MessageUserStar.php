<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class MessageUserStar extends Model
{
    protected $table = 'message_user_stars';

    protected $fillable = [
        'message_id',
        'user_id',
        'inspector_id',
        'mcd_staff_id',
        'builder_staff_id',
        'is_starred',
    ];

    public function message()
    {
        return $this->belongsTo(Message::class);
    }
}