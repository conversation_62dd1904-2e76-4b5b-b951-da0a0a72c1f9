<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\AnnotatedPdf;
use App\Models\Annotation;
use App\Models\User;
use App\Models\McdStaff;
use App\Models\BuilderStaff;
use Illuminate\Support\Facades\Log;

class AnnotatedPdfController extends Controller
{
    /**
     * Store a newly created annotated PDF.
     */
    public function store(Request $request)
    {
        Log::info('=== ANNOTATED PDF STORE API CALLED ===', [
            'all_request_data' => $request->all(),
            'has_file' => $request->hasFile('annotated_pdf'),
            'user_id_param' => $request->input('user_id'),
            'user_role_param' => $request->input('user_role')
        ]);
        // Handle preflight OPTIONS request
        if ($request->isMethod('OPTIONS')) {
            return response()->json(['status' => 'ok'], 200);
        }
        
        try {
            // Log incoming request for debugging
            Log::info('Annotated PDF store request received', [
                'project_id' => $request->input('project_id'),
                'department_id' => $request->input('department_id'),
                'review_step' => $request->input('review_step'),
                'has_file' => $request->hasFile('annotated_pdf'),
                'has_annotations' => $request->has('annotations')
            ]);

            // Check project_id first
            $project_id = $request->input('project_id');
            if (!$project_id || !is_numeric($project_id)) {
                Log::warning('Invalid project_id provided', ['project_id' => $project_id]);
                return response()->json([
                    'success' => false,
                    'message' => 'Project ID is missing or invalid.'
                ], 422);
            }

            // Validate request
            $request->validate([
                'annotated_pdf' => 'required|file|mimes:pdf|max:102400', // 10MB max
                'project_id' => 'required|exists:website_builder_projects,id',
                'department_id' => 'nullable|exists:mcd_staff_roles,id',
                'review_step' => 'nullable|string',
                'original_pdf_path' => 'nullable|string',
                'document_type' => 'nullable|string',
                'annotations' => 'nullable|string', // JSON string of annotations
                'user_id' => 'nullable|string', // Optional - can be empty string or null
                'user_role' => 'nullable|string' // Optional - can be empty string or null
            ]);

            $file = $request->file('annotated_pdf');
            
            // Create directory structure in public folder
            $directory = "annotated_pdfs/project_{$project_id}";
            $publicPath = public_path($directory);
            
            // Ensure directory exists
            if (!file_exists($publicPath)) {
                if (!mkdir($publicPath, 0755, true)) {
                    throw new \Exception('Failed to create directory: ' . $publicPath);
                }
            }
            
            // Generate unique filename
            $timestamp = now()->format('Ymd_His');
            $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
            $filename = "{$timestamp}_annotated_{$originalName}.pdf";
            
            // Save file to public directory
            $filePath = $directory . '/' . $filename;
            $fullPath = public_path($filePath);

            if (!$file->move($publicPath, $filename)) {
                throw new \Exception('Failed to save file to: ' . $fullPath);
            }
            
            // Save annotation data to annotations table if provided
            $annotationId = null;
            if ($request->has('annotations') && $request->annotations) {
                try {
                    $annotationData = json_decode($request->annotations, true);
                    if ($annotationData) {
                        $annotation = Annotation::create([
                            'project_id' => $project_id,
                            'department_id' => $request->input('department_id'),
                            'review_step' => $request->input('review_step'),
                            'annotation_data' => $annotationData
                        ]);
                        $annotationId = $annotation->id;
                    }
                } catch (\Exception $e) {
                    Log::warning('Failed to save annotation data: ' . $e->getMessage());
                    // Continue with PDF save even if annotation save fails
                }
            }
            
            // Get user information from request - handle empty strings and null values
            $userId = $request->input('user_id');
            $userRole = $request->input('user_role');

            // Convert empty strings to null for database storage
            if (empty($userId)) {
                $userId = null;
            }

            if (empty($userRole)) {
                $userRole = null;
            }

            // Save to annotated_pdfs table
            $annotatedPdf = AnnotatedPdf::create([
                'project_id' => $project_id,
                'department_id' => $request->input('department_id'),
                'review_step' => $request->input('review_step'),
                'original_pdf_path' => $request->input('original_pdf_path'),
                'annotated_pdf_path' => $filePath,
                'document_type' => $request->input('document_type') ?: 'annotated_review',
                'uploaded_by' => $userId,
                'uploaded_by_role' => $userRole
            ]);
            
            Log::info('Annotated PDF saved successfully', [
                'project_id' => $project_id,
                'file_path' => $filePath,
                'annotation_id' => $annotationId
            ]);
            
            return response()->json([
                'success' => true, 
                'message' => 'Annotated PDF saved successfully',
                'data' => [
                    'id' => $annotatedPdf->id,
                    'path' => $filePath,
                    'download_url' => asset($filePath),
                    'annotation_id' => $annotationId
                ]
            ]);
            
        } catch (\Illuminate\Validation\ValidationException $e) {
            $errors = [];
            foreach ($e->errors() as $field => $messages) {
                foreach ($messages as $message) {
                    $errors[] = "$field: $message";
                }
            }
            return response()->json([
                'success' => false,
                'message' => 'Validation failed: ' . implode(', ', $errors)
            ], 422);
        } catch (\Throwable $e) {
            Log::error('Error saving annotated PDF', [
                'error' => $e->getMessage(),
                'project_id' => $request->input('project_id'),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Server error: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get annotated PDFs for a project.
     */
    public function getProjectAnnotatedPdfs(Request $request, $project_id)
    {
        // Handle preflight OPTIONS request
        if ($request->isMethod('OPTIONS')) {
            return response()->json(['status' => 'ok'], 200);
        }
        
        try {
            $annotatedPdfs = AnnotatedPdf::where('project_id', $project_id)
                ->when($request->input('department_id'), function($query, $departmentId) {
                    return $query->where('department_id', $departmentId);
                })
                ->when($request->input('review_step'), function($query, $reviewStep) {
                    return $query->where('review_step', $reviewStep);
                })
                ->orderBy('created_at', 'desc')
                ->get();
            
            return response()->json([
                'success' => true,
                'data' => $annotatedPdfs->map(function($pdf) {
                    return [
                        'id' => $pdf->id,
                        'original_pdf_path' => $pdf->original_pdf_path,
                        'annotated_pdf_path' => $pdf->annotated_pdf_path,
                        'download_url' => asset($pdf->annotated_pdf_path),
                        'document_type' => $pdf->document_type,
                        'created_at' => $pdf->created_at->format('Y-m-d H:i:s'),
                        'department_id' => $pdf->department_id,
                        'review_step' => $pdf->review_step,
                        'uploaded_by' => $pdf->uploaded_by,
                        'uploaded_by_role' => $pdf->uploaded_by_role
                    ];
                })
            ]);
            
        } catch (\Exception $e) {
            Log::error('Error fetching annotated PDFs', [
                'error' => $e->getMessage(),
                'project_id' => $project_id
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch annotated PDFs'
            ], 500);
        }
    }

    /**
     * Get annotations for a project.
     */
    public function getProjectAnnotations(Request $request, $project_id)
    {
        try {
            $annotations = Annotation::where('project_id', $project_id)
                ->when($request->input('department_id'), function($query, $departmentId) {
                    return $query->where('department_id', $departmentId);
                })
                ->when($request->input('review_step'), function($query, $reviewStep) {
                    return $query->where('review_step', $reviewStep);
                })
                ->orderBy('created_at', 'desc')
                ->get();
            
            return response()->json([
                'success' => true,
                'data' => $annotations->map(function($annotation) {
                    return [
                        'id' => $annotation->id,
                        'project_id' => $annotation->project_id,
                        'department_id' => $annotation->department_id,
                        'review_step' => $annotation->review_step,
                        'annotation_data' => $annotation->annotation_data,
                        'created_at' => $annotation->created_at->format('Y-m-d H:i:s')
                    ];
                })
            ]);
            
        } catch (\Exception $e) {
            Log::error('Error fetching annotations', [
                'error' => $e->getMessage(),
                'project_id' => $project_id
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch annotations'
            ], 500);
        }
    }

    /**
     * Download annotated PDF by file ID.
     */
    public function downloadAnnotatedPdf(Request $request)
    {
        // Handle preflight OPTIONS request
        if ($request->isMethod('OPTIONS')) {
            return response()->json(['status' => 'ok'], 200);
        }

        $fileId = $request->query('fileId');

        if (!$fileId) {
            return response()->json([
                'success' => false,
                'error' => 'File ID is required'
            ], 400);
        }

        // Find the annotated PDF record
        $annotatedPdf = AnnotatedPdf::find($fileId);

        if (!$annotatedPdf) {
            return response()->json([
                'success' => false,
                'error' => 'File not found'
            ], 404);
        }

        $filePath = public_path($annotatedPdf->annotated_pdf_path);

        if (!file_exists($filePath)) {
            return response()->json([
                'success' => false,
                'error' => 'Physical file not found'
            ], 404);
        }

        $filename = basename($annotatedPdf->annotated_pdf_path);

        return response()->download($filePath, $filename, [
            'Content-Type' => 'application/pdf',
            'Access-Control-Allow-Origin' => '*',
            'Access-Control-Expose-Headers' => 'Content-Disposition'
        ]);
    }

    /**
     * Get the current user's role based on active authentication guard.
     */
    private function getUserRole()
    {
        if (auth()->guard('staff_mcd')->check()) {
            $user = auth()->guard('staff_mcd')->user();
            return $user->role->name ?? 'mcd_staff';
        } elseif (auth()->guard('web')->check()) {
            return 'admin'; // Web users are admin by default
        } elseif (auth()->guard('staff_builder')->check()) {
            $user = auth()->guard('staff_builder')->user();
            return $user->role->name ?? 'builder_staff';
        }

        return 'unknown';
    }

    /**
     * Get the current authenticated user ID from any active guard.
     */
    private function getUserId()
    {
        if (auth()->guard('staff_mcd')->check()) {
            return auth()->guard('staff_mcd')->id();
        } elseif (auth()->guard('web')->check()) {
            return auth()->guard('web')->id();
        } elseif (auth()->guard('staff_builder')->check()) {
            return auth()->guard('staff_builder')->id();
        }

        return null;
    }
}