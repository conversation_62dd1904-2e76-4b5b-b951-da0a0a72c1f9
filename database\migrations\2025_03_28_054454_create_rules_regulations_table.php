<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up() {
        Schema::create('rules_regulations', function (Blueprint $table) {
            $table->id();
            $table->string('title'); // Rule Title
            $table->text('description'); // Rule Description
            $table->string('document')->nullable(); // Image/document path (nullable)
            $table->enum('status', ['active', 'inactive'])->default('active'); // Status
            $table->timestamps();
        });
    }

    public function down() {
        Schema::dropIfExists('rules_regulations');
    }
};
