<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Inspector</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        body {
            background-color: #f4f6f9;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .main-content {
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .card-header {
            background-color: #007bff;
            color: white;
            border-radius: 10px 10px 0 0;
            padding: 15px 20px;
        }
        .card-header h4 {
            margin: 0;
            font-size: 1.5rem;
        }
        .card-body {
            padding: 30px;
        }
        .form-group label {
            font-weight: 600;
            margin-bottom: 8px;
        }
        .form-control, .form-select {
            border-radius: 6px;
            border: 1px solid #ced4da;
            transition: border-color 0.3s;
        }
        .form-control:focus, .form-select:focus {
            border-color: #007bff;
            box-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
        }
        .invalid-feedback {
            font-size: 0.875rem;
        }
        .btn-primary {
            background-color: #007bff;
            border: none;
            border-radius: 6px;
            padding: 10px 20px;
            transition: background-color 0.3s;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-secondary {
            border-radius: 6px;
            padding: 10px 20px;
        }
        .card-footer {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-top: 1px solid #e9ecef;
        }
        .required {
            color: #dc3545;
        }
        .current-image {
            max-width: 100px;
            margin-top: 10px;
        }
        @media (max-width: 576px) {
            .card-body {
                padding: 20px;
            }
            .card-header h4 {
                font-size: 1.25rem;
            }
        }
    </style>
</head>
<body>
    <div class="main-content">
        <section class="section">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-12 col-lg-10">
                        <div class="card">
                            <div class="card-header">
                                <h4>Inspectors Management > Edit Inspector</h4>
                            </div>
                            <form action="{{ route('inspectors.update', $inspector) }}" method="POST" enctype="multipart/form-data">
                                @csrf
                                @method('PUT')
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <div class="form-group">
                                                <label for="first_name">First Name <span class="required">*</span></label>
                                                <input type="text" name="first_name" id="first_name" class="form-control @error('first_name') is-invalid @enderror" value="{{ old('first_name', $inspector->first_name) }}" required>
                                                @error('first_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="form-group">
                                                <label for="last_name">Last Name <span class="required">*</span></label>
                                                <input type="text" name="last_name" id="last_name" class="form-control @error('last_name') is-invalid @enderror" value="{{ old('last_name', $inspector->last_name) }}" required>
                                                @error('last_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="form-group">
                                                <label for="email">Email <span class="required">*</span></label>
                                                <input type="email" name="email" id="email" class="form-control @error('email') is-invalid @enderror" value="{{ old('email', $inspector->email) }}" required>
                                                @error('email')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="form-group">
                                                <label for="password">Password</label>
                                                <input type="password" name="password" id="password" class="form-control @error('password') is-invalid @enderror">
                                                <small class="form-text text-muted">Leave blank to keep current password.</small>
                                                @error('password')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        {{-- <div class="col-md-6 mb-3">
                                            <div class="form-group">
                                                <label for="phone">Phone <span class="required">*</span></label>
                                                <input type="text" name="phone" id="phone" class="form-control @error('phone') is-invalid @enderror" value="{{ old('phone', $inspector->phone) }}" required>
                                                @error('phone')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div> --}}
                                        <div class="col-md-6 mb-3">
                                            <div class="form-group">
                                                <label for="phone">Phone <span class="required">*</span></label>
                                                <div class="input-group">
                                                    <select name="country_code" id="country_code" class="form-select" style="max-width: 100px;" required>
                                                        <option value="+91" {{ (old('country_code') ?? Str::substr($inspector->phone, 0, 3)) == '+91' ? 'selected' : '' }}>+91</option>
                                                        <option value="+1" {{ (old('country_code') ?? Str::substr($inspector->phone, 0, 2)) == '+1' ? 'selected' : '' }}>+1</option>
                                                        <option value="+44" {{ (old('country_code') ?? Str::substr($inspector->phone, 0, 3)) == '+44' ? 'selected' : '' }}>+44</option>
                                                        <!-- Add more country codes as needed -->
                                                    </select>
                                                    <input type="text" name="phone" id="phone" class="form-control @error('phone') is-invalid @enderror"
                                                           value="{{ old('phone', preg_replace('/^\+\d{1,4}/', '', $inspector->phone)) }}" required>
                                                </div>
                                                @error('phone')
                                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                                @enderror
                                                @error('country_code')
                                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6 mb-3">
                                            <div class="form-group">
                                                <label for="designation">Designation <span class="required">*</span></label>
                                                <input type="text" name="designation" id="designation" class="form-control @error('designation') is-invalid @enderror" value="{{ old('designation', $inspector->designation) }}" required>
                                                @error('designation')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        {{-- <div class="col-md-6 mb-3">
                                            <div class="form-group">
                                                <label for="department">Department (Trade) <span class="required">*</span></label>
                                                <input type="text" name="department" id="department" class="form-control @error('department') is-invalid @enderror" value="{{ old('department', $inspector->department) }}" required>
                                                @error('department')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div> --}}
                                        <div class="col-md-6 mb-3">
                                            <div class="form-group">
                                                <label>Department (Trade) <span class="text-danger">*</span></label>
                                                <select name="department" class="form-control @error('department') is-invalid @enderror">
                                                    <option value="">Select Department</option>
                                                    @foreach($roles as $role)
                                                        <option value="{{ $role->id }}" {{ old('department', $inspector->department) == $role->id ? 'selected' : '' }}>{{ $role->name }}</option>
                                                    @endforeach
                                                </select>
                                                @error('department') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="form-group">
                                                <label for="inspector_id">Inspector ID</label>
                                                <input type="text" name="inspector_id" id="inspector_id" class="form-control @error('inspector_id') is-invalid @enderror" value="{{ old('inspector_id', $inspector->inspector_id) }}">
                                                @error('inspector_id')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="form-group">
                                                <label for="image">Profile Image</label>
                                                <input type="file" name="image" id="image" class="form-control @error('image') is-invalid @enderror" accept="image/*">
                                                @if($inspector->image)
                                                    {{-- <img src="{{ $inspector->image }}" alt="Current Image" class="current-image"> --}}
                                                    <img src="http://localhost/mcdconstructions/public/inspector/{{ basename($inspector->image) }}" alt="Current image" class="current-image" />
                                                    <small class="form-text text-muted">Current image</small>
                                                @endif
                                                @error('image')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-12 mb-3">
                                            <div class="form-group">
                                                <label for="address">Address </label>
                                                <textarea name="address" id="address" class="form-control @error('address') is-invalid @enderror" rows="4" >{{ old('address', $inspector->address) }}</textarea>
                                                @error('address')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <div class="form-group">
                                                <label for="status">Status <span class="required">*</span></label>
                                                <select name="status" id="status" class="form-select @error('status') is-invalid @enderror" required>
                                                    <option value="active" {{ old('status', $inspector->status) == 'active' ? 'selected' : '' }}>Active</option>
                                                    <option value="inactive" {{ old('status', $inspector->status) == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                                </select>
                                                @error('status')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer text-end">
                                    <button class="btn btn-primary me-2" type="submit">Update</button>
                                    <a href="{{ route('inspectors.index') }}" class="btn btn-secondary">Cancel</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Bootstrap 5 JS (Optional for interactivity) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>