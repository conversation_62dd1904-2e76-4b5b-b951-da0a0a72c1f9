@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4>Assign Permit Numbers to Departments</h4>
                    </div>
                    <div class="card-body">
                        @if ($projects->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="table-1">
                                    <thead>
                                        <tr>
                                            <th>Sr No</th>
                                            <th>Project Name</th>
                                            <th>Departments</th>
                                            <th>Assigned Permits</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($projects as $key => $project)
                                            <tr>
                                                <td class="text-center">{{ $key + 1 }}</td>
                                                <td>{{ $project->project_name }}</td>
                                                <td>{{ implode(', ', $project->department_names) }}</td>
                                                <td>
                                                    @foreach($project->projectDepartmentPermits as $permit)
                                                        <span class="badge badge-primary">{{ $permit->department->name }}: {{ $permit->permit_number }}</span>
                                                    @endforeach
                                                </td>
                                                <td>
                                                    <form action="{{ route('project-department-permits.assign.store', $project) }}" method="POST">
                                                        @csrf
                                                        <div class="form-group">
                                                            <select name="mcd_staff_role_id" class="form-control" required>
                                                                <option value="">Select Department</option>
                                                                @foreach($project->department_names as $deptName)
                                                                    @php
                                                                        $dept = \App\Models\McdStaffRole::where('name', $deptName)->first();
                                                                    @endphp
                                                                    @if($dept)
                                                                        <option value="{{ $dept->id }}">{{ $dept->name }}</option>
                                                                    @endif
                                                                @endforeach
                                                            </select>
                                                        </div>
                                                        <div class="form-group">
                                                            <input type="text" name="permit_number" class="form-control" placeholder="Enter Permit Number" required>
                                                        </div>
                                                        <button type="submit" class="btn btn-primary btn-sm">Assign</button>
                                                    </form>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="alert alert-info text-center">
                                <i class="fa fa-info-circle mr-2"></i>
                                No projects found.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('style')
    <style>
        .btn-sm { 
            padding: 0.25rem 0.5rem; 
        }
        .badge {
            font-size: 0.8rem;
            padding: 0.3em 0.6em;
            margin-right: 0.5rem;
        }
        .table th, .table td {
            vertical-align: middle;
        }
        .alert {
            background-color: #e7f1ff;
            border: 1px solid #b8daff;
            color: #004085;
            padding: 0.75rem 1.25rem;
            border-radius: 0.25rem;
            margin-bottom: 1rem;
        }
    </style>
@endpush

@push('script')
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            @if(session('success'))
                Swal.fire({
                    title: 'Success!',
                    text: '{{ session('success') }}',
                    icon: 'success',
                    showConfirmButton: false,
                    timer: 4000,
                    timerProgressBar: true,
                    position: 'top-end',
                    toast: true,
                    customClass: {
                        popup: 'swal2-popup-custom',
                        title: 'swal2-title-custom',
                        content: 'swal2-content-custom'
                    }
                });
            @endif
        });
    </script>

    <style>
        .swal2-popup-custom {
            border-radius: 8px;
            padding: 15px;
            background: #ffffff;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
        }
        .swal2-title-custom {
            color: #28a745;
            font-weight: bold;
            font-size: 18px;
        }
        .swal2-content-custom {
            font-size: 14px;
            color: #555;
        }
    </style>
@endpush