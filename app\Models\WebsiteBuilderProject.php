<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;


class WebsiteBuilderProject extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_name',
        'project_address',
        'city',
        'state',
        'zip',
        'status',
        'objection_comment', // Added new field
        'architect_category_id',
        'builder_sub_category_id',
        'scope_of_work',
        'trades_involved',
        'expected_start_date',
        'expected_end_date',
        'project_image',
        'full_plan_set',
        'site_plan',
        'structural_calculations',
        'engineering_reports',
        'energy_calculations',
        'special_certifications',
         'inspector_id',
        'inspector_assigned_at',
        'approval_status'
    ];
    protected $casts = [
        'trades_involved' => 'array', 
    ];
    public function architectCategory()
    {
        return $this->belongsTo(ArchitectCategory::class, 'architect_category_id');
    }

    public function builderSubCategory()
    {
        return $this->belongsTo(BuilderSubCategory::class, 'builder_sub_category_id');
    }

    public function permitNumbers()
{
    return $this->hasMany(PermitNumber::class);
}
 public function inspector()
    {
        return $this->belongsTo(Inspector::class);
    }
public function reviewDocuments()
{
    return $this->hasMany(ProjectReviewDocument::class, 'project_id');
}
// public function physicalInspectionReportnew()
// {
//     return $this->hasOne(PhysicalInspectionReport::class, 'project_id');
// }
public function physicalInspectionReportnew()
{
    return $this->hasOne(PhysicalInspectionReport::class, 'project_id')->latestOfMany('created_at');
}

public function physicalInspectionReport()
{
    return $this->hasMany(PhysicalInspectionReport::class, 'project_id', 'id');
}

public function reviewDocumentsPc()
{
    return $this->hasMany(ProjectReviewDocumentPc::class, 'project_id');
}

public function physicalInspectionReports()
{
    return $this->hasMany(PhysicalInspectionReport::class, 'project_id');
}
public function projectDepartmentPermits()
    {
        return $this->hasMany(ProjectDepartmentPermit::class, 'website_builder_project_id');
    }
    public function departmentPermitNumbers()
    {
        $inspector = Auth::guard('api')->user();
        $departmentId = $inspector ? $inspector->department : null;

        return $this->hasMany(ProjectDepartmentPermit::class, 'website_builder_project_id')
            ->where('mcd_staff_role_id', $departmentId)
            ->select('id', 'website_builder_project_id', 'permit_number');
    }
    // App\Models\WebsiteBuilderProject.php

public function permitNumbersDepartment()
{
    return $this->hasMany(ProjectDepartmentPermit::class, 'website_builder_project_id');
}
public function latestInspectionReport()
{
    return $this->hasOne(PhysicalInspectionReport::class)->latestOfMany('created_at');
}

}