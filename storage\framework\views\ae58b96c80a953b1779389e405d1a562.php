<nav class="navbar navbar-expand-lg main-navbar sticky">
    <div class="form-inline mr-auto">
        <ul class="navbar-nav mr-3">
            <li><a href="#" data-toggle="sidebar"
                    class="nav-link nav-link-lg collapse-btn"> <i
                        data-feather="align-justify"></i>
                </a></li>
                 <?php if(Auth::guard('web')->check()): ?>
                        <li style="margin-top: 8px;">Municipal Admin Panel 
                        </li>
                    <?php elseif(Auth::guard('staff_mcd')->check()): ?>
                        <li style="margin-top: 8px;">Municipal Staff 
            </li>       <?php endif; ?>
           
            <li></li>
        </ul>
    </div>
    <ul class="navbar-nav navbar-right">
        <li class="dropdown"><a href="#" data-toggle="dropdown"
                class="nav-link dropdown-toggle nav-link-lg nav-link-user"> 
                <span style="color: rgb(12, 5, 5); margin-right: 10px; margin-top: 20px;" class="d-sm-none d-lg-inline-block">
                    <?php if(Auth::guard('web')->check()): ?>
                        <?php echo e(Auth::guard('web')->user()->name); ?>

                    <?php elseif(Auth::guard('staff_mcd')->check()): ?>
                        Staff: <?php echo e(Auth::guard('staff_mcd')->user()->staff_name); ?>-(<?php echo e(Auth::guard('staff_mcd')->user()->department->name); ?>)
                    <?php endif; ?>
                </span>               
                <img alt="image"
                    src="<?php echo e(asset('assets\img\users\download.jfif')); ?>" 
                    class="user-img-radious-style"> 
                <span class="d-sm-none d-lg-inline-block"></span></a>
            <div class="dropdown-menu dropdown-menu-right pullDown">
                <a href="<?php echo e(route('profile.edit')); ?>" class="dropdown-item has-icon"> 
                    <i class="far fa-user"></i> Profile
                </a>
                <div class="dropdown-divider"></div>
                <form method="POST" action="<?php echo e(route('logout')); ?>">
                    <?php echo csrf_field(); ?>
                    <?php if (isset($component)) { $__componentOriginal68cb1971a2b92c9735f83359058f7108 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal68cb1971a2b92c9735f83359058f7108 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dropdown-link','data' => ['href' => route('logout'),'onclick' => 'event.preventDefault();
                        this.closest(\'form\').submit();']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dropdown-link'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('logout')),'onclick' => 'event.preventDefault();
                        this.closest(\'form\').submit();']); ?>
                        <?php echo e(__('Log Out')); ?>

                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal68cb1971a2b92c9735f83359058f7108)): ?>
<?php $attributes = $__attributesOriginal68cb1971a2b92c9735f83359058f7108; ?>
<?php unset($__attributesOriginal68cb1971a2b92c9735f83359058f7108); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal68cb1971a2b92c9735f83359058f7108)): ?>
<?php $component = $__componentOriginal68cb1971a2b92c9735f83359058f7108; ?>
<?php unset($__componentOriginal68cb1971a2b92c9735f83359058f7108); ?>
<?php endif; ?>
                </form>
            </div>
        </li>
    </ul>
</nav>

<!-- JavaScript to display the current date -->
<script>
    // Get the current date
    const today = new Date();
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    const formattedDate = today.toLocaleDateString('en-US', options);

    // Insert the date into the span element
    document.getElementById('current-date').textContent = formattedDate;
</script><?php /**PATH C:\xampp\htdocs\mcdconstructions\resources\views/mcdpanel/layouts/header.blade.php ENDPATH**/ ?>