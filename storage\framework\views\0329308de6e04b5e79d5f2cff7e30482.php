<?php $__env->startSection('content'); ?>
    <div class="main-content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <h2 class="page-title">Physical Inspection Report</h2>

                    <!-- Top Buttons -->
                    <div class="mt-2 mb-2 d-flex justify-content-end">
                        <a href="<?php echo e(route('physicalinspection.download', $project->id)); ?>" class="btn btn-success me-2">
                            <i class="fas fa-download"></i> Download Report
                        </a>
                        <a href="<?php echo e(route('physicalinspection.report')); ?>" class="btn btn-primary">Back to Reports</a>
                    </div>

                    <!-- Project Details -->
                    <div class="card mb-2">
                        <div class="card-header">
                            <h2 class="card-title">Project Details</h2>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Project Name:</strong> <?php echo e($project->project_name); ?></p>
                                    <p><strong>Address:</strong> <?php echo e($project->full_address); ?></p>
                                    <p><strong>Project Status:</strong> <?php echo e($project->status); ?></p>
                                    <p><strong>Departments:</strong> <?php echo e(implode(', ', $project->department_names)); ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Scope of Work:</strong> <?php echo e($project->scope_of_work ?? 'N/A'); ?></p>
                                    <p><strong>Expected Start Date:</strong> <?php echo e($project->expected_start_date ?? 'N/A'); ?>

                                    </p>
                                    <p><strong>Expected End Date:</strong> <?php echo e($project->expected_end_date ?? 'N/A'); ?></p>
                                    <p><strong>Property Owner:</strong> <?php echo e($project->property_owner_name ?? 'N/A'); ?></p>
                                </div>
                            </div>
                            <?php if($project->project_image): ?>
                                <div class="mt-3">
                                    <p><strong>Project Image:</strong></p>
                                    <img src="<?php echo e(url('public' . $project->project_image)); ?>" alt="Project Image"
                                        class="img-fluid" style="max-width: 200px;">
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Inspection Reports -->
                    <?php if($inspectionReports->isNotEmpty()): ?>
                        <div class="accordion" id="inspectionReportsAccordion">
                            <?php $__currentLoopData = $inspectionReports; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $report): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="card mb-2">
                                    <div class="card-header" id="heading<?php echo e($index); ?>">
                                        <h2 class="card-title mb-0">
                                            <button class="btn btn-link text-decoration-none" type="button"
                                                data-bs-toggle="collapse" data-bs-target="#collapse<?php echo e($index); ?>"
                                                aria-expanded="<?php echo e($index === 0 ? 'true' : 'false'); ?>"
                                                aria-controls="collapse<?php echo e($index); ?>">
                                                Physical Inspection Report #<?php echo e($index + 1); ?> (
                                                <?php $__currentLoopData = $report->inspector_data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $inspector): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    Inspector:<?php echo e($inspector['name']); ?> |
                                                    Department:<?php echo e($inspector['department']); ?><?php if(!$loop->last): ?>
                                                        ,
                                                    <?php endif; ?>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                )
                                            </button>

                                        </h2>
                                    </div>
                                    <div id="collapse<?php echo e($index); ?>"
                                        class="collapse <?php echo e($index === 0 ? 'show' : ''); ?>"
                                        aria-labelledby="heading<?php echo e($index); ?>">
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <p><strong>Departments:</strong>
                                                        <?php $__currentLoopData = $report->inspector_data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $inspector): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <?php echo e($inspector['department']); ?><?php if(!$loop->last): ?>
                                                                ,
                                                            <?php endif; ?>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </p>
                                                    <p><strong>Inspectors:</strong>
                                                        <?php $__currentLoopData = $report->inspector_data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $inspector): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <?php echo e($inspector['name']); ?> <?php if(!$loop->last): ?>
                                                                ,
                                                            <?php endif; ?>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </p>
                                                    <p><strong>Inspection Date:</strong>
                                                        <?php echo e($report->inspection_done_at ? $report->inspection_done_at->format('Y-m-d H:i:s') : 'N/A'); ?>

                                                    </p>
                                                    <p><strong>Note:</strong> <?php echo e($report->note ?? 'N/A'); ?></p>
                                                    <p><strong>Violation Note:</strong>
                                                        <?php echo e($report->violation_note ?? 'N/A'); ?></p>
                                                </div>
                                                <div class="col-md-6">
                                                    <?php
                                                        $reportStatus = $report->report_status ?? null;
                                                        switch ($reportStatus) {
                                                            case 0:
                                                                $statusText = 'Approval';
                                                                $badgeClass = 'badge-success';
                                                                break;
                                                            case 1:
                                                                $statusText = 'Violation';
                                                                $badgeClass = 'badge-danger';
                                                                break;
                                                            case 2:
                                                                $statusText = 'Pending';
                                                                $badgeClass = 'badge-warning';
                                                                break;
                                                            default:
                                                                $statusText = 'N/A';
                                                                $badgeClass = 'badge-light';
                                                                break;
                                                        }
                                                    ?>
                                                    <p>
                                                        <strong>Inspection Report Status:</strong>
                                                        <span class="badge <?php echo e($badgeClass); ?>"><?php echo e($statusText); ?></span>
                                                    </p>
                                                    <p><strong>Potential Violations:</strong>
                                                        <?php echo e(implode(', ', $report->potential_violations ?? []) ?: 'None'); ?>

                                                    </p>
                                                </div>
                                            </div>
                                            <?php if(!empty($report->report_images)): ?>
                                                <div class="mt-3">
                                                    <p><strong>Images:</strong></p>
                                                    <div class="row">
                                                        <?php $__currentLoopData = $report->report_images; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <div class="col-md-3 mb-3">
                                                                <a href="<?php echo e(url('/' . $image)); ?>" target="_blank">
                                                                    <img src="<?php echo e(url('/' . $image)); ?>" alt="Report Image"
                                                                        class="img-fluid rounded">
                                                                </a>
                                                            </div>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <div class="card">
                            <div class="card-body">
                                <p class="text-danger">No physical inspection reports available for this project.</p>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Bottom Buttons -->
                    <div class="mt-4 mb-3 d-flex justify-content-end">
                        <a href="<?php echo e(route('physicalinspection.download', $project->id)); ?>" class="btn btn-success me-2">
                            <i class="fas fa-download"></i> Download Report
                        </a>
                        <a href="<?php echo e(route('physicalinspection.report')); ?>" class="btn btn-primary">Back to Reports</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include Bootstrap JS for collapse functionality -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize collapse functionality for each accordion item
            const collapseButtons = document.querySelectorAll('[data-bs-toggle="collapse"]');
            collapseButtons.forEach(button => {
                const targetSelector = button.getAttribute('data-bs-target');
                const target = document.querySelector(targetSelector);
                if (target) {
                    // Initialize Bootstrap Collapse
                    const bsCollapse = new bootstrap.Collapse(target, {
                        toggle: false // Prevent automatic toggling on initialization
                    });
                    // Handle click to toggle collapse
                    button.addEventListener('click', function(event) {
                        event.preventDefault(); // Prevent default behavior
                        if (target.classList.contains('show')) {
                            bsCollapse.hide(); // Close if open
                        } else {
                            bsCollapse.show(); // Open if closed
                        }
                    });
                }
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('mcdpanel.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\mcdconstructions\resources\views/BuilderWebsiteDashboard/physicalinspection/detailedreport.blade.php ENDPATH**/ ?>