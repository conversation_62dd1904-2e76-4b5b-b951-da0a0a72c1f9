<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Architect;
use App\Models\McdStaff;
use App\Models\BuilderStaff;
use App\Models\Inspector;
use Illuminate\Support\Facades\DB;
use App\Models\Message;
use App\Models\Attachment;
use App\Models\MessageUserStar;
use Illuminate\Support\Facades\Auth;
use App\Http\Resources\MessageResource;
use App\Http\Resources\ArchitectResource;
use App\Http\Resources\BuilderResource;
use App\Http\Resources\InspectorResource;
use App\Http\Resources\McdResource;

class EmailMessageApiController extends Controller
{
    public function index(Request $request)
    {
        try {
            $builderCount = User::where('role_id', 2)
                ->select('email', 'name', DB::raw('"User" as source'))
                ->union(BuilderStaff::select('email', 'staff_name as name', DB::raw('"BuilderStaff" as source')))
                ->distinct('email')
                ->count();

            $architectCount = DB::table('users')
                ->where('role_id', 4)
                ->select('email', 'name', DB::raw('"User" as source'))
                ->union(DB::table('architects')->where('role_id', 3)->select('email', 'name', DB::raw('"Architect" as source')))
                ->distinct('email')
                ->count();

            $mcdCount = User::where('role_id', 3)
                ->select('email', 'name', DB::raw('"User" as source'))
                ->union(McdStaff::select('email', 'staff_name as name', DB::raw('"McdStaff" as source')))
                ->distinct('email')
                ->count();

            $inspectorCount = Inspector::count();

            $guard = auth()->guard('staff_mcd')->check() ? 'staff_mcd' :
                     (auth()->guard('staff_builder')->check() ? 'staff_builder' :
                     (auth()->guard('api')->check() ? 'api' : 'web'));
            $userId = auth()->guard('staff_mcd')->check() ? auth()->guard('staff_mcd')->id() :
                      (auth()->guard('staff_builder')->check() ? auth()->guard('staff_builder')->id() :
                      (auth()->guard('api')->check() ? auth()->guard('api')->id() : Auth::id()));

            $inboxMessages = Message::with([
                'senderInspectorRelation', 'senderUserRelation', 'senderMcdStaffRelation', 'senderBuilderStaffRelation',
                'receiverInspectorRelation', 'receiverUserRelation', 'receiverMcdStaffRelation', 'receiverBuilderStaffRelation',
                'attachments', 'userStars'
            ])
                ->where(function ($query) use ($userId, $guard) {
                    if ($guard === 'staff_mcd') {
                        $query->where('receiver_id_mcd_staff', $userId);
                    } elseif ($guard === 'staff_builder') {
                        $query->where('receiver_id_builder_staff', $userId);
                    } elseif ($guard === 'api') {
                        $query->where('receiver_id_inspector', $userId);
                    } else {
                        $query->where('receiver_id', $userId);
                    }
                })
                ->whereNull('deleted_at')
                ->get()
                ->map(function ($message) {
                    $message->status = 'inbox';
                    return $message;
                });

            $outboxMessages = Message::with([
                'senderInspectorRelation', 'senderUserRelation', 'senderMcdStaffRelation', 'senderBuilderStaffRelation',
                'receiverInspectorRelation', 'receiverUserRelation', 'receiverMcdStaffRelation', 'receiverBuilderStaffRelation',
                'attachments', 'userStars'
            ])
                ->where(function ($query) use ($userId, $guard) {
                    if ($guard === 'staff_mcd') {
                        $query->where('sender_id_mcd_staff', $userId);
                    } elseif ($guard === 'staff_builder') {
                        $query->where('sender_id_builder_staff', $userId);
                    } elseif ($guard === 'api') {
                        $query->where('sender_id_inspector', $userId);
                    } else {
                        $query->where('sender_id', $userId);
                    }
                })
                ->whereNull('deleted_at')
                ->get()
                ->map(function ($message) {
                    $message->status = 'outbox';
                    return $message;
                });

            $starredMessages = Message::with([
                'senderInspectorRelation', 'senderUserRelation', 'senderMcdStaffRelation', 'senderBuilderStaffRelation',
                'receiverInspectorRelation', 'receiverUserRelation', 'receiverMcdStaffRelation', 'receiverBuilderStaffRelation',
                'attachments', 'userStars'
            ])
                ->whereHas('userStars', function ($query) use ($userId, $guard) {
                    if ($guard === 'staff_mcd') {
                        $query->where('mcd_staff_id', $userId)->where('is_starred', true);
                    } elseif ($guard === 'staff_builder') {
                        $query->where('builder_staff_id', $userId)->where('is_starred', true);
                    } elseif ($guard === 'api') {
                        $query->where('inspector_id', $userId)->where('is_starred', true);
                    } else {
                        $query->where('user_id', $userId)->where('is_starred', true);
                    }
                })
                ->whereNull('deleted_at')
                ->where(function ($query) use ($userId, $guard) {
                    if ($guard === 'staff_mcd') {
                        $query->where('sender_id_mcd_staff', $userId)->orWhere('receiver_id_mcd_staff', $userId);
                    } elseif ($guard === 'staff_builder') {
                        $query->where('sender_id_builder_staff', $userId)->orWhere('receiver_id_builder_staff', $userId);
                    } elseif ($guard === 'api') {
                        $query->where('sender_id_inspector', $userId)->orWhere('receiver_id_inspector', $userId);
                    } else {
                        $query->where('sender_id', $userId)->orWhere('receiver_id', $userId);
                    }
                })
                ->get()
                ->map(function ($message) {
                    $message->status = 'starred';
                    return $message;
                });

            $trashMessages = Message::onlyTrashed()
                ->with([
                    'senderInspectorRelation', 'senderUserRelation', 'senderMcdStaffRelation', 'senderBuilderStaffRelation',
                    'receiverInspectorRelation', 'receiverUserRelation', 'receiverMcdStaffRelation', 'receiverBuilderStaffRelation',
                    'attachments', 'userStars'
                ])
                ->where(function ($query) use ($userId, $guard) {
                    if ($guard === 'staff_mcd') {
                        $query->where('sender_id_mcd_staff', $userId)->orWhere('receiver_id_mcd_staff', $userId);
                    } elseif ($guard === 'staff_builder') {
                        $query->where('sender_id_builder_staff', $userId)->orWhere('receiver_id_builder_staff', $userId);
                    } elseif ($guard === 'api') {
                        $query->where('sender_id_inspector', $userId)->orWhere('receiver_id_inspector', $userId);
                    } else {
                        $query->where('sender_id', $userId)->orWhere('receiver_id', $userId);
                    }
                })
                ->get()
                ->map(function ($message) {
                    $message->status = 'trash';
                    return $message;
                });

            $messages = collect()->merge($inboxMessages)->merge($outboxMessages)->merge($starredMessages)->merge($trashMessages)->unique('id');

            return response()->json([
                'success' => true,
                'data' => [
                    'builder_count' => $builderCount,
                    'architect_count' => $architectCount,
                    'mcd_count' => $mcdCount,
                    'inspector_count' => $inspectorCount,
                    'messages' => MessageResource::collection($messages)
                ],
                'message' => 'Messages and counts retrieved successfully'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving messages',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function architects(Request $request)
    {
        try {
            $architects = DB::table('users')
                ->where('role_id', 4)
                ->select('id', 'name', 'email', DB::raw('created_at AS created_at'), DB::raw('"User" AS source'))
                ->union(
                    DB::table('architects')
                        ->where('role_id', 3)
                        ->select('id', 'name', 'email', DB::raw('created_at AS created_at'), DB::raw('"Architect" AS source'))
                )
                ->distinct('email')
                ->get();

            return response()->json([
                'success' => true,
                'data' => ArchitectResource::collection($architects),
                'message' => 'Architects retrieved successfully'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving architects',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function storeEngineer(Request $request)
    {
        try {
            if (!Auth::check() && !auth()->guard('staff_mcd')->check() && !auth()->guard('staff_builder')->check() && !auth()->guard('api')->check()) {
                return response()->json(['success' => false, 'message' => 'User not authenticated.'], 401);
            }

            $request->validate([
                'receiver_email' => [
                    'required',
                    'email',
                    function ($attribute, $value, $fail) {
                        $existsInUsers = User::where('email', $value)->exists();
                        $existsInArchitects = Architect::where('email', $value)->exists();
                        if (!$existsInUsers && !$existsInArchitects) {
                            $fail('The selected email does not exist in the system.');
                        }
                    },
                ],
                'body' => 'required|string',
                'attachments.*' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:204800',
            ]);

            $receiver = User::where('email', $request->receiver_email)->first() ?? Architect::where('email', $request->receiver_email)->first();
            if (!$receiver) {
                throw new \Exception('Receiver not found.');
            }

            $messageData = [
                'subject' => 'Message Sent to Engineer',
                'body' => $request->body,
                'attachment_count' => $request->hasFile('attachments') ? count($request->file('attachments')) : 0,
                'receiver_id' => $receiver->id,
            ];

            $guard = auth()->guard('staff_mcd')->check() ? 'staff_mcd' :
                     (auth()->guard('staff_builder')->check() ? 'staff_builder' :
                     (auth()->guard('api')->check() ? 'api' : 'web'));
            $senderId = auth()->guard('staff_mcd')->check() ? auth()->guard('staff_mcd')->id() :
                        (auth()->guard('staff_builder')->check() ? auth()->guard('staff_builder')->id() :
                        (auth()->guard('api')->check() ? auth()->guard('api')->id() : Auth::id()));

            if ($guard === 'staff_mcd') {
                $messageData['sender_id_mcd_staff'] = $senderId;
            } elseif ($guard === 'staff_builder') {
                $messageData['sender_id_builder_staff'] = $senderId;
            } elseif ($guard === 'api') {
                $messageData['sender_id_inspector'] = $senderId;
            } else {
                $messageData['sender_id'] = $senderId;
            }

            $message = Message::create($messageData);

            if ($request->hasFile('attachments')) {
                $destinationPath = public_path('attachments');
                if (!file_exists($destinationPath)) {
                    mkdir($destinationPath, 0755, true);
                }
                if (!is_writable($destinationPath)) {
                    throw new \Exception('Attachments directory is not writable.');
                }
                foreach ($request->file('attachments') as $file) {
                    if ($file->isValid()) {
                        $filename = uniqid() . '.' . $file->getClientOriginalExtension();
                        $file->move($destinationPath, $filename);
                        Attachment::create([
                            'message_id' => $message->id,
                            'file_path' => 'attachments/' . $filename,
                            'file_name' => $file->getClientOriginalName(),
                        ]);
                    } else {
                        throw new \Exception('Invalid file uploaded.');
                    }
                }
            }

            return response()->json([
                'success' => true,
                'data' => new MessageResource($message),
                'message' => 'Your message has been sent to the Engineer Communication Panel successfully.'
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed: ' . implode(', ', array_merge(...$e->errors())),
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while sending the message',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function builders(Request $request)
    {
        try {
            $builders = User::where('role_id', 2)
                ->select('id', 'name', 'email', 'created_at', DB::raw('"User" as source'))
                ->union(BuilderStaff::select('id', 'staff_name as name', 'email', 'created_at', DB::raw('"BuilderStaff" as source')))
                ->distinct('email')
                ->get();

            return response()->json([
                'success' => true,
                'data' => BuilderResource::collection($builders),
                'message' => 'Builders retrieved successfully'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving builders',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function storeBuilder(Request $request)
    {
        try {
            if (!Auth::check() && !auth()->guard('staff_mcd')->check() && !auth()->guard('staff_builder')->check() && !auth()->guard('api')->check()) {
                return response()->json(['success' => false, 'message' => 'User not authenticated.'], 401);
            }

            $request->validate([
                'receiver_email' => [
                    'required',
                    'email',
                    function ($attribute, $value, $fail) {
                        $existsInUsers = User::where('email', $value)->exists();
                        $existsInBuilderStaff = BuilderStaff::where('email', $value)->exists();
                        if (!$existsInUsers && !$existsInBuilderStaff) {
                            $fail('The selected email does not exist in the system.');
                        }
                    },
                ],
                'body' => 'required|string',
                'attachments.*' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:204800',
            ]);

            $receiver = User::where('email', $request->receiver_email)->first() ?? BuilderStaff::where('email', $request->receiver_email)->first();
            if (!$receiver) {
                throw new \Exception('Receiver not found.');
            }

            $messageData = [
                'subject' => 'Message Sent to Builder',
                'body' => $request->body,
                'attachment_count' => $request->hasFile('attachments') ? count($request->file('attachments')) : 0,
            ];

            $guard = auth()->guard('staff_mcd')->check() ? 'staff_mcd' :
                     (auth()->guard('staff_builder')->check() ? 'staff_builder' :
                     (auth()->guard('api')->check() ? 'api' : 'web'));
            $senderId = auth()->guard('staff_mcd')->check() ? auth()->guard('staff_mcd')->id() :
                        (auth()->guard('staff_builder')->check() ? auth()->guard('staff_builder')->id() :
                        (auth()->guard('api')->check() ? auth()->guard('api')->id() : Auth::id()));

            if ($guard === 'staff_mcd') {
                $messageData['sender_id_mcd_staff'] = $senderId;
            } elseif ($guard === 'staff_builder') {
                $messageData['sender_id_builder_staff'] = $senderId;
            } elseif ($guard === 'api') {
                $messageData['sender_id_inspector'] = $senderId;
            } else {
                $messageData['sender_id'] = $senderId;
            }

            if ($receiver instanceof User) {
                $messageData['receiver_id'] = $receiver->id;
            } else {
                $messageData['receiver_id_builder_staff'] = $receiver->id;
            }

            $message = Message::create($messageData);

            if ($request->hasFile('attachments')) {
                $destinationPath = public_path('attachments');
                if (!file_exists($destinationPath)) {
                    mkdir($destinationPath, 0755, true);
                }
                if (!is_writable($destinationPath)) {
                    throw new \Exception('Attachments directory is not writable.');
                }
                foreach ($request->file('attachments') as $file) {
                    if ($file->isValid()) {
                        $filename = uniqid() . '.' . $file->getClientOriginalExtension();
                        $file->move($destinationPath, $filename);
                        Attachment::create([
                            'message_id' => $message->id,
                            'file_path' => 'attachments/' . $filename,
                            'file_name' => $file->getClientOriginalName(),
                        ]);
                    } else {
                        throw new \Exception('Invalid file uploaded.');
                    }
                }
            }

            return response()->json([
                'success' => true,
                'data' => new MessageResource($message),
                'message' => 'Your message has been sent to the Builder Communication Panel successfully.'
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed: ' . implode(', ', array_merge(...$e->errors())),
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while sending the message',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function mcd(Request $request)
    {
        try {
            $users = User::where('role_id', 3)->get(['id', 'name', 'email', 'created_at']);
            $mcdStaff = McdStaff::get(['id', 'staff_name as name', 'email', 'created_at']);
            $mcd = $users->merge($mcdStaff)->unique('email');

            return response()->json([
                'success' => true,
                'data' => McdResource::collection($mcd),
                'message' => 'MCD staff retrieved successfully'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving MCD staff',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function storeMcd(Request $request)
    {
        try {
            if (!Auth::check() && !auth()->guard('staff_mcd')->check() && !auth()->guard('staff_builder')->check() && !auth()->guard('api')->check()) {
                return response()->json(['success' => false, 'message' => 'User not authenticated.'], 401);
            }

            $request->validate([
                'receiver_email' => [
                    'required',
                    'email',
                    function ($attribute, $value, $fail) {
                        $existsInUsers = User::where('email', $value)->exists();
                        $existsInMcdStaff = McdStaff::where('email', $value)->exists();
                        if (!$existsInUsers && !$existsInMcdStaff) {
                            $fail('The selected email does not exist in the system.');
                        }
                    },
                ],
                'body' => 'required|string',
                'attachments.*' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:204800',
            ]);

            $receiver = User::where('email', $request->receiver_email)->first() ?? McdStaff::where('email', $request->receiver_email)->first();
            if (!$receiver) {
                throw new \Exception('Receiver not found.');
            }

            $messageData = [
                'subject' => 'Message Sent to MCD',
                'body' => $request->body,
                'attachment_count' => $request->hasFile('attachments') ? count($request->file('attachments')) : 0,
            ];

            $guard = auth()->guard('staff_mcd')->check() ? 'staff_mcd' :
                     (auth()->guard('staff_builder')->check() ? 'staff_builder' :
                     (auth()->guard('api')->check() ? 'api' : 'web'));
            $senderId = auth()->guard('staff_mcd')->check() ? auth()->guard('staff_mcd')->id() :
                        (auth()->guard('staff_builder')->check() ? auth()->guard('staff_builder')->id() :
                        (auth()->guard('api')->check() ? auth()->guard('api')->id() : Auth::id()));

            if ($guard === 'staff_mcd') {
                $messageData['sender_id_mcd_staff'] = $senderId;
            } elseif ($guard === 'staff_builder') {
                $messageData['sender_id_builder_staff'] = $senderId;
            } elseif ($guard === 'api') {
                $messageData['sender_id_inspector'] = $senderId;
            } else {
                $messageData['sender_id'] = $senderId;
            }

            if ($receiver instanceof User) {
                $messageData['receiver_id'] = $receiver->id;
            } else {
                $messageData['receiver_id_mcd_staff'] = $receiver->id;
            }

            $message = Message::create($messageData);

            if ($request->hasFile('attachments')) {
                $destinationPath = public_path('attachments');
                if (!file_exists($destinationPath)) {
                    mkdir($destinationPath, 0755, true);
                }
                if (!is_writable($destinationPath)) {
                    throw new \Exception('Attachments directory is not writable.');
                }
                foreach ($request->file('attachments') as $file) {
                    if ($file->isValid()) {
                        $filename = uniqid() . '.' . $file->getClientOriginalExtension();
                        $file->move($destinationPath, $filename);
                        Attachment::create([
                            'message_id' => $message->id,
                            'file_path' => 'attachments/' . $filename,
                            'file_name' => $file->getClientOriginalName(),
                        ]);
                    } else {
                        throw new \Exception('Invalid file uploaded.');
                    }
                }
            }

            return response()->json([
                'success' => true,
                'data' => new MessageResource($message),
                'message' => 'Your message has been sent to the MCD Communication Panel successfully.'
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed: ' . implode(', ', array_merge(...$e->errors())),
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while sending the message',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function inspectors(Request $request)
    {
        try {
            $inspectors = Inspector::select('id', 'first_name', 'last_name', 'email', 'created_at')->get();

            return response()->json([
                'success' => true,
                'data' => InspectorResource::collection($inspectors),
                'message' => 'Inspectors retrieved successfully'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving inspectors',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function storeInspector(Request $request)
    {
        try {
            if (!Auth::check() && !auth()->guard('staff_mcd')->check() && !auth()->guard('staff_builder')->check() && !auth()->guard('api')->check()) {
                return response()->json(['success' => false, 'message' => 'User not authenticated.'], 401);
            }

            $request->validate([
                'receiver_email' => [
                    'required',
                    'email',
                    function ($attribute, $value, $fail) {
                        if (!Inspector::where('email', $value)->exists()) {
                            $fail('The selected email does not exist in the system.');
                        }
                    },
                ],
                'body' => 'required|string',
                'attachments.*' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:204800',
            ]);

            $receiver = Inspector::where('email', $request->receiver_email)->first();
            if (!$receiver) {
                throw new \Exception('Receiver not found.');
            }

            $messageData = [
                'subject' => 'Message Sent to Inspector',
                'body' => $request->body,
                'receiver_id_inspector' => $receiver->id,
                'attachment_count' => $request->hasFile('attachments') ? count($request->file('attachments')) : 0,
            ];

            $guard = auth()->guard('staff_mcd')->check() ? 'staff_mcd' :
                     (auth()->guard('staff_builder')->check() ? 'staff_builder' :
                     (auth()->guard('api')->check() ? 'api' : 'web'));
            $senderId = auth()->guard('staff_mcd')->check() ? auth()->guard('staff_mcd')->id() :
                        (auth()->guard('staff_builder')->check() ? auth()->guard('staff_builder')->id() :
                        (auth()->guard('api')->check() ? auth()->guard('api')->id() : Auth::id()));

            if ($guard === 'staff_mcd') {
                $messageData['sender_id_mcd_staff'] = $senderId;
            } elseif ($guard === 'staff_builder') {
                $messageData['sender_id_builder_staff'] = $senderId;
            } elseif ($guard === 'api') {
                $messageData['sender_id_inspector'] = $senderId;
            } else {
                $messageData['sender_id'] = $senderId;
            }

            $message = Message::create($messageData);

            if ($request->hasFile('attachments')) {
                $destinationPath = public_path('attachments');
                if (!file_exists($destinationPath)) {
                    mkdir($destinationPath, 0755, true);
                }
                if (!is_writable($destinationPath)) {
                    throw new \Exception('Attachments directory is not writable.');
                }
                foreach ($request->file('attachments') as $file) {
                    if ($file->isValid()) {
                        $filename = uniqid() . '.' . $file->getClientOriginalExtension();
                        $file->move($destinationPath, $filename);
                        Attachment::create([
                            'message_id' => $message->id,
                            'file_path' => 'attachments/' . $filename,
                            'file_name' => $file->getClientOriginalName(),
                        ]);
                    } else {
                        throw new \Exception('Invalid file uploaded.');
                    }
                }
            }

            return response()->json([
                'success' => true,
                'data' => new MessageResource($message),
                'message' => 'Your message has been sent to the Inspector Communication Panel successfully.'
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed: ' . implode(', ', array_merge(...$e->errors())),
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while sending the message',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function store(Request $request)
    {
        try {
            if (!Auth::check() && !auth()->guard('staff_mcd')->check() && !auth()->guard('staff_builder')->check() && !auth()->guard('api')->check()) {
                return response()->json(['success' => false, 'message' => 'User not authenticated.'], 401);
            }

            $request->validate([
                'receiver_email' => 'required|email',
                'body' => 'required|string',
                'recipient_type' => 'required|in:Architect,Builder,MCD,Inspector',
                'attachments.*' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:204800',
            ]);

            $receiver = null;
            $messageData = [
                'subject' => 'Message Sent to ' . ucfirst($request->recipient_type),
                'body' => $request->body,
                'attachment_count' => $request->hasFile('attachments') ? count($request->file('attachments')) : 0,
            ];

            $guard = auth()->guard('staff_mcd')->check() ? 'staff_mcd' :
                     (auth()->guard('staff_builder')->check() ? 'staff_builder' :
                     (auth()->guard('api')->check() ? 'api' : 'web'));
            $senderId = auth()->guard('staff_mcd')->check() ? auth()->guard('staff_mcd')->id() :
                        (auth()->guard('staff_builder')->check() ? auth()->guard('staff_builder')->id() :
                        (auth()->guard('api')->check() ? auth()->guard('api')->id() : Auth::id()));

            if ($guard === 'staff_mcd') {
                $messageData['sender_id_mcd_staff'] = $senderId;
            } elseif ($guard === 'staff_builder') {
                $messageData['sender_id_builder_staff'] = $senderId;
            } elseif ($guard === 'api') {
                $messageData['sender_id_inspector'] = $senderId;
            } else {
                $messageData['sender_id'] = $senderId;
            }

            switch ($request->recipient_type) {
                case 'Architect':
                    $receiver = User::where('email', $request->receiver_email)->first() ?? Architect::where('email', $request->receiver_email)->first();
                    if (!$receiver) {
                        return response()->json(['success' => false, 'message' => 'Receiver email not found.'], 404);
                    }
                    $messageData['receiver_id'] = $receiver->id;
                    break;

                case 'Builder':
                    $receiver = User::where('email', $request->receiver_email)->first() ?? BuilderStaff::where('email', $request->receiver_email)->first();
                    if (!$receiver) {
                        return response()->json(['success' => false, 'message' => 'Receiver email not found.'], 404);
                    }
                    $messageData[$receiver instanceof User ? 'receiver_id' : 'receiver_id_builder_staff'] = $receiver->id;
                    break;

                case 'MCD':
                    $receiver = User::where('email', $request->receiver_email)->first() ?? McdStaff::where('email', $request->receiver_email)->first();
                    if (!$receiver) {
                        return response()->json(['success' => false, 'message' => 'Receiver email not found.'], 404);
                    }
                    $messageData[$receiver instanceof User ? 'receiver_id' : 'receiver_id_mcd_staff'] = $receiver->id;
                    break;

                case 'Inspector':
                    $receiver = Inspector::where('email', $request->receiver_email)->first();
                    if (!$receiver) {
                        return response()->json(['success' => false, 'message' => 'Receiver email not found.'], 404);
                    }
                    $messageData['receiver_id_inspector'] = $receiver->id;
                    break;

                default:
                    return response()->json(['success' => false, 'message' => 'Invalid recipient type.'], 422);
            }

            $message = Message::create($messageData);

            if ($request->hasFile('attachments')) {
                $destinationPath = public_path('attachments');
                if (!file_exists($destinationPath)) {
                    mkdir($destinationPath, 0755, true);
                }
                if (!is_writable($destinationPath)) {
                    throw new \Exception('Attachments directory is not writable.');
                }
                foreach ($request->file('attachments') as $file) {
                    if ($file->isValid()) {
                        $filename = uniqid() . '.' . $file->getClientOriginalExtension();
                        $file->move($destinationPath, $filename);
                        Attachment::create([
                            'message_id' => $message->id,
                            'file_path' => 'attachments/' . $filename,
                            'file_name' => $file->getClientOriginalName(),
                        ]);
                    } else {
                        throw new \Exception('Invalid file uploaded.');
                    }
                }
            }

            return response()->json([
                'success' => true,
                'data' => new MessageResource($message),
                'message' => "Your message has been sent to the " . ucfirst($request->recipient_type) . " Communication Panel successfully."
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed: ' . implode(', ', array_merge(...$e->errors())),
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while sending the message',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function star(Request $request, $message_id)
    {
        try {
            $request->validate([
                'message_id' => 'required|exists:messages,id',
                'is_starred' => 'nullable|boolean',
            ]);

            $message = Message::findOrFail($message_id);

            $guard = auth()->guard('staff_mcd')->check() ? 'staff_mcd' :
                     (auth()->guard('staff_builder')->check() ? 'staff_builder' :
                     (auth()->guard('api')->check() ? 'api' : 'web'));
            $userId = auth()->guard('staff_mcd')->check() ? auth()->guard('staff_mcd')->id() :
                      (auth()->guard('staff_builder')->check() ? auth()->guard('staff_builder')->id() :
                      (auth()->guard('api')->check() ? auth()->guard('api')->id() : Auth::id()));

            $isAuthorized = $guard === 'staff_mcd' ? ($message->sender_id_mcd_staff == $userId || $message->receiver_id_mcd_staff == $userId) :
                            ($guard === 'staff_builder' ? ($message->sender_id_builder_staff == $userId || $message->receiver_id_builder_staff == $userId) :
                            ($guard === 'api' ? ($message->sender_id_inspector == $userId || $message->receiver_id_inspector == $userId) :
                            ($message->sender_id == $userId || $message->receiver_id == $userId)));

            if (!$isAuthorized) {
                return response()->json(['success' => false, 'message' => 'Unauthorized action.'], 403);
            }

            $starData = ['message_id' => $message->id];
            if ($guard === 'staff_mcd') {
                $starData['mcd_staff_id'] = $userId;
            } elseif ($guard === 'staff_builder') {
                $starData['builder_staff_id'] = $userId;
            } elseif ($guard === 'api') {
                $starData['inspector_id'] = $userId;
            } else {
                $starData['user_id'] = $userId;
            }

            $star = MessageUserStar::where($starData)->first();
            $isStarred = $request->has('is_starred') ? $request->is_starred : !$star?->is_starred;

            if ($star) {
                $star->update(['is_starred' => $isStarred]);
            } else {
                $starData['is_starred'] = $isStarred;
                MessageUserStar::create($starData);
            }

            return response()->json([
                'success' => true,
                'is_starred' => $isStarred,
                'message' => 'Star status updated successfully.'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while toggling star status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy($id)
    {
        try {
            $message = Message::findOrFail($id);

            $guard = auth()->guard('staff_mcd')->check() ? 'staff_mcd' :
                     (auth()->guard('staff_builder')->check() ? 'staff_builder' :
                     (auth()->guard('api')->check() ? 'api' : 'web'));
            $userId = auth()->guard('staff_mcd')->check() ? auth()->guard('staff_mcd')->id() :
                      (auth()->guard('staff_builder')->check() ? auth()->guard('staff_builder')->id() :
                      (auth()->guard('api')->check() ? auth()->guard('api')->id() : Auth::id()));

            $isAuthorized = $guard === 'staff_mcd' ? ($message->sender_id_mcd_staff == $userId || $message->receiver_id_mcd_staff == $userId) :
                            ($guard === 'staff_builder' ? ($message->sender_id_builder_staff == $userId || $message->receiver_id_builder_staff == $userId) :
                            ($guard === 'api' ? ($message->sender_id_inspector == $userId || $message->receiver_id_inspector == $userId) :
                            ($message->sender_id == $userId || $message->receiver_id == $userId)));

            if (!$isAuthorized) {
                return response()->json(['success' => false, 'message' => 'Unauthorized action.'], 403);
            }

            $message->delete();

            return response()->json([
                'success' => true,
                'message' => 'Message moved to trash successfully.'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while deleting the message',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function inbox()
    {
        try {
            $guard = auth()->guard('staff_mcd')->check() ? 'staff_mcd' :
                     (auth()->guard('staff_builder')->check() ? 'staff_builder' :
                     (auth()->guard('api')->check() ? 'api' : 'web'));
            $userId = auth()->guard('staff_mcd')->check() ? auth()->guard('staff_mcd')->id() :
                      (auth()->guard('staff_builder')->check() ? auth()->guard('staff_builder')->id() :
                      (auth()->guard('api')->check() ? auth()->guard('api')->id() : Auth::id()));

            $messages = Message::with([
                'senderInspectorRelation', 'senderUserRelation', 'senderMcdStaffRelation', 'senderBuilderStaffRelation',
                'receiverInspectorRelation', 'receiverUserRelation', 'receiverMcdStaffRelation', 'receiverBuilderStaffRelation',
                'attachments', 'userStars'
            ])
                ->where(function ($query) use ($userId, $guard) {
                    if ($guard === 'staff_mcd') {
                        $query->where('receiver_id_mcd_staff', $userId);
                    } elseif ($guard === 'staff_builder') {
                        $query->where('receiver_id_builder_staff', $userId);
                    } elseif ($guard === 'api') {
                        $query->where('receiver_id_inspector', $userId);
                    } else {
                        $query->where('receiver_id', $userId);
                    }
                })
                ->whereNull('deleted_at')
                ->get()
                ->map(function ($message) {
                    $message->status = 'inbox';
                    return $message;
                });

            if ($messages->isEmpty()) {
                return response()->json(['success' => true, 'message' => 'No messages available'], 200);
            }

            return response()->json([
                'success' => true,
                'data' => MessageResource::collection($messages),
                'message' => 'Inbox messages retrieved successfully'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving inbox messages',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function outbox()
    {
        try {
            $guard = auth()->guard('staff_mcd')->check() ? 'staff_mcd' :
                     (auth()->guard('staff_builder')->check() ? 'staff_builder' :
                     (auth()->guard('api')->check() ? 'api' : 'web'));
            $userId = auth()->guard('staff_mcd')->check() ? auth()->guard('staff_mcd')->id() :
                      (auth()->guard('staff_builder')->check() ? auth()->guard('staff_builder')->id() :
                      (auth()->guard('api')->check() ? auth()->guard('api')->id() : Auth::id()));

            $messages = Message::with([
                'senderInspectorRelation', 'senderUserRelation', 'senderMcdStaffRelation', 'senderBuilderStaffRelation',
                'receiverInspectorRelation', 'receiverUserRelation', 'receiverMcdStaffRelation', 'receiverBuilderStaffRelation',
                'attachments', 'userStars'
            ])
                ->where(function ($query) use ($userId, $guard) {
                    if ($guard === 'staff_mcd') {
                        $query->where('sender_id_mcd_staff', $userId);
                    } elseif ($guard === 'staff_builder') {
                        $query->where('sender_id_builder_staff', $userId);
                    } elseif ($guard === 'api') {
                        $query->where('sender_id_inspector', $userId);
                    } else {
                        $query->where('sender_id', $userId);
                    }
                })
                ->whereNull('deleted_at')
                ->get()
                ->map(function ($message) {
                    $message->status = 'outbox';
                    return $message;
                });

            if ($messages->isEmpty()) {
                return response()->json(['success' => true, 'message' => 'No messages available'], 200);
            }

            return response()->json([
                'success' => true,
                'data' => MessageResource::collection($messages),
                'message' => 'Outbox messages retrieved successfully'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving outbox messages',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function starred()
    {
        try {
            $guard = auth()->guard('staff_mcd')->check() ? 'staff_mcd' :
                     (auth()->guard('staff_builder')->check() ? 'staff_builder' :
                     (auth()->guard('api')->check() ? 'api' : 'web'));
            $userId = auth()->guard('staff_mcd')->check() ? auth()->guard('staff_mcd')->id() :
                      (auth()->guard('staff_builder')->check() ? auth()->guard('staff_builder')->id() :
                      (auth()->guard('api')->check() ? auth()->guard('api')->id() : Auth::id()));

            $messages = Message::with([
                'senderInspectorRelation', 'senderUserRelation', 'senderMcdStaffRelation', 'senderBuilderStaffRelation',
                'receiverInspectorRelation', 'receiverUserRelation', 'receiverMcdStaffRelation', 'receiverBuilderStaffRelation',
                'attachments', 'userStars'
            ])
                ->whereHas('userStars', function ($query) use ($userId, $guard) {
                    if ($guard === 'staff_mcd') {
                        $query->where('mcd_staff_id', $userId)->where('is_starred', true);
                    } elseif ($guard === 'staff_builder') {
                        $query->where('builder_staff_id', $userId)->where('is_starred', true);
                    } elseif ($guard === 'api') {
                        $query->where('inspector_id', $userId)->where('is_starred', true);
                    } else {
                        $query->where('user_id', $userId)->where('is_starred', true);
                    }
                })
                ->whereNull('deleted_at')
                ->where(function ($query) use ($userId, $guard) {
                    if ($guard === 'staff_mcd') {
                        $query->where('sender_id_mcd_staff', $userId)->orWhere('receiver_id_mcd_staff', $userId);
                    } elseif ($guard === 'staff_builder') {
                        $query->where('sender_id_builder_staff', $userId)->orWhere('receiver_id_builder_staff', $userId);
                    } elseif ($guard === 'api') {
                        $query->where('sender_id_inspector', $userId)->orWhere('receiver_id_inspector', $userId);
                    } else {
                        $query->where('sender_id', $userId)->orWhere('receiver_id', $userId);
                    }
                })
                ->get()
                ->map(function ($message) {
                    $message->status = 'starred';
                    return $message;
                });

            if ($messages->isEmpty()) {
                return response()->json(['success' => true, 'message' => 'No messages available'], 200);
            }

            return response()->json([
                'success' => true,
                'data' => MessageResource::collection($messages),
                'message' => 'Starred messages retrieved successfully'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving starred messages',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function trash()
    {
        try {
            $guard = auth()->guard('staff_mcd')->check() ? 'staff_mcd' :
                     (auth()->guard('staff_builder')->check() ? 'staff_builder' :
                     (auth()->guard('api')->check() ? 'api' : 'web'));
            $userId = auth()->guard('staff_mcd')->check() ? auth()->guard('staff_mcd')->id() :
                      (auth()->guard('staff_builder')->check() ? auth()->guard('staff_builder')->id() :
                      (auth()->guard('api')->check() ? auth()->guard('api')->id() : Auth::id()));

            $messages = Message::onlyTrashed()
                ->with([
                    'senderInspectorRelation', 'senderUserRelation', 'senderMcdStaffRelation', 'senderBuilderStaffRelation',
                    'receiverInspectorRelation', 'receiverUserRelation', 'receiverMcdStaffRelation', 'receiverBuilderStaffRelation',
                    'attachments', 'userStars'
                ])
                ->where(function ($query) use ($userId, $guard) {
                    if ($guard === 'staff_mcd') {
                        $query->where('sender_id_mcd_staff', $userId)->orWhere('receiver_id_mcd_staff', $userId);
                    } elseif ($guard === 'staff_builder') {
                        $query->where('sender_id_builder_staff', $userId)->orWhere('receiver_id_builder_staff', $userId);
                    } elseif ($guard === 'api') {
                        $query->where('sender_id_inspector', $userId)->orWhere('receiver_id_inspector', $userId);
                    } else {
                        $query->where('sender_id', $userId)->orWhere('receiver_id', $userId);
                    }
                })
                ->get()
                ->map(function ($message) {
                    $message->status = 'trash';
                    return $message;
                });

            if ($messages->isEmpty()) {
                return response()->json(['success' => true, 'message' => 'No messages available'], 200);
            }

            return response()->json([
                'success' => true,
                'data' => MessageResource::collection($messages),
                'message' => 'Trash messages retrieved successfully'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving trash messages',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function deleteAllTrashedMessages()
    {
        try {
            $guard = auth()->guard('staff_mcd')->check() ? 'staff_mcd' :
                     (auth()->guard('staff_builder')->check() ? 'staff_builder' :
                     (auth()->guard('api')->check() ? 'api' : 'web'));
            $userId = auth()->guard('staff_mcd')->check() ? auth()->guard('staff_mcd')->id() :
                      (auth()->guard('staff_builder')->check() ? auth()->guard('staff_builder')->id() :
                      (auth()->guard('api')->check() ? auth()->guard('api')->id() : Auth::id()));

            $trashedMessages = Message::onlyTrashed()
                ->where(function ($query) use ($userId, $guard) {
                    if ($guard === 'staff_mcd') {
                        $query->where('sender_id_mcd_staff', $userId)->orWhere('receiver_id_mcd_staff', $userId);
                    } elseif ($guard === 'staff_builder') {
                        $query->where('sender_id_builder_staff', $userId)->orWhere('receiver_id_builder_staff', $userId);
                    } elseif ($guard === 'api') {
                        $query->where('sender_id_inspector', $userId)->orWhere('receiver_id_inspector', $userId);
                    } else {
                        $query->where('sender_id', $userId)->orWhere('receiver_id', $userId);
                    }
                });

            $count = $trashedMessages->count();

            if ($count === 0) {
                return response()->json(['success' => true, 'message' => 'No messages found in trash to delete'], 200);
            }

            $trashedMessages->forceDelete();

            return response()->json(['success' => true, 'message' => 'Trashed messages deleted'], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while deleting trashed messages',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function showMessageDetail($id)
    {
        try {
            $guard = auth()->guard('staff_mcd')->check() ? 'staff_mcd' :
                     (auth()->guard('staff_builder')->check() ? 'staff_builder' :
                     (auth()->guard('api')->check() ? 'api' : 'web'));
            $userId = auth()->guard('staff_mcd')->check() ? auth()->guard('staff_mcd')->id() :
                      (auth()->guard('staff_builder')->check() ? auth()->guard('staff_builder')->id() :
                      (auth()->guard('api')->check() ? auth()->guard('api')->id() : Auth::id()));

            $message = Message::withTrashed()
                ->with([
                    'senderInspectorRelation', 'senderUserRelation', 'senderMcdStaffRelation', 'senderBuilderStaffRelation',
                    'receiverInspectorRelation', 'receiverUserRelation', 'receiverMcdStaffRelation', 'receiverBuilderStaffRelation',
                    'attachments', 'userStars'
                ])
                ->findOrFail($id);

            $isAuthorized = $guard === 'staff_mcd' ? ($message->sender_id_mcd_staff == $userId || $message->receiver_id_mcd_staff == $userId) :
                            ($guard === 'staff_builder' ? ($message->sender_id_builder_staff == $userId || $message->receiver_id_builder_staff == $userId) :
                            ($guard === 'api' ? ($message->sender_id_inspector == $userId || $message->receiver_id_inspector == $userId) :
                            ($message->sender_id == $userId || $message->receiver_id == $userId)));

            if (!$isAuthorized) {
                return response()->json(['success' => false, 'message' => 'Unauthorized access to this message.'], 403);
            }

            $message->status = $message->deleted_at ? 'trash' :
                               ($message->userStars->where(function ($star) use ($userId, $guard) {
                                   return $guard === 'staff_mcd' ? $star->mcd_staff_id == $userId :
                                          ($guard === 'staff_builder' ? $star->builder_staff_id == $userId :
                                          ($guard === 'api' ? $star->inspector_id == $userId : $star->user_id == $userId));
                               })->where('is_starred', true)->isNotEmpty() ? 'starred' :
                               ($guard === 'staff_mcd' && $message->sender_id_mcd_staff == $userId ||
                                $guard === 'staff_builder' && $message->sender_id_builder_staff == $userId ||
                                $guard === 'api' && $message->sender_id_inspector == $userId ||
                                $guard === 'web' && $message->sender_id == $userId ? 'outbox' : 'inbox'));

            return response()->json([
                'success' => true,
                'data' => new MessageResource($message),
                'message' => 'Message details retrieved successfully.'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving message details',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}