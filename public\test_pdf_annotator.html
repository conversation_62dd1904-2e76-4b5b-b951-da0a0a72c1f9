<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Annotator Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>PDF Annotator Integration Test</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test PDF Proxy Endpoint</h5>
                    </div>
                    <div class="card-body">
                        <p>Testing the proxy endpoint with a sample PDF file:</p>
                        <a href="/proxy-pdf?path=project_docs/1747824331_invoice_38 (10).pdf&projectId=1&departmentId=1" 
                           target="_blank" class="btn btn-primary">
                            Test Proxy Endpoint
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test PDF Annotator Integration</h5>
                    </div>
                    <div class="card-body">
                        <p>Testing the complete integration with the annotator tool:</p>
                        <a href="/pdf-annotator/index.html?file=/proxy-pdf?path=project_docs/1747824331_invoice_38 (10).pdf&projectId=1&departmentId=1&reviewStep=1" 
                           target="_blank" class="btn btn-success">
                            Test Complete Integration
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Results</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults">
                            <p>Click the test buttons above to verify the implementation.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Test the proxy endpoint
        async function testProxyEndpoint() {
            try {
                const response = await fetch('/proxy-pdf?path=project_docs/1747824331_invoice_38 (10).pdf&projectId=1&departmentId=1');
                const results = document.getElementById('testResults');
                
                if (response.ok) {
                    results.innerHTML = '<div class="alert alert-success">✅ Proxy endpoint is working correctly!</div>';
                } else {
                    results.innerHTML = '<div class="alert alert-danger">❌ Proxy endpoint failed: ' + response.status + '</div>';
                }
            } catch (error) {
                document.getElementById('testResults').innerHTML = '<div class="alert alert-danger">❌ Error testing proxy endpoint: ' + error.message + '</div>';
            }
        }

        // Auto-test on page load
        window.addEventListener('load', testProxyEndpoint);
    </script>
</body>
</html> 