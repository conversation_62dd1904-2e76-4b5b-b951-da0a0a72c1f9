@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <div class="container mx-auto px-4 py-8 max-w-7xl">
            <!-- Page Header -->
            <div class="card-header d-flex justify-content-between align-items-center mb-8 bg-white border-b border-indigo-200 p-4">
                <h1 class="text-3xl font-bold text-indigo-900">Project Details: {{ $project->project_name }}</h1>
                <a href="{{ route('dashboard') }}" class="btn btn-primary">Back to List</a>
            </div>

            <!-- Main Content -->
            <div class="bg-white shadow-xl rounded-lg overflow-hidden">
                <!-- Project Header -->
                <div class="bg-indigo-50 p-6 border-b border-indigo-200">
                    <div class="flex justify-between items-center">
                        <div>
                            <h2 class="text-2xl font-semibold text-indigo-800">{{ $project->project_name }}</h2>
                            <p class="text-indigo-600">{{ $project->project_address }}, {{ $project->city }}, {{ $project->state }} {{ $project->zip }}</p>
                        </div>
                        <button x-data="{ open: false }" @click="open = true"
                            class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-800 transition-colors duration-200">
                            @if ($project->permitNumbers->isNotEmpty())
                                <ul>
                                    @foreach ($project->permitNumbers as $permit)
                                        <li>PUID: {{ $permit->permit_number }}</li>
                                    @endforeach
                                </ul>
                            @else
                                <p>PUID: (Yet to Assign)</p>
                            @endif
                        </button>
                    </div>
                </div>

                <!-- Project Details Grid -->
                <div class="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Left Column -->
                    <div>
                        <!-- Basic Information -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-indigo-900 mb-2">Basic Information</h3>
                            <div class="space-y-3">
                                <p><strong class="text-gray-800">Scope of Work:</strong> <span class="text-indigo-700">{{ $project->scope_of_work ?? 'N/A' }}</span></p>
                                <p><strong class="text-gray-800">Department (Trade):</strong> <span class="text-indigo-700">
                                    @if (!empty($project->department_names))
                                        {{ implode(', ', $project->department_names) }}
                                    @else
                                        N/A
                                    @endif
                                </span></p>
                            </div>
                        </div>
                        <!-- Department Permits -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-indigo-900 mb-2">Department Permit Number</h3>
                            <div class="space-y-3">
                                @if (!empty($project->department_permit_info))
                                    <ul>
                                        @foreach ($project->department_permit_info as $permit)
                                            <li><strong class="text-gray-800">{{ $permit['name'] }}:</strong> <span class="text-indigo-700">{{ $permit['permit_number'] ?? 'N/A' }}</span></li>
                                        @endforeach
                                    </ul>
                                @else
                                    <p class="text-indigo-700">No department permits assigned.</p>
                                @endif
                            </div>
                        </div>
                        <!-- Dates -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-indigo-900 mb-2">Project Timeline</h3>
                            <div class="space-y-3">
                                <p><strong class="text-gray-800">Expected Start Date:</strong> <span class="text-indigo-700">{{ $project->expected_start_date ? \Carbon\Carbon::parse($project->expected_start_date)->format('M d, Y') : 'N/A' }}</span></p>
                                <p><strong class="text-gray-800">Expected End Date:</strong> <span class="text-indigo-700">{{ $project->expected_end_date ? \Carbon\Carbon::parse($project->expected_end_date)->format('M d, Y') : 'N/A' }}</span></p>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column -->
                    <div>
                        <!-- Project Documents -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-indigo-900 mb-2">Project Documents</h3>
                            <div class="space-y-3">
                                @if ($allDocuments->isEmpty())
                                    <p class="text-indigo-700">No documents or images available for this project.</p>
                                @else
                                    @foreach ($allDocuments as $doc)
                                        <p>
                                            <strong class="text-gray-800">{{ $doc->file_type_display }} ({{ $doc->department_name }}):</strong>
                                            @php
                                                $ext = strtolower(pathinfo($doc->file_path, PATHINFO_EXTENSION));
                                                $baseUrl = $doc->file_type === 'project_image'
                                                    ? 'http://localhost/builderwebsite/public/profile_images/'
                                                    : 'http://localhost/builderwebsite/public/project_documents/';
                                                $url = $baseUrl . basename($doc->file_path);
                                            @endphp
                                            @if (in_array($ext, ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg']))
                                                <a href="{{ $url }}" target="_blank" class="text-indigo-600 hover:text-indigo-800 transition-colors duration-200">View Image</a>
                                            @elseif ($ext === 'pdf')
                                                @php
                                                    $userId = null;
                                                    $userRole = 'unknown';
                                                    $userName = 'Unknown User';

                                                    if (auth()->guard('staff_mcd')->check()) {
                                                        $userId = auth()->guard('staff_mcd')->id();
                                                        $user = auth()->guard('staff_mcd')->user();
                                                        $userRole = $user->role->name ?? 'mcd_staff';
                                                        $userName = $user->staff_name ?? 'MCD Staff';
                                                    } elseif (auth()->guard('web')->check()) {
                                                        $userId = auth()->guard('web')->id();
                                                        $user = auth()->guard('web')->user();
                                                        $userRole = 'admin';
                                                        $userName = $user->name ?? 'Admin User';
                                                    } elseif (auth()->guard('staff_builder')->check()) {
                                                        $userId = auth()->guard('staff_builder')->id();
                                                        $user = auth()->guard('staff_builder')->user();
                                                        $userRole = $user->role->name ?? 'builder_staff';
                                                        $userName = $user->staff_name ?? 'Builder Staff';
                                                    }
                                                @endphp
                                                {{-- <a href="http://localhost:3001/?file={{ $url }}&projectId={{ $project->id }}&userId={{ $userId }}&userRole={{ $userRole }}&userName={{ urlencode($userName) }}" target="_blank"
                                                   class="text-indigo-600 hover:text-indigo-800 transition-colors duration-200">Edit PDF</a> --}}
                                                <a href="{{ $url }}" target="_blank" class="text-indigo-600 hover:text-indigo-800 transition-colors duration-200 ml-2">View PDF</a>
                                            @else
                                                <a href="{{ $url }}" target="_blank" class="text-indigo-600 hover:text-indigo-800 transition-colors duration-200">View File</a>
                                            @endif
                                        </p>
                                    @endforeach
                                @endif
                            </div>
                        </div>

                        <!-- Additional Information -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-indigo-900 mb-2">Additional Information</h3>
                            <div class="space-y-3">
                                <p><strong class="text-gray-800">Special Certifications:</strong> <span class="text-indigo-700">{{ $project->special_certifications ?? 'N/A' }}</span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom Back Button -->
            <div class="card-header d-flex justify-content-end align-items-center mt-6 bg-white border-t border-indigo-200 p-4">
                <a href="{{ route('dashboard') }}" class="btn btn-primary">Back to List</a>
            </div>
        </div>
    </div>
@endsection

@push('style')
    <!-- Tailwind CSS CDN -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        [x-cloak] { display: none; }
        .btn-info {
            background-color: #17a2b8;
            border-color: #17a2b8;
            color: #fff;
        }
        .btn-info:hover {
            background-color: #138496;
            border-color: #117a8b;
        }
    </style>
@endpush

@push('script')
    <!-- Alpine.js CDN -->
    <script src="//unpkg.com/alpinejs" defer></script>
@endpush