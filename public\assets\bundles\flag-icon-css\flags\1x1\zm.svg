<svg xmlns="http://www.w3.org/2000/svg" height="512" width="512" id="flag-icon-css-zm">
  <defs>
    <clipPath id="a">
      <path fill-opacity=".67" d="M248.03 0h496.06v496.06H248.03z"/>
    </clipPath>
  </defs>
  <g fill-rule="evenodd" clip-path="url(#a)" transform="translate(-256) scale(1.0321)">
    <path fill="#198a00" d="M0 0h744.09v496.06H0z"/>
    <path fill="#ef7d00" d="M656.77 178.21h87.321v317.82H656.77z"/>
    <path d="M569.56 178.06h87.227v317.89H569.56z"/>
    <path fill="#de2010" d="M480.51 178.26h89.074v317.81H480.51z"/>
    <g stroke="#000" fill="#ef7d00">
      <path stroke-linejoin="round" d="M685.586 63.548s32.861-14.042 36.045-16.937c1.448 1.737-13.173 19.398-41.546 25.912 25.767-6.08 47.771-25.188 52.259-24.61 1.303.29 1.012 18.675-58.05 34.599 40.823-10.713 64.563-30.4 64.274-28.229.29.434-4.053 15.49-39.809 27.215 9.989-2.316 37.059-18.819 36.77-15.634.868 1.303-26.202 36.045-75.128 24.754 39.085 10.133 64.418-14.331 68.037-13.897.724.145-7.239 21.425-56.456 22.728 23.596-2.461 16.792-.146 16.792-.146s-13.898 10.857-30.69 3.475c13.174 3.62 14.621 3.764 14.91 4.922-.868 1.447-11.725 3.619-22.147-2.027 8.395 3.474 15.923 4.054 16.067 5.357-.144.433-5.5 3.474-10.277 1.302-4.777-2.172-48.785-29.241-48.785-29.241l74.984-20.557 2.75 1.014zm-88.156 73.227c-6.595 0-6.776 5.868-6.776 5.868s-.545.423-.182 3.267c1.089-1.936 1.634-2.541 1.634-2.541.726.121 3.75 1.028 8.53-2.722-4.357 4.536-1.754 6.23-1.754 6.23s-1.029 3.57 2.298 4.296c-1.028-1.452-.423-2.723-.423-2.723s4.658-.483 4.355-6.17c.182 5.142 2.965 6.412 2.965 6.412s0 2.844 3.024 3.086c-1.632-1.452-1.27-3.69-1.27-3.69s3.933-2.844.605-7.684c1.996-1.15 3.63-4.355 3.63-4.355s-2.722-1.15-3.992-2.117c-.606-1.272-.061-8.228-.061-8.228l-1.634-9.015-4.658 14.036c.18-1.996.302 6.05-6.292 6.05z" stroke-width=".902"/>
      <path stroke-linejoin="round" d="M629.998 116.528c.144.145 4.922 5.501 9.554 5.211 1.737-1.447-3.619-4.632-3.619-5.356 1.882 1.738 9.844 8.541 14.91 6.08 2.027-2.895-3.619-2.46-9.844-10.278 4.343 2.895 15.2 9.265 20.267 6.804 2.172-2.316-11.291-9.555-15.779-15.2l-11.58-5.935-15.78 12.884 11.871 5.79z" stroke-width=".902"/>
      <path stroke-linejoin="round" d="M612.19 72.523s5.357-3.185 26.781-1.592c2.316.29 14.62-4.198 18.53-5.501 6.224-1.158 26.056-5.501 31.413-9.554 3.763-.435-1.015 6.948-5.79 8.685-5.068 2.317-23.452 8.686-29.967 7.818 7.818.145 3.475 6.514-9.554 3.474 6.225 3.474 3.909 4.053 3.909 4.053s-11.002.58-13.897-1.882c7.383 2.75 4.343 3.764 4.343 3.764s-7.528.724-11.002-.868c5.356 1.592 2.605 2.605 2.605 2.605s-4.342.724-7.961-.579c-3.619-1.303-9.12-10.422-9.41-10.422z" stroke-linecap="round" stroke-width=".902"/>
      <path stroke-linejoin="round" d="M620.78 119.53l.787 14.157s-.424.726-.787 1.15c-.363.423-10.042-1.513-8.711 5.868 0 3.025.06 3.81 2.118 5.505-.545-1.997-.364-3.388-.364-3.388s2.722 1.634 5.385-2.843c-1.815 4.416-.545 5.99-.122 6.11.424.787-.725 4.356 2.904 4.295-1.39-1.33-.725-3.267-.725-3.267s3.811-.604 2.48-7.501c1.392-1.392 1.936-.06 1.936-.06s.423 4.113 3.75 3.266c1.513.847-.241 3.085-.241 3.085s2.42.061 3.145-1.996c.727-2.057 1.573-5.747-2.42-7.26-.484-1.451 1.574-1.572 1.574-1.572s2.54.846 3.266 2.117c.726 1.27.484-3.327-2.661-3.812-3.872-.12-4.114-.907-4.114-1.088 0-.182-.666-10.285-.968-13.189l-6.232.423z" stroke-width=".902"/>
      <path stroke-linejoin="round" d="M634.25 131.592c.047-.649-6.605-9.606-4.917-10.764 1.686.348 4.453 4.422 6.695 3.1-.68-1.589-2.682-.695-4.956-4.76-2.273-4.596-2.605-11.495-10.567-19.02 5.155 7.963 16.848 12.631 17.343 10.31s-10.296-11.104-9.743-13.22c2.146 4.346 12.981 14.03 20.964 13.228.548-1.865-6.537-5.628-8.36-9.141-5.1-3.426-18.748-15.19-19.021-17.918-4.966-7.243-8.067-9.435-9.51-10.269-.558-.657-.698-1.286-.807-1.685-3.083-7.268 1-9.628 3.39-9.977 1.967-.219 2.538.076 3.954-.61-1.677-.655-3.352-1.268-5.027-1.924 2.185 1.603 7.766.192 6.556 4.808 2.447-.931 7.654-7.125-5.9-8.523-4.412-5.06-22.548-7.97-27.087 14.117.362.31.552.564 1.705 1.426-5.731-2.77-21.178-4.911-26.743-5.81-15.043-4.34-30.664-14.798-32.165-13.512-2.008.891 9.087 11.215 8.51 11.443-9.62-5.61-18.41-9.584-26.587-13.2-5.754-2.151-11.516-7.159-12.152-6.206-2.117 4.386 9.224 15.413 11.334 16.71 2.11 1.24 18.702 8.937 18.458 9.033-24.991-11.344-29.182-13.4-30.435-14.707-2.203-.497-7.57-7.741-9.064-7.276-.819.805.823 12.252 12.748 17.39 1.944 1.417 25.563 9.89 25.332 10.554-.058.166-27.052-11.141-28.101-11.545-5.205-2.042-11.59-9.195-12.784-8.39-1.14.73 2.943 8.473 8.202 10.901 2.757 1.216 13.33 6.453 22.857 9.786.608.22-17.123-6.724-25.649-10.286-3.865-2.146-5.685-4.82-6.274-4.244-.87.575 1.407 12.013 28.802 18.467.722.307 9.644-2.114 9.203-1.793-.11.08-8.86 1.985-9.71 1.92-.759-.115-5.552.568-5.639.816-.29.774 1.737 4.87 15.882 4.015 1.808-.108 11.621-3.573 10.797-2.828-.412.373-13.363 4.581-14.35 4.664-.856.121-5.405.597-5.574 1.08-.205.66 3.368 3.576 10.964 4.043 6.726.357 19.506-4.11 19.25-3.82-.257.29-12.32 4.423-12.636 4.675-.437.247-4.855.486-5.01.826-.36.844 6.373 7.14 26.283.42-2 2.702-11.65 4.55-11.637 5.037-.045.353 2.127 2.45 5.5 3.201 1.688.375 3.997.299 6.107-.035 3.79-.758 7.784-2.257 12.858-6.947.607 1.472-12.676 8.193-12.052 8.834 2.88 2.671 12.447-.344 13.074-.694.627-.351 18.423-10.386 18.361-11.547.277 1.488-22.943 14.105-22.692 14.623 1.49 2.378 9.422-.125 9.801-.344.379-.218 10.123-5.482 10.39-5.638.267-.155-11.452 6.86-10.408 7.927-.505 4.555 18.301-3.144 19.775-4.102.737-.48-9.034 4.296-9.06 6.565 3.147 5.32 13.222 3.632 14.776 2.462.777-.586-.915 3.952-.37 3.54.19-.085 2.192-2.452 2.961-4.165-.245 1.486-1.35 3.828-1.994 6.514-.646 2.687-.83 5.719-1.716 8.94-.203 1.414 6.208-2.767 5.277-12.761.549 5.199-2.024 14.92-1.351 15.425 1.347 1.01 5.07-4.842 5.447-8.799 1.106 1.999 3.426 6.171 5.423 7.131-.217-3.024.038-2.885-.86-5.857.518-3.945.59-9.406.716-15.572 5.425 10.393 7.589 14.782 6.073 22.74 1.613.673 4.19-5.554 3.982-8.76 2.072 8.907 11.014 10.243 11.156 9.984z" stroke-linecap="round" stroke-width=".962"/>
      <path stroke-linejoin="round" d="M543.884 65.612s-3.693 2.79-8.288 2.625c1.148 4.596 10.667 1.067 10.667 1.067s-4.595 5.088-7.878 5.908c1.97 1.642 10.258 1.067 11.653.247 1.394-.821 3.857-4.021 3.857-4.021s-8.535 9.847-9.766 9.765c-.164 1.148 8.945.738 10.832-.985 1.887-1.723 6.647-4.267 6.647-4.267s-12.473 8.698-13.048 8.698c4.678 1.23 14.033-1.477 20.023-5.17-8.863 5.908-9.683 6.975-14.196 8.944 4.021.903 6.072 3.611 19.694-2.297 7.796-3.693 12.226-10.422 12.226-10.422-4.84 6.483-12.473 11.98-20.924 17.397-.493.984 9.026 5.005 21.17-7.632" stroke-linecap="round" stroke-width=".962"/>
      <path d="M609.86 93.018s.82 3.857 4.102 6.811c3.283 2.954 3.529 5.826 3.529 5.826M608.22 67.91s1.149 3.446 4.513 5.661c3.447 2.216 8.618 9.19 9.028 10.422.397 1.49 2.379 12.227 2.215 13.375m-36.681-24.125c.246 1.313-4.842 7.96 2.133 15.755-6.319 7.713-6.318 8.698-6.318 8.698s3.528 1.97 9.847-3.447c10.42 12.802 6.948 18.66 6.948 18.66" stroke-linecap="round" stroke-width=".962"/>
      <path stroke-linejoin="round" d="M599.075 103.874s-.96-1.375.751-6.51c1.542 1.856 3.255 2.253 4.007 3.004.75.752 8.619 1.89 9.12 7.15" stroke-linecap="round" stroke-width=".962"/>
      <path d="M608.322 55.673c0-.386-1.585-3.786-7.496.464 3.168.193 6.297 2.009 7.496-.464z" stroke-width=".962"/>
      <path stroke-linejoin="round" d="M664.74 71.22c.29.145 17.66 3.909 23.451 2.027-7.383 9.843-22.149 3.184-22.149 3.184 7.238 2.172 7.528 2.027 9.844 4.054.724 1.882-13.318 1.013-17.95-1.593 12.884 4.054 13.173 3.764 13.608 5.356.58 2.172-21.136-.579-23.017-3.474 5.79 4.488 8.975 5.356 12.16 7.383-3.91 1.737-11.147 3.474-24.465-5.935 17.516 15.923 33.295 14.91 35.756 17.805-6.08 8.975-29.676-5.356-40.533-12.594-10.857-7.238 23.74 17.516 26.926 17.081-1.593 2.46-13.029.29-13.753-.868" stroke-width=".962"/>
      <path d="M669.23 87.868c-2.027.29-8.107.29-8.975.144" stroke-linecap="round" stroke-width=".962"/>
      <path stroke-linejoin="round" d="M538.796 62.986s13.212 6.975 18.791 6.482c-1.476 1.395-3.692 1.97-3.692 1.97 1.395.574 5.252 2.626 11.078 1.313-1.395 1.395-3.2 2.954-3.2 2.954s5.005 1.887 10.667-1.067c-1.723 2.133-2.708 3.693-2.708 3.693l3.94.246" stroke-linecap="round" stroke-width=".962"/>
    </g>
  </g>
</svg>
