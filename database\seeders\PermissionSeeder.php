<?php

namespace Database\Seeders;

use App\Models\Permission;
use Illuminate\Database\Seeder;

class PermissionSeeder extends Seeder
{
    public function run()
    {
        $permissions = [
            ['name' => 'view_dashboard', 'description' => 'View the dashboard'],
            ['name' => 'view_roles', 'description' => 'View roles list'],
            ['name' => 'edit_roles', 'description' => 'Edit roles'],
            ['name' => 'view_staff', 'description' => 'View staff list'],
            ['name' => 'edit_staff', 'description' => 'Edit staff'],
            ['name' => 'view_permissions', 'description' => 'View permissions'],
            ['name' => 'edit_permissions', 'description' => 'Edit permissions'],
            ['name' => 'view_department_trade', 'description' => 'View department (trade)'],
            ['name' => 'view_inspectors', 'description' => 'View inspectors'],
            ['name' => 'create_review_process', 'description' => 'Create review process'],
            ['name' => 'view_departmental_review', 'description' => 'View departmental review'],
            ['name' => 'view_commission_review', 'description' => 'View planning commission review'],
            ['name' => 'view_total_projects', 'description' => 'View total projects review'],
            ['name' => 'assign_permit_number', 'description' => 'Assign permit number'],
            ['name' => 'view_avg_review_time', 'description' => 'View average review time'],
            ['name' => 'view_active_inspections', 'description' => 'View active inspections'],
            ['name' => 'view_field_reports', 'description' => 'View field reports submitted'],
            ['name' => 'view_compliance_flags', 'description' => 'View compliance flags raised'],
            ['name' => 'view_photo_attachments', 'description' => 'View photo attachments'],
            ['name' => 'view_approval_time_trends', 'description' => 'View approval time trends'],
            ['name' => 'view_project_volume', 'description' => 'View project volume comparison'],
            ['name' => 'view_backlogs_analysis', 'description' => 'View backlogs analysis'],
            ['name' => 'view_efficiency_metrics', 'description' => 'View efficiency metrics'],
            ['name' => 'view_data_sync_status', 'description' => 'View data sync status'],
            ['name' => 'view_api_requests', 'description' => 'View API requests'],
            ['name' => 'view_pending_third_party', 'description' => 'View pending third-party reviews'],
            ['name' => 'view_connected_departments', 'description' => 'View connected departments'],
            ['name' => 'view_notifications', 'description' => 'View notifications'],
            ['name' => 'view_compliance', 'description' => 'View compliance list'],
            ['name' => 'view_chat', 'description' => 'View chat'],
            ['name' => 'view_email', 'description' => 'View email'],
            ['name' => 'add_rules', 'description' => 'Add rules'],
            ['name' => 'view_rules', 'description' => 'View rules list'],
            ['name' => 'manage_inspectors', 'description' => 'Manage inspectors'],
            ['name' => 'manage_builders', 'description' => 'Manage builders'],
            ['name' => 'manage_engineers', 'description' => 'Manage engineers'],
            ['name' => 'manage_subscriptions', 'description' => 'Manage subscriptions'],
        ];

        Permission::insert($permissions);
    }
}
