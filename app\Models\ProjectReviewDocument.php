<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProjectReviewDocument extends Model
{
    protected $fillable = [
        'project_id',
        'department_id',
        'review_step',
        'document_type',
        'file_path',
        'remarks',
        'uploaded_by',
        'uploaded_by_role',
        'reviewed_by'
    ];

    public function project()
    {
        return $this->belongsTo(WebsiteBuilderProject::class);
    }

    public function department()
    {
        return $this->belongsTo(McdStaffRole::class, 'department_id');
    }


public function reviewer()
{
    return $this->belongsTo(\App\Models\McdStaff::class, 'reviewed_by');
}

}
