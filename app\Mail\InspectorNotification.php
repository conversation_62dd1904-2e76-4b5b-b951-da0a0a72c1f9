<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class InspectorNotification extends Mailable
{
    use Queueable, SerializesModels;

    public $notificationData;

    public function __construct($notificationData)
    {
        $this->notificationData = $notificationData;
    }

    public function build()
    {
        return $this->subject($this->notificationData['title'])
                    ->view('emails.inspector_notification')
                    ->with([
                        'title' => $this->notificationData['title'],
                        'message' => $this->notificationData['message'],
                        'priority' => $this->notificationData['priority'],
                        'project' => $this->notificationData['project'],
                        'sent_at' => $this->notificationData['sent_at'],
                    ]);
    }
}