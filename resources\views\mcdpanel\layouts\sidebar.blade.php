<div class="main-sidebar sidebar-style-2">
    @php   
    $user = Auth::guard('staff_mcd')->user();
    $isAdmin = Auth::guard('web')->check();
    $permissions = $user && $user->role ? $user->role->permissions : [];

    function hasPermission($permissions, $key, $type = 'read') {
        // Access the global $isAdmin inside the function
        return (Auth::guard('web')->check()) || 
               (isset($permissions[$key]) && $permissions[$key][$type] == 1);
    }
@endphp

    <aside id="sidebar-wrapper">
        <div class="sidebar-brand">
            @if ($isAdmin || hasPermission($permissions, 'dashboard'))
                <a href="{{ route('dashboard') }}"> 
                    <img alt="image" src="{{ asset('assets/img/cos.png') }}" style="height: 70px;" class="header-logo" /> 
                    <span class="logo-name" style="font-size: 12px;"></span>
                </a>
            @endif
        </div>

        <ul class="sidebar-menu">
            @if ($isAdmin || hasPermission($permissions, 'dashboard'))
                <li class="dropdown {{ request()->routeIs('dashboard') ? 'active' : '' }}">
                    <a href="{{ route('dashboard') }}" class="nav-link"><i class="fas fa-th"></i><span>Dashboard</span></a>
                </li>
            @endif

            {{-- Roles Management --}}
            @if ($isAdmin)
            <li class="dropdown {{ 
                request()->routeIs('mcd-staff.create') || 
                request()->routeIs('mcd-staff.edit') || 
                request()->routeIs('roles.index') || 
                request()->routeIs('mcd-staff.index') || 
                request()->routeIs('permissions.index') ? 'active' : '' 
            }}">
                <a href="javascript:void(0)" class="menu-toggle nav-link has-dropdown"><i class="fas fa-clipboard-list"></i><span>Roles Management</span></a>
                <ul class="dropdown-menu">
                    <li><a class="nav-link {{ request()->routeIs('roles.index') ? 'active' : '' }}" href="{{ route('roles.index') }}">Roles List</a></li>
                    <li><a class="nav-link {{ request()->routeIs('mcd-staff.index') ? 'active' : '' }}" href="{{ route('mcd-staff.index') }}">Staff List</a></li>
                    <li><a class="nav-link {{ request()->routeIs('permissions.index') ? 'active' : '' }}" href="{{ route('permissions.index') }}">Permissions</a></li>
                </ul>
            </li>
            @endif

            {{-- Department Management --}}
            @if (hasPermission($permissions, 'department_trade') || hasPermission($permissions, 'inspectors'))
            <li class="dropdown {{ 
                request()->routeIs('mcd-staff-roles.index') || 
                request()->routeIs('inspectors.index') ? 'active' : '' 
            }}">
                <a href="javascript:void(0)" class="menu-toggle nav-link has-dropdown"><i class="fas fa-clipboard-list"></i><span>Department Management</span></a>
                <ul class="dropdown-menu">
                    @if (hasPermission($permissions, 'department_trade'))
                    <li><a class="nav-link {{ request()->routeIs('mcd-staff-roles.index') ? 'active' : '' }}" href="{{ route('mcd-staff-roles.index') }}">Department (Trade)</a></li>
                    @endif
                    @if (hasPermission($permissions, 'inspectors'))
                    <li><a class="nav-link {{ request()->routeIs('inspectors.index') ? 'active' : '' }}" href="{{ route('inspectors.index') }}">Inspectors</a></li>
                    @endif
                </ul>
            </li>
            @endif

            {{-- Project Management --}}
            @if (
                hasPermission($permissions, 'review_processes') || hasPermission($permissions, 'departmental_review') ||
                hasPermission($permissions, 'plannining_commission_review') || hasPermission($permissions, 'total_review_process') ||
                hasPermission($permissions, 'assign_puid') || hasPermission($permissions, 'assign_permit_number') ||
                hasPermission($permissions, 'avg_review_time')
            )
            <li class="dropdown {{ 
                request()->routeIs('myprojects.index') || 
                request()->routeIs('myprojects.non_pending') || 
                request()->routeIs('project_municipals.index') || 
                request()->routeIs('average-review-times.index') || 
                request()->routeIs('review_processes.index') || 
                request()->routeIs('deptreview.index') || 
                request()->routeIs('assign-permit.index') ||
                request()->routeIs('commissionreview') || 
                request()->routeIs('permit-numbers.index') ? 'active' : '' 
            }}">
                <a href="javascript:void(0)" class="menu-toggle nav-link has-dropdown"><i class="far fa-lightbulb"></i><span>Project Management</span></a>
                <ul class="dropdown-menu">
                    @if (hasPermission($permissions, 'review_processes'))
                    <li><a class="nav-link {{ request()->routeIs('review_processes.index') ? 'active' : '' }}" href="{{ route('review_processes.index') }}">Create Review Process</a></li>
                    @endif
                    @if (hasPermission($permissions, 'departmental_review'))
                    <li><a class="nav-link {{ request()->routeIs('deptreview.index') ? 'active' : '' }}" href="{{ route('deptreview.index') }}">Departmental Review</a></li>
                    @endif
                    @if (hasPermission($permissions, 'plannining_commission_review'))
                    <li><a class="nav-link {{ request()->routeIs('commissionreview') ? 'active' : '' }}" href="{{ route('commissionreview') }}">Planning Commission Review</a></li>
                    @endif
                    {{-- @if (hasPermission($permissions, 'total_review_process'))
                    <li><a class="nav-link {{ request()->routeIs('myprojects.non_pending') ? 'active' : '' }}" href="{{ route('myprojects.non_pending') }}">Total Projects Review</a></li>
                    @endif --}}
                    @if (hasPermission($permissions, 'assign_puid'))
                    <li><a class="nav-link {{ request()->routeIs('assign-permit-numbers.index') ? 'active' : '' }}" href="{{ route('assign-permit-numbers.index') }}">Assign PUID</a></li>
                    @endif
                    @if (hasPermission($permissions, 'assign_permit_number'))
                    <li><a class="nav-link {{ request()->routeIs('project-department-permits.index') ? 'active' : '' }}" href="{{ route('project-department-permits.index') }}">Assign Permit Number</a></li>
                    @endif
                    @if (hasPermission($permissions, 'avg_review_time'))
                    <li><a class="nav-link {{ request()->routeIs('average-review-times.index') ? 'active' : '' }}" href="{{ route('average-review-times.index') }}">Avg Review Time</a></li>
                    @endif
                </ul>
            </li>
            @endif

            {{-- Repeat below for each module --}}
            @if (hasPermission($permissions, 'active_inspections'))
            <li class="dropdown">
                <a href="javascript:void(0)" class="menu-toggle nav-link has-dropdown"><i class="fas fa-file-alt"></i><span>Physical Inspection</span></a>
                <ul class="dropdown-menu">
                    <li><a class="nav-link" href="{{ route('physicalinspection.index') }}">Active Inspections</a></li>
                    <li><a class="nav-link" href="{{ route('physicalinspection.report') }}">Field Reports Submitted</a></li>
                    <li><a class="nav-link" href="{{ route('physical-inspection.complianceflag') }}">Compliance Flags Raised</a></li>
                </ul>
            </li>
            @endif

            @if (hasPermission($permissions, 'backlogs_analysis'))
            <li class="dropdown {{ request()->routeIs('myprojects.backlogs') ? 'active' : '' }}">
                <a href="javascript:void(0)" class="menu-toggle nav-link has-dropdown"><i class="fas fa-landmark"></i><span>Municipal Performance</span></a>
                <ul class="dropdown-menu">
                    <li><a class="nav-link" href="{{ route('myprojects.backlogs') }}">Backlogs Analysis</a></li>
                </ul>
            </li>
            @endif

            @if (hasPermission($permissions, 'notification'))
            <li class="dropdown {{ request()->routeIs('notifications.index') ? 'active' : '' }}">
                <a href="javascript:void(0)" class="menu-toggle nav-link has-dropdown"><i class="fas fa-bell"></i><span>Notification</span></a>
                <ul class="dropdown-menu">
                    <li><a class="nav-link" href="{{ route('notifications.index') }}">List Of Notification</a></li>
                </ul>
            </li>
            @endif

            @if (hasPermission($permissions, 'compliance'))
            <li class="dropdown {{ request()->routeIs('compliances.create') ? 'active' : '' }}">
                <a href="javascript:void(0)" class="menu-toggle nav-link has-dropdown"><i class="fas fa-clipboard-check"></i><span>Compliance</span></a>
                <ul class="dropdown-menu">
                    @if (hasPermission($permissions, 'compliance', 'write'))
                        <li><a class="nav-link {{ request()->routeIs('compliances.create') ? 'active' : '' }}" href="{{ route('compliances.create') }}">Add Compliance</a></li>
                    @endif
                    <li><a class="nav-link" href="{{ route('compliances.index') }}">List Of Compliance</a></li>

                </ul>
            </li>
            @endif

            @if (hasPermission($permissions, 'guidelines'))
            <li class="dropdown">
                <a href="javascript:void(0)" class="menu-toggle nav-link has-dropdown"><i class="fas fa-bullhorn"></i><span>Guidelines</span></a>
                <ul class="dropdown-menu">
                    <li><a class="nav-link" href="{{ route('guidelines.index') }}">Guidelines</a></li>
                </ul>
            </li>
            @endif

            @if (hasPermission($permissions, 'communication'))
            <li class="dropdown">
                <a href="javascript:void(0)" class="menu-toggle nav-link has-dropdown"><i class="fas fa-comments"></i><span>Communication</span></a>
                <ul class="dropdown-menu">
                    <li><a class="nav-link" href="{{ route('communication.index') }}">Messages</a></li>
                </ul>
            </li>
            @endif

            @if (hasPermission($permissions, 'rules'))
            <li class="dropdown {{ request()->routeIs('rules.index') || request()->routeIs('rules.create') ? 'active' : '' }}">
                <a href="javascript:void(0)" class="menu-toggle nav-link has-dropdown"><i class="fas fa-scroll"></i><span>Rules & Regulation</span></a>
                <ul class="dropdown-menu">
                     @if (hasPermission($permissions, 'rules', 'write'))
                         <li><a class="nav-link" href="{{ route('rules.create') }}">Add Rules</a></li>
                    @endif
                    <li><a class="nav-link" href="{{ route('rules.index') }}">Rules list</a></li>
                </ul>
            </li>
            @endif

            {{-- <li class="dropdown">
                <a href="http://localhost:3001/" class="nav-link" target="_blank">
                    <i class="fas fa-file-pdf"></i>
                    <span>PDF Annotator Tool</span>
                </a>
            </li> --}}
            

        </ul>
    </aside>
</div>
<style>
/* Ensure the sidebar has a clean base style */
.main-sidebar {
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* Style for the sidebar menu */
.sidebar-menu .nav-link {
    color: #333;
    padding: 10px 15px;
    display: flex;
    align-items: center;
    transition: background-color 0.3s ease;
}
.dropdown-menu > li {
    margin-bottom: 4px; /* Adjust as needed */
}


/* Style for active parent dropdown */
.sidebar-menu .dropdown.active > .nav-link {
    background-color: #cacef5; /* Light gray for active parent */
    color: #000;
    font-weight: 500;
}

/* Style for active submenu item */
.sidebar-menu .dropdown-menu .nav-link.active {
    background-color: #828ef1; /* Slightly darker gray for active submenu */
    color: #060505;
    font-weight: bold;
}

/* Hover effect for menu and submenu items */
.sidebar-menu .nav-link:hover {
    background-color: #f5f5f5; /* Lighter gray for hover */
}

/* Dropdown menu styling */
.sidebar-menu .dropdown-menu {
    background-color: #fafafa;
    padding: 0;
    margin: 0;
    list-style: none;
}

.sidebar-menu .dropdown-menu .nav-link {
    padding-left: 30px; /* Indent submenu items */
}

/* Ensure dropdown toggle works */
.menu-toggle::after {
    content: '\f078'; /* Font Awesome chevron-down */
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    float: right;
}

/* Optional: Style for icons */
.sidebar-menu .nav-link i {
    margin-right: 10px;
}
</style>