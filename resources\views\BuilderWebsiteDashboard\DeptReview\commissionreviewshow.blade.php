@extends('mcdpanel.layouts.master')
@php
    $user = Auth::guard('staff_mcd')->user();
    $permissions = $user && $user->role ? $user->role->permissions : [];
    $isAdmin = Auth::guard('web')->check();
    $hasWritePermission = $isAdmin || (isset($permissions['plannining_commission_review']['write']) && $permissions['plannining_commission_review']['write'] == 1);
@endphp

@section('content')
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-start flex-column flex-md-row">
                        <div>
                            <h3 class="mb-1">Project Details: {{ $project->project_name }}</h3>
                            <p class="mb-0">
                                {{ $project->project_address ?? 'N/A' }}, {{ $project->city ?? '' }},
                                {{ $project->state ?? '' }} {{ $project->zip ?? '' }}
                            </p>
                            @if ($project->permitNumbers->isNotEmpty())
                                <button type="button" class="btn w-100 text-start mt-2">
                                    <strong> PUID:</strong>
                                    @foreach ($project->permitNumbers as $permit)
                                        <span class="badge bg-success me-1 my-1">{{ $permit->permit_number }}</span>
                                    @endforeach
                                </button>
                            @else
                                <button type="button" class="btn btn-outline-warning w-100 text-start mt-2">
                                    <strong><i class="bi bi-exclamation-circle me-2"></i>PUID:</strong>
                                    <span class="text-danger">Yet to Assign</span>
                                </button>
                            @endif
                            <p class="mb-0">
                                <strong>Engineer Name:</strong> {{ $project->engineer_name ?? 'N/A' }}
                            </p>
                        </div>
                        <div class="btn-group mt-3 mt-md-0">
                            {{-- <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                                data-bs-target="#assignDepartmentModal">
                                Assign Department
                            </button>    &nbsp; &nbsp; --}}
                            <a href="{{ route('commissionreview') }}" class="btn btn-primary">Back</a>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Review Process -->
                        <div class="row">
                            <div class="col-12 ">
                                <div class=" d-flex justify-content-between align-items-center">
                                    <h5 class="mb-3">Planning Commission Review Process</h5>
                                    

                                     @if ($hasWritePermission)
                                        <button type="button"
                                            class="btn btn-outline-info mb-3 px-4 py-2 fw-semibold rounded-pill shadow-sm"
                                            data-bs-toggle="modal" data-bs-target="#assignDepartmentModal">
                                            <i class="bi bi-person-plus me-2"></i> Assign Department
                                        </button>
                                    @else
                                        <button type="button"
                                            class="btn btn-outline-info mb-3 px-4 py-2 fw-semibold rounded-pill shadow-sm"
                                            onclick="showAccessDenied()">
                                            <i class="bi bi-person-plus me-2"></i> Assign Department
                                        </button>
                                    @endif


                                </div>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover" id="review-table">
                                        <thead>
                                            <tr>
                                                <th rowspan="2">Department (Trade)</th>
                                                <th rowspan="2">Review Steps</th>
                                                <th colspan="2" class="text-center dept-review">Department Review</th>
                                                <th colspan="2" class="text-center pc-review">Planning Commission Review
                                                </th>
                                                <th rowspan="2">Action (Review Documents)</th>
                                            </tr>
                                            <tr>
                                                <th class="dept-review">Dept Status</th>
                                                <th class="dept-review">Dept Remark</th>
                                                <th class="pc-review">PC Status</th>
                                                {{-- <th class="pc-review">Reviewed By</th> --}}
                                                <th class="pc-review">PC Remark</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($departments as $department)
                                                @foreach ($department['steps'] as $index => $step)
                                                    <tr data-department="{{ $department['name'] }}">
                                                        @if ($index === 0)
                                                            <td rowspan="{{ count($department['steps']) }}">
                                                                {{ $department['name'] }}
                                                            </td>
                                                        @endif
                                                        <td>{{ $step['step'] }}</td>
                                                        <td class="dept-review">
                                                            @php
                                                                $status = $step['status'] ?? 'Not Started';
                                                                $badgeClass = match ($status) {
                                                                    'Approved' => 'success',
                                                                    'Violation' => 'warning',
                                                                    'Pending' => 'info',
                                                                    'Rejected' => 'danger',
                                                                    default => 'dark',
                                                                };
                                                            @endphp
                                                            <span class="badge bg-{{ $badgeClass }}">
                                                                {{ $status }}
                                                            </span>
                                                        </td>
                                                        <td class="dept-review">{{ $step['remarks'] ?? '-' }}</td>
                                                        <td class="pc-review">
                                                            @php
                                                                $pcStatus = $step['pc_status'] ?? 'Not Started';
                                                                $pcBadgeClass = match ($pcStatus) {
                                                                    'Approved' => 'success',
                                                                    'Violation' => 'warning',
                                                                    'Pending' => 'info',
                                                                    'Rejected' => 'danger',
                                                                    default => 'dark',
                                                                };
                                                            @endphp
                                                            <span class="badge bg-{{ $pcBadgeClass }}">
                                                                {{ $pcStatus }}
                                                            </span>
                                                        </td>
                                                        {{-- <td>
                                                        
                                                        @php $departmentId = $department['id']; @endphp
                                                                <a href="{{ route('review.history.page', [
                                                                    'project_id' => $project->id,
                                                                    'department_id' => $departmentId,
                                                                    'review_step' => $step['step']
                                                                ]) }}" class="btn btn-sm btn-outline-primary">
                                                                    View
                                                                </a>
                                                            
                                                        </td> --}}
                                                        <td class="pc-review">{{ $step['pc_remarks'] ?? '-' }}</td>
                                                        <td>
                                                            <a href="{{ route('showprojectdocumentpc', ['id' => $project->id, 'department' => $department['name'], 'step_index' => $index]) }}"
                                                                class="btn btn-sm btn-outline-primary d-flex align-items-center justify-content-center gap-1 px-3 py-1 rounded-pill shadow-sm"
                                                                title="View and Review the Document">
                                                                <i class="fas fa-file-alt"></i>
                                                                <span>Review</span>
                                                            </a>

                                                            @php $departmentId = $department['id']; @endphp
    <a href="{{ route('review.history.page.pc', [
        'project_id' => $project->id,
        'department_id' => $departmentId,
        'review_step' => $step['step']
    ]) }}"
        class="btn btn-sm btn-outline-secondary d-flex align-items-center justify-content-center gap-1 px-3 py-1 rounded-pill shadow-sm"
        title="View Review History">
        <i class="fas fa-eye"></i>
        <span>View</span>
    </a>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Assign Department Modal -->
        <div class="modal fade" id="assignDepartmentModal" tabindex="-1" aria-labelledby="assignDepartmentModalLabel"
            aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="assignDepartmentModalLabel">Assign Department</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form id="assign-department-form" action="{{ route('assign.department', $project->id) }}"
                        method="POST">
                        @csrf
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="department_id" class="form-label">Select Department</label>
                                <select class="form-control" id="department_id" name="department_id" required>
    <option value="">Select a Department</option>
    @foreach ($allDepartments as $deptName)
        @php
            $role = \App\Models\McdStaffRole::where('name', $deptName)->first();
            $steps = \App\Models\ReviewProcess::whereJsonContains('department', $deptName)->first();
        @endphp
        @if ($role)
            <option
                value="{{ $role->id }}"
                data-steps="{{ json_encode($steps->review_steps ?? []) }}">
                {{ $deptName }}
            </option>
        @endif
    @endforeach
</select>

                            </div>
                            {{-- <div class="mb-3">
                                <label for="remark" class="form-label">Remark</label>
                                <textarea class="form-control" id="remark" name="remark" rows="4" placeholder="Enter your remark"></textarea>
                            </div> --}}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="submit" class="btn btn-primary">Assign</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('style')
    <style>
        .table th,
        .table td {
            vertical-align: middle;
        }

        .table-bordered {
            border: 1px solid #dee2e6;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
        }

        .badge {
            font-size: 0.8rem;
            padding: 0.3em 0.6em;
        }

        /* Make table headings larger and bold */
        .table th {
            font-size: 1.1rem;
            /* Slightly larger font size */
            font-weight: 700;
            /* Bold font weight */
        }

        /* Highlight Department Review columns (Dept Status, Dept Remark) */
        .dept-review {
            background-color: #e6ffff !important;
            /* Very light gray color */
        }

        /* Highlight Planning Commission Review columns (PC Status, PC Remark) */
        .pc-review {
            background-color: #e6ffe6 !important;
            /* Very light gray color */
        }
    </style>
@endpush

@push('script')
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        $(document).ready(function() {
            $('#assign-department-form').on('submit', function(e) {
                e.preventDefault();

                const form = $(this);
                const url = form.attr('action');
                const data = form.serialize();

                $.ajax({
                    url: url,
                    type: 'POST',
                    data: data,
                    success: function(response) {
                        // Close the modal
                        $('#assignDepartmentModal').modal('hide');

                        // Get the selected department and its steps
                        const departmentId = $('#department_id').val();
                        const departmentName = $('#department_id option:selected').text();
                        const reviewSteps = JSON.parse($('#department_id option:selected').data(
                            'steps') || '[]');

                        // Check if department already exists in the table
                        const existingDepartment = $(
                                `#review-table tbody tr[data-department="${departmentName}"]`)
                            .length > 0;

                        if (!existingDepartment && reviewSteps.length > 0) {
                            // Build the table rows for the new department
                            let rows = '';
                            reviewSteps.forEach((step, stepIndex) => {
                                const row = `
                                    <tr data-department="${departmentName}">
                                        ${stepIndex === 0 ? `<td rowspan="${reviewSteps.length}">${departmentName}</td>` : ''}
                                        <td>${step}</td>
                                        <td class="dept-review"><span class="badge bg-dark">Not Started</span></td>
                                        <td class="dept-review">-</td>
                                        <td class="pc-review"><span class="badge bg-dark">Not Started</span></td>
                                        <td class="pc-review">-</td>
                                        <td>
                                            <a href="${'{{ route('showprojectdocumentpc', ['id' => $project->id, 'department' => "' + departmentName + '", 'step_index' => "' + stepIndex + '"]) }}'}"
                                               class="btn btn-sm btn-outline-primary d-flex align-items-center justify-content-center gap-1 px-3 py-1 rounded-pill shadow-sm"
                                               title="View and Review the Document">
                                                <i class="fas fa-file-alt"></i>
                                                <span>Review</span>
                                            </a>
                                        </td>
                                    </tr>`;
                                rows += row;
                            });

                            // Append the new rows to the table
                            $('#review-table tbody').append(rows);
                        }

                        // Show success message
                        alert('Department assigned successfully.');
                    },
                    error: function(xhr) {
                        alert('Error assigning department: ' + (xhr.responseJSON?.message ||
                            'Unknown error'));
                    }
                });
            });
        });

      
    function showAccessDenied() {
        Swal.fire({
            icon: 'error',
            title: 'Access Denied',
            text: 'You do not have permission to modify the Planning Commission Review.'
        });
    }

    </script>
@endpush
