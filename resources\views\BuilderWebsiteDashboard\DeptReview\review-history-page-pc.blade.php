@extends('mcdpanel.layouts.master')

@section('content')
<div class="main-content">

    <!-- Project Details -->
                    <div class="card mb-2">
                        <div class="card-header">
                            <h2 class="card-title">Project Details</h2>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Project Name:</strong> {{ $project->project_name }}</p>
                                    <p><strong>Address:</strong> {{ $project->full_address }}</p>
                                    <p><strong>Project Status:</strong> {{ $project->status }}</p>
                                    <p><strong>Departments:</strong> {{ implode(', ', $project->department_names) }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Scope of Work:</strong> {{ $project->scope_of_work ?? 'N/A' }}</p>
                                    <p><strong>Expected Start Date:</strong> {{ $project->expected_start_date ?? 'N/A' }}</p>
                                    <p><strong>Expected End Date:</strong> {{ $project->expected_end_date ?? 'N/A' }}</p>
                                    <p><strong>Property Owner:</strong> {{ $project->property_owner_name ?? 'N/A' }}</p>
                                </div>
                            </div>
                            @if ($project->project_image)
                                <div class="mt-3">
                                    <p><strong>Project Image:</strong></p>
                                    @php $baseUrl = 'http://localhost/builderwebsite/'; @endphp
                                    <img src="{{ $baseUrl.'public/' .$project->project_image }}" alt="Project Image" class="img-fluid" style="max-width: 200px;">
                                </div>
                            @endif
                        </div>
                    </div>
    <!-- Review History -->
<div class="card mt-4">
    <div class="card-header">
        <h2 class="card-title">Review History</h2>
    </div>
    <div class="card-body">
        @if ($reviews->count())
            <div class="table-responsive">
                <table class="table table-bordered table-hover mb-0">
                    <thead class="thead-light">
                        <tr>
                            <th>Department</th>
                             <th>Reviewed By</th>
                            <th>Step</th>
                            <th>Status</th>
                           
                            <th>Remarks</th>
                            <th>Reviewed At</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($reviews as $review)
                            <tr>
                                <td>{{ $project->department_names[$review->department_id] ?? 'N/A' }}</td>
                                <td>{{ $review->reviewer->staff_name ?? 'N/A' }}</td>

                                <td>{{ ucfirst(str_replace('_', ' ', $review->review_step)) }}</td>
                                <td>
                                    @php
                                        $statusMap = [0 => 'Pending', 1 => 'Violation', 2 => 'Approved', 3 => 'Rejected'];
                                        $badgeClass = match($review->status) {
                                            1 => 'badge-warning text-dark',
                                            2 => 'badge-success',
                                            3 => 'badge-danger',
                                            default => 'badge-info',
                                        };
                                    @endphp
                                    <span class="badge {{ $badgeClass }}">
                                        {{ $statusMap[$review->status] ?? 'Unknown' }}
                                    </span>
                                </td>
                                <td>{{ $review->remarks ?? '-' }}</td>
                                <td>{{ \Carbon\Carbon::parse($review->created_at)->format('d M Y, h:i A') }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="alert alert-info mb-0">No review history found.</div>
        @endif
    </div>
</div>


    <a href="{{ url()->previous() }}" class="btn btn-secondary mt-3">Back</a>
</div>
@endsection
