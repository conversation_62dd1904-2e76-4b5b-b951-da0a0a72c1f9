const express = require('express');
const fileUpload = require('express-fileupload');
const fs = require('fs').promises;
const { PDFDocument, rgb } = require('pdf-lib');
const path = require('path');
const cors = require('cors');
const crypto = require('crypto');
const { Sequelize, DataTypes } = require('sequelize');

const app = express();
const PORT = process.env.PORT || 3001;

// Create uploads directory if it doesn't exist
const UPLOADS_DIR = path.join(__dirname, 'uploads');
fs.mkdir(UPLOADS_DIR, { recursive: true }).catch(console.error);

// Create tmp directory if it doesn't exist
const TMP_DIR = path.join(__dirname, 'tmp');
fs.mkdir(TMP_DIR, { recursive: true }).catch(console.error);

// Enable CORS for all routes with proper configuration
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? 'https://yourdomain.com' 
    : ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:5173'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Middleware
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));
app.use(fileUpload({ 
  limits: { fileSize: 500 * 1024 * 1024 },
  abortOnLimit: true,
  responseOnLimit: 'File size limit exceeded',
  createParentPath: true,
  useTempFiles: true,
  tempFileDir: path.join(__dirname, 'tmp')
}));

// MySQL connection using provided credentials
const sequelize = new Sequelize('realstateconst1', 'root', '', {
  host: '127.0.0.1',
  port: 3306,
  dialect: 'mysql',
  logging: false
});

// Annotation model
const Annotation = sequelize.define('Annotation', {
  file_id: { type: DataTypes.STRING, allowNull: false, unique: true },
  original_name: { type: DataTypes.STRING, allowNull: false },
  annotations: { type: DataTypes.TEXT, allowNull: false },
  timestamp: { type: DataTypes.BIGINT, allowNull: false }
}, {
  tableName: 'annotations',
  timestamps: false
});

// Ensure table exists
sequelize.sync();

// Test connection
sequelize.authenticate()
  .then(() => console.log('MySQL connection established!'))
  .catch(err => console.error('MySQL connection error:', err));

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Global error handler caught:', err);
  
  // Handle file size limit error
  if (err.code === 'LIMIT_FILE_SIZE') {
    return res.status(413).json({
      error: 'File size limit exceeded (max 50MB)',
      code: 'FILE_TOO_LARGE'
    });
  }
  
  // Handle other known errors
  if (err.status) {
    return res.status(err.status).json({
      error: err.message,
      code: err.code || 'UNKNOWN_ERROR'
    });
  }
  
  // Handle unknown errors
  res.status(500).json({
    error: 'Internal server error',
    code: 'INTERNAL_ERROR',
    details: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// Request validation middleware
const validateSaveRequest = (req, res, next) => {
  console.log('Validating save request...');
  console.log('Files received:', req.files ? Object.keys(req.files) : 'No files');
  console.log('Body keys:', Object.keys(req.body));
  
  try {
  const { annotations } = req.body;
  const pdfFile = req.files?.pdf;
  
  if (!annotations) {
      console.log('Missing annotations data');
    return res.status(400).json({ 
      error: 'Missing annotations data',
      code: 'MISSING_ANNOTATIONS'
    });
  }
  
  if (!pdfFile) {
      console.log('Missing PDF file');
    return res.status(400).json({ 
      error: 'Missing PDF file',
      code: 'MISSING_PDF'
    });
  }
  
  // Validate PDF file type
  if (!pdfFile.mimetype.includes('pdf')) {
      console.log('Invalid file type:', pdfFile.mimetype);
    return res.status(400).json({ 
      error: 'Invalid file type. Only PDF files are allowed.',
      code: 'INVALID_FILE_TYPE'
    });
  }
  
  try {
    const parsedAnnotations = typeof annotations === 'string' ? JSON.parse(annotations) : annotations;
    req.parsedAnnotations = parsedAnnotations;
      console.log('Annotations parsed successfully');
  } catch (e) {
      console.log('Failed to parse annotations:', e.message);
    return res.status(400).json({ 
      error: 'Invalid annotation JSON format',
      code: 'INVALID_JSON',
      details: e.message
    });
  }
  
  next();
  } catch (err) {
    console.error('Error in validation middleware:', err);
    next(err);
  }
};

// Save annotations and original PDF
app.post('/save-annotations', validateSaveRequest, async (req, res, next) => {
  console.log('Processing save-annotations request...');
  let pdfPath = null;
  
  try {
    const { parsedAnnotations } = req;
    const pdfFile = req.files?.pdf;
    
    if (!pdfFile || !parsedAnnotations) {
      console.log('Missing required data:', { hasPdf: !!pdfFile, hasAnnotations: !!parsedAnnotations });
      return res.status(400).json({
        error: 'Missing required data',
        code: 'MISSING_DATA'
      });
    }
    
    // Generate unique file ID
    const fileId = crypto.randomUUID();
    const timestamp = Date.now();
    
    console.log('Saving PDF file...');
    // Save PDF file
    pdfPath = path.join(UPLOADS_DIR, `${fileId}.pdf`);
    await pdfFile.mv(pdfPath);
    console.log('PDF file saved successfully');
    
    // Save annotation metadata to MySQL (stringify JSON)
    try {
      console.log('Saving to database...');
      await Annotation.create({
        file_id: fileId,
        original_name: pdfFile.name,
        annotations: JSON.stringify(parsedAnnotations),
        timestamp
      });
      console.log('Annotation saved to MySQL:', fileId);
    } catch (dbErr) {
      console.error('Error saving annotation to MySQL:', dbErr);
      // Clean up the PDF file since DB save failed
      if (pdfPath) {
        try {
          await fs.unlink(pdfPath);
          console.log('Cleaned up PDF file after DB error');
        } catch (unlinkErr) {
          console.error('Failed to clean up PDF file:', unlinkErr);
        }
      }
      throw dbErr; // Let the global error handler handle it
    }
    
    console.log('Successfully saved everything. Sending response...');
    return res.status(200).json({ 
      message: 'Annotations and PDF saved successfully',
      fileId,
      timestamp
    });
  } catch (err) {
    // Clean up any saved file if there was an error
    if (pdfPath) {
      try {
        await fs.unlink(pdfPath);
        console.log('Cleaned up PDF file after error');
      } catch (unlinkErr) {
        console.error('Failed to clean up PDF file:', unlinkErr);
      }
    }
    next(err); // Let the global error handler handle it
  }
});

// Download annotated PDF
app.get('/download-annotated-pdf', async (req, res) => {
  try {
    const { fileId } = req.query;
    
    if (!fileId) {
      return res.status(400).json({ 
        error: 'Missing fileId parameter',
        code: 'MISSING_FILE_ID'
      });
    }
    
    // Check if files exist
    const pdfPath = path.join(UPLOADS_DIR, `${fileId}.pdf`);
    
    try {
      await fs.access(pdfPath);
    } catch {
      return res.status(404).json({ 
        error: 'File not found',
        code: 'FILE_NOT_FOUND'
      });
    }
    
    // Load PDF and annotation metadata from MySQL
    const annotationRecord = await Annotation.findOne({ where: { file_id: fileId } });
    if (!annotationRecord) {
      return res.status(404).json({ error: 'Annotation metadata not found', code: 'ANNOTATION_NOT_FOUND' });
    }
    const annotations = JSON.parse(annotationRecord.annotations);
    const originalName = annotationRecord.original_name;
    
    console.log('Processing annotations for download:', JSON.stringify(annotations, null, 2));
    
    // Load PDF document
    const pdfBuffer = await fs.readFile(pdfPath);
    const pdfDoc = await PDFDocument.load(pdfBuffer);
    const pages = pdfDoc.getPages();
    
    // Process annotations for each page
    const imagePromises = [];
    
    Object.entries(annotations).forEach(([pageNum, objects]) => {
      const pageIndex = parseInt(pageNum) - 1;
      if (!pages[pageIndex] || !Array.isArray(objects)) return;
      
      const page = pages[pageIndex];
      const { width, height } = page.getSize();
      
      objects.forEach(obj => {
        try {
          // Convert percent coordinates to PDF coordinates
          const left = (obj.left / 100) * width;
          const top = height - ((obj.top / 100) * height); // PDF-lib origin is bottom-left
          
          // Handle different annotation types
          switch (obj.type) {
            case 'path':
              if (obj.path) {
                let pathData = obj.path;
                const strokeColor = obj.stroke ? rgb(...hexToRgb01(obj.stroke)) : rgb(0, 0, 0);
                const strokeWidth = obj.strokeWidth || 2;
                
                if (typeof pathData === 'string') {
                  // Handle SVG path string - extract coordinate pairs and draw lines
                  const coordMatches = pathData.match(/([0-9.-]+)\s+([0-9.-]+)/g);
                  if (coordMatches && coordMatches.length > 1) {
                    for (let i = 0; i < coordMatches.length - 1; i++) {
                      const [x1, y1] = coordMatches[i].split(/\s+/).map(parseFloat);
                      const [x2, y2] = coordMatches[i + 1].split(/\s+/).map(parseFloat);
                      
                      // Convert from percentage to PDF coordinates
                      const pdfX1 = (x1 / 100) * width;
                      const pdfY1 = height - ((y1 / 100) * height);
                      const pdfX2 = (x2 / 100) * width;
                      const pdfY2 = height - ((y2 / 100) * height);
                      
                      page.drawLine({
                        start: { x: pdfX1, y: pdfY1 },
                        end: { x: pdfX2, y: pdfY2 },
                        thickness: strokeWidth,
                        color: strokeColor,
                      });
                    }
                    console.log('Drawing path annotation from string:', { 
                      type: obj.type, 
                      stroke: obj.stroke, 
                      strokeWidth: obj.strokeWidth, 
                      segments: coordMatches.length - 1
                    });
                  }
                } else if (Array.isArray(pathData) && pathData.length > 0) {
                  // Handle array-based path data
                  for (let i = 0; i < pathData.length - 1; i++) {
                    const currentCmd = pathData[i];
                    const nextCmd = pathData[i + 1];
                    
                    if (Array.isArray(currentCmd) && Array.isArray(nextCmd) && 
                        currentCmd.length >= 3 && nextCmd.length >= 3) {
                      
                      // Convert coordinates from percentage to PDF coordinates
                      const x1 = (currentCmd[1] / 100) * width;
                      const y1 = height - ((currentCmd[2] / 100) * height);
                      const x2 = (nextCmd[1] / 100) * width;
                      const y2 = height - ((nextCmd[2] / 100) * height);
                      
                      // Draw line segment
                      page.drawLine({
                        start: { x: x1, y: y1 },
                        end: { x: x2, y: y2 },
                        thickness: strokeWidth,
                        color: strokeColor,
                      });
                    }
                  }
                  console.log('Drawing path annotation from array:', { 
                    type: obj.type, 
                    stroke: obj.stroke, 
                    strokeWidth: obj.strokeWidth, 
                    pathLength: pathData.length,
                    segments: pathData.length - 1
                  });
                }
              }
              break;
              
            case 'i-text':
              console.log('Drawing text annotation:', obj);
              page.drawText(obj.text || '', {
                x: left,
                y: top,
                size: obj.fontSize || 20,
                color: obj.fill ? rgb(...hexToRgb01(obj.fill)) : rgb(0, 0, 0),
                maxWidth: obj.width ? (obj.width / 100) * width : undefined,
              });
              break;
              
            case 'rect':
              console.log('Drawing rectangle annotation:', obj);
              page.drawRectangle({
                x: left,
                y: top - ((obj.height || 0) / 100) * height,
                width: obj.width ? (obj.width / 100) * width : 100,
                height: obj.height ? (obj.height / 100) * height : 50,
                color: obj.fill ? rgb(...hexToRgb01(obj.fill)) : undefined,
                borderColor: obj.stroke ? rgb(...hexToRgb01(obj.stroke)) : undefined,
                borderWidth: obj.strokeWidth || 1,
              });
              break;
              
            case 'image':
              if (obj.src) {
                const match = obj.src.match(/^data:image\/(png|jpeg);base64,(.*)$/);
                if (match) {
                  const imgBytes = Buffer.from(match[2], 'base64');
                  imagePromises.push((async () => {
                    try {
                      const img = match[1] === 'png' ? 
                        await pdfDoc.embedPng(imgBytes) : 
                        await pdfDoc.embedJpg(imgBytes);
                      page.drawImage(img, {
                        x: left,
                        y: top - ((obj.height || 0) / 100) * height,
                        width: obj.width ? (obj.width / 100) * width : img.width,
                        height: obj.height ? (obj.height / 100) * height : img.height,
                      });
                      console.log('Drawing image annotation:', obj);
                    } catch (imgError) {
                      console.error('Error processing image annotation:', imgError);
                    }
                  })());
                }
              }
              break;
              
            case 'group':
              // Remove arrow group processing
              break;
              
            default:
              console.warn('Unknown annotation type:', obj.type);
          }
        } catch (objError) {
          console.error('Error processing annotation object:', objError, obj);
        }
      });
    });
    
    // Wait for all image processing to complete
    await Promise.all(imagePromises);
    
    // Generate the final PDF
    const pdfBytes = await pdfDoc.save();
    
    // Set response headers
    const filename = `${originalName.replace(/\.[^/.]+$/, '')}-annotated.pdf`;
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Length', pdfBytes.length);
    
    res.send(Buffer.from(pdfBytes));
    
  } catch (err) {
    console.error('Error generating annotated PDF:', err);
    res.status(500).json({ 
      error: 'Internal server error while generating PDF',
      code: 'PDF_GENERATION_ERROR',
      details: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: Date.now() });
});

// Helper: Convert hex color to [r,g,b] in 0-1 range
function hexToRgb01(hex) {
  if (!hex || typeof hex !== 'string') return [0, 0, 0];
  
  hex = hex.replace('#', '');
  if (hex.length === 3) {
    hex = hex.split('').map(x => x + x).join('');
  }
  
  if (hex.length !== 6) return [0, 0, 0];
  
  const num = parseInt(hex, 16);
  if (isNaN(num)) return [0, 0, 0];
  
  return [
    ((num >> 16) & 255) / 255,
    ((num >> 8) & 255) / 255,
    (num & 255) / 255
  ];
}

// Serve PDF annotator at root
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'pdf-annotator', 'index.html'));
});

// Serve static files
app.use(express.static('public'));
app.use(express.static(path.join('public', 'pdf-annotator')));

// Handle 404
app.use((req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    code: 'NOT_FOUND'
  });
});

// Start server
const startServer = async () => {
  try {
    // Test database connection
    await sequelize.authenticate();
    console.log('Database connection established');
    
    // Sync database tables
    await sequelize.sync();
    console.log('Database tables synchronized');
    
    // Start listening
app.listen(PORT, () => {
  console.log(`Annotation backend running on http://localhost:${PORT}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`Uploads directory: ${UPLOADS_DIR}`);
});
  } catch (err) {
    console.error('Failed to start server:', err);
    process.exit(1);
  }
};

startServer();

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0); 
}); 