@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4>Roles Management > Staff List</h4>
                        {{-- <a href="{{ route('mcd-staff.create') }}" class="btn btn-primary">
                            <i class="fa fa-plus"></i> Add Staff
                        </a> --}}
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="table-1">
                                <thead>
                                    <tr>
                                        <th class="text-center">Sr No</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Roles</th>
                                        <th>Department(Trades)</th>
                                        <th>Status</th>
                                        <th class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($staff as $key => $member)
                                        <tr>
                                            <td class="text-center">{{ $key + 1 }}</td>
                                            <td>{{ $member->staff_name }}</td>
                                            <td>{{ $member->email }}</td>
                                            <td>{{ $member->role->name }}</td>
                                            <td>{{ $member->department->name ?? '-' }}</td>
                                            <td>
                                                <label class="switch">
                                                    <input type="checkbox" class="status-toggle" 
                                                           data-id="{{ $member->id }}" 
                                                           {{ $member->status ? 'checked' : '' }}>
                                                    <span class="slider round"></span>
                                                </label>
                                            </td>
                                            <td class="text-center">
                                                <a href="{{ route('mcd-staff.edit', $member->id) }}" 
                                                   class="btn btn-sm btn-primary mr-1" title="Edit">
                                                    <i class="fa fa-edit"></i>
                                                </a>
                                                <form action="{{ route('mcd-staff.destroy', $member->id) }}" 
                                                      method="POST" class="d-inline delete-form">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger delete-btn" 
                                                            title="Delete" data-name="{{ $member->staff_name }}">
                                                        <i class="fa fa-trash"></i>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="7" class="text-center">No staff found</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('style')
    <style>
        /* Toggle Switch Styling */
        .switch {
            position: relative;
            display: inline-block;
            width: 25px; /* Reduced width */
            height: 14px; /* Reduced height */
        }
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #dc3545; /* Red for inactive */
            transition: .4s;
            border-radius: 14px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 10px; /* Smaller toggle */
            width: 10px; /* Smaller toggle */
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #28a745; /* Green for active */
        }
        input:checked + .slider:before {
            transform: translateX(11px); /* Adjusted for new size */
        }
    </style>
@endpush



@push('script')
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Success Message from Session
            @if(session('success'))
                Swal.fire({
                    title: 'Success!',
                    text: '{{ session('success') }}',
                    icon: 'success',
                    showConfirmButton: false,
                    timer: 4000,
                    timerProgressBar: true,
                    position: 'top-end',
                    toast: true,
                    customClass: {
                        popup: 'swal2-popup-custom',
                        title: 'swal2-title-custom',
                        content: 'swal2-content-custom'
                    }
                });
            @endif

            // Delete Confirmation (unchanged)
            document.querySelectorAll('.delete-btn').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const form = this.closest('form');
                    const staffName = this.getAttribute('data-name');

                    Swal.fire({
                        title: 'Are you sure?',
                        text: `You are about to delete "${staffName}". This action cannot be undone!`,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#3085d6',
                        confirmButtonText: 'Yes, delete it!'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            form.submit();
                        }
                    });
                });
            });

            // Status Toggle Functionality
            document.querySelectorAll('.status-toggle').forEach(toggle => {
                toggle.addEventListener('change', function() {
                    let staffId = this.getAttribute('data-id');
                    let newStatus = this.checked ? 1 : 0;

                    let form = document.createElement('form');
                    form.method = 'POST';
                    form.action = "{{ route('mcd-staff.update.status', 'STAFF_ID_PLACEHOLDER') }}".replace('STAFF_ID_PLACEHOLDER', staffId);

                    let csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = '_token';
                    csrfInput.value = "{{ csrf_token() }}";
                    form.appendChild(csrfInput);

                    let statusInput = document.createElement('input');
                    statusInput.type = 'hidden';
                    statusInput.name = 'status';
                    statusInput.value = newStatus;
                    form.appendChild(statusInput);

                    // Debugging
                    console.log("Form action: ", form.action);
                    console.log("CSRF Token: ", csrfInput.value);
                    console.log("New Status: ", newStatus);

                    document.body.appendChild(form);
                    form.submit();
                });
            });
        });
    </script>

   
@endpush