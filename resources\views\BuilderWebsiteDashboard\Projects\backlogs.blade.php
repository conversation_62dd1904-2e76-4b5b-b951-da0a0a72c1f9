@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4> Municipal > Backlogs Analysis </h4>
                    </div>
                    <div class="card-body">
                        <!-- Trade Filter Form -->

                           

                            {{-- <form method="GET" action="{{ route('deptreview.index') }}" class="mb-3">
                            <label for="tradeFilter">Filter by Department:</label>
                            <select name="trade" id="tradeFilter" class="form-control w-25" onchange="this.form.submit()">
                                <option value="">All Departments</option>
                                @foreach($departments as $id => $name)
                                    <option value="{{ $id }}" {{ request('trade') == $id ? 'selected' : '' }}>{{ $name }}</option>
                                @endforeach
                            </select>
                        </form> --}}




                        @if ($projects->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="table-1">
                                    <thead class="bg-gray-200 text-gray-800">
                                        <tr>
                                            <th>Sr No</th>
                                            <th>Project Name</th>
                                            <th>Address</th>
                                            <th>Scope of Work</th>
                                            <th>Department(Trade)</th>
                                            <th>Date Range</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($projects as $key => $project)
                                            <tr>
                                                <td class="text-center">{{ $key + 1 }}</td>
                                                {{-- <td>{{ $project->project_name ?? 'N/A' }}</td> --}}
                                                <td>
                                                        @if ($project->project_name)
                                                            <a
                                                            href="{{ route('myprojects.show.status', ['id' => $project->id]) }}">
                                                            {{ $project->project_name }}
                                                        </a>
                                                        @else
                                                            N/A
                                                        @endif
                                                    </td>

                                                
                                                
                                                {{-- Combined Address --}}
                                                <td>
                                                    {{ $project->project_address ?? 'N/A' }},
                                                    {{ $project->city ?? '' }},
                                                    {{ $project->state ?? '' }} {{ $project->zip ?? '' }}
                                                </td>

                                                <td>{{ $project->scope_of_work ?? 'N/A' }}</td>

                                                {{-- Department Names --}}
                                                <td>
                                                    @if (!empty($project->department_names))
                                                        {{ implode(', ', $project->department_names) }}
                                                    @else
                                                        N/A
                                                    @endif
                                                </td>
                                                
                                                
                                                 {{-- Combined Date Range --}}
                                                    <td>
                                                        {{ $project->expected_start_date ? \Carbon\Carbon::parse($project->expected_start_date)->format('d M Y') : 'N/A' }}
                                                        -
                                                        {{ $project->expected_end_date ? \Carbon\Carbon::parse($project->expected_end_date)->format('d M Y') : 'N/A' }}
                                                    </td>
                                               <td>
                                                    @php
                                                        switch ($project->project_status) {
                                                            case 2:
                                                                $statusText = 'Approved';
                                                                $badgeClass = 'success'; // Green
                                                                break;
                                                            case 1:
                                                                $statusText = 'Violation';
                                                                $badgeClass = 'danger'; // Red
                                                                break;
                                                            default:
                                                                $statusText = 'Pending';
                                                                $badgeClass = 'warning'; // Yellow
                                                                break;
                                                        }
                                                    @endphp

                                                    <span class="badge bg-{{ $badgeClass }}">
                                                        {{ $statusText }}
                                                    </span>
                                                </td>

                                                
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="alert alert-info text-center">
                                <i class="fa fa-info-circle mr-2"></i>
                                No projects found.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('style')
    <style>
        .btn-sm {
            padding: 0.25rem 0.5rem;
        }
        .badge {
            font-size: 0.8rem;
            padding: 0.3em 0.6em;
        }
        .table th, .table td {
            vertical-align: middle;
        }
        .table-responsive {
            overflow-x: auto;
        }
        .form-control.w-25 {
            width: 25%;
            display: inline-block;
        }
    </style>
@endpush

@push('scripts')
    <!-- Include jQuery and Bootstrap JS (if not already in master layout) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
@endpush