<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProjectMunicipalsTable extends Migration
{
    public function up()
    {
        Schema::create('project_municipals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained('website_builder_projects')->onDelete('cascade');
            $table->foreignId('municipal_id')->constrained('municipals')->onDelete('cascade');
            $table->string('status')->nullable();
            $table->text('description')->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('project_municipals');
    }
}