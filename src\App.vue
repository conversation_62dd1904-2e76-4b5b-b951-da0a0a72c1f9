<template>
  <div class="app-container">
    <!-- Toolbar -->
    <div class="toolbar">
      <!--<div class="tool-section">
        <input type="file" 
               class="form-control" 
               accept=".pdf" 
               @change="handleFileUpload">
      </div>-->
      <div class="tool-section">
        <button 
          class="btn" 
          :class="currentTool === 'pen' ? 'btn-primary' : 'btn-outline-primary'"
          @click="setTool('pen')"
        >
          ✏️ Pen
        </button>
        <button 
          class="btn" 
          :class="currentTool === 'text' ? 'btn-primary' : 'btn-outline-primary'"
          @click="setTool('text')"
        >
          📝 Text
        </button>
        <button
          class="btn"
          :class="currentTool === 'rectangle' ? 'btn-primary' : 'btn-outline-primary'"
          @click="setTool('rectangle')"
        >
          ⬜ Rectangle
        </button>
        <button
          class="btn"
          :class="currentTool === 'arrow' ? 'btn-primary' : 'btn-outline-primary'"
          @click="setTool('arrow')"
        >
          → Arrow
        </button>
        <button class="btn" 
                :class="{ 'btn-primary': currentTool === 'signature', 'btn-outline-primary': currentTool !== 'signature' }"
                @click="openSignaturePad">
          <i class="bi bi-pen"></i> Signature
        </button>
        <div class="text-controls" v-if="currentTool === 'text'">
          <div class="text-color-picker">
            <input type="color"
                   v-model="textColor"
                   @input="updateTextColor"
                   :title="'Current text color: ' + textColor">
            <span class="text-color-preview" :style="{ backgroundColor: textColor }"></span>
          </div>
          <div class="text-size-selector">
            <select v-model="textSize" @change="updateTextSize" class="form-select">
              <option v-for="size in [12, 14, 16, 18, 20, 24, 28, 32, 36, 48, 60, 72]" :key="size" :value="size">{{ size }}px</option>
            </select>
          </div>
        </div>
        <!-- Tool Settings -->
        <div class="tool-section" v-if="currentTool === 'pen' || currentTool === 'arrow'">
          <div class="brush-thickness">
            <label>Thickness:</label>
            <input type="range" 
                   class="form-range" 
                   min="1" 
                   max="20" 
                   step="1"
                   v-model="brushThickness"
                   @input="updateBrushThickness">
            <span class="brush-size">{{ brushThickness }}px</span>
          </div>
          <div class="color-picker">
            <label>Color:</label>
            <input type="color"
                   v-model="brushColor"
                   @input="updateBrushColor"
                   :title="'Current color: ' + brushColor">
          </div>
        </div>

        <div class="tool-section" v-if="currentTool === 'text'">
          <div class="text-size">
            <label>Size:</label>
            <input type="range" 
                   class="form-range" 
                   min="8" 
                   max="72" 
                   step="2"
                   v-model="textSize"
                   @input="updateTextSize">
            <span class="text-size-label">{{ textSize }}px</span>
          </div>
          <div class="color-picker">
            <label>Color:</label>
            <input type="color"
                   v-model="textColor"
                   @input="updateTextColor"
                   :title="'Current color: ' + textColor">
          </div>
        </div>

        <!-- Arrow Direction Selector -->
        <div class="tool-section" v-if="currentTool === 'arrow'">
          <div class="arrow-directions">
            <label>Direction:</label>
            <div class="arrow-buttons">
              <button
                v-for="(symbol, direction) in arrowTypes"
                :key="direction"
                class="btn btn-sm"
                :class="currentArrowType === direction ? 'btn-primary' : 'btn-outline-secondary'"
                @click="selectArrowType(direction)"
                :title="direction"
              >
                {{ symbol }}
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="tool-section">
        <button class="btn btn-secondary" @click="undo" :disabled="!canUndo">
          <i class="bi bi-arrow-counterclockwise"></i> Undo
        </button>
        <button class="btn btn-secondary" @click="redo" :disabled="!canRedo">
          <i class="bi bi-arrow-clockwise"></i> Redo
        </button>
        <button class="btn btn-danger" @click="clearAnnotations">
          <i class="bi bi-trash"></i> Clear
        </button>
       
        <button class="btn btn-primary" @click="saveToBackend" :disabled="isSaving">
          <i class="bi bi-cloud-upload" v-if="!isSaving"></i>
          <i class="bi bi-arrow-clockwise spin" v-if="isSaving"></i>
          {{ isSaving ? 'Saving...' : 'Save to Backend' }}
        </button>
        <button class="btn btn-primary" @click="downloadEditedPdf" :disabled="isDownloading || !savedFileId">
          <i class="bi bi-cloud-download" v-if="!isDownloading"></i>
          <i class="bi bi-arrow-clockwise spin" v-if="isDownloading"></i>
          {{ isDownloading ? 'Downloading...' : 'Download Edited PDF' }}
        </button>
      </div>

      <!-- Zoom Controls -->
      <div class="tool-section">
        <button class="btn btn-outline-secondary" @click="zoomOut" :disabled="zoomLevel <= 25">
          <i class="bi bi-zoom-out"></i>
        </button>
        <span class="zoom-display">{{ zoomLevel }}%</span>
        <button class="btn btn-outline-secondary" @click="zoomIn" :disabled="zoomLevel >= 300">
          <i class="bi bi-zoom-in"></i>
        </button>
        <button class="btn btn-outline-secondary" @click="resetZoom" title="Reset to 100%">
          <i class="bi bi-aspect-ratio"></i>
        </button>
      </div>
    </div>

    <!-- Loading Message -->
    <div v-if="loadingMessage" class="loading-overlay">
      <div class="loading-content">
        <i class="bi bi-arrow-clockwise spin"></i>
        <p>{{ loadingMessage }}</p>
      </div>
    </div>

    <!-- Error Message -->
    <div v-if="showError" class="error-toast">
      <div class="error-content">
        <i class="bi bi-exclamation-triangle"></i>
        <span>{{ error }}</span>
        <button @click="showError = false" class="error-close">×</button>
      </div>
    </div>

    <!-- PDF Viewer -->
    <div class="viewer-container" ref="viewerContainer">
      <div class="pdf-container" ref="pdfContainer">
        <div v-show="!pdfLoaded" class="upload-prompt">
          <p>Upload a PDF file to begin</p>
        </div>
        <div class="pdf-content"></div>
      </div>
    </div>

    <!-- Signature Pad Modal -->
    <SignaturePad 
      v-if="showSignaturePad"
      v-model:show="showSignaturePad"
      @save="handleSignature" />
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, Teleport } from 'vue'
import SignaturePad from './components/SignaturePad.vue'
import { PDFRenderer } from './pdfRenderer'
import { AnnotationTools } from './annotationTools'

// State
const viewerContainer = ref(null)
const pdfContainer = ref(null)
const currentTool = ref(null)
const brushThickness = ref(2)
const brushColor = ref('#000000')
const textColor = ref('#000000')
const textSize = ref(20)
const currentArrowType = ref('right')
const pdfLoaded = ref(false)
const showSignaturePad = ref(false)
const currentFile = ref(null)
const isRendering = ref(false)
const canUndo = ref(false)
const canRedo = ref(false)
const zoomLevel = ref(100)
const backendUrl = 'http://localhost:8000'
const savedFileId = ref(null)
const currentProjectId = ref(null)
const currentDepartmentId = ref(null)
const currentReviewStep = ref(null)
const originalPdfUrl = ref(null)

// Loading states
const isLoading = ref(false)
const isSaving = ref(false)
const isDownloading = ref(false)
const loadingMessage = ref('')

// Error handling
const error = ref(null)
const showError = ref(false)

let resizeTimeout = null
let resizeObserver = null
let lastWidth = 0

// Initialize modules
const pdfRenderer = new PDFRenderer()
const annotationTools = new AnnotationTools()

// Arrow types for direction selector
const arrowTypes = {
  'right': '→',
  'left': '←',
  'up': '↑',
  'down': '↓',
  'up-right': '↗',
  'down-right': '↘',
  'down-left': '↙',
  'up-left': '↖'
}

// Set up state change listener
annotationTools.onStateChange(({ canUndo: newCanUndo, canRedo: newCanRedo }) => {
  canUndo.value = newCanUndo
  canRedo.value = newCanRedo
})

// Helper to get query parameter
function getQueryParam(name) {
  const url = new URL(window.location.href);
  return url.searchParams.get(name);
}

// Error handling utility
function handleError(error, context = '') {
  console.error(`Error in ${context}:`, error)
  const message = error.response?.data?.error || error.message || 'An unexpected error occurred'
  showErrorMessage(message)
}

function showErrorMessage(message) {
  error.value = message
  showError.value = true
  setTimeout(() => {
    showError.value = false
  }, 5000)
}

function showSuccessMessage(message) {
  // You could implement a success toast here
  alert(message) // Temporary - replace with proper toast
}

// Update renderPDF function
async function renderPDF(file, forceRender = false) {
  if (isRendering.value && !forceRender) return
  if (!pdfContainer.value) {
    console.error('PDF container not found')
    return
  }
  
  // Get current container width
  const containerWidth = pdfContainer.value.clientWidth || 0
  
  // Only render if width changed significantly (more than 10px) or forced
  if (!forceRender && Math.abs(containerWidth - lastWidth) < 10) return
  
  lastWidth = containerWidth
  isRendering.value = true

  try {
    // Get or create pdf-content div
    let contentDiv = pdfContainer.value.querySelector('.pdf-content')
    if (!contentDiv) {
      contentDiv = document.createElement('div')
      contentDiv.className = 'pdf-content'
      pdfContainer.value.appendChild(contentDiv)
    }
    
    // Clear existing content
    contentDiv.innerHTML = ''
    annotationTools.clearAnnotations()

    // Load and render PDF
    const arrayBuffer = await file.arrayBuffer()
    const numPages = await pdfRenderer.loadDocument(arrayBuffer)

    // Calculate scale based on container width
    const maxWidth = contentDiv.clientWidth - 40 // 40px for margins
    pdfRenderer.setScale(maxWidth)

    // Render each page
    for (let pageNum = 1; pageNum <= numPages; pageNum++) {
      const { container, canvas } = await pdfRenderer.renderPage(
        pageNum, 
        contentDiv
      )
      if (canvas && canvas instanceof HTMLCanvasElement) {
        annotationTools.addCanvas(pageNum, canvas)
      }
    }

    // Load saved annotations if any
    loadSavedAnnotations()
    // Save initial state for undo
    annotationTools._saveState()
    updateUndoRedoState()
    
    // Set pdfLoaded after successful render
    pdfLoaded.value = true
  } catch (error) {
    console.error('Error loading PDF:', error)
    pdfLoaded.value = false
    showErrorMessage('Failed to load PDF: ' + error.message)
  } finally {
    isRendering.value = false
  }
}

// Handle file upload
async function handleFileUpload(event) {
  const file = event.target.files[0]
  if (!file) return

  pdfLoaded.value = false // Reset loaded state
  currentFile.value = file
  
  // Small delay to ensure DOM is updated
  await new Promise(resolve => setTimeout(resolve, 0))
  await renderPDF(file, true)
}

// Handle resize with debounce
function handleResize(entries) {
  if (resizeTimeout) {
    clearTimeout(resizeTimeout)
  }

  resizeTimeout = setTimeout(() => {
    if (!pdfLoaded.value || !currentFile.value || !pdfContainer.value) return

    const containerWidth = pdfContainer.value.clientWidth
    if (Math.abs(containerWidth - lastWidth) >= 10) {
      renderPDF(currentFile.value)
    }
  }, 500) // Increased debounce time to 500ms
}

// Tool selection
function setTool(tool) {
  console.log('Setting tool to:', tool)
  currentTool.value = tool
  annotationTools.setActiveTool(tool)
  console.log('Annotation tools canvases count:', annotationTools.canvases.size)
  
  // Ensure brush settings are applied when switching to pen or arrow tool
  if (tool === 'pen' || tool === 'arrow') {
    annotationTools.setBrushColor(brushColor.value)
    annotationTools.setBrushThickness(brushThickness.value)
    console.log('Pen tool activated with color:', brushColor.value, 'thickness:', brushThickness.value)
  }
  // Ensure text settings are applied when switching to text tool
  if (tool === 'text') {
    annotationTools.setTextColor(textColor.value)
    annotationTools.setTextSize(textSize.value)
  }
}

// Arrow controls
function selectArrowType(direction) {
  currentArrowType.value = direction
  annotationTools.setArrowType(direction)
  console.log('Arrow type set to:', direction, arrowTypes[direction])
}

// Brush controls
function updateBrushThickness() {
  annotationTools.setBrushThickness(brushThickness.value)
}

function updateBrushColor() {
  annotationTools.setBrushColor(brushColor.value)
}

// Text controls
function updateTextColor() {
  annotationTools.setTextColor(textColor.value)
}

function updateTextSize() {
  annotationTools.setTextSize(textSize.value)
}

// Signature handling
function openSignaturePad() {
  showSignaturePad.value = true
}

function handleSignature(signatureData) {
  annotationTools.addSignature(signatureData)
  annotationTools._saveState()
  updateUndoRedoState()
}

// Save annotations
function saveAnnotations() {
  const annotations = annotationTools.saveAnnotations()
  localStorage.setItem('pdfAnnotations', JSON.stringify(annotations))
  alert('Annotations saved!')
  updateUndoRedoState()
}

// Load saved annotations
function loadSavedAnnotations() {
  const saved = localStorage.getItem('pdfAnnotations')
  if (saved) {
    try {
      const annotations = JSON.parse(saved)
      annotationTools.loadAnnotations(annotations)
    } catch (error) {
      console.error('Failed to load annotations:', error)
    }
  }
}

function updateUndoRedoState() {
  const state = {
    canUndo: annotationTools.undoStack.length > 1,
    canRedo: annotationTools.redoStack.length > 0
  }
  canUndo.value = state.canUndo
  canRedo.value = state.canRedo
}

function undo() {
  if (annotationTools.undo()) {
    updateUndoRedoState()
  }
}

function redo() {
  if (annotationTools.redo()) {
    updateUndoRedoState()
  }
}

function clearAnnotations() {
  annotationTools.clearAnnotations()
  updateUndoRedoState()
}

// Zoom functions
function zoomIn() {
  const newZoom = Math.min(zoomLevel.value + 25, 300)
  setZoom(newZoom)
}

function zoomOut() {
  const newZoom = Math.max(zoomLevel.value - 25, 25)
  setZoom(newZoom)
}

function resetZoom() {
  setZoom(100)
}

function setZoom(level) {
  zoomLevel.value = level
  annotationTools.setZoom(level)
}

// Keyboard shortcut handler for zoom
function handleKeydown(event) {
  if (event.ctrlKey || event.metaKey) {
    if (event.key === '=' || event.key === '+') {
      event.preventDefault()
      zoomIn()
    } else if (event.key === '-') {
      event.preventDefault()
      zoomOut()
    } else if (event.key === '0') {
      event.preventDefault()
      resetZoom()
    }
  }
}

// Update onMounted to only handle file loading
onMounted(async () => {
  if (viewerContainer.value) {
    resizeObserver = new ResizeObserver(handleResize)
    resizeObserver.observe(viewerContainer.value)
  }

  // Add keyboard shortcuts for zoom
  document.addEventListener('keydown', handleKeydown)

  // Extract URL parameters
  const fileUrl = getQueryParam('file')
  const projectId = getQueryParam('projectId')
  const departmentId = getQueryParam('departmentId')
  const reviewStep = getQueryParam('reviewStep')

  // Store parameters for later use
  currentProjectId.value = projectId
  currentDepartmentId.value = departmentId
  currentReviewStep.value = reviewStep
  originalPdfUrl.value = fileUrl

  console.log('URL Parameters extracted:', {
    projectId: currentProjectId.value,
    departmentId: currentDepartmentId.value,
    reviewStep: currentReviewStep.value,
    fileUrl: originalPdfUrl.value
  })

  // If file URL is provided, load it
  if (fileUrl) {
    try {
      const response = await fetch(fileUrl)
      const blob = await response.blob()
      const file = new File([blob], 'document.pdf', { type: 'application/pdf' })
      currentFile.value = file
      await renderPDF(file, true)
    } catch (err) {
      console.error('Error loading PDF from URL:', err)
      showErrorMessage('Failed to load PDF from URL')
    }
  }
})

onBeforeUnmount(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
  // Clean up keyboard event listener
  document.removeEventListener('keydown', handleKeydown)
  // Clean up state change listener
  annotationTools.offStateChange(updateUndoRedoState)
})

// Enhanced saveToBackend function with proper parameter validation and PDF generation
async function saveToBackend() {
  if (!currentFile.value) {
    showErrorMessage('No PDF loaded.')
    return
  }

  // Validate required parameters
  if (!currentProjectId.value) {
    showErrorMessage('Project ID is missing. Please reload the page with proper URL parameters.')
    console.error('Missing project ID:', {
      currentProjectId: currentProjectId.value,
      urlParams: window.location.search
    })
    return
  }

  console.log('Validation passed. Parameters:', {
    projectId: currentProjectId.value,
    departmentId: currentDepartmentId.value,
    reviewStep: currentReviewStep.value,
    originalPdfUrl: originalPdfUrl.value
  })

  if (isSaving.value) return // Prevent double submission

  isSaving.value = true
  loadingMessage.value = 'Generating annotated PDF and saving to backend...'

  try {
    // Get annotations data
    const annotations = annotationTools.saveAnnotations()
    console.log('Annotations to send:', JSON.stringify(annotations, null, 2))

    // Generate annotated PDF with annotations burned in
    const annotatedPdfBlob = await generateAnnotatedPDF()

    // Create FormData with all required fields
    const formData = new FormData()
    formData.append('annotated_pdf', annotatedPdfBlob, `annotated_${Date.now()}.pdf`)
    formData.append('project_id', currentProjectId.value.toString())
    formData.append('annotations', JSON.stringify(annotations))

    // Add optional parameters if available
    if (currentDepartmentId.value) {
      formData.append('department_id', currentDepartmentId.value.toString())
    }
    if (currentReviewStep.value) {
      formData.append('review_step', currentReviewStep.value.toString())
    }
    if (originalPdfUrl.value) {
      formData.append('original_pdf_path', originalPdfUrl.value)
    }
    formData.append('document_type', 'annotated_review')

    // Simple URL parameter extraction - if available use them, if not send empty string
    let finalUserId = '';
    let finalUserRole = '';
    let finalUserName = '';
    let finalReviewStep = '';

    // Try to get user parameters from URL
    const currentUrl = window.location.href;
    console.log('🔍 Current URL:', currentUrl);

    // Extract all URL parameters for debugging
    const urlParams = new URLSearchParams(window.location.search);
    console.log('🔍 All URL parameters:');
    for (let [key, value] of urlParams.entries()) {
      console.log(`  ${key}: ${value}`);
    }

    const userIdMatch = currentUrl.match(/[?&]userId=([^&]*)/);
    const userRoleMatch = currentUrl.match(/[?&]userRole=([^&]*)/);
    const userNameMatch = currentUrl.match(/[?&]userName=([^&]*)/);
    const reviewStepMatch = currentUrl.match(/[?&]reviewStep=([^&]*)/);

    console.log('🔍 Regex matches:');
    console.log('  userIdMatch:', userIdMatch);
    console.log('  userRoleMatch:', userRoleMatch);
    console.log('  userNameMatch:', userNameMatch);
    console.log('  reviewStepMatch:', reviewStepMatch);

    if (userIdMatch) {
      finalUserId = decodeURIComponent(userIdMatch[1]);
      console.log('✅ Found userId:', finalUserId);
    } else {
      console.log('❌ userId not found in URL');
    }

    if (userRoleMatch) {
      finalUserRole = decodeURIComponent(userRoleMatch[1]);
      console.log('✅ Found userRole:', finalUserRole);
    } else {
      console.log('❌ userRole not found in URL');
    }

    if (userNameMatch) {
      finalUserName = decodeURIComponent(userNameMatch[1]);
      console.log('✅ Found userName:', finalUserName);
    } else {
      console.log('❌ userName not found in URL');
    }

    if (reviewStepMatch) {
      finalReviewStep = decodeURIComponent(reviewStepMatch[1]);
      console.log('✅ Found reviewStep:', finalReviewStep);
    } else {
      console.log('❌ reviewStep not found in URL');
      // Try alternative parameter names
      const reviewStepAltMatch = currentUrl.match(/[?&]review_step=([^&]*)/);
      if (reviewStepAltMatch) {
        finalReviewStep = decodeURIComponent(reviewStepAltMatch[1]);
        console.log('✅ Found review_step (alternative):', finalReviewStep);
      }
    }

    console.log('📤 Final values being sent:');
    console.log('  userId:', finalUserId);
    console.log('  userRole:', finalUserRole);
    console.log('  userName:', finalUserName);
    console.log('  reviewStep:', finalReviewStep);

    // Add user information to FormData
    formData.append('user_id', finalUserId);
    formData.append('user_role', finalUserRole);
    formData.append('user_name', finalUserName);
    // NOTE: review_step is already added above at line 521 - removed duplicate to prevent overwrite

    // Debug log FormData contents
    console.log('FormData contents:')
    for (let [key, value] of formData.entries()) {
      console.log(`${key}:`, value)
    }

    console.log('Sending request to:', `${backendUrl}/api/annotated-pdfs`)
    const response = await fetch(`${backendUrl}/api/annotated-pdfs`, {
      method: 'POST',
      body: formData,
      headers: {
        'Accept': 'application/json'
        // Note: Don't set Content-Type for FormData, let browser set it with boundary
      }
    })
    
    console.log('Response status:', response.status)

    let data
    try {
      data = await response.json()
      console.log('Parsed response data:', data)
    } catch (parseErr) {
      console.error('Failed to parse JSON response:', parseErr)
      throw new Error('Invalid JSON response from server')
    }

    if (!response.ok) {
      throw new Error(data?.message || `HTTP ${response.status}: ${response.statusText}`)
    }

    if (data?.success) {
      savedFileId.value = data.data?.id || data.fileId
      showSuccessMessage('Annotations and PDF saved to backend successfully!')
      console.log('Save successful:', data)
    } else {
      throw new Error(data?.message || 'Unknown error occurred')
    }
    
  } catch (err) {
    console.error('Error in saveToBackend:', err)
    if (err instanceof TypeError && err.message.includes('Failed to fetch')) {
      handleError(new Error('Could not connect to the server. Please check your connection.'), 'saveToBackend')
    } else {
    handleError(err, 'saveToBackend')
    }
  } finally {
    isSaving.value = false
    loadingMessage.value = ''
  }
}

async function downloadEditedPdf() {
  if (!savedFileId.value) {
    showErrorMessage('Please save to backend first.')
    return
  }
  
  if (isDownloading.value) return // Prevent double submission
  
  isDownloading.value = true
  loadingMessage.value = 'Generating annotated PDF...'
  
  try {
    const response = await fetch(`${backendUrl}/api/download-annotated-pdf?fileId=${encodeURIComponent(savedFileId.value)}`)
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error || `HTTP ${response.status}: Failed to download PDF`)
    }
    
    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    
    // Get filename from response header or use default
    const contentDisposition = response.headers.get('content-disposition')
    let filename = 'annotated.pdf'
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/)
      if (filenameMatch) {
        filename = filenameMatch[1]
      }
    }
    
    a.download = filename
    document.body.appendChild(a)
    a.click()
    a.remove()
    window.URL.revokeObjectURL(url)
    
    showSuccessMessage('PDF downloaded successfully!')
    
  } catch (err) {
    handleError(err, 'downloadEditedPdf')
  } finally {
    isDownloading.value = false
    loadingMessage.value = ''
  }
}

// Generate annotated PDF with annotations burned into the document
async function generateAnnotatedPDF() {
  try {
    // Import PDF-lib for PDF manipulation
    const { PDFDocument, rgb } = await import('pdf-lib')

    // Load the original PDF
    const originalPdfBytes = await currentFile.value.arrayBuffer()
    const pdfDoc = await PDFDocument.load(originalPdfBytes)

    // Get all annotation canvases
    const canvases = annotationTools.getCanvases()

    // Process each page
    for (let pageIndex = 0; pageIndex < canvases.length; pageIndex++) {
      const canvas = canvases[pageIndex]
      if (!canvas) continue

      const page = pdfDoc.getPage(pageIndex)
      const { width, height } = page.getSize()

      // Get canvas as image data
      const canvasDataUrl = canvas.toDataURL('image/png')

      // Convert data URL to bytes
      const response = await fetch(canvasDataUrl)
      const imageBytes = await response.arrayBuffer()

      // Embed the image in the PDF
      const image = await pdfDoc.embedPng(imageBytes)

      // Draw the annotations on the page
      page.drawImage(image, {
        x: 0,
        y: 0,
        width: width,
        height: height,
        opacity: 1.0
      })
    }

    // Save the modified PDF
    const pdfBytes = await pdfDoc.save()

    // Return as Blob
    return new Blob([pdfBytes], { type: 'application/pdf' })

  } catch (error) {
    console.error('Error generating annotated PDF:', error)

    // Fallback: return original file if PDF generation fails
    console.warn('Falling back to original PDF due to generation error')
    return currentFile.value
  }
}
</script>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
}

.toolbar {
  padding: 0.5rem;
  border-bottom: 1px solid #ddd;
  display: flex;
  gap: 0.5rem;
  background: #f8f9fa;
  min-height: 38px;
}

.tool-section {
  display: flex;
  gap: 0.25rem;
}

button.btn, .toolbar .btn {
  font-size: 0.9rem;
  padding: 0.25rem 0.5rem;
  min-width: 32px;
  min-height: 32px;
  line-height: 1.2;
}

.viewer-container {
  flex: 1;
  overflow: auto;
  padding: 1rem;
  background: #f0f0f0;
}

.pdf-container {
  position: relative;
  min-height: 400px;
  background: white;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
  max-width: 1200px;
}

.upload-prompt {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #666;
}

.pdf-content {
  min-height: 100%;
  width: 100%;
}

.pdf-content canvas {
  margin: 0 auto;
  display: block;
}

.brush-thickness {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0 0.25rem;
  min-width: 100px;
}

.form-range {
  flex: 1;
  height: 1.25rem;
}

.brush-size {
  font-size: 0.75rem;
  color: #666;
  min-width: 2.5rem;
}

.text-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0 0.25rem;
}

.text-color-picker {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.text-color-picker input[type="color"] {
  width: 22px;
  height: 22px;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}

.text-color-preview {
  width: 14px;
  height: 14px;
  border-radius: 3px;
  border: 1.5px solid #ddd;
}

.text-size-selector .form-select {
  min-width: 48px;
  font-size: 0.9rem;
  padding: 0.1rem 0.3rem;
  height: 28px;
}

/* Loading and Error States */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.loading-content i {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #007bff;
}

.loading-content p {
  margin: 0;
  font-size: 1.1rem;
  color: #333;
}

.error-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1001;
  max-width: 400px;
}

.error-content {
  background: #dc3545;
  color: white;
  padding: 1rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 20px rgba(220, 53, 69, 0.3);
}

.error-content i {
  font-size: 1.2rem;
}

.error-close {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  margin-left: auto;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-close:hover {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

/* Spinning animation */
.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Zoom Controls */
.zoom-display {
  display: inline-block;
  min-width: 50px;
  text-align: center;
  font-weight: 500;
  color: #495057;
  margin: 0 8px;
  padding: 6px 8px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  font-size: 0.875rem;
}

.tool-section .btn {
  margin-right: 5px;
}

.tool-section .btn:last-child {
  margin-right: 0;
}

/* PDF Page Zoom Transitions */
.pdf-page {
  transition: transform 0.2s ease-in-out, margin-bottom 0.2s ease-in-out;
}

/* Ensure PDF container can handle zoomed content */
.pdf-container {
  overflow: auto;
  max-height: 80vh;
}

/* Arrow Direction Selector */
.arrow-directions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.arrow-directions label {
  font-weight: 500;
  color: #495057;
  margin-bottom: 4px;
}

.arrow-buttons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 4px;
  max-width: 120px;
}

.arrow-buttons .btn {
  padding: 4px 8px;
  font-size: 16px;
  min-width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
}
</style>