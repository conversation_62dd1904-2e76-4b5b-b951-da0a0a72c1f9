@extends('mcdpanel.layouts.master')
@php
    $user = Auth::guard('staff_mcd')->user();
    $permissions = $user && $user->role ? $user->role->permissions : [];
    $isAdmin = Auth::guard('web')->check();
    $hasWritePermission = $isAdmin || (isset($permissions['compliance']['write']) && $permissions['compliance']['write'] == 1);
@endphp

@section('content')
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4>Compliance > List of Project Compliances</h4>
                        @if ($hasWritePermission)
                        <a href="{{ route('compliances.create') }}" class="btn btn-primary btn-sm">
                            <i class="fa fa-plus"></i> Add Compliance
                        </a>
                        @else
                        
                                <button type="button" class="btn btn-light btn-sm" onclick="showAccessDeniedNotification()">
                                    <i class="fas fa-plus-circle"></i> Add Compliance
                                </button>
                            @endif
                    </div>
                    <div class="card-body">
                        @if ($compliances->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="table-1">
                                    <thead>
                                        <tr>
                                            <th>Sr No</th>
                                            <th>Project Name</th>
                                            <th>Compliance Notes</th>
                                            <th>Created At</th>
                                            <th class="text-center">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($compliances as $key => $compliance)
                                            <tr>
                                                <td class="text-center">{{ $key + 1 }}</td>
                                                <td>{{ $compliance->project->project_name ?? 'N/A' }}</td>
                                                <td>{{ $compliance->compliance_notes }}</td>
                                                <td>{{ $compliance->created_at->format('Y-m-d') }}</td>
                                                <td class="text-center">
                                                    @if ($hasWritePermission)
                                                        <a href="{{ route('compliances.edit', $compliance->id) }}"
                                                            class="btn btn-sm btn-primary mr-1" title="Edit">
                                                            <i class="fa fa-edit"></i>
                                                        </a>
                                                    @else
                                                        <button type="button" class="btn btn-light btn-sm" onclick="showAccessDeniedNotification()">
                                                           <i class="fa fa-edit"></i>
                                                        </button>
                                                    @endif  
                                                    @if ($hasWritePermission)  
                                                            <form action="{{ route('compliances.destroy', $compliance->id) }}"
                                                            method="POST" class="d-inline delete-form">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="btn btn-sm btn-danger delete-btn"
                                                                    title="Delete"
                                                                    data-name="{{ optional($compliance->project)->project_name }}">
                                                                <i class="fa fa-trash"></i>
                                                            </button>
                                                        </form>

                                                    @else
                                                        <button type="button" class="btn btn-danger btn-sm" onclick="showAccessDeniedNotification()" title="Delete Notification">
                                                                <i class="fas fa-trash"></i>
                                                        </button>
                                                    @endif    

                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="alert alert-info text-center">
                                <i class="fa fa-info-circle mr-2"></i>
                                No compliances found.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('style')
    <style>
        .btn-sm {
            padding: 0.25rem 0.5rem;
        }

        .table th,
        .table td {
            vertical-align: middle;
        }

        .alert {
            background-color: #e7f1ff;
            border: 1px solid #b8daff;
            color: #004085;
            padding: 0.75rem 1.25rem;
            border-radius: 0.25rem;
            margin-bottom: 1rem;
        }
    </style>
@endpush

@push('script')
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            @if (session('success'))
                Swal.fire({
                    title: 'Success!',
                    text: '{{ session('success') }}',
                    icon: 'success',
                    showConfirmButton: false,
                    timer: 4000,
                    toast: true,
                    position: 'top-end'
                });
            @endif

            // Delete Confirmation
            document.querySelectorAll('.delete-btn').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const form = this.closest('form');
                    const projectName = this.getAttribute('data-name');

                    Swal.fire({
                        title: 'Are you sure?',
                        text: `You are about to delete the compliance for "${projectName}". This action cannot be undone!`,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#3085d6',
                        confirmButtonText: 'Yes, delete it!',
                        cancelButtonText: 'Cancel'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            form.submit();
                        }
                    });
                });
            });
        });

        function showAccessDeniedNotification() {
        Swal.fire({
            icon: 'error',
            title: 'Access Denied',
            text: 'You do not have permission to modify the Compliances.'
        });
    }
    </script>
@endpush
