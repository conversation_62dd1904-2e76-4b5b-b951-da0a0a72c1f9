@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4>Edit Review Process</h4>
                        <a href="{{ route('review_processes.index') }}" class="btn btn-primary">Back to List</a>
                    </div>
                    <div class="card-body">
                        @if ($errors->any())
                            <div class="alert alert-danger">
                                <ul>
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        <form action="{{ route('review_processes.update', $reviewProcess->id) }}" method="POST">
                            @csrf
                            @method('PUT')

                            <div class="mb-3">
                                <label class="form-label d-block">Choose Level of Review <span class="text-danger">*</span></label>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="review_level" id="review_level_departmental" value="Department" {{ $reviewProcess->review_level == 'Department' ? 'checked' : '' }} required style="box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);">
                                    <label class="form-check-label" for="review_level_departmental">Department</label>
                                </div>
                                {{-- <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="review_level" id="review_level_mc" value="Planning Commission" {{ $reviewProcess->review_level == 'Planning Commission' ? 'checked' : '' }} required style="box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);">
                                    <label class="form-check-label" for="review_level_mc">Planning Commission</label>
                                </div> --}}
                            </div>

                            <div class="mb-3">
                                <label for="department" class="form-label">Departments (Trade) <span class="text-danger">*</span></label>
                                <div class="d-flex flex-wrap gap-3">
                                    @foreach ($trades as $trade)
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="checkbox" name="department[]" id="department_{{ $trade->id }}" value="{{ $trade->name }}" {{ in_array($trade->name, old('department', $reviewProcess->department ?? [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="department_{{ $trade->id }}">{{ $trade->name }}</label>
                                        </div>
                                    @endforeach
                                </div>
                                <small class="form-text text-muted">Select one or more departments</small>
                            </div>

                            <div class="mb-3">
                                {{-- <label class="form-label">Review Steps</label> --}}
                                <div id="review-steps">
                                    @foreach ($reviewProcess->review_steps as $index => $step)
                                        <div class="review-step-container mb-2">
                                            <label class="form-label font-weight-bold">Review Steps {{ $index + 1 }}<span class="text-red-500">*</span></label>
                                            <div class="d-flex align-items-center">
                                                <input type="text" name="review_steps[]" class="form-control" value="{{ $step }}" placeholder="Review Step {{ $index + 1 }}" required>
                                                <button type="button" class="btn btn-primary btn-sm add-step mx-2">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                                <button type="button" class="btn btn-danger btn-sm delete-step {{ count($reviewProcess->review_steps) == 1 && $index == 0 ? 'disabled' : '' }}">
                                                    <i class="fas fa-minus"></i>
                                                </button>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">Update</button>
                                <a href="{{ route('review_processes.index') }}" class="btn btn-secondary">Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('style')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <style>
        .form-check-inline {
            margin-right: 1rem;
        }
        .form-check-input {
            margin-top: 0.25rem;
        }
        .form-check-label {
            margin-left: 0.5rem;
        }
    </style>
@endpush

@push('script')
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        $(document).ready(function() {
            function updateStepHeadingsAndPlaceholders() {
                $('.review-step-container').each(function(index) {
                    const stepNumber = index + 1;
                    $(this).find('label').text(`Review Steps ${stepNumber}`);
                    $(this).find('input[name="review_steps[]"]').attr('placeholder', `Review Step ${stepNumber}`);
                });
            }

            updateStepHeadingsAndPlaceholders();

            $(document).on('click', '.add-step', function() {
                const stepCount = $('.review-step-container').length + 1;
                let newStep = `
                    <div class="review-step-container mb-2">
                        <label class="form-label font-weight-bold">Review Steps ${stepCount}</label>
                        <div class="d-flex align-items-center">
                            <input type="text" name="review_steps[]" class="form-control" placeholder="Review Step ${stepCount}" required>
                            <button type="button" class="btn btn-primary btn-sm add-step mx-2">
                                <i class="fas fa-plus"></i>
                            </button>
                            <button type="button" class="btn btn-danger btn-sm delete-step">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>`;
                $('#review-steps').append(newStep);
                updateStepHeadingsAndPlaceholders();
                updateDeleteButtons();
            });

            $(document).on('click', '.delete-step', function() {
                if ($('.review-step-container').length > 1) {
                    $(this).closest('.review-step-container').remove();
                    updateStepHeadingsAndPlaceholders();
                    updateDeleteButtons();
                }
            });

            function updateDeleteButtons() {
                $('.delete-step').prop('disabled', $('.review-step-container').length === 1);
            }

            @if(session('success'))
                Swal.fire({
                    title: 'Success!',
                    text: '{{ session('success') }}',
                    icon: 'success',
                    showConfirmButton: false,
                    timer: 4000,
                    timerProgressBar: true,
                    position: 'top-end',
                    toast: true,
                    customClass: {
                        popup: 'swal2-popup-custom',
                        title: 'swal2-title-custom',
                        content: 'swal2-content-custom'
                    }
                });
            @endif
        });
    </script>
@endpush