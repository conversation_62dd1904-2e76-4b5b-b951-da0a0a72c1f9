@extends('mcdpanel.layouts.master')

@section('content')
<div class="main-content">
    <section class="section">
        <div class="section-body">
            <div class="row">
                <div class="col-12 col-md-12 col-lg-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4>Municipal > Edit</h4>
                        </div>

                        <form action="{{ route('Municipals.update', $municipal->id) }}" method="POST">
                            @csrf
                            @method('PUT')
                            <div class="card-body">
                                <!-- Municipal Details -->
                                <h5 class="section-title">🏛️ Municipal Details</h5>
                                <hr>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Title<span class="text-danger">*</span></label>
                                            <input type="text" name="title" id="title" class="form-control @error('title') is-invalid @enderror" value="{{ old('title', $municipal->title) }}" required>
                                            @error('title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Location<span class="text-danger">*</span></label>
                                            <input type="text" name="location" id="location" class="form-control @error('location') is-invalid @enderror" value="{{ old('location', $municipal->location) }}" required>
                                            @error('location')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label>Description<span class="text-danger">*</span></label>
                                            <textarea name="description" id="description" class="form-control @error('description') is-invalid @enderror" required>{{ old('description', $municipal->description) }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Status and Priority -->
                                <h5 class="section-title">📊 Status & Priority</h5>
                                <hr>
                                <div class="row">
                                    <input type="hidden" name="status" value="pending">


                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Priority<span class="text-danger">*</span></label>
                                            <select name="priority" id="priority" class="form-control @error('priority') is-invalid @enderror" required>
                                                <option value="low" {{ old('priority', $municipal->priority) == 'low' ? 'selected' : '' }}>Low</option>
                                                <option value="medium" {{ old('priority', $municipal->priority) == 'medium' ? 'selected' : '' }}>Medium</option>
                                                <option value="high" {{ old('priority', $municipal->priority) == 'high' ? 'selected' : '' }}>High</option>
                                            </select>
                                            @error('priority')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Dates and Assignment -->
                                <h5 class="section-title">📅 Dates & Assignment</h5>
                                <hr>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Reported Date<span class="text-danger">*</span></label>
                                            <input type="date" name="reported_date" id="reported_date" class="form-control @error('reported_date') is-invalid @enderror" value="{{ old('reported_date', $municipal->reported_date) }}" required>
                                            @error('reported_date')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Resolution Date</label>
                                            <input type="date" name="resolution_date" id="resolution_date" class="form-control @error('resolution_date') is-invalid @enderror" value="{{ old('resolution_date', $municipal->resolution_date) }}">
                                            @error('resolution_date')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Assigned To</label>
                                            <input type="text" name="assigned_to" id="assigned_to" class="form-control @error('assigned_to') is-invalid @enderror" value="{{ old('assigned_to', $municipal->assigned_to) }}">
                                            @error('assigned_to')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Budget</label>
                                            <input type="number" name="budget" id="budget" class="form-control @error('budget') is-invalid @enderror" value="{{ old('budget', $municipal->budget) }}" step="0.01">
                                            @error('budget')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="card-footer text-right">
                                <button type="submit" class="btn btn-primary mr-1">Update Municipal</button>
                                <a href="{{ route('Municipals.index') }}" class="btn btn-secondary">Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Custom Styling -->
    <style>
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
    </style>
@endsection

@push('script')
<script>
    // Title Validation
    document.getElementById('title').addEventListener('input', function(event) {
        this.value = this.value.replace(/[0-9]/g, '');
    });

    // Budget Validation
    document.getElementById('budget').addEventListener('input', function() {
        this.value = this.value.replace(/[^0-9.]/g, '');
        const parts = this.value.split('.');
        if (parts.length > 2) this.value = parts[0] + '.' + parts.slice(1).join('');
        if (parts[1] && parts[1].length > 2) this.value = parts[0] + '.' + parts[1].substring(0, 2);
    });

    // Assigned To Validation (assuming it should be text only)
    document.getElementById('assigned_to').addEventListener('input', function(event) {
        this.value = this.value.replace(/[0-9]/g, '');
    });
</script>
@endpush