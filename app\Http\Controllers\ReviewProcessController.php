<?php

namespace App\Http\Controllers;

use App\Models\ReviewProcess;
use Illuminate\Http\Request;
use App\Models\McdStaffRole;
use Illuminate\Validation\Rule;

use Illuminate\Support\Facades\Validator;

class ReviewProcessController extends Controller
{
    // public function index()
    // {
    //     $reviewProcesses = ReviewProcess::all();
    //     return view('BuilderWebsiteDashboard.ReviewProcess.index', compact('reviewProcesses'));
    // }
    // public function index(Request $request)
    // {

    //     $department = $request->query('department');
    //     $query = ReviewProcess::query();

    //     if ($department) {
    //         $query->whereRaw('JSON_CONTAINS(department, ?)', [json_encode($department)]);
    //     }

    //     $reviewProcesses = $query->get();
    //     return view('BuilderWebsiteDashboard.ReviewProcess.index', compact('reviewProcesses'));
    // }
    public function index(Request $request)
{
    //dd("hello");
    $reviewLevel = $request->query('review_level', 'Department'); // Default to 'Department'
    $query = ReviewProcess::query();

    if ($reviewLevel) {
        $query->where('review_level', $reviewLevel);
    }

    $reviewProcesses = $query->get();
    return view('BuilderWebsiteDashboard.ReviewProcess.index', compact('reviewProcesses', 'reviewLevel'));
}

    public function create()
    {
            $trades = McdStaffRole::all(); 

        return view('BuilderWebsiteDashboard.ReviewProcess.create', compact('trades'));
    }

    public function store(Request $request)
    {
    $validDepartments = McdStaffRole::pluck('name')->toArray(); 

        $validator = Validator::make($request->all(), [
            'review_level' => 'required|in:Department,Planning Commission',
            'department' => 'required|array|min:1',
    'department.*' => ['required', Rule::in($validDepartments)],
            'review_steps' => 'required|array|min:1',
            'review_steps.*' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        ReviewProcess::create([
            'review_level' => $request->review_level,
            'department' => $request->department,
            'review_steps' => $request->review_steps,
        ]);

        return redirect()->route('review_processes.index')->with('success', 'Review process created successfully!');
    }

    public function edit(ReviewProcess $reviewProcess)
    {
        $trades = McdStaffRole::all();
        return view('BuilderWebsiteDashboard.ReviewProcess.edit', compact('reviewProcess', 'trades'));
    }

    public function update(Request $request, ReviewProcess $reviewProcess)
    {
            $validDepartments = McdStaffRole::pluck('name')->toArray(); 

        $validator = Validator::make($request->all(), [
            'review_level' => 'required|in:Department,Planning Commission',
            'department' => 'required|array|min:1',
    'department.*' => ['required', Rule::in($validDepartments)],
            'review_steps' => 'required|array|min:1',
            'review_steps.*' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $reviewProcess->update([
            'review_level' => $request->review_level,
            'department' => $request->department,
            'review_steps' => $request->review_steps,
        ]);

        return redirect()->route('review_processes.index')->with('success', 'Review process updated successfully!');
    }
    public function destroy($id)
{
    try {
        $reviewProcess = ReviewProcess::find($id);

        if (!$reviewProcess) {
            return redirect()
                ->route('review_processes.index')
                ->with('error', 'Review process not found!');
        }

        $reviewProcess->delete();

        return redirect()
            ->route('review_processes.index')
            ->with('success', 'Review process deleted successfully!');
    } catch (\Exception $e) {
        return redirect()
            ->route('review_processes.index')
            ->with('error', 'An error occurred while deleting the review process: ' . $e->getMessage());
    }
}

    
}