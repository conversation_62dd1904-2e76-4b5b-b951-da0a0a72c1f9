@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center gap-3">
                            <h4>Super Admins</h4>
                            <span class="badge bg-primary">
                                {{ $superadmins->count() }}
                            </span>
                        </div>
                        <div class="d-flex align-items-center gap-3">
                            <a href="{{ route('communication.index') }}" class="btn btn-sm btn-outline-secondary">
                                <i class="fa fa-arrow-left me-1"></i> Back
                            </a>
                            <a href="{{ route('communication.index') }}" class="btn btn-sm btn-primary">
                                <i class="fa fa-comment-alt me-1"></i> Message
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Superadmin User Cards -->
                        @if ($superadmins->count() > 0)
                            <div class="row">
                                @forelse ($superadmins as $superadmin)
                                    <div class="col-md-4 col-sm-6 mb-4">
                                        <div class="card shadow-sm">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <h5 class="card-title font-semibold">{{ $superadmin['name'] }}</h5>
                                                        <p class="text-muted font-light">
                                                            {{ $superadmin['source'] }}
                                                        </p>
                                                    </div>
                                                    <span class="text-muted font-light text-sm">
                                                        {{ $superadmin['created_at'] ? \Carbon\Carbon::parse($superadmin['created_at'])->format('d M Y') : now()->format('d M Y') }}
                                                    </span>
                                                </div>
                                                <p class="text-muted font-light mt-3">{{ $superadmin['email'] }}</p>
                                                <button onclick="openMessageModal('{{ addslashes($superadmin['name']) }}', '{{ $superadmin['email'] }}')"
                                                        class="btn btn-sm btn-primary mt-2">
                                                    <i class="fa fa-comment-alt me-1"></i> Message
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                @empty
                                    <div class="alert alert-info text-center">
                                        <i class="fa fa-info-circle mr-2"></i> No Superadmins found.
                                    </div>
                                @endforelse
                            </div>
                        @else
                            <div class="alert alert-info text-center">
                                <i class="fa fa-info-circle mr-2"></i> No Superadmins found.
                            </div>
                        @endif

                        <!-- Message Modal -->
                        <div id="messageModal" class="modal fade" tabindex="-1" aria-hidden="true">
                            <div class="modal-dialog modal-dialog-centered modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="superadminName">Send Message</h5>
                                        <button type="button" class="btn-close" onclick="closeMessageModal()" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <form id="messageForm" method="POST" action="{{ route('messages.storeSuperadmin') }}" enctype="multipart/form-data">
                                            @csrf
                                            <input type="hidden" id="receiver_email" name="receiver_email">
                                            <div class="mb-3">
                                                <label for="message" class="form-label text-muted">Message</label>
                                                <textarea id="message" name="body" class="form-control" rows="4" required></textarea>
                                            </div>
                                            <div class="mb-3">
                                                <label for="attachments" class="form-label text-muted">Attachments (Multiple)</label>
                                                <input type="file" id="attachments" name="attachments[]" multiple accept=".jpg,.jpeg,.png,.pdf" class="form-control">
                                                <div id="previewContainer" class="mt-2 d-flex flex-wrap gap-2"></div>
                                            </div>
                                            <div class="d-flex justify-content-end gap-2">
                                                <button type="button" onclick="closeMessageModal()" class="btn btn-sm btn-outline-secondary">Cancel</button>
                                                <button type="submit" class="btn btn-sm btn-primary">
                                                    <i class="fa fa-paper-plane me-1"></i> Send Message
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('includes')
    <meta name="csrf-token" content="{{ csrf_token() }}">
@endpush

@push('style')
    <style>
        .btn-sm {
            padding: 0.25rem 0.5rem;
        }
        .badge {
            font-size: 0.8rem;
            padding: 0.3em 0.6em;
        }
        .card-title {
            font-size: 1.1rem;
        }
        .modal-content {
            border-radius: 0.5rem;
        }
        .text-muted {
            color: #6c757d !important;
        }
        .w-25 {
            width: 80px;
            height: 80px;
        }
        .object-fit-cover {
            object-fit: cover;
        }
        .text-xs {
            font-size: 0.75rem;
        }
        .shadow-sm {
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .rounded-bottom-start {
            border-bottom-left-radius: 0.25rem;
        }
    </style>
@endpush

@push('script')
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        let selectedFiles = [];

        function openMessageModal(name, email) {
            try {
                document.getElementById('superadminName').textContent = `Send Message to ${name}`;
                document.getElementById('receiver_email').value = email;
                document.getElementById('messageForm').reset();
                selectedFiles = [];
                document.getElementById('previewContainer').innerHTML = '';
                $('#messageModal').modal('show');
            } catch (error) {
                console.error('Error opening modal:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'Failed to open message modal.'
                });
            }
        }

        function closeMessageModal() {
            try {
                $('#messageModal').modal('hide');
                document.getElementById('messageForm').reset();
                document.getElementById('previewContainer').innerHTML = '';
                selectedFiles = [];
            } catch (error) {
                console.error('Error closing modal:', error);
            }
        }

        document.getElementById('attachments').addEventListener('change', function(e) {
            const newFiles = Array.from(e.target.files);
            selectedFiles.push(...newFiles);
            renderFilePreviews();
            e.target.value = '';
        });

        function renderFilePreviews() {
            const container = document.getElementById('previewContainer');
            container.innerHTML = '';

            selectedFiles.forEach((file, index) => {
                const wrapper = document.createElement('div');
                wrapper.className = 'position-relative w-25 rounded overflow-hidden shadow-sm';

                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(event) {
                        wrapper.innerHTML = `
                            <img src="${event.target.result}" alt="${file.name}" class="w-100 h-100 object-fit-cover">
                            <div class="position-absolute top-0 end-0 bg-danger text-white text-xs rounded-bottom-start px-1 cursor-pointer" onclick="removePreview(${index})">X</div>
                        `;
                        container.appendChild(wrapper);
                    };
                    reader.readAsDataURL(file);
                } else if (file.type === 'application/pdf') {
                    wrapper.innerHTML = `
                        <div class="w-100 h-100 bg-light d-flex align-items-center justify-content-center text-xs text-muted">PDF: ${file.name}</div>
                        <div class="position-absolute top-0 end-0 bg-danger text-white text-xs rounded-bottom-start px-1 cursor-pointer" onclick="removePreview(${index})">X</div>
                    `;
                    container.appendChild(wrapper);
                } else {
                    wrapper.innerHTML = `
                        <div class="w-100 h-100 bg-light d-flex align-items-center justify-content-center text-xs text-muted">${file.name}</div>
                        <div class="position-absolute top-0 end-0 bg-danger text-white text-xs rounded-bottom-start px-1 cursor-pointer" onclick="removePreview(${index})">X</div>
                    `;
                    container.appendChild(wrapper);
                }
            });
        }

        function removePreview(index) {
            selectedFiles.splice(index, 1);
            renderFilePreviews();
        }

        document.getElementById('messageForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const submitButton = this.querySelector('button[type="submit"]');
            submitButton.disabled = true;
            submitButton.innerHTML = `
                <i class="fa fa-spinner fa-spin me-1"></i> Sending...
            `;

            const formData = new FormData(this);
            selectedFiles.forEach(file => formData.append('attachments[]', file));

            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Success!',
                        text: data.message || 'Message sent successfully.',
                        confirmButtonText: 'OK',
                        timer: 5000,
                        timerProgressBar: true
                    }).then(() => {
                        closeMessageModal();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: data.message || 'Failed to send message.'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: `An error occurred while sending the message: ${error.message}`
                });
            })
            .finally(() => {
                submitButton.disabled = false;
                submitButton.innerHTML = `
                    <i class="fa fa-paper-plane me-1"></i> Send Message
                `;
            });
        });
    </script>
@endpush