<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Guideline;
use Illuminate\Http\Request;
use App\Http\Resources\GuidlinesResources;

class GuidlineController extends Controller
{
    // public function index()
    // {
    //     $guidlines = Guideline::get();
    //     if($guidlines->count() > 0)
    //     {
    //         return GuidlinesResources::collection($guidlines);

    //     }
    //     else{
    //         return response()->json(['message'=>'No record available'],200);
    //     }
    // }
    public function index()
{
    $guidelines = Guideline::get();

    if ($guidelines->count() > 0) {
        $response = $guidelines->map(function ($item) {
            return [
                'id' => $item->id,
                'type'=>$item->type,
                'title' => $item->title,
                'description' => $item->description,
                'date_time' => $item->date_time,
                // 'created_at' => $item->created_at,
                // Add more fields if needed
            ];
        });

        return response()->json($response, 200);
    } else {
        return response()->json(['message' => 'No record available'], 200);
    }
}

    public function show($id)
{
    $guideline = Guideline::find($id);

    if ($guideline) {
        return new GuidlinesResources($guideline);
    } else {
        return response()->json(['message' => 'Guideline not found'], 404);
    }
}

}
