<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\BuilderProject;
use Illuminate\Support\Facades\Auth;
use App\Models\WebsiteBuilderProject; // Change to WebsiteBuilderProject

class BuilderProjectController extends Controller
{
    public function index()
    {
        $projects = BuilderProject::where('status', 'pending')->get();
        // Optional: Log to verify data
        // \Log::info('Pending Projects:', $projects->toArray());
        return view('modules.projects.index', compact('projects'));
    }



    public function index1()
    {

        // dd('aa');
        $projects = BuilderProject::all()->whereIn('status', ['approved', 'rejected']);
        // dd($projects->toSql(), $projects->getBindings());
        // $projects = $projects->get();
        // dd($projects);
        return view('modules.total-projects-reviewed.index', compact('projects'));
    }

    public function add()
    {
        return view('modules.projects.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'location' => 'nullable|string|max:255',
            'budget' => 'nullable|numeric',
            'client_name' => 'nullable|string|max:255',
            'country_code' => 'required|string',
            'custom_country_code' => 'nullable|string|regex:/^\+\d+$/',
            'client_contact' => 'nullable|string|max:20',
            'assigned_engineer' => 'nullable|string|max:255',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date',
            'status' => 'required|string|in:pending,approved,rejected',
        ]);

        $countryCode = $request->country_code === 'other' ? $request->custom_country_code : $request->country_code;
        $clientContact = $countryCode . ($request->client_contact ?? '');

        BuilderProject::create([
            'name' => $request->name,
            'description' => $request->description,
            'location' => $request->location,
            'budget' => $request->budget,
            'client_name' => $request->client_name,
            'client_contact' => $clientContact,
            'assigned_engineer' => $request->assigned_engineer,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'status' => $request->status,
        ]);

        return redirect()->route('project.index')->with('success', 'Project created successfully.');
    }

    public function edit($id)
    {
        $project = BuilderProject::findOrFail($id);
        return view('modules.projects.edit', compact('project'));
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'location' => 'nullable|string|max:255',
            'budget' => 'nullable|numeric',
            'client_name' => 'nullable|string|max:255',
            'country_code' => 'required|string',
            'custom_country_code' => 'nullable|string|regex:/^\+\d+$/',
            'client_contact' => 'nullable|string|max:20',
            'assigned_engineer' => 'nullable|string|max:255',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date',
            'status' => 'required|string|in:pending,approved,rejected',
        ]);

        $countryCode = $request->country_code === 'other' ? $request->custom_country_code : $request->country_code;
        $clientContact = $countryCode . ($request->client_contact ?? '');

        $project = BuilderProject::findOrFail($id);
        $project->update([
            'name' => $request->name,
            'description' => $request->description,
            'location' => $request->location,
            'budget' => $request->budget,
            'client_name' => $request->client_name,
            'client_contact' => $clientContact,
            'assigned_engineer' => $request->assigned_engineer,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'status' => $request->status,
        ]);

        return redirect()->route('project.index')->with('success', 'Project updated successfully.');
    }

    public function destroy($id)
    {
        BuilderProject::findOrFail($id)->delete();
        return redirect()->route('project.index')->with('success', 'Project deleted successfully.');
    }

    public function updateStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|string|in:pending,approved,rejected',
        ]);

        $project = BuilderProject::findOrFail($id);
        $project->status = $request->status;
        $project->save();

        return response()->json(['success' => true, 'message' => 'Status updated successfully!']);
    }
}
