<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Add Staff</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        .invalid-feedback { color: #dc2626; font-size: 0.9rem; }
        .is-invalid { border-color: #dc2626; box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1); }
        .highlight-input { 
            border-width: 2px; 
            background-color: #f8fafc; 
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); 
            transition: all 0.2s ease-in-out; 
        }
        .highlight-input:focus { 
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3); 
            background-color: #ffffff; 
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <div class="container mx-auto px-4 py-10">
        <div class="max-w-5xl mx-auto bg-white rounded-xl shadow-xl overflow-hidden">
            <div class="bg-blue-700 text-white px-8 py-6">
                <h2 class="text-2xl font-bold">Roles Management > Add Staff</h2>
            </div>
            <form action="{{ route('mcd-staff.store') }}" method="POST" class="p-8">
                @csrf
             <input type="hidden" name="token" value="{{ $token }}">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                     <div>
                        <label class="block text-base font-semibold text-gray-800">Role <span class="text-red-500">*</span></label>
                        <input type="hidden" name="role_id" value="{{ $selectedRole->id ?? old('role_id') }}">
                        <input type="text" class="mt-2 block w-full border-gray-400 rounded-lg highlight-input bg-gray-100 text-lg p-4" value="{{ $selectedRole->name ?? $roles->find(old('role_id'))?->name }}" disabled>
                        @error('role_id') <div class="invalid-feedback">{{ $message }}</div> @enderror
                    </div>
                    <div>
                        <lable class="block text-base font-semiblod text-gray-800">Department (Trade)<span class="text-danger">*</span></lable>
                        <select name="department_id" class="mt-2 block w-full border-gray-400 rounded-lg highlight-input focus:ring-blue-500 focus:border-blue-500 text-lg p-4  @error('department_id') is-invalid @enderror">
                            <option value="">Select Department</option>
                                @foreach($departments as $department)
                                    <option value="{{ $department->id }}" {{ old('department_id')}}>{{ $department->name }}</option>
                                @endforeach
                        </select>
                        @error('department_id') <div class="invalid-feedback">{{ $message }}</div> @enderror
                    </div>
                    <div>
                        <label class="block text-base font-semibold text-gray-800">Staff Name <span class="text-red-500">*</span></label>
                        <input type="text" name="staff_name" class="mt-2 block w-full border-gray-400 rounded-lg highlight-input focus:ring-blue-500 focus:border-blue-500 text-lg p-4 @error('staff_name') is-invalid @enderror" value="{{ old('staff_name') }}">
                        @error('staff_name') <div class="invalid-feedback">{{ $message }}</div> @enderror
                    </div>
                    <div>
                        <label class="block text-base font-semibold text-gray-800">Email <span class="text-red-500">*</span></label>
                        <input type="email" name="email" class="mt-2 block w-full border-gray-400 rounded-lg highlight-input focus:ring-blue-500 focus:border-blue-500 text-lg p-4 @error('email') is-invalid @enderror" value="{{ old('email') }}">
                        @error('email') <div class="invalid-feedback">{{ $message }}</div> @enderror
                    </div>
                    <div>
                        <label class="block text-base font-semibold text-gray-800">Password <span class="text-red-500">*</span></label>
                        <input type="password" name="password" class="mt-2 block w-full border-gray-400 rounded-lg highlight-input focus:ring-blue-500 focus:border-blue-500 text-lg p-4 @error('password') is-invalid @enderror">
                        @error('password') <div class="invalid-feedback">{{ $message }}</div> @enderror
                    </div>
                   
                    <div>
                        <label class="block text-base font-semibold text-gray-800">Contact Number</label>
                        <input type="text" name="contact_number" class="mt-2 block w-full border-gray-400 rounded-lg highlight-input focus:ring-blue-500 focus:border-blue-500 text-lg p-4 @error('contact_number') is-invalid @enderror" value="{{ old('contact_number') }}">
                        @error('contact_number') <div class="invalid-feedback">{{ $message }}</div> @enderror
                    </div>
                    <div>
                        <label class="block text-base font-semibold text-gray-800">Status</label>
                        <select name="status" class="mt-2 block w-full border-gray-400 rounded-lg highlight-input focus:ring-blue-500 focus:border-blue-500 text-lg p-4 @error('status') is-invalid @enderror">
                            <option value="1" {{ old('status', 1) == 1 ? 'selected' : '' }}>Active</option>
                            <option value="0" {{ old('status') == 0 ? 'selected' : '' }}>Inactive</option>
                        </select>
                        @error('status') <div class="invalid-feedback">{{ $message }}</div> @enderror
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-base font-semibold text-gray-800">Description</label>
                        <textarea name="description" class="mt-2 block w-full border-gray-400 rounded-lg highlight-input focus:ring-blue-500 focus:border-blue-500 text-lg p-4 @error('description') is-invalid @enderror" rows="5">{{ old('description') }}</textarea>
                        @error('description') <div class="invalid-feedback">{{ $message }}</div> @enderror
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-base font-semibold text-gray-800">Address</label>
                        <textarea name="address" class="mt-2 block w-full border-gray-400 rounded-lg highlight-input focus:ring-blue-500 focus:border-blue-500 text-lg p-4 @error('address') is-invalid @enderror" rows="5">{{ old('address') }}</textarea>
                        @error('address') <div class="invalid-feedback">{{ $message }}</div> @enderror
                    </div>
                </div>
                <div class="mt-8 flex justify-end space-x-4">
                    <button type="submit" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">Submit</button>
                    <a href="{{ route('mcd-staff.index') }}" class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</body>
</html>