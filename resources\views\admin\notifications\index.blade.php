@extends('mcdpanel.layouts.master')notification
@php
    $user = Auth::guard('staff_mcd')->user();
    $permissions = $user && $user->role ? $user->role->permissions : [];
    $isAdmin = Auth::guard('web')->check();
    $hasWritePermission = $isAdmin || (isset($permissions['notification']['write']) && $permissions['notification']['write'] == 1);
@endphp

@section('content')
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4><i class="fas fa-bell"></i> All Notifications</h4>
                        <div class="card-header-action">
                            {{-- <a href="{{ route('notifications.create') }}" class="btn btn-light btn-sm"><i class="fas fa-plus-circle"></i> Send Notification</a> --}}
                            @if ($hasWritePermission)
                                <a href="{{ route('notifications.create') }}" class="btn btn-light btn-sm">
                                    <i class="fas fa-plus-circle"></i> Send Notification
                                </a>
                            @else
                                <button type="button" class="btn btn-light btn-sm" onclick="showAccessDeniedNotification()">
                                    <i class="fas fa-plus-circle"></i> Send Notification
                                </button>
                            @endif
                        </div>
                    </div>
                    <div class="card-body">
                        @if ($notifications->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="table-1">
                                    <thead>
                                        <tr>
                                            <th>Title</th>
                                            <th>Inspector</th>
                                            <th>Tag</th>
                                            <th>Sent At</th>
                                            <th>Status</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($notifications as $notification)
                                            <tr>
                                                <td>
                                                    <strong>{{ $notification->title }}</strong>
                                                </td>
                                                <td>
                                                    @if ($notification->inspector)
                                                        {{ $notification->inspector->first_name }} {{ $notification->inspector->last_name }}
                                                    @else
                                                        <span class="text-danger">Inspector Not Found</span>
                                                    @endif
                                                </td>
                                               <td>
                                                    @php
                                                        $badgeClass = match($notification->tag) {
                                                            'Approved' => 'success',
                                                            'Violation' => 'danger',
                                                            'Message' => 'info',
                                                            default => 'secondary',
                                                        };
                                                    @endphp
                                                    <span class="badge badge-{{ $badgeClass }}">
                                                        {{ ucfirst($notification->tag) }}
                                                    </span>
                                                </td>

                                                <td>
                                                    {{ \Carbon\Carbon::parse($notification->sent_at)->format('d M, Y H:i') }}
                                                </td>
                                                <td>
                                                    @if ($notification->sent_at)
                                                        <span class="badge badge-success">Sent</span>
                                                    @else
                                                        <span class="badge badge-secondary">Pending</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <a href="{{ route('notifications.show', $notification->id) }}" class="btn btn-info btn-sm" title="View Notification"><i class="fas fa-eye"></i></a>
                                                    {{-- <form action="{{ route('notifications.destroy', $notification->id) }}" method="POST" class="delete-form" style="display:inline;" data-notification-title="{{ $notification->title }}">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-danger btn-sm" title="Delete Notification"><i class="fas fa-trash"></i></button>
                                                    </form> --}}

                                                    @if ($hasWritePermission)
                                                        <form action="{{ route('notifications.destroy', $notification->id) }}" method="POST" class="delete-form" style="display:inline;" data-notification-title="{{ $notification->title }}">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="btn btn-danger btn-sm" title="Delete Notification">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    @else
                                                        <button type="button" class="btn btn-danger btn-sm" onclick="showAccessDeniedNotification()" title="Delete Notification">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="alert alert-info text-center">
                                <i class="fa fa-info-circle mr-2"></i>
                                No notifications found.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('style')
    <style>
        .btn-sm {
            padding: 0.25rem 0.5rem;
        }
        .badge {
            font-size: 0.8rem;
            padding: 0.3em 0.6em;
        }
        .table th, .table td {
            vertical-align: middle;
        }
    </style>
@endpush

@push('script')
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Success Message from Session
            @if(session('success'))
                Swal.fire({
                    title: 'Success!',
                    text: '{{ session('success') }}',
                    icon: 'success',
                    showConfirmButton: false,
                    timer: 4000,
                    timerProgressBar: true,
                    position: 'top-end',
                    toast: true,
                    customClass: {
                        popup: 'swal2-popup-custom',
                        title: 'swal2-title-custom',
                        content: 'swal2-content-custom'
                    }
                });
            @endif

            // Delete Confirmation Handler
            document.querySelectorAll('.delete-form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const notificationTitle = this.getAttribute('data-notification-title');

                    Swal.fire({
                        title: 'Are you sure?',
                        text: `Delete notification "${notificationTitle}"?`,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#3085d6',
                        cancelButtonColor: '#d33',
                        confirmButtonText: 'Yes, delete it!',
                        cancelButtonText: 'Cancel'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            this.submit();
                        }
                    });
                });
            });
        });

         function showAccessDeniedNotification() {
        Swal.fire({
            icon: 'error',
            title: 'Access Denied',
            text: 'You do not have permission to send notifications.'
        });
    }
    </script>

    <style>
        .swal2-popup-custom {
            border-radius: 8px;
            padding: 15px;
            background: #ffffff;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
        }
        .swal2-title-custom {
            color: #28a745;
            font-weight: bold;
            font-size: 18px;
        }
        .swal2-content-custom {
            font-size: 14px;
            color: #555;
        }
    </style>
@endpush