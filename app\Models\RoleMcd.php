<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class RoleMcd extends Model
{
    protected $table = 'roles_mcd';

    protected $fillable = [
        'name',
        'description',
        'status',
        'permissions'
    ];

     protected $casts = [
        'status' => 'boolean',
        'permissions' => 'array' // Cast permissions as array
    ];

    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    public function permissions()
    {
        return $this->belongsToMany(Permission::class, 'role_permissions', 'role_id', 'permission_id')
                    ->withPivot('can_read', 'can_write')
                    ->withTimestamps();
    }

    

    public function staff()
{
    return $this->belongsToMany(McdStaff::class, 'staff_roles', 'role_id', 'staff_id');
}

}
