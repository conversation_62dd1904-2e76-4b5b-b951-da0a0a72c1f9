@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content py-6">
        <div class="container">
            <br>
            <div class="row justify-content-center">
                <div class="col-12 col-lg-12">
                    <div class="card shadow-lg border-0 rounded-3 overflow-hidden">
                        <div class="card-header bg-orange-600 text-[#FFFFFF] d-flex justify-content-between align-items-center py-3 px-4">
                            <h5 class="mb-0 fw-bold"><i class="fas fa-user-lock me-2"></i> Assign Permissions to {{ $role->name }}</h5>
                            {{-- <a href="{{ route('mcd-permission-roles.edit-permissions', $role->id) }}"
                               class="btn btn-warning btn-sm fw-bold text-dark shadow-sm" title="Edit Permissions">
                                <i class="fas fa-edit me-1"></i> Edit
                            </a> --}}
                        </div>

                        <div class="card-body bg-light p-4">
                            <form action="{{ route('mcd-permission-roles.store-permissions', $role->id) }}" method="POST">
                                @csrf
                                @method('PUT')

                                @if (session('success'))
                                    <div class="alert alert-success rounded-2 shadow-sm">{{ session('success') }}</div>
                                @endif
                                @if ($errors->any())
                                    <div class="alert alert-danger rounded-2 shadow-sm">
                                        <ul class="mb-0">
                                            @foreach ($errors->all() as $error)
                                                <li>{{ $error }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif

                                <div class="row g-3">
                                    @foreach ($modules as $key => $module)
                                        <div class="col-12">
                                            <div class="card border-0 shadow-sm mb-2 transition-all hover-shadow rounded-3 {{ $loop->iteration % 2 == 0 ? 'bg-row-even' : 'bg-row-odd' }}">
                                                <div class="card-body p-3 d-flex align-items-center justify-content-between">
                                                    <div class="custom-control custom-checkbox mb-2">
                                                        <input type="checkbox" 
                                                               class="custom-control-input module-checkbox"
                                                               id="{{ $key }}"
                                                               name="permissions[{{ $key }}][enabled]" 
                                                               value="1"
                                                               {{ array_key_exists($key, (array) $role->permissions) ? 'checked' : '' }}>
                                                        <label class="custom-control-label text-dark fw-bold fs-6" 
                                                               for="{{ $key }}"
                                                               aria-label="Enable {{ $module }}">
                                                            <i class="fas fa-shield-alt text-primary me-2"></i>
                                                            {{ $module }}
                                                        </label>
                                                    </div>
                                                   
                                                    <div class="permission-options d-flex gap-2 {{ isset($role->permissions[$key]) ? '' : 'disabled-options' }}">
                                                        <div class="permission-box p-1 px-2 rounded-2 shadow-sm bg-white transition-all">
                                                            <div class="form-check form-check-inline mb-0">
                                                                <input class="custom-radio" 
                                                                    type="radio"
                                                                    name="permissions[{{ $key }}][access]"
                                                                    id="{{ $key }}_no_permission"
                                                                    value="no_permission"
                                                                    {{ !isset($role->permissions[$key]) || (isset($role->permissions[$key]) && !$role->permissions[$key]['read'] && !$role->permissions[$key]['write']) ? 'checked' : '' }}>
                                                                <label class="form-check-label text-dark fw-bold fs-7" 
                                                                    for="{{ $key }}_no_permission">
                                                                    <i class="fas fa-ban me-1 text-danger"></i> No Permission
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="permission-box p-1 px-2 rounded-2 shadow-sm bg-white transition-all">
                                                            <div class="form-check form-check-inline mb-0">
                                                                <input class="custom-radio" 
                                                                    type="radio"
                                                                    name="permissions[{{ $key }}][access]"
                                                                    id="{{ $key }}_read"
                                                                    value="read"
                                                                    {{ isset($role->permissions[$key]['read']) && $role->permissions[$key]['read'] == 1 && (!isset($role->permissions[$key]['write']) || $role->permissions[$key]['write'] == 0) ? 'checked' : '' }}>
                                                                <label class="form-check-label text-dark fw-bold fs-7" 
                                                                    for="{{ $key }}_read">
                                                                    <i class="fas fa-eye me-1 text-success"></i> Read
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="permission-box p-1 px-2 rounded-2 shadow-sm bg-white transition-all">
                                                            <div class="form-check form-check-inline mb-0">
                                                                <input class="custom-radio" 
                                                                    type="radio"
                                                                    name="permissions[{{ $key }}][access]"
                                                                    id="{{ $key }}_write"
                                                                    value="write"
                                                                    {{ isset($role->permissions[$key]['write']) && $role->permissions[$key]['write'] == 1 ? 'checked' : '' }}>
                                                                <label class="form-check-label text-dark fw-bold fs-7" 
                                                                    for="{{ $key }}_write">
                                                                    <i class="fas fa-edit me-1 text-info"></i> Read & Write
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>

                                <div class="mt-4 text-end" >
                                    <button type="submit"
                                            class="btn bg-orange-600 text-[#FFFFFF] btn-md px-4 py-1 fw-bold shadow-sm rounded-pill transition-all hover-scale" style="border-radius:5px;">
                                        <i class="fas fa-check-circle me-1"></i> Save Permissions
                                    </button>
                                    <a href="{{ route('mcd-permission-roles.assign-permissions', $role->id) }}"
   class="btn bg-gray-600 text-[#FFFFFF] btn-md px-4 py-1 fw-bold ms-2 shadow-sm rounded-pill transition-all hover-scale" style="border-radius:5px;">
    <i class="fas fa-times-circle me-1"></i> Cancel
</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* General Styles */
        .bg-gradient-primary {
            background: linear-gradient(90deg, #1e3a8a, #3b82f6);
        }

        .transition-all {
            transition: all 0.3s ease;
        }

        .hover-shadow:hover {
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
            transform: translateY(-3px);
        }

        .hover-scale:hover {
            transform: scale(1.05);
        }

        .rounded-3 {
            border-radius: 1rem !important;
        }

        .rounded-2 {
            border-radius: 0.5rem !important;
        }

        .bg-row-even {
            background-color: #f9fafb;
        }

        .bg-row-odd {
            background-color: #f1f5f9;
        }

        .fs-6 {
            font-size: 1rem !important;
        }

        .fs-7 {
            font-size: 0.875rem !important;
        }

        .fw-bold {
            font-weight: 700 !important;
        }

        .gap-2 {
            gap: 0.5rem;
        }

        /* Permission Box */
        .permission-box {
            /* border: 1px solid #e5e7eb;
            background: #ffffff; */
            display: flex;
    align-items: center;
    padding: 0.3rem 0.8rem;
    border-radius: 9999px;
    border: 1px solid #e5e7eb;
    background: #ffffff;
    transition: all 0.2s ease;
        }

        .permission-box:hover {
            /* border-color: #3b82f6;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); */
             border-color: rgb(234 88 12);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .disabled-options .permission-box {
            border-color: #d1d5db;
            background: #f3f4f6;
            opacity: 0.6;
        }

        /* Custom Radio Button Styling */
        .custom-radio {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            width: 1.25em;
            height: 1.25em;
            margin-top: 0.1em;
            position: relative;
            cursor: pointer;
            background: transparent;
            border: none;
            outline: none;
            box-shadow: none;
            padding: 0;
        }

        .custom-radio::before {
            content: '✗';
            color: #fd0a0a;
            font-size: 1.3em;
            text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: block;
        }

        .custom-radio:checked::before {
            content: '✓';
            color: #034e1f;
            font-weight: bolder;
        }

        .disabled-options .custom-radio::before {
            color: #9ca3af;
        }

        .custom-radio:focus,
        .custom-radio:active,
        .custom-radio:hover {
            background: transparent;
            border: none;
            outline: none;
            box-shadow: none;
        }

        .form-check-input {
            display: none;
        }

        .form-check-label {
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .form-check-label:hover {
            color: #1e40af;
        }

        .disabled-options {
            pointer-events: none;
        }

        .permission-options {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 10rem;
            flex-wrap: wrap;
        }
    </style>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            $('.module-checkbox').on('change', function() {
                const $options = $(this).closest('.card-body').find('.permission-options');
                const $radioButtons = $options.find('input[type="radio"]');
                const moduleId = this.id;

                if (this.checked) {
                    $radioButtons.prop('disabled', false);
                    $options.removeClass('disabled-options');
                    if (!$radioButtons.is(':checked')) {
                        $(`#${moduleId}_read`).prop('checked', true);
                    }
                } else {
                    $radioButtons.prop('disabled', true);
                    $options.addClass('disabled-options');
                    $radioButtons.prop('checked', false);
                    $(`#${moduleId}_no_permission`).prop('checked', true);
                }
            });

            $('.module-checkbox').each(function() {
                const $options = $(this).closest('.card-body').find('.permission-options');
                const $radioButtons = $options.find('input[type="radio"]');
                
                if (!this.checked) {
                    $radioButtons.prop('disabled', true);
                    $options.addClass('disabled-options');
                    $(`#${this.id}_no_permission`).prop('checked', true);
                } else {
                    $radioButtons.prop('disabled', false);
                    $options.removeClass('disabled-options');
                    if (!$radioButtons.is(':checked')) {
                        $(`#${this.id}_read`).prop('checked', true);
                    }
                }
            });
        });
    </script>
@endsection