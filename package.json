{"$schema": "https://json.schemastore.org/package.json", "name": "pdf-annotation-tool", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "start": "npm run dev", "server": "node server.cjs", "dev:full": "concurrently \"npm run dev\" \"npm run server\"", "dev:all": "concurrently \"node server.cjs\" \"vite\"", "lint": "eslint src --ext .js,.vue", "format": "prettier --write src/**/*.{js,vue}"}, "dependencies": {"axios": "^1.7.4", "bootstrap": "^5.3.3", "cors": "^2.8.5", "express": "^5.1.0", "express-fileupload": "^1.5.1", "fabric": "^6.6.5", "mysql2": "^3.14.1", "pdf-lib": "^1.17.1", "pdfjs-dist": "^3.11.174", "sequelize": "^6.37.7", "vue": "^3.4.21"}, "devDependencies": {"@tailwindcss/forms": "^0.5.2", "@vitejs/plugin-vue": "^5.0.4", "alpinejs": "^3.4.2", "autoprefixer": "^10.4.2", "concurrently": "^9.2.0", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.4.31", "tailwindcss": "^3.1.0", "vite": "^6.0.11"}}