import{g as is}from"./index-BxnrM61j.js";/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var Ci=function(r,e){return Ci=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,n){t.__proto__=n}||function(t,n){for(var i in n)n.hasOwnProperty(i)&&(t[i]=n[i])},Ci(r,e)};function X(r,e){Ci(r,e);function t(){this.constructor=r}r.prototype=e===null?Object.create(e):(t.prototype=e.prototype,new t)}var he=function(){return he=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++){t=arguments[n];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e},he.apply(this,arguments)};function as(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,n=Object.getOwnPropertySymbols(r);i<n.length;i++)e.indexOf(n[i])<0&&Object.prototype.propertyIsEnumerable.call(r,n[i])&&(t[n[i]]=r[n[i]]);return t}function pe(r,e,t,n){function i(a){return a instanceof t?a:new t(function(o){o(a)})}return new(t||(t=Promise))(function(a,o){function s(l){try{f(n.next(l))}catch(h){o(h)}}function u(l){try{f(n.throw(l))}catch(h){o(h)}}function f(l){l.done?a(l.value):i(l.value).then(s,u)}f((n=n.apply(r,[])).next())})}function ge(r,e){var t={label:0,sent:function(){if(a[0]&1)throw a[1];return a[1]},trys:[],ops:[]},n,i,a,o;return o={next:s(0),throw:s(1),return:s(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function s(f){return function(l){return u([f,l])}}function u(f){if(n)throw new TypeError("Generator is already executing.");for(;t;)try{if(n=1,i&&(a=f[0]&2?i.return:f[0]?i.throw||((a=i.return)&&a.call(i),0):i.next)&&!(a=a.call(i,f[1])).done)return a;switch(i=0,a&&(f=[f[0]&2,a.value]),f[0]){case 0:case 1:a=f;break;case 4:return t.label++,{value:f[1],done:!1};case 5:t.label++,i=f[1],f=[0];continue;case 7:f=t.ops.pop(),t.trys.pop();continue;default:if(a=t.trys,!(a=a.length>0&&a[a.length-1])&&(f[0]===6||f[0]===2)){t=0;continue}if(f[0]===3&&(!a||f[1]>a[0]&&f[1]<a[3])){t.label=f[1];break}if(f[0]===6&&t.label<a[1]){t.label=a[1],a=f;break}if(a&&t.label<a[2]){t.label=a[2],t.ops.push(f);break}a[2]&&t.ops.pop(),t.trys.pop();continue}f=e.call(r,t)}catch(l){f=[6,l],i=0}finally{n=a=0}if(f[0]&5)throw f[1];return{value:f[0]?f[1]:void 0,done:!0}}}function ke(){for(var r=0,e=0,t=arguments.length;e<t;e++)r+=arguments[e].length;for(var n=Array(r),i=0,e=0;e<t;e++)for(var a=arguments[e],o=0,s=a.length;o<s;o++,i++)n[i]=a[o];return n}var Ar="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Gr=new Uint8Array(256);for(var yn=0;yn<Ar.length;yn++)Gr[Ar.charCodeAt(yn)]=yn;var os=function(r){for(var e="",t=r.length,n=0;n<t;n+=3)e+=Ar[r[n]>>2],e+=Ar[(r[n]&3)<<4|r[n+1]>>4],e+=Ar[(r[n+1]&15)<<2|r[n+2]>>6],e+=Ar[r[n+2]&63];return t%3===2?e=e.substring(0,e.length-1)+"=":t%3===1&&(e=e.substring(0,e.length-2)+"=="),e},aa=function(r){var e=r.length*.75,t=r.length,n,i=0,a,o,s,u;r[r.length-1]==="="&&(e--,r[r.length-2]==="="&&e--);var f=new Uint8Array(e);for(n=0;n<t;n+=4)a=Gr[r.charCodeAt(n)],o=Gr[r.charCodeAt(n+1)],s=Gr[r.charCodeAt(n+2)],u=Gr[r.charCodeAt(n+3)],f[i++]=a<<2|o>>4,f[i++]=(o&15)<<4|s>>2,f[i++]=(s&3)<<6|u&63;return f},ss=/^(data)?:?([\w\/\+]+)?;?(charset=[\w-]+|base64)?.*,/i,us=function(r){var e=r.trim(),t=e.substring(0,100),n=t.match(ss);if(!n)return aa(e);var i=n[0],a=e.substring(i.length);return aa(a)},le=function(r){return r.charCodeAt(0)},fs=function(r){return r.codePointAt(0)},un=function(r,e){return jt(r.toString(16),e,"0").toUpperCase()},Xn=function(r){return un(r,2)},Lt=function(r){return String.fromCharCode(r)},cs=function(r){return Lt(parseInt(r,16))},jt=function(r,e,t){for(var n="",i=0,a=e-r.length;i<a;i++)n+=t;return n+r},rt=function(r,e,t){for(var n=r.length,i=0;i<n;i++)e[t++]=r.charCodeAt(i);return n},ls=function(r){return r.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")},fn=function(r){return r.replace(/\t|\u0085|\u2028|\u2029/g,"    ").replace(/[\b\v]/g,"")},hs=["\\n","\\f","\\r","\\u000B"],Qa=function(r){return/^[\n\f\r\u000B]$/.test(r)},_a=function(r){return r.split(/[\n\f\r\u000B]/)},$a=function(r){return r.replace(/[\n\f\r\u000B]/g," ")},eo=function(r,e){var t=r.charCodeAt(e),n,i=e+1,a=1;return t>=55296&&t<=56319&&r.length>i&&(n=r.charCodeAt(i),n>=56320&&n<=57343&&(a=2)),[r.slice(e,e+a),a]},ds=function(r){for(var e=[],t=0,n=r.length;t<n;){var i=eo(r,t),a=i[0],o=i[1];e.push(a),t+=o}return e},vs=function(r){for(var e=hs.join("|"),t=["$"],n=0,i=r.length;n<i;n++){var a=r[n];if(Qa(a))throw new TypeError("`wordBreak` must not include "+e);t.push(a===""?".":ls(a))}var o=t.join("|");return new RegExp("("+e+")|((.*?)("+o+"))","gm")},ps=function(r,e,t,n){for(var i=vs(e),a=fn(r).match(i),o="",s=0,u=[],f=function(){o!==""&&u.push(o),o="",s=0},l=0,h=a.length;l<h;l++){var d=a[l];if(Qa(d))f();else{var v=n(d);s+v>t&&f(),o+=d,s+=v}}return f(),u},gs=/^D:(\d\d\d\d)(\d\d)?(\d\d)?(\d\d)?(\d\d)?(\d\d)?([+\-Z])?(\d\d)?'?(\d\d)?'?$/,to=function(r){var e=r.match(gs);if(e){var t=e[1],n=e[2],i=n===void 0?"01":n,a=e[3],o=a===void 0?"01":a,s=e[4],u=s===void 0?"00":s,f=e[5],l=f===void 0?"00":f,h=e[6],d=h===void 0?"00":h,v=e[7],y=v===void 0?"Z":v,w=e[8],F=w===void 0?"00":w,S=e[9],R=S===void 0?"00":S,C=y==="Z"?"Z":""+y+F+":"+R,B=new Date(t+"-"+i+"-"+o+"T"+u+":"+l+":"+d+C);return B}},zi=function(r,e){for(var t,n=0,i;n<r.length;){var a=r.substring(n).match(e);if(!a)return{match:i,pos:n};i=a,n+=((t=a.index)!==null&&t!==void 0?t:0)+a[0].length}return{match:i,pos:n}},Nn=function(r){return r[r.length-1]},Ti=function(r){if(r instanceof Uint8Array)return r;for(var e=r.length,t=new Uint8Array(e),n=0;n<e;n++)t[n]=r.charCodeAt(n);return t},ys=function(){for(var r=[],e=0;e<arguments.length;e++)r[e]=arguments[e];for(var t=r.length,n=[],i=0;i<t;i++){var a=r[i];n[i]=a instanceof Uint8Array?a:Ti(a)}for(var o=0,i=0;i<t;i++)o+=r[i].length;for(var s=new Uint8Array(o),u=0,f=0;f<t;f++)for(var l=n[f],h=0,d=l.length;h<d;h++)s[u++]=l[h];return s},bs=function(r){for(var e=0,t=0,n=r.length;t<n;t++)e+=r[t].length;for(var i=new Uint8Array(e),a=0,t=0,n=r.length;t<n;t++){var o=r[t];i.set(o,a),a+=o.length}return i},ro=function(r){for(var e="",t=0,n=r.length;t<n;t++)e+=Lt(r[t]);return e},ms=function(r,e){return r.id-e.id},xs=function(r,e){for(var t=[],n=0,i=r.length;n<i;n++){var a=r[n],o=r[n-1];(n===0||e(a)!==e(o))&&t.push(a)}return t},Fr=function(r){for(var e=r.length,t=0,n=Math.floor(e/2);t<n;t++){var i=t,a=e-t-1,o=r[t];r[i]=r[a],r[a]=o}return r},ws=function(r){for(var e=0,t=0,n=r.length;t<n;t++)e+=r[t];return e},Ss=function(r,e){for(var t=new Array(e-r),n=0,i=t.length;n<i;n++)t[n]=r+n;return t},Fs=function(r,e){for(var t=new Array(e.length),n=0,i=e.length;n<i;n++)t[n]=r[e[n]];return t},ks=function(r){return r instanceof Uint8Array||r instanceof ArrayBuffer||typeof r=="string"},Ir=function(r){if(typeof r=="string")return us(r);if(r instanceof ArrayBuffer)return new Uint8Array(r);if(r instanceof Uint8Array)return r;throw new TypeError("`input` must be one of `string | ArrayBuffer | Uint8Array`")},Er=function(){return new Promise(function(r){setTimeout(function(){return r()},0)})},Cs=function(r,e){e===void 0&&(e=!0);var t=[];e&&t.push(65279);for(var n=0,i=r.length;n<i;){var a=r.codePointAt(n);if(a<65536)t.push(a),n+=1;else if(a<1114112)t.push(no(a),io(a)),n+=2;else throw new Error("Invalid code point: 0x"+Xn(a))}return new Uint16Array(t)},Ts=function(r){return r>=0&&r<=65535},Ps=function(r){return r>=65536&&r<=1114111},no=function(r){return Math.floor((r-65536)/1024)+55296},io=function(r){return(r-65536)%1024+56320},tr;(function(r){r.BigEndian="BigEndian",r.LittleEndian="LittleEndian"})(tr||(tr={}));var Ur="�".codePointAt(0),ao=function(r,e){if(e===void 0&&(e=!0),r.length<=1)return String.fromCodePoint(Ur);for(var t=e?As(r):tr.BigEndian,n=e?2:0,i=[];r.length-n>=2;){var a=sa(r[n++],r[n++],t);if(Ds(a))if(r.length-n<2)i.push(Ur);else{var o=sa(r[n++],r[n++],t);oa(o)?i.push(a,o):i.push(Ur)}else oa(a)?(n+=2,i.push(Ur)):i.push(a)}return n<r.length&&i.push(Ur),String.fromCodePoint.apply(String,i)},Ds=function(r){return r>=55296&&r<=56319},oa=function(r){return r>=56320&&r<=57343},sa=function(r,e,t){if(t===tr.LittleEndian)return e<<8|r;if(t===tr.BigEndian)return r<<8|e;throw new Error("Invalid byteOrder: "+t)},As=function(r){return oo(r)?tr.BigEndian:so(r)?tr.LittleEndian:tr.BigEndian},oo=function(r){return r[0]===254&&r[1]===255},so=function(r){return r[0]===255&&r[1]===254},uo=function(r){return oo(r)||so(r)},Rs=function(r){var e=String(r);if(Math.abs(r)<1){var t=parseInt(r.toString().split("e-")[1]);if(t){var n=r<0;n&&(r*=-1),r*=Math.pow(10,t-1),e="0."+new Array(t).join("0")+r.toString().substring(2),n&&(e="-"+e)}}else{var t=parseInt(r.toString().split("+")[1]);t>20&&(t-=20,r/=Math.pow(10,t),e=r.toString()+new Array(t+1).join("0"))}return e},Dn=function(r){return Math.ceil(r.toString(2).length/8)},kr=function(r){for(var e=new Uint8Array(Dn(r)),t=1;t<=e.length;t++)e[t-1]=r>>(e.length-t)*8;return e},cn=function(r){throw new Error(r)},ci={},ua;function or(){return ua||(ua=1,function(r){var e=typeof Uint8Array<"u"&&typeof Uint16Array<"u"&&typeof Int32Array<"u";function t(a,o){return Object.prototype.hasOwnProperty.call(a,o)}r.assign=function(a){for(var o=Array.prototype.slice.call(arguments,1);o.length;){var s=o.shift();if(s){if(typeof s!="object")throw new TypeError(s+"must be non-object");for(var u in s)t(s,u)&&(a[u]=s[u])}}return a},r.shrinkBuf=function(a,o){return a.length===o?a:a.subarray?a.subarray(0,o):(a.length=o,a)};var n={arraySet:function(a,o,s,u,f){if(o.subarray&&a.subarray){a.set(o.subarray(s,s+u),f);return}for(var l=0;l<u;l++)a[f+l]=o[s+l]},flattenChunks:function(a){var o,s,u,f,l,h;for(u=0,o=0,s=a.length;o<s;o++)u+=a[o].length;for(h=new Uint8Array(u),f=0,o=0,s=a.length;o<s;o++)l=a[o],h.set(l,f),f+=l.length;return h}},i={arraySet:function(a,o,s,u,f){for(var l=0;l<u;l++)a[f+l]=o[s+l]},flattenChunks:function(a){return[].concat.apply([],a)}};r.setTyped=function(a){a?(r.Buf8=Uint8Array,r.Buf16=Uint16Array,r.Buf32=Int32Array,r.assign(r,n)):(r.Buf8=Array,r.Buf16=Array,r.Buf32=Array,r.assign(r,i))},r.setTyped(e)}(ci)),ci}var Cr={},Bt={},cr={},fa;function Os(){if(fa)return cr;fa=1;var r=or(),e=4,t=0,n=1,i=2;function a(b){for(var W=b.length;--W>=0;)b[W]=0}var o=0,s=1,u=2,f=3,l=258,h=29,d=256,v=d+1+h,y=30,w=19,F=2*v+1,S=15,R=16,C=7,B=256,E=16,D=17,P=18,I=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],V=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],q=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],L=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],Z=512,j=new Array((v+2)*2);a(j);var Q=new Array(y*2);a(Q);var te=new Array(Z);a(te);var Y=new Array(l-f+1);a(Y);var J=new Array(h);a(J);var Te=new Array(y);a(Te);function be(b,W,H,ee,T){this.static_tree=b,this.extra_bits=W,this.extra_base=H,this.elems=ee,this.max_length=T,this.has_stree=b&&b.length}var Ge,ze,Ne;function Ue(b,W){this.dyn_tree=b,this.max_code=0,this.stat_desc=W}function Ve(b){return b<256?te[b]:te[256+(b>>>7)]}function qe(b,W){b.pending_buf[b.pending++]=W&255,b.pending_buf[b.pending++]=W>>>8&255}function Pe(b,W,H){b.bi_valid>R-H?(b.bi_buf|=W<<b.bi_valid&65535,qe(b,b.bi_buf),b.bi_buf=W>>R-b.bi_valid,b.bi_valid+=H-R):(b.bi_buf|=W<<b.bi_valid&65535,b.bi_valid+=H)}function Ie(b,W,H){Pe(b,H[W*2],H[W*2+1])}function ae(b,W){var H=0;do H|=b&1,b>>>=1,H<<=1;while(--W>0);return H>>>1}function $e(b){b.bi_valid===16?(qe(b,b.bi_buf),b.bi_buf=0,b.bi_valid=0):b.bi_valid>=8&&(b.pending_buf[b.pending++]=b.bi_buf&255,b.bi_buf>>=8,b.bi_valid-=8)}function it(b,W){var H=W.dyn_tree,ee=W.max_code,T=W.stat_desc.static_tree,M=W.stat_desc.has_stree,p=W.stat_desc.extra_bits,K=W.stat_desc.extra_base,se=W.stat_desc.max_length,c,N,z,g,k,O,re=0;for(g=0;g<=S;g++)b.bl_count[g]=0;for(H[b.heap[b.heap_max]*2+1]=0,c=b.heap_max+1;c<F;c++)N=b.heap[c],g=H[H[N*2+1]*2+1]+1,g>se&&(g=se,re++),H[N*2+1]=g,!(N>ee)&&(b.bl_count[g]++,k=0,N>=K&&(k=p[N-K]),O=H[N*2],b.opt_len+=O*(g+k),M&&(b.static_len+=O*(T[N*2+1]+k)));if(re!==0){do{for(g=se-1;b.bl_count[g]===0;)g--;b.bl_count[g]--,b.bl_count[g+1]+=2,b.bl_count[se]--,re-=2}while(re>0);for(g=se;g!==0;g--)for(N=b.bl_count[g];N!==0;)z=b.heap[--c],!(z>ee)&&(H[z*2+1]!==g&&(b.opt_len+=(g-H[z*2+1])*H[z*2],H[z*2+1]=g),N--)}}function bt(b,W,H){var ee=new Array(S+1),T=0,M,p;for(M=1;M<=S;M++)ee[M]=T=T+H[M-1]<<1;for(p=0;p<=W;p++){var K=b[p*2+1];K!==0&&(b[p*2]=ae(ee[K]++,K))}}function je(){var b,W,H,ee,T,M=new Array(S+1);for(H=0,ee=0;ee<h-1;ee++)for(J[ee]=H,b=0;b<1<<I[ee];b++)Y[H++]=ee;for(Y[H-1]=ee,T=0,ee=0;ee<16;ee++)for(Te[ee]=T,b=0;b<1<<V[ee];b++)te[T++]=ee;for(T>>=7;ee<y;ee++)for(Te[ee]=T<<7,b=0;b<1<<V[ee]-7;b++)te[256+T++]=ee;for(W=0;W<=S;W++)M[W]=0;for(b=0;b<=143;)j[b*2+1]=8,b++,M[8]++;for(;b<=255;)j[b*2+1]=9,b++,M[9]++;for(;b<=279;)j[b*2+1]=7,b++,M[7]++;for(;b<=287;)j[b*2+1]=8,b++,M[8]++;for(bt(j,v+1,M),b=0;b<y;b++)Q[b*2+1]=5,Q[b*2]=ae(b,5);Ge=new be(j,I,d+1,v,S),ze=new be(Q,V,0,y,S),Ne=new be(new Array(0),q,0,w,C)}function ct(b){var W;for(W=0;W<v;W++)b.dyn_ltree[W*2]=0;for(W=0;W<y;W++)b.dyn_dtree[W*2]=0;for(W=0;W<w;W++)b.bl_tree[W*2]=0;b.dyn_ltree[B*2]=1,b.opt_len=b.static_len=0,b.last_lit=b.matches=0}function fr(b){b.bi_valid>8?qe(b,b.bi_buf):b.bi_valid>0&&(b.pending_buf[b.pending++]=b.bi_buf),b.bi_buf=0,b.bi_valid=0}function mt(b,W,H,ee){fr(b),qe(b,H),qe(b,~H),r.arraySet(b.pending_buf,b.window,W,H,b.pending),b.pending+=H}function dt(b,W,H,ee){var T=W*2,M=H*2;return b[T]<b[M]||b[T]===b[M]&&ee[W]<=ee[H]}function We(b,W,H){for(var ee=b.heap[H],T=H<<1;T<=b.heap_len&&(T<b.heap_len&&dt(W,b.heap[T+1],b.heap[T],b.depth)&&T++,!dt(W,ee,b.heap[T],b.depth));)b.heap[H]=b.heap[T],H=T,T<<=1;b.heap[H]=ee}function me(b,W,H){var ee,T,M=0,p,K;if(b.last_lit!==0)do ee=b.pending_buf[b.d_buf+M*2]<<8|b.pending_buf[b.d_buf+M*2+1],T=b.pending_buf[b.l_buf+M],M++,ee===0?Ie(b,T,W):(p=Y[T],Ie(b,p+d+1,W),K=I[p],K!==0&&(T-=J[p],Pe(b,T,K)),ee--,p=Ve(ee),Ie(b,p,H),K=V[p],K!==0&&(ee-=Te[p],Pe(b,ee,K)));while(M<b.last_lit);Ie(b,B,W)}function xt(b,W){var H=W.dyn_tree,ee=W.stat_desc.static_tree,T=W.stat_desc.has_stree,M=W.stat_desc.elems,p,K,se=-1,c;for(b.heap_len=0,b.heap_max=F,p=0;p<M;p++)H[p*2]!==0?(b.heap[++b.heap_len]=se=p,b.depth[p]=0):H[p*2+1]=0;for(;b.heap_len<2;)c=b.heap[++b.heap_len]=se<2?++se:0,H[c*2]=1,b.depth[c]=0,b.opt_len--,T&&(b.static_len-=ee[c*2+1]);for(W.max_code=se,p=b.heap_len>>1;p>=1;p--)We(b,H,p);c=M;do p=b.heap[1],b.heap[1]=b.heap[b.heap_len--],We(b,H,1),K=b.heap[1],b.heap[--b.heap_max]=p,b.heap[--b.heap_max]=K,H[c*2]=H[p*2]+H[K*2],b.depth[c]=(b.depth[p]>=b.depth[K]?b.depth[p]:b.depth[K])+1,H[p*2+1]=H[K*2+1]=c,b.heap[1]=c++,We(b,H,1);while(b.heap_len>=2);b.heap[--b.heap_max]=b.heap[1],it(b,W),bt(H,se,b.bl_count)}function mr(b,W,H){var ee,T=-1,M,p=W[0*2+1],K=0,se=7,c=4;for(p===0&&(se=138,c=3),W[(H+1)*2+1]=65535,ee=0;ee<=H;ee++)M=p,p=W[(ee+1)*2+1],!(++K<se&&M===p)&&(K<c?b.bl_tree[M*2]+=K:M!==0?(M!==T&&b.bl_tree[M*2]++,b.bl_tree[E*2]++):K<=10?b.bl_tree[D*2]++:b.bl_tree[P*2]++,K=0,T=M,p===0?(se=138,c=3):M===p?(se=6,c=3):(se=7,c=4))}function Yt(b,W,H){var ee,T=-1,M,p=W[0*2+1],K=0,se=7,c=4;for(p===0&&(se=138,c=3),ee=0;ee<=H;ee++)if(M=p,p=W[(ee+1)*2+1],!(++K<se&&M===p)){if(K<c)do Ie(b,M,b.bl_tree);while(--K!==0);else M!==0?(M!==T&&(Ie(b,M,b.bl_tree),K--),Ie(b,E,b.bl_tree),Pe(b,K-3,2)):K<=10?(Ie(b,D,b.bl_tree),Pe(b,K-3,3)):(Ie(b,P,b.bl_tree),Pe(b,K-11,7));K=0,T=M,p===0?(se=138,c=3):M===p?(se=6,c=3):(se=7,c=4)}}function wt(b){var W;for(mr(b,b.dyn_ltree,b.l_desc.max_code),mr(b,b.dyn_dtree,b.d_desc.max_code),xt(b,b.bl_desc),W=w-1;W>=3&&b.bl_tree[L[W]*2+1]===0;W--);return b.opt_len+=3*(W+1)+5+5+4,W}function xr(b,W,H,ee){var T;for(Pe(b,W-257,5),Pe(b,H-1,5),Pe(b,ee-4,4),T=0;T<ee;T++)Pe(b,b.bl_tree[L[T]*2+1],3);Yt(b,b.dyn_ltree,W-1),Yt(b,b.dyn_dtree,H-1)}function Jt(b){var W=4093624447,H;for(H=0;H<=31;H++,W>>>=1)if(W&1&&b.dyn_ltree[H*2]!==0)return t;if(b.dyn_ltree[9*2]!==0||b.dyn_ltree[10*2]!==0||b.dyn_ltree[13*2]!==0)return n;for(H=32;H<d;H++)if(b.dyn_ltree[H*2]!==0)return n;return t}var Et=!1;function wr(b){Et||(je(),Et=!0),b.l_desc=new Ue(b.dyn_ltree,Ge),b.d_desc=new Ue(b.dyn_dtree,ze),b.bl_desc=new Ue(b.bl_tree,Ne),b.bi_buf=0,b.bi_valid=0,ct(b)}function Qt(b,W,H,ee){Pe(b,(o<<1)+(ee?1:0),3),mt(b,W,H)}function at(b){Pe(b,s<<1,3),Ie(b,B,j),$e(b)}function Mt(b,W,H,ee){var T,M,p=0;b.level>0?(b.strm.data_type===i&&(b.strm.data_type=Jt(b)),xt(b,b.l_desc),xt(b,b.d_desc),p=wt(b),T=b.opt_len+3+7>>>3,M=b.static_len+3+7>>>3,M<=T&&(T=M)):T=M=H+5,H+4<=T&&W!==-1?Qt(b,W,H,ee):b.strategy===e||M===T?(Pe(b,(s<<1)+(ee?1:0),3),me(b,j,Q)):(Pe(b,(u<<1)+(ee?1:0),3),xr(b,b.l_desc.max_code+1,b.d_desc.max_code+1,p+1),me(b,b.dyn_ltree,b.dyn_dtree)),ct(b),ee&&fr(b)}function Sr(b,W,H){return b.pending_buf[b.d_buf+b.last_lit*2]=W>>>8&255,b.pending_buf[b.d_buf+b.last_lit*2+1]=W&255,b.pending_buf[b.l_buf+b.last_lit]=H&255,b.last_lit++,W===0?b.dyn_ltree[H*2]++:(b.matches++,W--,b.dyn_ltree[(Y[H]+d+1)*2]++,b.dyn_dtree[Ve(W)*2]++),b.last_lit===b.lit_bufsize-1}return cr._tr_init=wr,cr._tr_stored_block=Qt,cr._tr_flush_block=Mt,cr._tr_tally=Sr,cr._tr_align=at,cr}var li,ca;function fo(){if(ca)return li;ca=1;function r(e,t,n,i){for(var a=e&65535|0,o=e>>>16&65535|0,s=0;n!==0;){s=n>2e3?2e3:n,n-=s;do a=a+t[i++]|0,o=o+a|0;while(--s);a%=65521,o%=65521}return a|o<<16|0}return li=r,li}var hi,la;function co(){if(la)return hi;la=1;function r(){for(var n,i=[],a=0;a<256;a++){n=a;for(var o=0;o<8;o++)n=n&1?3988292384^n>>>1:n>>>1;i[a]=n}return i}var e=r();function t(n,i,a,o){var s=e,u=o+a;n^=-1;for(var f=o;f<u;f++)n=n>>>8^s[(n^i[f])&255];return n^-1}return hi=t,hi}var di,ha;function ji(){return ha||(ha=1,di={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}),di}var da;function Es(){if(da)return Bt;da=1;var r=or(),e=Os(),t=fo(),n=co(),i=ji(),a=0,o=1,s=3,u=4,f=5,l=0,h=1,d=-2,v=-3,y=-5,w=-1,F=1,S=2,R=3,C=4,B=0,E=2,D=8,P=9,I=15,V=8,q=29,L=256,Z=L+1+q,j=30,Q=19,te=2*Z+1,Y=15,J=3,Te=258,be=Te+J+1,Ge=32,ze=42,Ne=69,Ue=73,Ve=91,qe=103,Pe=113,Ie=666,ae=1,$e=2,it=3,bt=4,je=3;function ct(c,N){return c.msg=i[N],N}function fr(c){return(c<<1)-(c>4?9:0)}function mt(c){for(var N=c.length;--N>=0;)c[N]=0}function dt(c){var N=c.state,z=N.pending;z>c.avail_out&&(z=c.avail_out),z!==0&&(r.arraySet(c.output,N.pending_buf,N.pending_out,z,c.next_out),c.next_out+=z,N.pending_out+=z,c.total_out+=z,c.avail_out-=z,N.pending-=z,N.pending===0&&(N.pending_out=0))}function We(c,N){e._tr_flush_block(c,c.block_start>=0?c.block_start:-1,c.strstart-c.block_start,N),c.block_start=c.strstart,dt(c.strm)}function me(c,N){c.pending_buf[c.pending++]=N}function xt(c,N){c.pending_buf[c.pending++]=N>>>8&255,c.pending_buf[c.pending++]=N&255}function mr(c,N,z,g){var k=c.avail_in;return k>g&&(k=g),k===0?0:(c.avail_in-=k,r.arraySet(N,c.input,c.next_in,k,z),c.state.wrap===1?c.adler=t(c.adler,N,k,z):c.state.wrap===2&&(c.adler=n(c.adler,N,k,z)),c.next_in+=k,c.total_in+=k,k)}function Yt(c,N){var z=c.max_chain_length,g=c.strstart,k,O,re=c.prev_length,_=c.nice_match,$=c.strstart>c.w_size-be?c.strstart-(c.w_size-be):0,Ce=c.window,qt=c.w_mask,Le=c.prev,De=c.strstart+Te,Ze=Ce[g+re-1],ot=Ce[g+re];c.prev_length>=c.good_match&&(z>>=2),_>c.lookahead&&(_=c.lookahead);do if(k=N,!(Ce[k+re]!==ot||Ce[k+re-1]!==Ze||Ce[k]!==Ce[g]||Ce[++k]!==Ce[g+1])){g+=2,k++;do;while(Ce[++g]===Ce[++k]&&Ce[++g]===Ce[++k]&&Ce[++g]===Ce[++k]&&Ce[++g]===Ce[++k]&&Ce[++g]===Ce[++k]&&Ce[++g]===Ce[++k]&&Ce[++g]===Ce[++k]&&Ce[++g]===Ce[++k]&&g<De);if(O=Te-(De-g),g=De-Te,O>re){if(c.match_start=N,re=O,O>=_)break;Ze=Ce[g+re-1],ot=Ce[g+re]}}while((N=Le[N&qt])>$&&--z!==0);return re<=c.lookahead?re:c.lookahead}function wt(c){var N=c.w_size,z,g,k,O,re;do{if(O=c.window_size-c.lookahead-c.strstart,c.strstart>=N+(N-be)){r.arraySet(c.window,c.window,N,N,0),c.match_start-=N,c.strstart-=N,c.block_start-=N,g=c.hash_size,z=g;do k=c.head[--z],c.head[z]=k>=N?k-N:0;while(--g);g=N,z=g;do k=c.prev[--z],c.prev[z]=k>=N?k-N:0;while(--g);O+=N}if(c.strm.avail_in===0)break;if(g=mr(c.strm,c.window,c.strstart+c.lookahead,O),c.lookahead+=g,c.lookahead+c.insert>=J)for(re=c.strstart-c.insert,c.ins_h=c.window[re],c.ins_h=(c.ins_h<<c.hash_shift^c.window[re+1])&c.hash_mask;c.insert&&(c.ins_h=(c.ins_h<<c.hash_shift^c.window[re+J-1])&c.hash_mask,c.prev[re&c.w_mask]=c.head[c.ins_h],c.head[c.ins_h]=re,re++,c.insert--,!(c.lookahead+c.insert<J)););}while(c.lookahead<be&&c.strm.avail_in!==0)}function xr(c,N){var z=65535;for(z>c.pending_buf_size-5&&(z=c.pending_buf_size-5);;){if(c.lookahead<=1){if(wt(c),c.lookahead===0&&N===a)return ae;if(c.lookahead===0)break}c.strstart+=c.lookahead,c.lookahead=0;var g=c.block_start+z;if((c.strstart===0||c.strstart>=g)&&(c.lookahead=c.strstart-g,c.strstart=g,We(c,!1),c.strm.avail_out===0)||c.strstart-c.block_start>=c.w_size-be&&(We(c,!1),c.strm.avail_out===0))return ae}return c.insert=0,N===u?(We(c,!0),c.strm.avail_out===0?it:bt):(c.strstart>c.block_start&&(We(c,!1),c.strm.avail_out===0),ae)}function Jt(c,N){for(var z,g;;){if(c.lookahead<be){if(wt(c),c.lookahead<be&&N===a)return ae;if(c.lookahead===0)break}if(z=0,c.lookahead>=J&&(c.ins_h=(c.ins_h<<c.hash_shift^c.window[c.strstart+J-1])&c.hash_mask,z=c.prev[c.strstart&c.w_mask]=c.head[c.ins_h],c.head[c.ins_h]=c.strstart),z!==0&&c.strstart-z<=c.w_size-be&&(c.match_length=Yt(c,z)),c.match_length>=J)if(g=e._tr_tally(c,c.strstart-c.match_start,c.match_length-J),c.lookahead-=c.match_length,c.match_length<=c.max_lazy_match&&c.lookahead>=J){c.match_length--;do c.strstart++,c.ins_h=(c.ins_h<<c.hash_shift^c.window[c.strstart+J-1])&c.hash_mask,z=c.prev[c.strstart&c.w_mask]=c.head[c.ins_h],c.head[c.ins_h]=c.strstart;while(--c.match_length!==0);c.strstart++}else c.strstart+=c.match_length,c.match_length=0,c.ins_h=c.window[c.strstart],c.ins_h=(c.ins_h<<c.hash_shift^c.window[c.strstart+1])&c.hash_mask;else g=e._tr_tally(c,0,c.window[c.strstart]),c.lookahead--,c.strstart++;if(g&&(We(c,!1),c.strm.avail_out===0))return ae}return c.insert=c.strstart<J-1?c.strstart:J-1,N===u?(We(c,!0),c.strm.avail_out===0?it:bt):c.last_lit&&(We(c,!1),c.strm.avail_out===0)?ae:$e}function Et(c,N){for(var z,g,k;;){if(c.lookahead<be){if(wt(c),c.lookahead<be&&N===a)return ae;if(c.lookahead===0)break}if(z=0,c.lookahead>=J&&(c.ins_h=(c.ins_h<<c.hash_shift^c.window[c.strstart+J-1])&c.hash_mask,z=c.prev[c.strstart&c.w_mask]=c.head[c.ins_h],c.head[c.ins_h]=c.strstart),c.prev_length=c.match_length,c.prev_match=c.match_start,c.match_length=J-1,z!==0&&c.prev_length<c.max_lazy_match&&c.strstart-z<=c.w_size-be&&(c.match_length=Yt(c,z),c.match_length<=5&&(c.strategy===F||c.match_length===J&&c.strstart-c.match_start>4096)&&(c.match_length=J-1)),c.prev_length>=J&&c.match_length<=c.prev_length){k=c.strstart+c.lookahead-J,g=e._tr_tally(c,c.strstart-1-c.prev_match,c.prev_length-J),c.lookahead-=c.prev_length-1,c.prev_length-=2;do++c.strstart<=k&&(c.ins_h=(c.ins_h<<c.hash_shift^c.window[c.strstart+J-1])&c.hash_mask,z=c.prev[c.strstart&c.w_mask]=c.head[c.ins_h],c.head[c.ins_h]=c.strstart);while(--c.prev_length!==0);if(c.match_available=0,c.match_length=J-1,c.strstart++,g&&(We(c,!1),c.strm.avail_out===0))return ae}else if(c.match_available){if(g=e._tr_tally(c,0,c.window[c.strstart-1]),g&&We(c,!1),c.strstart++,c.lookahead--,c.strm.avail_out===0)return ae}else c.match_available=1,c.strstart++,c.lookahead--}return c.match_available&&(g=e._tr_tally(c,0,c.window[c.strstart-1]),c.match_available=0),c.insert=c.strstart<J-1?c.strstart:J-1,N===u?(We(c,!0),c.strm.avail_out===0?it:bt):c.last_lit&&(We(c,!1),c.strm.avail_out===0)?ae:$e}function wr(c,N){for(var z,g,k,O,re=c.window;;){if(c.lookahead<=Te){if(wt(c),c.lookahead<=Te&&N===a)return ae;if(c.lookahead===0)break}if(c.match_length=0,c.lookahead>=J&&c.strstart>0&&(k=c.strstart-1,g=re[k],g===re[++k]&&g===re[++k]&&g===re[++k])){O=c.strstart+Te;do;while(g===re[++k]&&g===re[++k]&&g===re[++k]&&g===re[++k]&&g===re[++k]&&g===re[++k]&&g===re[++k]&&g===re[++k]&&k<O);c.match_length=Te-(O-k),c.match_length>c.lookahead&&(c.match_length=c.lookahead)}if(c.match_length>=J?(z=e._tr_tally(c,1,c.match_length-J),c.lookahead-=c.match_length,c.strstart+=c.match_length,c.match_length=0):(z=e._tr_tally(c,0,c.window[c.strstart]),c.lookahead--,c.strstart++),z&&(We(c,!1),c.strm.avail_out===0))return ae}return c.insert=0,N===u?(We(c,!0),c.strm.avail_out===0?it:bt):c.last_lit&&(We(c,!1),c.strm.avail_out===0)?ae:$e}function Qt(c,N){for(var z;;){if(c.lookahead===0&&(wt(c),c.lookahead===0)){if(N===a)return ae;break}if(c.match_length=0,z=e._tr_tally(c,0,c.window[c.strstart]),c.lookahead--,c.strstart++,z&&(We(c,!1),c.strm.avail_out===0))return ae}return c.insert=0,N===u?(We(c,!0),c.strm.avail_out===0?it:bt):c.last_lit&&(We(c,!1),c.strm.avail_out===0)?ae:$e}function at(c,N,z,g,k){this.good_length=c,this.max_lazy=N,this.nice_length=z,this.max_chain=g,this.func=k}var Mt;Mt=[new at(0,0,0,0,xr),new at(4,4,8,4,Jt),new at(4,5,16,8,Jt),new at(4,6,32,32,Jt),new at(4,4,16,16,Et),new at(8,16,32,32,Et),new at(8,16,128,128,Et),new at(8,32,128,256,Et),new at(32,128,258,1024,Et),new at(32,258,258,4096,Et)];function Sr(c){c.window_size=2*c.w_size,mt(c.head),c.max_lazy_match=Mt[c.level].max_lazy,c.good_match=Mt[c.level].good_length,c.nice_match=Mt[c.level].nice_length,c.max_chain_length=Mt[c.level].max_chain,c.strstart=0,c.block_start=0,c.lookahead=0,c.insert=0,c.match_length=c.prev_length=J-1,c.match_available=0,c.ins_h=0}function b(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=D,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new r.Buf16(te*2),this.dyn_dtree=new r.Buf16((2*j+1)*2),this.bl_tree=new r.Buf16((2*Q+1)*2),mt(this.dyn_ltree),mt(this.dyn_dtree),mt(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new r.Buf16(Y+1),this.heap=new r.Buf16(2*Z+1),mt(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new r.Buf16(2*Z+1),mt(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function W(c){var N;return!c||!c.state?ct(c,d):(c.total_in=c.total_out=0,c.data_type=E,N=c.state,N.pending=0,N.pending_out=0,N.wrap<0&&(N.wrap=-N.wrap),N.status=N.wrap?ze:Pe,c.adler=N.wrap===2?0:1,N.last_flush=a,e._tr_init(N),l)}function H(c){var N=W(c);return N===l&&Sr(c.state),N}function ee(c,N){return!c||!c.state||c.state.wrap!==2?d:(c.state.gzhead=N,l)}function T(c,N,z,g,k,O){if(!c)return d;var re=1;if(N===w&&(N=6),g<0?(re=0,g=-g):g>15&&(re=2,g-=16),k<1||k>P||z!==D||g<8||g>15||N<0||N>9||O<0||O>C)return ct(c,d);g===8&&(g=9);var _=new b;return c.state=_,_.strm=c,_.wrap=re,_.gzhead=null,_.w_bits=g,_.w_size=1<<_.w_bits,_.w_mask=_.w_size-1,_.hash_bits=k+7,_.hash_size=1<<_.hash_bits,_.hash_mask=_.hash_size-1,_.hash_shift=~~((_.hash_bits+J-1)/J),_.window=new r.Buf8(_.w_size*2),_.head=new r.Buf16(_.hash_size),_.prev=new r.Buf16(_.w_size),_.lit_bufsize=1<<k+6,_.pending_buf_size=_.lit_bufsize*4,_.pending_buf=new r.Buf8(_.pending_buf_size),_.d_buf=1*_.lit_bufsize,_.l_buf=3*_.lit_bufsize,_.level=N,_.strategy=O,_.method=z,H(c)}function M(c,N){return T(c,N,D,I,V,B)}function p(c,N){var z,g,k,O;if(!c||!c.state||N>f||N<0)return c?ct(c,d):d;if(g=c.state,!c.output||!c.input&&c.avail_in!==0||g.status===Ie&&N!==u)return ct(c,c.avail_out===0?y:d);if(g.strm=c,z=g.last_flush,g.last_flush=N,g.status===ze)if(g.wrap===2)c.adler=0,me(g,31),me(g,139),me(g,8),g.gzhead?(me(g,(g.gzhead.text?1:0)+(g.gzhead.hcrc?2:0)+(g.gzhead.extra?4:0)+(g.gzhead.name?8:0)+(g.gzhead.comment?16:0)),me(g,g.gzhead.time&255),me(g,g.gzhead.time>>8&255),me(g,g.gzhead.time>>16&255),me(g,g.gzhead.time>>24&255),me(g,g.level===9?2:g.strategy>=S||g.level<2?4:0),me(g,g.gzhead.os&255),g.gzhead.extra&&g.gzhead.extra.length&&(me(g,g.gzhead.extra.length&255),me(g,g.gzhead.extra.length>>8&255)),g.gzhead.hcrc&&(c.adler=n(c.adler,g.pending_buf,g.pending,0)),g.gzindex=0,g.status=Ne):(me(g,0),me(g,0),me(g,0),me(g,0),me(g,0),me(g,g.level===9?2:g.strategy>=S||g.level<2?4:0),me(g,je),g.status=Pe);else{var re=D+(g.w_bits-8<<4)<<8,_=-1;g.strategy>=S||g.level<2?_=0:g.level<6?_=1:g.level===6?_=2:_=3,re|=_<<6,g.strstart!==0&&(re|=Ge),re+=31-re%31,g.status=Pe,xt(g,re),g.strstart!==0&&(xt(g,c.adler>>>16),xt(g,c.adler&65535)),c.adler=1}if(g.status===Ne)if(g.gzhead.extra){for(k=g.pending;g.gzindex<(g.gzhead.extra.length&65535)&&!(g.pending===g.pending_buf_size&&(g.gzhead.hcrc&&g.pending>k&&(c.adler=n(c.adler,g.pending_buf,g.pending-k,k)),dt(c),k=g.pending,g.pending===g.pending_buf_size));)me(g,g.gzhead.extra[g.gzindex]&255),g.gzindex++;g.gzhead.hcrc&&g.pending>k&&(c.adler=n(c.adler,g.pending_buf,g.pending-k,k)),g.gzindex===g.gzhead.extra.length&&(g.gzindex=0,g.status=Ue)}else g.status=Ue;if(g.status===Ue)if(g.gzhead.name){k=g.pending;do{if(g.pending===g.pending_buf_size&&(g.gzhead.hcrc&&g.pending>k&&(c.adler=n(c.adler,g.pending_buf,g.pending-k,k)),dt(c),k=g.pending,g.pending===g.pending_buf_size)){O=1;break}g.gzindex<g.gzhead.name.length?O=g.gzhead.name.charCodeAt(g.gzindex++)&255:O=0,me(g,O)}while(O!==0);g.gzhead.hcrc&&g.pending>k&&(c.adler=n(c.adler,g.pending_buf,g.pending-k,k)),O===0&&(g.gzindex=0,g.status=Ve)}else g.status=Ve;if(g.status===Ve)if(g.gzhead.comment){k=g.pending;do{if(g.pending===g.pending_buf_size&&(g.gzhead.hcrc&&g.pending>k&&(c.adler=n(c.adler,g.pending_buf,g.pending-k,k)),dt(c),k=g.pending,g.pending===g.pending_buf_size)){O=1;break}g.gzindex<g.gzhead.comment.length?O=g.gzhead.comment.charCodeAt(g.gzindex++)&255:O=0,me(g,O)}while(O!==0);g.gzhead.hcrc&&g.pending>k&&(c.adler=n(c.adler,g.pending_buf,g.pending-k,k)),O===0&&(g.status=qe)}else g.status=qe;if(g.status===qe&&(g.gzhead.hcrc?(g.pending+2>g.pending_buf_size&&dt(c),g.pending+2<=g.pending_buf_size&&(me(g,c.adler&255),me(g,c.adler>>8&255),c.adler=0,g.status=Pe)):g.status=Pe),g.pending!==0){if(dt(c),c.avail_out===0)return g.last_flush=-1,l}else if(c.avail_in===0&&fr(N)<=fr(z)&&N!==u)return ct(c,y);if(g.status===Ie&&c.avail_in!==0)return ct(c,y);if(c.avail_in!==0||g.lookahead!==0||N!==a&&g.status!==Ie){var $=g.strategy===S?Qt(g,N):g.strategy===R?wr(g,N):Mt[g.level].func(g,N);if(($===it||$===bt)&&(g.status=Ie),$===ae||$===it)return c.avail_out===0&&(g.last_flush=-1),l;if($===$e&&(N===o?e._tr_align(g):N!==f&&(e._tr_stored_block(g,0,0,!1),N===s&&(mt(g.head),g.lookahead===0&&(g.strstart=0,g.block_start=0,g.insert=0))),dt(c),c.avail_out===0))return g.last_flush=-1,l}return N!==u?l:g.wrap<=0?h:(g.wrap===2?(me(g,c.adler&255),me(g,c.adler>>8&255),me(g,c.adler>>16&255),me(g,c.adler>>24&255),me(g,c.total_in&255),me(g,c.total_in>>8&255),me(g,c.total_in>>16&255),me(g,c.total_in>>24&255)):(xt(g,c.adler>>>16),xt(g,c.adler&65535)),dt(c),g.wrap>0&&(g.wrap=-g.wrap),g.pending!==0?l:h)}function K(c){var N;return!c||!c.state?d:(N=c.state.status,N!==ze&&N!==Ne&&N!==Ue&&N!==Ve&&N!==qe&&N!==Pe&&N!==Ie?ct(c,d):(c.state=null,N===Pe?ct(c,v):l))}function se(c,N){var z=N.length,g,k,O,re,_,$,Ce,qt;if(!c||!c.state||(g=c.state,re=g.wrap,re===2||re===1&&g.status!==ze||g.lookahead))return d;for(re===1&&(c.adler=t(c.adler,N,z,0)),g.wrap=0,z>=g.w_size&&(re===0&&(mt(g.head),g.strstart=0,g.block_start=0,g.insert=0),qt=new r.Buf8(g.w_size),r.arraySet(qt,N,z-g.w_size,g.w_size,0),N=qt,z=g.w_size),_=c.avail_in,$=c.next_in,Ce=c.input,c.avail_in=z,c.next_in=0,c.input=N,wt(g);g.lookahead>=J;){k=g.strstart,O=g.lookahead-(J-1);do g.ins_h=(g.ins_h<<g.hash_shift^g.window[k+J-1])&g.hash_mask,g.prev[k&g.w_mask]=g.head[g.ins_h],g.head[g.ins_h]=k,k++;while(--O);g.strstart=k,g.lookahead=J-1,wt(g)}return g.strstart+=g.lookahead,g.block_start=g.strstart,g.insert=g.lookahead,g.lookahead=0,g.match_length=g.prev_length=J-1,g.match_available=0,c.next_in=$,c.input=Ce,c.avail_in=_,g.wrap=re,l}return Bt.deflateInit=M,Bt.deflateInit2=T,Bt.deflateReset=H,Bt.deflateResetKeep=W,Bt.deflateSetHeader=ee,Bt.deflate=p,Bt.deflateEnd=K,Bt.deflateSetDictionary=se,Bt.deflateInfo="pako deflate (from Nodeca project)",Bt}var lr={},va;function lo(){if(va)return lr;va=1;var r=or(),e=!0,t=!0;try{String.fromCharCode.apply(null,[0])}catch{e=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch{t=!1}for(var n=new r.Buf8(256),i=0;i<256;i++)n[i]=i>=252?6:i>=248?5:i>=240?4:i>=224?3:i>=192?2:1;n[254]=n[254]=1,lr.string2buf=function(o){var s,u,f,l,h,d=o.length,v=0;for(l=0;l<d;l++)u=o.charCodeAt(l),(u&64512)===55296&&l+1<d&&(f=o.charCodeAt(l+1),(f&64512)===56320&&(u=65536+(u-55296<<10)+(f-56320),l++)),v+=u<128?1:u<2048?2:u<65536?3:4;for(s=new r.Buf8(v),h=0,l=0;h<v;l++)u=o.charCodeAt(l),(u&64512)===55296&&l+1<d&&(f=o.charCodeAt(l+1),(f&64512)===56320&&(u=65536+(u-55296<<10)+(f-56320),l++)),u<128?s[h++]=u:u<2048?(s[h++]=192|u>>>6,s[h++]=128|u&63):u<65536?(s[h++]=224|u>>>12,s[h++]=128|u>>>6&63,s[h++]=128|u&63):(s[h++]=240|u>>>18,s[h++]=128|u>>>12&63,s[h++]=128|u>>>6&63,s[h++]=128|u&63);return s};function a(o,s){if(s<65534&&(o.subarray&&t||!o.subarray&&e))return String.fromCharCode.apply(null,r.shrinkBuf(o,s));for(var u="",f=0;f<s;f++)u+=String.fromCharCode(o[f]);return u}return lr.buf2binstring=function(o){return a(o,o.length)},lr.binstring2buf=function(o){for(var s=new r.Buf8(o.length),u=0,f=s.length;u<f;u++)s[u]=o.charCodeAt(u);return s},lr.buf2string=function(o,s){var u,f,l,h,d=s||o.length,v=new Array(d*2);for(f=0,u=0;u<d;){if(l=o[u++],l<128){v[f++]=l;continue}if(h=n[l],h>4){v[f++]=65533,u+=h-1;continue}for(l&=h===2?31:h===3?15:7;h>1&&u<d;)l=l<<6|o[u++]&63,h--;if(h>1){v[f++]=65533;continue}l<65536?v[f++]=l:(l-=65536,v[f++]=55296|l>>10&1023,v[f++]=56320|l&1023)}return a(v,f)},lr.utf8border=function(o,s){var u;for(s=s||o.length,s>o.length&&(s=o.length),u=s-1;u>=0&&(o[u]&192)===128;)u--;return u<0||u===0?s:u+n[o[u]]>s?u:s},lr}var vi,pa;function ho(){if(pa)return vi;pa=1;function r(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}return vi=r,vi}var ga;function Bs(){if(ga)return Cr;ga=1;var r=Es(),e=or(),t=lo(),n=ji(),i=ho(),a=Object.prototype.toString,o=0,s=4,u=0,f=1,l=2,h=-1,d=0,v=8;function y(R){if(!(this instanceof y))return new y(R);this.options=e.assign({level:h,method:v,chunkSize:16384,windowBits:15,memLevel:8,strategy:d,to:""},R||{});var C=this.options;C.raw&&C.windowBits>0?C.windowBits=-C.windowBits:C.gzip&&C.windowBits>0&&C.windowBits<16&&(C.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new i,this.strm.avail_out=0;var B=r.deflateInit2(this.strm,C.level,C.method,C.windowBits,C.memLevel,C.strategy);if(B!==u)throw new Error(n[B]);if(C.header&&r.deflateSetHeader(this.strm,C.header),C.dictionary){var E;if(typeof C.dictionary=="string"?E=t.string2buf(C.dictionary):a.call(C.dictionary)==="[object ArrayBuffer]"?E=new Uint8Array(C.dictionary):E=C.dictionary,B=r.deflateSetDictionary(this.strm,E),B!==u)throw new Error(n[B]);this._dict_set=!0}}y.prototype.push=function(R,C){var B=this.strm,E=this.options.chunkSize,D,P;if(this.ended)return!1;P=C===~~C?C:C===!0?s:o,typeof R=="string"?B.input=t.string2buf(R):a.call(R)==="[object ArrayBuffer]"?B.input=new Uint8Array(R):B.input=R,B.next_in=0,B.avail_in=B.input.length;do{if(B.avail_out===0&&(B.output=new e.Buf8(E),B.next_out=0,B.avail_out=E),D=r.deflate(B,P),D!==f&&D!==u)return this.onEnd(D),this.ended=!0,!1;(B.avail_out===0||B.avail_in===0&&(P===s||P===l))&&(this.options.to==="string"?this.onData(t.buf2binstring(e.shrinkBuf(B.output,B.next_out))):this.onData(e.shrinkBuf(B.output,B.next_out)))}while((B.avail_in>0||B.avail_out===0)&&D!==f);return P===s?(D=r.deflateEnd(this.strm),this.onEnd(D),this.ended=!0,D===u):(P===l&&(this.onEnd(u),B.avail_out=0),!0)},y.prototype.onData=function(R){this.chunks.push(R)},y.prototype.onEnd=function(R){R===u&&(this.options.to==="string"?this.result=this.chunks.join(""):this.result=e.flattenChunks(this.chunks)),this.chunks=[],this.err=R,this.msg=this.strm.msg};function w(R,C){var B=new y(C);if(B.push(R,!0),B.err)throw B.msg||n[B.err];return B.result}function F(R,C){return C=C||{},C.raw=!0,w(R,C)}function S(R,C){return C=C||{},C.gzip=!0,w(R,C)}return Cr.Deflate=y,Cr.deflate=w,Cr.deflateRaw=F,Cr.gzip=S,Cr}var Tr={},Ft={},pi,ya;function Ns(){if(ya)return pi;ya=1;var r=30,e=12;return pi=function(n,i){var a,o,s,u,f,l,h,d,v,y,w,F,S,R,C,B,E,D,P,I,V,q,L,Z,j;a=n.state,o=n.next_in,Z=n.input,s=o+(n.avail_in-5),u=n.next_out,j=n.output,f=u-(i-n.avail_out),l=u+(n.avail_out-257),h=a.dmax,d=a.wsize,v=a.whave,y=a.wnext,w=a.window,F=a.hold,S=a.bits,R=a.lencode,C=a.distcode,B=(1<<a.lenbits)-1,E=(1<<a.distbits)-1;e:do{S<15&&(F+=Z[o++]<<S,S+=8,F+=Z[o++]<<S,S+=8),D=R[F&B];t:for(;;){if(P=D>>>24,F>>>=P,S-=P,P=D>>>16&255,P===0)j[u++]=D&65535;else if(P&16){I=D&65535,P&=15,P&&(S<P&&(F+=Z[o++]<<S,S+=8),I+=F&(1<<P)-1,F>>>=P,S-=P),S<15&&(F+=Z[o++]<<S,S+=8,F+=Z[o++]<<S,S+=8),D=C[F&E];r:for(;;){if(P=D>>>24,F>>>=P,S-=P,P=D>>>16&255,P&16){if(V=D&65535,P&=15,S<P&&(F+=Z[o++]<<S,S+=8,S<P&&(F+=Z[o++]<<S,S+=8)),V+=F&(1<<P)-1,V>h){n.msg="invalid distance too far back",a.mode=r;break e}if(F>>>=P,S-=P,P=u-f,V>P){if(P=V-P,P>v&&a.sane){n.msg="invalid distance too far back",a.mode=r;break e}if(q=0,L=w,y===0){if(q+=d-P,P<I){I-=P;do j[u++]=w[q++];while(--P);q=u-V,L=j}}else if(y<P){if(q+=d+y-P,P-=y,P<I){I-=P;do j[u++]=w[q++];while(--P);if(q=0,y<I){P=y,I-=P;do j[u++]=w[q++];while(--P);q=u-V,L=j}}}else if(q+=y-P,P<I){I-=P;do j[u++]=w[q++];while(--P);q=u-V,L=j}for(;I>2;)j[u++]=L[q++],j[u++]=L[q++],j[u++]=L[q++],I-=3;I&&(j[u++]=L[q++],I>1&&(j[u++]=L[q++]))}else{q=u-V;do j[u++]=j[q++],j[u++]=j[q++],j[u++]=j[q++],I-=3;while(I>2);I&&(j[u++]=j[q++],I>1&&(j[u++]=j[q++]))}}else if((P&64)===0){D=C[(D&65535)+(F&(1<<P)-1)];continue r}else{n.msg="invalid distance code",a.mode=r;break e}break}}else if((P&64)===0){D=R[(D&65535)+(F&(1<<P)-1)];continue t}else if(P&32){a.mode=e;break e}else{n.msg="invalid literal/length code",a.mode=r;break e}break}}while(o<s&&u<l);I=S>>3,o-=I,S-=I<<3,F&=(1<<S)-1,n.next_in=o,n.next_out=u,n.avail_in=o<s?5+(s-o):5-(o-s),n.avail_out=u<l?257+(l-u):257-(u-l),a.hold=F,a.bits=S},pi}var gi,ba;function zs(){if(ba)return gi;ba=1;var r=or(),e=15,t=852,n=592,i=0,a=1,o=2,s=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],u=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],f=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],l=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];return gi=function(d,v,y,w,F,S,R,C){var B=C.bits,E=0,D=0,P=0,I=0,V=0,q=0,L=0,Z=0,j=0,Q=0,te,Y,J,Te,be,Ge=null,ze=0,Ne,Ue=new r.Buf16(e+1),Ve=new r.Buf16(e+1),qe=null,Pe=0,Ie,ae,$e;for(E=0;E<=e;E++)Ue[E]=0;for(D=0;D<w;D++)Ue[v[y+D]]++;for(V=B,I=e;I>=1&&Ue[I]===0;I--);if(V>I&&(V=I),I===0)return F[S++]=1<<24|64<<16|0,F[S++]=1<<24|64<<16|0,C.bits=1,0;for(P=1;P<I&&Ue[P]===0;P++);for(V<P&&(V=P),Z=1,E=1;E<=e;E++)if(Z<<=1,Z-=Ue[E],Z<0)return-1;if(Z>0&&(d===i||I!==1))return-1;for(Ve[1]=0,E=1;E<e;E++)Ve[E+1]=Ve[E]+Ue[E];for(D=0;D<w;D++)v[y+D]!==0&&(R[Ve[v[y+D]]++]=D);if(d===i?(Ge=qe=R,Ne=19):d===a?(Ge=s,ze-=257,qe=u,Pe-=257,Ne=256):(Ge=f,qe=l,Ne=-1),Q=0,D=0,E=P,be=S,q=V,L=0,J=-1,j=1<<V,Te=j-1,d===a&&j>t||d===o&&j>n)return 1;for(;;){Ie=E-L,R[D]<Ne?(ae=0,$e=R[D]):R[D]>Ne?(ae=qe[Pe+R[D]],$e=Ge[ze+R[D]]):(ae=96,$e=0),te=1<<E-L,Y=1<<q,P=Y;do Y-=te,F[be+(Q>>L)+Y]=Ie<<24|ae<<16|$e|0;while(Y!==0);for(te=1<<E-1;Q&te;)te>>=1;if(te!==0?(Q&=te-1,Q+=te):Q=0,D++,--Ue[E]===0){if(E===I)break;E=v[y+R[D]]}if(E>V&&(Q&Te)!==J){for(L===0&&(L=V),be+=P,q=E-L,Z=1<<q;q+L<I&&(Z-=Ue[q+L],!(Z<=0));)q++,Z<<=1;if(j+=1<<q,d===a&&j>t||d===o&&j>n)return 1;J=Q&Te,F[J]=V<<24|q<<16|be-S|0}}return Q!==0&&(F[be+Q]=E-L<<24|64<<16|0),C.bits=V,0},gi}var ma;function js(){if(ma)return Ft;ma=1;var r=or(),e=fo(),t=co(),n=Ns(),i=zs(),a=0,o=1,s=2,u=4,f=5,l=6,h=0,d=1,v=2,y=-2,w=-3,F=-4,S=-5,R=8,C=1,B=2,E=3,D=4,P=5,I=6,V=7,q=8,L=9,Z=10,j=11,Q=12,te=13,Y=14,J=15,Te=16,be=17,Ge=18,ze=19,Ne=20,Ue=21,Ve=22,qe=23,Pe=24,Ie=25,ae=26,$e=27,it=28,bt=29,je=30,ct=31,fr=32,mt=852,dt=592,We=15,me=We;function xt(T){return(T>>>24&255)+(T>>>8&65280)+((T&65280)<<8)+((T&255)<<24)}function mr(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new r.Buf16(320),this.work=new r.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function Yt(T){var M;return!T||!T.state?y:(M=T.state,T.total_in=T.total_out=M.total=0,T.msg="",M.wrap&&(T.adler=M.wrap&1),M.mode=C,M.last=0,M.havedict=0,M.dmax=32768,M.head=null,M.hold=0,M.bits=0,M.lencode=M.lendyn=new r.Buf32(mt),M.distcode=M.distdyn=new r.Buf32(dt),M.sane=1,M.back=-1,h)}function wt(T){var M;return!T||!T.state?y:(M=T.state,M.wsize=0,M.whave=0,M.wnext=0,Yt(T))}function xr(T,M){var p,K;return!T||!T.state||(K=T.state,M<0?(p=0,M=-M):(p=(M>>4)+1,M<48&&(M&=15)),M&&(M<8||M>15))?y:(K.window!==null&&K.wbits!==M&&(K.window=null),K.wrap=p,K.wbits=M,wt(T))}function Jt(T,M){var p,K;return T?(K=new mr,T.state=K,K.window=null,p=xr(T,M),p!==h&&(T.state=null),p):y}function Et(T){return Jt(T,me)}var wr=!0,Qt,at;function Mt(T){if(wr){var M;for(Qt=new r.Buf32(512),at=new r.Buf32(32),M=0;M<144;)T.lens[M++]=8;for(;M<256;)T.lens[M++]=9;for(;M<280;)T.lens[M++]=7;for(;M<288;)T.lens[M++]=8;for(i(o,T.lens,0,288,Qt,0,T.work,{bits:9}),M=0;M<32;)T.lens[M++]=5;i(s,T.lens,0,32,at,0,T.work,{bits:5}),wr=!1}T.lencode=Qt,T.lenbits=9,T.distcode=at,T.distbits=5}function Sr(T,M,p,K){var se,c=T.state;return c.window===null&&(c.wsize=1<<c.wbits,c.wnext=0,c.whave=0,c.window=new r.Buf8(c.wsize)),K>=c.wsize?(r.arraySet(c.window,M,p-c.wsize,c.wsize,0),c.wnext=0,c.whave=c.wsize):(se=c.wsize-c.wnext,se>K&&(se=K),r.arraySet(c.window,M,p-K,se,c.wnext),K-=se,K?(r.arraySet(c.window,M,p-K,K,0),c.wnext=K,c.whave=c.wsize):(c.wnext+=se,c.wnext===c.wsize&&(c.wnext=0),c.whave<c.wsize&&(c.whave+=se))),0}function b(T,M){var p,K,se,c,N,z,g,k,O,re,_,$,Ce,qt,Le=0,De,Ze,ot,lt,pn,gn,He,St,tt=new r.Buf8(4),Wt,It,ia=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!T||!T.state||!T.output||!T.input&&T.avail_in!==0)return y;p=T.state,p.mode===Q&&(p.mode=te),N=T.next_out,se=T.output,g=T.avail_out,c=T.next_in,K=T.input,z=T.avail_in,k=p.hold,O=p.bits,re=z,_=g,St=h;e:for(;;)switch(p.mode){case C:if(p.wrap===0){p.mode=te;break}for(;O<16;){if(z===0)break e;z--,k+=K[c++]<<O,O+=8}if(p.wrap&2&&k===35615){p.check=0,tt[0]=k&255,tt[1]=k>>>8&255,p.check=t(p.check,tt,2,0),k=0,O=0,p.mode=B;break}if(p.flags=0,p.head&&(p.head.done=!1),!(p.wrap&1)||(((k&255)<<8)+(k>>8))%31){T.msg="incorrect header check",p.mode=je;break}if((k&15)!==R){T.msg="unknown compression method",p.mode=je;break}if(k>>>=4,O-=4,He=(k&15)+8,p.wbits===0)p.wbits=He;else if(He>p.wbits){T.msg="invalid window size",p.mode=je;break}p.dmax=1<<He,T.adler=p.check=1,p.mode=k&512?Z:Q,k=0,O=0;break;case B:for(;O<16;){if(z===0)break e;z--,k+=K[c++]<<O,O+=8}if(p.flags=k,(p.flags&255)!==R){T.msg="unknown compression method",p.mode=je;break}if(p.flags&57344){T.msg="unknown header flags set",p.mode=je;break}p.head&&(p.head.text=k>>8&1),p.flags&512&&(tt[0]=k&255,tt[1]=k>>>8&255,p.check=t(p.check,tt,2,0)),k=0,O=0,p.mode=E;case E:for(;O<32;){if(z===0)break e;z--,k+=K[c++]<<O,O+=8}p.head&&(p.head.time=k),p.flags&512&&(tt[0]=k&255,tt[1]=k>>>8&255,tt[2]=k>>>16&255,tt[3]=k>>>24&255,p.check=t(p.check,tt,4,0)),k=0,O=0,p.mode=D;case D:for(;O<16;){if(z===0)break e;z--,k+=K[c++]<<O,O+=8}p.head&&(p.head.xflags=k&255,p.head.os=k>>8),p.flags&512&&(tt[0]=k&255,tt[1]=k>>>8&255,p.check=t(p.check,tt,2,0)),k=0,O=0,p.mode=P;case P:if(p.flags&1024){for(;O<16;){if(z===0)break e;z--,k+=K[c++]<<O,O+=8}p.length=k,p.head&&(p.head.extra_len=k),p.flags&512&&(tt[0]=k&255,tt[1]=k>>>8&255,p.check=t(p.check,tt,2,0)),k=0,O=0}else p.head&&(p.head.extra=null);p.mode=I;case I:if(p.flags&1024&&($=p.length,$>z&&($=z),$&&(p.head&&(He=p.head.extra_len-p.length,p.head.extra||(p.head.extra=new Array(p.head.extra_len)),r.arraySet(p.head.extra,K,c,$,He)),p.flags&512&&(p.check=t(p.check,K,$,c)),z-=$,c+=$,p.length-=$),p.length))break e;p.length=0,p.mode=V;case V:if(p.flags&2048){if(z===0)break e;$=0;do He=K[c+$++],p.head&&He&&p.length<65536&&(p.head.name+=String.fromCharCode(He));while(He&&$<z);if(p.flags&512&&(p.check=t(p.check,K,$,c)),z-=$,c+=$,He)break e}else p.head&&(p.head.name=null);p.length=0,p.mode=q;case q:if(p.flags&4096){if(z===0)break e;$=0;do He=K[c+$++],p.head&&He&&p.length<65536&&(p.head.comment+=String.fromCharCode(He));while(He&&$<z);if(p.flags&512&&(p.check=t(p.check,K,$,c)),z-=$,c+=$,He)break e}else p.head&&(p.head.comment=null);p.mode=L;case L:if(p.flags&512){for(;O<16;){if(z===0)break e;z--,k+=K[c++]<<O,O+=8}if(k!==(p.check&65535)){T.msg="header crc mismatch",p.mode=je;break}k=0,O=0}p.head&&(p.head.hcrc=p.flags>>9&1,p.head.done=!0),T.adler=p.check=0,p.mode=Q;break;case Z:for(;O<32;){if(z===0)break e;z--,k+=K[c++]<<O,O+=8}T.adler=p.check=xt(k),k=0,O=0,p.mode=j;case j:if(p.havedict===0)return T.next_out=N,T.avail_out=g,T.next_in=c,T.avail_in=z,p.hold=k,p.bits=O,v;T.adler=p.check=1,p.mode=Q;case Q:if(M===f||M===l)break e;case te:if(p.last){k>>>=O&7,O-=O&7,p.mode=$e;break}for(;O<3;){if(z===0)break e;z--,k+=K[c++]<<O,O+=8}switch(p.last=k&1,k>>>=1,O-=1,k&3){case 0:p.mode=Y;break;case 1:if(Mt(p),p.mode=Ne,M===l){k>>>=2,O-=2;break e}break;case 2:p.mode=be;break;case 3:T.msg="invalid block type",p.mode=je}k>>>=2,O-=2;break;case Y:for(k>>>=O&7,O-=O&7;O<32;){if(z===0)break e;z--,k+=K[c++]<<O,O+=8}if((k&65535)!==(k>>>16^65535)){T.msg="invalid stored block lengths",p.mode=je;break}if(p.length=k&65535,k=0,O=0,p.mode=J,M===l)break e;case J:p.mode=Te;case Te:if($=p.length,$){if($>z&&($=z),$>g&&($=g),$===0)break e;r.arraySet(se,K,c,$,N),z-=$,c+=$,g-=$,N+=$,p.length-=$;break}p.mode=Q;break;case be:for(;O<14;){if(z===0)break e;z--,k+=K[c++]<<O,O+=8}if(p.nlen=(k&31)+257,k>>>=5,O-=5,p.ndist=(k&31)+1,k>>>=5,O-=5,p.ncode=(k&15)+4,k>>>=4,O-=4,p.nlen>286||p.ndist>30){T.msg="too many length or distance symbols",p.mode=je;break}p.have=0,p.mode=Ge;case Ge:for(;p.have<p.ncode;){for(;O<3;){if(z===0)break e;z--,k+=K[c++]<<O,O+=8}p.lens[ia[p.have++]]=k&7,k>>>=3,O-=3}for(;p.have<19;)p.lens[ia[p.have++]]=0;if(p.lencode=p.lendyn,p.lenbits=7,Wt={bits:p.lenbits},St=i(a,p.lens,0,19,p.lencode,0,p.work,Wt),p.lenbits=Wt.bits,St){T.msg="invalid code lengths set",p.mode=je;break}p.have=0,p.mode=ze;case ze:for(;p.have<p.nlen+p.ndist;){for(;Le=p.lencode[k&(1<<p.lenbits)-1],De=Le>>>24,Ze=Le>>>16&255,ot=Le&65535,!(De<=O);){if(z===0)break e;z--,k+=K[c++]<<O,O+=8}if(ot<16)k>>>=De,O-=De,p.lens[p.have++]=ot;else{if(ot===16){for(It=De+2;O<It;){if(z===0)break e;z--,k+=K[c++]<<O,O+=8}if(k>>>=De,O-=De,p.have===0){T.msg="invalid bit length repeat",p.mode=je;break}He=p.lens[p.have-1],$=3+(k&3),k>>>=2,O-=2}else if(ot===17){for(It=De+3;O<It;){if(z===0)break e;z--,k+=K[c++]<<O,O+=8}k>>>=De,O-=De,He=0,$=3+(k&7),k>>>=3,O-=3}else{for(It=De+7;O<It;){if(z===0)break e;z--,k+=K[c++]<<O,O+=8}k>>>=De,O-=De,He=0,$=11+(k&127),k>>>=7,O-=7}if(p.have+$>p.nlen+p.ndist){T.msg="invalid bit length repeat",p.mode=je;break}for(;$--;)p.lens[p.have++]=He}}if(p.mode===je)break;if(p.lens[256]===0){T.msg="invalid code -- missing end-of-block",p.mode=je;break}if(p.lenbits=9,Wt={bits:p.lenbits},St=i(o,p.lens,0,p.nlen,p.lencode,0,p.work,Wt),p.lenbits=Wt.bits,St){T.msg="invalid literal/lengths set",p.mode=je;break}if(p.distbits=6,p.distcode=p.distdyn,Wt={bits:p.distbits},St=i(s,p.lens,p.nlen,p.ndist,p.distcode,0,p.work,Wt),p.distbits=Wt.bits,St){T.msg="invalid distances set",p.mode=je;break}if(p.mode=Ne,M===l)break e;case Ne:p.mode=Ue;case Ue:if(z>=6&&g>=258){T.next_out=N,T.avail_out=g,T.next_in=c,T.avail_in=z,p.hold=k,p.bits=O,n(T,_),N=T.next_out,se=T.output,g=T.avail_out,c=T.next_in,K=T.input,z=T.avail_in,k=p.hold,O=p.bits,p.mode===Q&&(p.back=-1);break}for(p.back=0;Le=p.lencode[k&(1<<p.lenbits)-1],De=Le>>>24,Ze=Le>>>16&255,ot=Le&65535,!(De<=O);){if(z===0)break e;z--,k+=K[c++]<<O,O+=8}if(Ze&&(Ze&240)===0){for(lt=De,pn=Ze,gn=ot;Le=p.lencode[gn+((k&(1<<lt+pn)-1)>>lt)],De=Le>>>24,Ze=Le>>>16&255,ot=Le&65535,!(lt+De<=O);){if(z===0)break e;z--,k+=K[c++]<<O,O+=8}k>>>=lt,O-=lt,p.back+=lt}if(k>>>=De,O-=De,p.back+=De,p.length=ot,Ze===0){p.mode=ae;break}if(Ze&32){p.back=-1,p.mode=Q;break}if(Ze&64){T.msg="invalid literal/length code",p.mode=je;break}p.extra=Ze&15,p.mode=Ve;case Ve:if(p.extra){for(It=p.extra;O<It;){if(z===0)break e;z--,k+=K[c++]<<O,O+=8}p.length+=k&(1<<p.extra)-1,k>>>=p.extra,O-=p.extra,p.back+=p.extra}p.was=p.length,p.mode=qe;case qe:for(;Le=p.distcode[k&(1<<p.distbits)-1],De=Le>>>24,Ze=Le>>>16&255,ot=Le&65535,!(De<=O);){if(z===0)break e;z--,k+=K[c++]<<O,O+=8}if((Ze&240)===0){for(lt=De,pn=Ze,gn=ot;Le=p.distcode[gn+((k&(1<<lt+pn)-1)>>lt)],De=Le>>>24,Ze=Le>>>16&255,ot=Le&65535,!(lt+De<=O);){if(z===0)break e;z--,k+=K[c++]<<O,O+=8}k>>>=lt,O-=lt,p.back+=lt}if(k>>>=De,O-=De,p.back+=De,Ze&64){T.msg="invalid distance code",p.mode=je;break}p.offset=ot,p.extra=Ze&15,p.mode=Pe;case Pe:if(p.extra){for(It=p.extra;O<It;){if(z===0)break e;z--,k+=K[c++]<<O,O+=8}p.offset+=k&(1<<p.extra)-1,k>>>=p.extra,O-=p.extra,p.back+=p.extra}if(p.offset>p.dmax){T.msg="invalid distance too far back",p.mode=je;break}p.mode=Ie;case Ie:if(g===0)break e;if($=_-g,p.offset>$){if($=p.offset-$,$>p.whave&&p.sane){T.msg="invalid distance too far back",p.mode=je;break}$>p.wnext?($-=p.wnext,Ce=p.wsize-$):Ce=p.wnext-$,$>p.length&&($=p.length),qt=p.window}else qt=se,Ce=N-p.offset,$=p.length;$>g&&($=g),g-=$,p.length-=$;do se[N++]=qt[Ce++];while(--$);p.length===0&&(p.mode=Ue);break;case ae:if(g===0)break e;se[N++]=p.length,g--,p.mode=Ue;break;case $e:if(p.wrap){for(;O<32;){if(z===0)break e;z--,k|=K[c++]<<O,O+=8}if(_-=g,T.total_out+=_,p.total+=_,_&&(T.adler=p.check=p.flags?t(p.check,se,_,N-_):e(p.check,se,_,N-_)),_=g,(p.flags?k:xt(k))!==p.check){T.msg="incorrect data check",p.mode=je;break}k=0,O=0}p.mode=it;case it:if(p.wrap&&p.flags){for(;O<32;){if(z===0)break e;z--,k+=K[c++]<<O,O+=8}if(k!==(p.total&4294967295)){T.msg="incorrect length check",p.mode=je;break}k=0,O=0}p.mode=bt;case bt:St=d;break e;case je:St=w;break e;case ct:return F;case fr:default:return y}return T.next_out=N,T.avail_out=g,T.next_in=c,T.avail_in=z,p.hold=k,p.bits=O,(p.wsize||_!==T.avail_out&&p.mode<je&&(p.mode<$e||M!==u))&&Sr(T,T.output,T.next_out,_-T.avail_out),re-=T.avail_in,_-=T.avail_out,T.total_in+=re,T.total_out+=_,p.total+=_,p.wrap&&_&&(T.adler=p.check=p.flags?t(p.check,se,_,T.next_out-_):e(p.check,se,_,T.next_out-_)),T.data_type=p.bits+(p.last?64:0)+(p.mode===Q?128:0)+(p.mode===Ne||p.mode===J?256:0),(re===0&&_===0||M===u)&&St===h&&(St=S),St}function W(T){if(!T||!T.state)return y;var M=T.state;return M.window&&(M.window=null),T.state=null,h}function H(T,M){var p;return!T||!T.state||(p=T.state,(p.wrap&2)===0)?y:(p.head=M,M.done=!1,h)}function ee(T,M){var p=M.length,K,se,c;return!T||!T.state||(K=T.state,K.wrap!==0&&K.mode!==j)?y:K.mode===j&&(se=1,se=e(se,M,p,0),se!==K.check)?w:(c=Sr(T,M,p,p),c?(K.mode=ct,F):(K.havedict=1,h))}return Ft.inflateReset=wt,Ft.inflateReset2=xr,Ft.inflateResetKeep=Yt,Ft.inflateInit=Et,Ft.inflateInit2=Jt,Ft.inflate=b,Ft.inflateEnd=W,Ft.inflateGetHeader=H,Ft.inflateSetDictionary=ee,Ft.inflateInfo="pako inflate (from Nodeca project)",Ft}var yi,xa;function vo(){return xa||(xa=1,yi={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}),yi}var bi,wa;function Ms(){if(wa)return bi;wa=1;function r(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}return bi=r,bi}var Sa;function Is(){if(Sa)return Tr;Sa=1;var r=js(),e=or(),t=lo(),n=vo(),i=ji(),a=ho(),o=Ms(),s=Object.prototype.toString;function u(h){if(!(this instanceof u))return new u(h);this.options=e.assign({chunkSize:16384,windowBits:0,to:""},h||{});var d=this.options;d.raw&&d.windowBits>=0&&d.windowBits<16&&(d.windowBits=-d.windowBits,d.windowBits===0&&(d.windowBits=-15)),d.windowBits>=0&&d.windowBits<16&&!(h&&h.windowBits)&&(d.windowBits+=32),d.windowBits>15&&d.windowBits<48&&(d.windowBits&15)===0&&(d.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new a,this.strm.avail_out=0;var v=r.inflateInit2(this.strm,d.windowBits);if(v!==n.Z_OK)throw new Error(i[v]);if(this.header=new o,r.inflateGetHeader(this.strm,this.header),d.dictionary&&(typeof d.dictionary=="string"?d.dictionary=t.string2buf(d.dictionary):s.call(d.dictionary)==="[object ArrayBuffer]"&&(d.dictionary=new Uint8Array(d.dictionary)),d.raw&&(v=r.inflateSetDictionary(this.strm,d.dictionary),v!==n.Z_OK)))throw new Error(i[v])}u.prototype.push=function(h,d){var v=this.strm,y=this.options.chunkSize,w=this.options.dictionary,F,S,R,C,B,E=!1;if(this.ended)return!1;S=d===~~d?d:d===!0?n.Z_FINISH:n.Z_NO_FLUSH,typeof h=="string"?v.input=t.binstring2buf(h):s.call(h)==="[object ArrayBuffer]"?v.input=new Uint8Array(h):v.input=h,v.next_in=0,v.avail_in=v.input.length;do{if(v.avail_out===0&&(v.output=new e.Buf8(y),v.next_out=0,v.avail_out=y),F=r.inflate(v,n.Z_NO_FLUSH),F===n.Z_NEED_DICT&&w&&(F=r.inflateSetDictionary(this.strm,w)),F===n.Z_BUF_ERROR&&E===!0&&(F=n.Z_OK,E=!1),F!==n.Z_STREAM_END&&F!==n.Z_OK)return this.onEnd(F),this.ended=!0,!1;v.next_out&&(v.avail_out===0||F===n.Z_STREAM_END||v.avail_in===0&&(S===n.Z_FINISH||S===n.Z_SYNC_FLUSH))&&(this.options.to==="string"?(R=t.utf8border(v.output,v.next_out),C=v.next_out-R,B=t.buf2string(v.output,R),v.next_out=C,v.avail_out=y-C,C&&e.arraySet(v.output,v.output,R,C,0),this.onData(B)):this.onData(e.shrinkBuf(v.output,v.next_out))),v.avail_in===0&&v.avail_out===0&&(E=!0)}while((v.avail_in>0||v.avail_out===0)&&F!==n.Z_STREAM_END);return F===n.Z_STREAM_END&&(S=n.Z_FINISH),S===n.Z_FINISH?(F=r.inflateEnd(this.strm),this.onEnd(F),this.ended=!0,F===n.Z_OK):(S===n.Z_SYNC_FLUSH&&(this.onEnd(n.Z_OK),v.avail_out=0),!0)},u.prototype.onData=function(h){this.chunks.push(h)},u.prototype.onEnd=function(h){h===n.Z_OK&&(this.options.to==="string"?this.result=this.chunks.join(""):this.result=e.flattenChunks(this.chunks)),this.chunks=[],this.err=h,this.msg=this.strm.msg};function f(h,d){var v=new u(d);if(v.push(h,!0),v.err)throw v.msg||i[v.err];return v.result}function l(h,d){return d=d||{},d.raw=!0,f(h,d)}return Tr.Inflate=u,Tr.inflate=f,Tr.inflateRaw=l,Tr.ungzip=f,Tr}var mi,Fa;function Us(){if(Fa)return mi;Fa=1;var r=or().assign,e=Bs(),t=Is(),n=vo(),i={};return r(i,e,t,n),mi=i,mi}var Vs=Us();const Zn=is(Vs);var ka="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Hr=new Uint8Array(256);for(var bn=0;bn<ka.length;bn++)Hr[ka.charCodeAt(bn)]=bn;var qs=function(r){var e=r.length*.75,t=r.length,n,i=0,a,o,s,u;r[r.length-1]==="="&&(e--,r[r.length-2]==="="&&e--);var f=new Uint8Array(e);for(n=0;n<t;n+=4)a=Hr[r.charCodeAt(n)],o=Hr[r.charCodeAt(n+1)],s=Hr[r.charCodeAt(n+2)],u=Hr[r.charCodeAt(n+3)],f[i++]=a<<2|o>>4,f[i++]=(o&15)<<4|s>>2,f[i++]=(s&3)<<6|u&63;return f},Ws=function(r){for(var e="",t=0;t<r.length;t++)e+=String.fromCharCode(r[t]);return e},po=function(r){return Ws(Zn.inflate(qs(r)))},Ks=function(r,e,t){for(var n="",i=0,a=e-r.length;i<a;i++)n+=t;return n+r};const Ls="eJyFWdtyGjkQ/RVqnnar8Bb4lpg3jEnCxgEvGDtxKg9iphm01oyILrZxKv++mrGd3az6KC8UnNa0+nrUGr5lI11VVLtskF198FaU1Dns9w9OOkf7/ePDrJu90bWbiorCgpH2RpLZO9WqaCReqZ8lnReJqKTa/SwL8DXJctPs9Lxs4oSS+bAuVVjXC7/tG/lAxYV0+SYbOOOpm402wojckVlQ8+T4wVFdUDHXlaifrTs91Q/Z4PNeMLu7t3/U6746POm+7vW/dLNlWGuUrOlCW+mkrrPBXr/X+4/gciPz25qszQbhyeyKjG2XZb3ewR+9Xi/sMdVO5k+ebHemcaHzW/57p3/y+qQbPk967We//TxoP191hoVeUWexs44q25nUuTZbbYSj4o9OZ6hUZ97osZ05WTJ3AQ37jMOqQtblIt9QG7lWycKJuhCmeJGGhSOxffccyqPj/W728eXX4cFJNxvavAmRyQbH++HnGf34vdc/etXNFq54d50NXh+2X6/C137v+CnQH8gZmYdQfP6WXX8MCppQTYMlditCBL53/wfTQ65EFeNfvQ6erlQsqX21akJc1rGs0EoJE+NbMnlToZFAVEFkQ3iABW2uGH3CUK1ojUTgMWEbjfaWeUp5G6N5aCwRw5vddkOM98EVqRlPrBJ2E8OPZHSM6prJkrtnVrqNIWbtOjQrg8o7Zq2VDwxId5x3xMe0lpzBuVaa0WGpkkCkmgaON/3qBVODpaHQiIybXz3ZliTi3DO2D2PoNIZGMXQWQ+MYehNDb2PoXQxNYujPGHofQ+cx9CGGpjE0i6GLGPorhuYxtIihyxhaxtBVDF3H0McY+hRDNzG0CqfQLTmeNlZBBvr0+TnIKbmUuTS5Z1jUN6xtw8nBtEjLb7wxDOesmB5j+JfpIIYLmIZiWC6GZAz9HUMMvTItzESL6VqG9rZMKGOI4QaGXpjY+xi6i6H7GGKYdMeQPl9foBBW3GHark9Vo5OqgEd9oe+ZOPOnc3NcqmZgiUuomehYnt1xZ8daaSPZ8wBoyb0Jx3jOBLBtGyvbiRNOLXw0Sy+DpNKAAhpxq/gXYhD6NdMda6bwwyTH0kwhypI70p5wdhR7Gjia3JEhpvfDLCRKI7YcqYXJnxgv/g3vSthEhNNSEKIfCQByUkpurWQaNXjqNtqjSfHp0OdLOwSAG31E7h03uLRMvlbEtDPoq0rkhqvhlSFu40I7kfP9VoRLFrH+G7YLcypCQLkJ1delML5SwjPb6DIMmQxL54L1gyq+YIfMyKNNsQ4zHj8UnoMDdoZwfoMqkJxX7A6Cj3czWzLdqcC+GuGM9tCa4RobSp5J2gTnk0D5CVA0Pp1RAqn7hC0o5J3kqvkTsGyY6gwBHlqmHtqBh2x77UI9QimVS75PljgMAjXDEljn0QNjvMlZIAju/pF0NH95VcFshSgnB3Ug+LhMkwYoVKOAUS+T2kZIG2DVcYInLXDTQkKUYHelH6kuGcEcbPE26aRPNklKOEQpNcCQHPp6k4jc5UYbRtkM7T4HcVsAvADWLtEGnq/M9t2G9e2Aw8xEM1CCQ4QDWq28cnKrmDHTAwcvgYNh1HJSqEKumdvVDlPDFOwjU8UyTpZZ4tTBohzYUSMaRAmdggBNgKLmzVsYGLjXbyujb6lm70CGSmnB1PsWJHuSYhQfupq/ioxBTRngkEaRuQEP3ICIPb/kAq/Axo6ZUEaQFFSStxwa/eDpiARDND4kqhIE+BG1Btp7hjKCjh6UKYt2xk7MkmMJ8PCMlGNy5XiSdvc6wYjYtIp5pSGBRTo9Z45R6Asw4bQ8HgrYhEJmTFsk6pWvyPfJOj4HiXNGFFQJw1hOCVaYgChNUOGcA6tD0DZCMSdDczMBDa5TFVWDqWn5i/yB+BByqARcGhx6ziqXVD4Ii2TqZmnLi8AS3L8dGqRoBIzwkM0LmXNpOAOKTNKbKciPBvg8XdZJ6RDoHEKO5meuGdDzmOiQMTrt0d63SVfAIDBJtgIwwaUvN7ps8l1r7v0I5lKPRUEV+rcqfaHlDvJH4FSdVBVCjk8IiXp87Jv/Ib90s/dk6gshTfPv8Zfv/wDUfBK2",Gs="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",Hs="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",Xs="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",Zs=JSON.parse('"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"'),Ys=JSON.parse('"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"'),Js=JSON.parse('"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"'),Qs=JSON.parse('"eJyNnVtzG8mxrf+KAk/nRGh8eBWleZPnItsaj0ZXWNvhB5BsUdgE0TLAFgjt2P/9AI2uzJUrV7X8olB/q4CuyspaVX0p8H8mP7V3d83yfvLj5MPfu/Xspnl0enH05Nmjs6dHz84mjye/tsv732d3za7AX5rF1+Z+fjXb426xUHh2N19shTBt5jef92f5e3M97+525K/3s8X86vnyZrEre7Q7Xv86f2iu/5jfX32e/Hi/6prHk58+z1azq/tm9bbZf/aXh/tmed1cv2nvZsuhbn/+c/sw+fGfPxw/efL4h5OT88fHR0dHj5+dHv/r8eT9rvBqMV82f7Tr+f28XU5+/GEng/Du8/zqdtms15Mfz3f8Q7Na98UmR0cnf9p90e4kv7e7Juyb81P7Zbvat+LR/7n6v4+Onz09f7z/96L/99n+32dH/b8Xj55ft5fNo7fb9X1zt3701+VVu/rSrmb3zfWfHj16vlg8erP/nvWjN826WX3dUQvVo/n60ezR/Wp23dzNVreP2k+Pfpsv2/vtl+aHXaHFo+cvHs2W1/+vXT2a775g3V2u59fz2WrerP+0q+wvu1Ndz5c3b68+N30f9DV5e7/7yGx1XdRdwZ9mX/4ydMnF8dPHk3+Uo/OT08eT5+urfaBXg/hzY8c/nBxdPJ68vb/+y3QnPun/+2H336dPD7319+Z+Nb/ahfOf/zOZ/mPy48nFTvh9V5H1l9kuiv/7mHDzcLWY3Rk/PT8/8H937S5alwtTzs+fHJRld3e576abZdau28VitjL+dNctPf/SrK72SV6EJ08uDsLsbietd9Hxmp2cQA36/vbanZ4O3zdbNctF86km0cdKNWbr/Teub73iT8+GTy26dQ7O1W5szvIpPm+/fG6WufiuKfP2OvP1Yrb+nIP1rVm1mbbLJsP7jSh5/3nViLKf2m4l6PyrKLuePwjYfG1E3zYhpp4O86VIq6t20YoIrZu7eZSsBxZ7E0i0+Xc3W2R8s2p2g1k0899ds+6NpijHR8dDRs9E+j3P6M+GLkom/pTRz/mDvzg6Pj6gX/2DJQIv8nf9Jcfpr96yvV3u0d/yGV/m9v/mY69k69/zGX/P9XqVv/6PXOp1/q43+YNvcyTe5Q++zx/8YOjZ2dDT01zqHxl9zGf8rxzVy91cdtvcB99wcafFgcqfi6Zy9sRM5Wo+v5qvrrq73B/d3rXXu+kHxkgwuFAZ+9gso8ucElfCgMW4zQ36lEvdZPQ5V3me0X/net3mUouclyJawnWE730Rwz6b9CrXSzi8iH2XP/g1Z+8ml3rIaJvRN6jqmedXTISTJ0clK1eV8jEbzRn7bLyfL66bHJLDXH/dbkScw/TsU8F9v0zz5DguI+7Tfl2IRmuf2arJ49OiXc0FXzeVb7nqVrt5/MoDePzsGIbNet6vW1MTy7JFD6ubbr5T7tp7vXTYy/0Xf0em0Jee/TQXTCygdis5uR64nt3cqDntwHEtRiuOfd81qwbG/umFLYZmN6vZFz/b6XnJrN0FRAMZF1ypb+blbD0S4XF1pRcL1gFR7y8ZDrFZLOZf1vO1kHZtvf/cdmGxaG5f5v2Q3N5zq9lXUdnZVXcv8MHLPy2ah6xVRtbd7GrVihNfrhp14uv2fnYVRpxL811PYgDc0HAcemV3l3O7NbdYpHbLm9mqu1vMOnGa9ma3zrwVXzdbhcWT9ctdyFkXnvuyZ3fdOnz56vrTbqEXVoa+QomTrC9AIvczvIIzPDm3M9ztnK5b4CnsamMmprzr/aBfr8UEtogntpRqI7cVSdvksrvxubsi3uW9mGL+mrrUnSBmoE//MW98apKd6l8Xe89XR7kGZbq4nn+dQ0L7R2LNfMEsBodXO37IV3rqQzZFUgxssu4vvmiYQFPzV/r5wlBxXO+IGY0H/0ylhzr6gF8FpJP4NcPOI+Ai5KQ4sWroRXHwq3LTQ5yKXMfXhTEPvJU6Lr+rCvjwqOVoNFVf6cvm2KVU7duisUI4k1VChsxk89fsiTYU5/HsZxdDnRftt2Z5IzL3TTyFX8WNJmc3OkiE6MOrNpGsKm294rb69U+OnJ3m3ed2JVr1is7uYai4wVviZ2USo7DZaOKMtYjpya2/w7Hu+lXOStSXUtCiWONkq8UE77rF/fzLYivqRQ30JA8NPLsolyaz1f18trief/qU+2pbt4bf43k8YceS5ZfRNBuZdbJk6VQZnsuaDdYy5vcYIJ8M6Yvw/ttuxYA34ewSaNXeNku8EDJzXDU383Vc+voQjZ0N03EeF+Yc3W5Uh+sRD3ZlDbmqRKalyPi4rKTUf9EIP3tW1q79ra54I8zi/Mv95wx/SgZoZq586/R4aON9Zd5oqrNjbRZ8Xls+jGRlDLBfL9PQsFsRXClzhVqP1Kae2jS6rg3KPI7t3KPLEp4xy7qgWdyLGz73waTdEzftiCPW43vXiZZQzC1Ucp3pY4FC71eqcYXztNyw6H18l8CrXSKv8/e9Tfn67FnJV72ifTk6//4WO84vJeyxjjLFZAtuGTFMzmvT2W+x2haHXdQ+zxYwNZRBvr80oVvd1hdjLr+MyyZPte90YGUoNLUG3UQzxQYN3ap6VffdW7lAtAyWNT8rPXi9swn10KONXQRWqC2ti+XPzs3Or+dXymh/jl8EC7Ox5e7vsX+8upV+ezOe10p1b60soZ9XTTpeDlgPUJ3NiEcWlL/Upnt2CrtFLBtqC7K4ErBvGx0KlSrcj55p0d7s+3vZinC3dTPtslSG8u6rKiP5ZvyKFmZyj3klfZdyHrebO8u8aHbPr43xX7r948h/PZ68bFbLP2bz1f4h8j8nz/cPqyePfzg9+tfj4ejgHgEd6hnRYOoIX8Sjg6sEhA1D4VU8ylXAqTdw66pAD+M/oOA8QRlCFtjh7lBAh4GD6HU4erc7Oj7xwxK0wEL7QXm/Ozz3oxIAQCEAwCEAQEsAAFEAQLEAABuGdmC9oyD5sDu6sKNp0D7uG3jkh6VJyKDugLsQ1i4nQ1dJhk4mQ5eToasmQyeSobNYABti4eTr7ujMjjbhaBuPSosAQc0HOhTC0WmIQmKcxqlzHqxFeSEQDVvjcuwW9ZVAlVrKoWxi7kKTqB+N6840mXvUBBrjxmmgF/46IxzyxnjcuyAHf5HBAQyRDRhXXmBiNgSTyBWMa2swmf3BBDYJE6JTFAx2UdA0l0LjMMbuYYKwkKJ1ucPYTIyP5aSwFZMqOVkxGJNrOZmsxoToNwWD6RS0yWgrEHmQ8WxEgx+gERmicBonI3LORlSUFwKRERmXRlTUVwJVaimNyMTc6SZRpxvXnW4yd7oJZETGyYgKf50RGpExNiIXpBEVGYzIEBmRcWVEJmYjMomMyLg2IpPZiExgIzIhGlHBYEQFTXMpNCJjbEQmCCMqWpc7jI3I+FhOCiMyqZKTFSMyuZaTyYhMiEZUMBhRQZuMtgKRERnPRoShQTeKnAIbRfIlEtmcgvyixsmmoii9KhR5VeNjDZHWFUvkXIk6JUwUddbEMpw6USVPiyIZWxBfVzhaXBTY50iVZhfKgONFTrYXReV9sUQ2wKiTC0ZRW2Esw34YVTbFqEZnDBrYY+DTSnk0yiiwW0ZVWGYo0FVSgc0zit8dGsJGoz42NCqGGsuMDo1krVGN/ho0MNnANxW+rXHy3Chm43WCtouUOgQlstwgseGC+EJTMluUpNVCgVea1qsuTRb1nEeoUhahpHMIS3AGoUbWihIZK0ivJUVTRcyWGjRpqFAC7BQpmSlKykpRz0aKKtkoStpEsQRbKGpsoKhF+wQFzBPoVJZF40TMtomaME2QO9nNbJgofSfJhVmiWk/yilFiiZEkTyaJWrRIUMAggW4k3WpK1ohSNsah9eiKhijkxskPnbMZFuWFQGSDxqUHFvWVQJVaSuszMaeESZQPxnUymMyZYAJ5nXEyusJfZ4QWZ4z9zQVpbkUGZzNEtmZceZqJ2dBMIjczrq3MZPYxE9jETIgOVjDYV0HTXAqNyxi7lgnCsorW5Q5jszI+lpPCpkyq5GTFoEyu5WSyJhOiLxUMplTQJqOtQORFxrMRlbqiEzmjgLpAXgQCm5FJLxQjO3JB+pHJrxSrVVZakqu5/12jBHBBZ4DrnAKukC+5QMZkwmvB0JocsjeBIs3JdHAnZ2RPLih/cjUblGvkUC5oi3KdPcoVNilXoksZB5syNhXl0KgcslO5IqzKxE50IZuVC6PpKuzKtVq6VgzL9Wq6JstyJXqWcTAtYxvBtoqRb7mQjatUDI3LGQXXBTIuENi4THqhGBmXC9K4TH6lWK2y0rhczZngGmWCCzoTXOdMcIWMywUyLhNeC4bG5ZCNCxRpXKaDcTkj43JBGZer2bhcI+NyQRuX62xcrrBxuRKNyzgYl7GpKIfG5ZCNyxVhXCZ2ogvZuFwYTVdhXK7V0rViXK5X0zUZlyvRuIyDcRnbCLZVjIzLhWxcq+GHPrwKhVBgCybLMsyGNQgvMiGzKlha1SC+ykRXT5pU0XKfF4V6vGDd30Xl3i6crKlgMqYBv04ETakgtiTj0pAGFeyoEDKjgpUVFS0bUVHIhgrWJlRUtqDC2YAKj/YzUDCfgUxTGTSegth2ChemM0hd6h42nIJHEk+YTVF04lWMpqiVxEsmU3i0mIGCwQxkk8g2E7KWgrOxDOmOzmKIQmicvMU5m0tRXghE9mJc+ktRXwlUqaW0GBNzV5tEfW1cd7bJ3NsmkM8YJ6Mp/HVGaDXG2GtckGZTZHAbQ2Q3xpXfmJgNxyRyHOPackxmzzGBTceE6DoFg+0UNM2l0HiMsfOYIKynaF3uMDYf42M5KezHpEpOVgzI5FpOJgsyIXpQwWBCBW0y2gpEPmQ8GdGfh9w89iPvDEMhfsBD9xgtUQNEAQPFYgXMwgRsiJCTw+96Pf7hxMjw010F/QSFTo1YoQGVV+KoZMFcfBj+XLzgVLxYc/qACfSRn3fouXVPfxQ7s0fFxQGFPgKeurynpY8AWU8As54ANvQEkGKpjtwLfh5swLW9Azzzo9I6QFBlpyWA/rUWuQGVToDwGaIYGqdAGlfRNDGH1CSKq3EOrgkcYRNimA1TrAv/kMMwzaUg9IYo/sZzJ3Du0/lSd/T7CGN3FMTdUTh3R+GyO4oouqNI3B2Fp+4oQuqOIlB3FMzdMfAPOQzTXAq7oyDujsJFdwwSd8eAqTt+HXriqR+VUwEq8QcUQg8cKgS0BByQxRqYhRnYEGEgJbiOZrag6I/iCqpHpQWAxHqq52kp1dO4iuoRrZB6RoujnoV1UU9KCxyV3jp25CuM/iguYnoUV/4HlJc0PU+rmQO1bX8Bx/VNj2jt0jNatuxZG49yndtKBVtZwTbXpJWLrF4RdWzjcr9HcaX/K44JiP5qPy7P7cicyVGJPqKw2D8IfxtG2GH18TccYYBKfgIKkQIOkQJaIgXI4gHM+gzYkJ9ASogczeJRbsEst2BWacFMtmCWWzATLZiJFsxSC2a5BXGl9be8xNqjLnyky83sKm3qZJu63Ca+SABFtLYTre1Ca19ODtfh534U7a9Hwut6nryup9HreiQvFnuFXLBnZdQBipb3cvC3Mz+Kc/9L9DdEecZ/Sf6GFPwNcJz+XwZ/A0aT/svB3+Ao17mtVLCVFWxzTdjfQBF1NH8DFNceLyd4Tfxyki6HX6asBp5c+eUkXf++FFkNCrnyy0m+4H054WvdPdmGbNnmrN7m7A0GjOOCBPoyUtVooSLfPTWNIVL1cKJCPLJIpkFGKo23qEJ6kEAZTSoNSFZF6lORPAq4QB6xVIKGDKk8RkjmIR3ltiqMhkWOeSryvYazE5CqTYEKjbedrYJUco2ogoGQQF5CqrIVKpIdhgqQ2ZCqfYcKsQWRzG5EcjSmKG5rI4ztitRkH79NhhvLx35o95SRxdvJrnyIh/vLygs76u+unvmh3csDhjftHMcffc2S8SfGtxas3zAQgFKf/xb2VB8T4zjU9lST/EEwCEvYcXxGjAMkdxyTVgtV0DleuEuXYsGRU7t0B6nca8HwGePwuSDDh7eOmEH48N5RaWm6eZQEEb6i1cIXdA5fEbc5Fhw+4yJ8YXCGIEYlhZJkHdBQ6ENVweAGIYQ4KinQUVbhDiWqQc+lUuiTEcpopm6oGuFQYJimQjcY4w5wQYa+yB8Eg3AXhIE2xiE2QQS3aLWwBp0DWsRtjgUH0XgK327JC3cb+qN4EdujeK3eI3Fd2/N0XdvTeF3bI7p67RldvfYsXL32JF6rvxr6/syP4rLzVe5vFz7YjPpq6GOPyz92R0/s6KP1/yvsZkDQdKflNoKf0m4jDKj4NHSDIeoL49QhxlWvmJi7xiTqH+PcSSZwT5kQu8sw9RnO3xQG7r3K7E0q9CPO3acRQY/idH5BiPpWTeYkUS+nx34DD3snob8jp06PIvV8FFX3xxI5B6JOiRBFzoaockpENeZF1Cg50k5uFT1Ok7F93KoIJEzatnwqOKRO2s58oTglUXUzs9IpnYLGOQWbziCjkFI+oUTZhJLKJdRzJqFKeYQSZxFqnEOoxQxChfKHtqzmOHHu1Des5gKQN7Rj8zRRyBnax3mRKeVLZRdnVilXQOFMKVuCIE0MUY4YpwQxrrLDxJwaJlFeGOekMIEzwoSYDoYpF3BjH4WBs6CyrY9U6H/c8nYaEfQ87oK7IER9rvbAkUS9XTB3dbzNgT3OCnU8y9T/LKs04DI5G7gEJQXLnBusc4qwHjOFVUoYkt9VY8rpk2SVRVQIkokUyClSILVIgQxjhRKN5ZxvVILSjlTOPtvDA3nnjDLOBco1F1SWuZrzyzXKLBc4p1zhbHIl5pFzyqCwY48jwllT26/HMmRK2MJ2SgyyI2xru2BGGSE3tbFGWWCc+79cbkL3G6LeN06db1z1vYm5602injfOHW8C97sJsdsNU6/jbQgKA/d55SYEqdDjeAviNCLob7wrcUGIelvdkyCJ+rpg7urySih0tSHqauPU1cZVV5uYu9ok6mrj3NUmcFebELvaMHU1vthNYeCurrzWTSp0Nb7yfBoRdDW+BX1BiLpavQNNEnV1wdTVfwy9PLyS/gf2MLLSu8jii78g4D0xwPbqLzB/wxegv+ILsLzjC6j0ILBZaJm9oQIovkXYI/HkrufpcV1P4zO6HtEzt57RQ8aehfcEexIfuP0B/Xb81FBjmdQfxYdKPSoJiig/vu55euh0oPDEFXB8ft0jeh7dM3rfbc/aeJTr3FYq2MoKtrkm/DgUFFFHe/AJKD4d/wNHxxD91xN8A74/im/A90i8Ad/z9AZ8T+Mb8D2Sb8D3Cr0B3zN6A75n4Q34N5PDWw8nfhRdu0fCmHuePLmn0Y57JN9h6hXy6J6V+AOKDvxmMN9TP4qvNr7JluvCextEb7CXAIlR/oZ6CWgc5W9EL4FC4/9N6CVgYfy/CXPEmzA9vBmmAdc+xhH0hsx/oOUWJfS+IUoB4yoPTMzJYBJlhHGdFiZzbphACWKcsgRvMp8Sonyp3GQmFTLHEKWPcZVDJuZEMomyybhOKZM5r0zg5DIhZli+j25omgPxMZfirFM3zQepPFmF1DNEqWdcpZ6JOfVMotQzrlPPZE49Eyj1jFPq4QP2U0KUepXH66RC6hmi1DOuUs/EnHomUeoZ16lnMqeeCZx6JsTUwxcIKEOmORAfcylOPfX2QJHS+34nUuA0HHnfTxcRKanf99NqJT0r7/tpmVNVv+8nVUxb/UZDRZUpHMpgIkeB0zmqMqljEZHasQAneFQraR4LpWSPckr5KFPipxc9ZJZOa6H8WPtEGhDVtzwOBd5OeB/k20naB/mWn3US5uK8zYtwKp62OCYhfSQ0iz8WRProLr/xWrY/pGvZntG1bM/UtWwv5GvZHtO1bM/4WraHfC3bw3gt2yO6ln03GNmZH8WR9C5ZFvA0Zt6hOQGSo+NdsCFg8d3Rd2g4jmaxETPRA3YN/sRRrQNmugNmogP8Mhy+V8V/luNvF+L+0at2AT78DpIRPkdVaERr7eI8sEpzG93ccH2OvITBq92IKDQqCp+3Xz43fb2889pYpBXNaWtVb3XVW9FTfOmOkqi+XbtjudxhfvHuxVZ0KBq0Ev1De7hAWTd385wZXSzUidN0tbh1Om6diBu/6IySiFunur3Lyb+Jh1s6FI3BV86G8+EG61Iy7bBOAnux3mOdVBGytMs6Ccmf8z7rpFCw0k5rFsCz09UPc+Xe6uqHJfLx2tUPy+zo6eqHOXl74TPR7OTyJpDVGx/tX2X6ptX6N9m/CdXu5YnAOJmLr3rCwOfFEH+NqmWaIUxgG3JhLFRqwnBNzBom0tRhvBbGPIkUJc0kRWhF4TSnmDDaTjW7mFZLido8Y3qtrWnGMaGSGWnuKcJKsVoI0lTkgpyPiiwmpSJ1oniankwY7QI1UZlW64LalGV6rQvy5GVKZdRuBNsqVmu+mtXCBUeY26KSvjPKaZ6Lso57LKOiH0ukPohynv+ingMedQ57VNOMmG7KnEmBZ8eRmzK6iJgp9U0ZrVZmzcpNGS3zDKpvykh1Vg1ZnlOjzDNrVP+DjJKzbCwxnlF5xo3ydxIqzb5RZael+wLR7eRNA/3F9fbkuTnKyZ1J/n7I5WxNJdScHYvwzB3V8U4Rs3jQ81we5Lb6wTyvR/k/iI6c42OJ8YSszvex1HiE8twf5dG8zOuAIK/qynj48sqAZL0+CIXUKiEU6KofzSuGKP8HnStXD7HEeOdWVxKx1HjnilVF1Ec9aVNVtnVlPHR5zfF+WGic+VGcI9/jggKQmCvf08IBaJwV34cFAjC69/0eFgJA4hT3fsKvH72fpDePypMBbGvaIcWcWq13SLGY2592SDHnSOQdUizEmKQdUsQpOpWdReHxCMZJ7yySIkVsZGeRLJFjp3cWSZGjWNlZJNUYT72zSIkU2bFNNvBoCYOrNtkIiQJb3WQj9BxUtclGSBxQuclGaDGYapNNliiQ9R0o5QEcxjDtQGFO0dM7UFjMcUs7UJhzxPIOFBZirNIOFOIUpcrOjfe8PaGuUMBqOzcqsgrfyM6NSgkKZnXnRkXn0FZ2blRUCvTolgVWOexhy0JiFOq8ZSEJKrxyy0LSKKRiy0JSOIxpy0LiFLrKG/7OOVz2xw6eK0bhcoHC5YIKl6s5XK5RuFzgcLnC4XIlhss5hcsECpdxDtfwA+7PM6FQFUyBKliFqWg5SEWhEBXMASqcw1N4DE6hFJoBU2AGSmH5MITkqR+VcACKvyX6IYUBePot0Q/YfED0W6IfQrOBhd8S/YDNdfQitOdF7LkexSsuF17Fo5gKPRL93fPU1z2N/dwjecuqV6j3exZvTvUodu8e+W/E9kdxu0GPSggAhYYAT5sSeloaAsiqC8x6DNjQY0BKCxyVK9szIOU20HlBsZObnJh2jweRTsxGJma4fwM452sj8rUR+Wp3ZBy18Sg3o63UuZV1bnPl+E4KKKLadssEUB5TcG/EOgTvSXjPdaFnu5yNXSX1Opl6XU49vpkAikjKTiRlF5JyOrjeuR/F17Wm6HqAxC6IKbke0LgLYhpcDxjtx5iC6wGJ2x+mE3x1coreBUi8JDkl7wIaX4ecCu8ChV58nKJ3AYqvOE4nuOdnit4FKNr3NHkX8GTCU/QuQGS10+BdwMIEO0XvclSc6qmRvX7qR6VNgOI7m9NkVMDT7wNN2agAx1/amwajAkY/lzdFo/KGtaFAm5uhfgl6SkYFNFdO/xL0NBgVsPhL0FM0KkdmVN4fXSjQ5YaoXzmckiMBzQ3RP2I4DY4ETMQ//kbhnmzDONnm8bxN4/bjYGTDD2V9RCdDZg80gcW7wCDgrV/Adr8XmD+kBOg3bQGWO7WA7Bmks72lPT23I5sqHcWp0jlOlU5tqnTEU6UrPlU6s6nSkU2Vhvply5k1wh8FAosLso/J1LBs/pWyj2hryGj6+xh8DQuW+AOy+Dsrk/sTIMOyzAvRZxrRXH/4hqzS3ka3Nz5bAy7i0Ig4NCoO/MBsz+ZhOM3juOsRbVb+OHglfEcrgtDWGtzqBreiYekZGUg++ADa4zBgoqN9decMV3eeAF0coZ2wl65mJZ22kk5YSXpeBJJymU65TEcugw8tEuOGpMcWSZDNVA8uksYNzo8ukpLaxw8vEmc/xe2Fg1ml7YXMlb2q7YUskdHWtheyzJabthcyJ/MtHB3KGA9JE8iLjcvxaqoYtKbxyDWBfcmEZE6mkFMbjxfRxmnE8jMr/hpVy+TgJrCNuzAWKmXorglXN7EWx+TvJlTjmJy+CGD3hsjzjbPxF6EV35umABNGg6UmA9Nq8ahNC6bX4pXmBxN4kkhPOVkQ00WRcM4wVvNbPXuYOmKraR4xoTKZmF613TytmEK2S48jFeYWqweSSpMBqTySVDKHRT6UVGJqvHgsqSSeeUCDyQcpzT8oqSkI9TwLoUoTEUp6LsISPB2hRjMSSjQpgYQOjJitAjWanVCSRoIFhJegzHaCGjsGaslkUaT5CiWassLz6mAc4km2+L5K1dP0hRrPYEH7TjjVPBZkMZWhPhLuNKGhNhbuNK2BBjMbUprcUOL5DbRWnyPNcqh9L6BqrkN5JGC1GQ+LpAsiFHniQ43nPvX6hNDEDAgqToKIR2YFPRVigXHnTxMiapU5EYuMTQ55ZkQxTg77ncJfLav2R5tw5D+X3h/F+3M9SvfhimXi9xa0yWibP8inMZ7PNVgwnqugTUbb/EE+l/F8rvDOE5ww8E2Fbyvfw+ePYq4EdCVUAehG0q38Bj45SvnU5SUcOG9Bm4y2+YN8OuP5XPYuBZzM2Eawrfgsn8+FfEJ7GwFOaGwj2FZ8lk/oQj4h/M37k0A2iWzTp/hE4m/eDwr+WeuTiDYZbfMH+VTqz1ofpMsdvdxbix3FFxv2ZGHn7I/ihNej+PwfhPjM6nKS3pC4nOCfGLxEBwcknrRdkl8DjU/aLoU7g0JP2i6DFwMLP5x4GTrncoJPFy4x/oBSXa9wOXZeyG3Qb1Vkr9JdiPOAb3NJ/T1pz+Z5wLe5ZOV70i9blG8y4VaV1t92mPIn6y+zq30Izwfaf3H+OyJHWSYtRypeeBv6aqMijrT90UPQtkGj3uaBNlDdaYa5Pjghn8Zv2OQvfciltrkUV1TN0YOkU8Mw17ZwqC3uRKQvfciltrkU11btTCySeuu5VE2+9axErLxeHkT+UCm/rZRPDaotGwZdXbads8ItorXGafqqjTzBgyy7lWW5HZUVSFH9Mi41A6TUDtCwIYA3+iQPuvRWl06NQS23ht91Hmqb3nUmDi3Iyy9DD7nUNpfiGqsV2SClt16HauW3XlmACoslnLMHUW4rynGl5bJu0NLLp0Pl8sunLECtxTrQ2YMotxXluNZybXjQPk0O96NO/SguwnpU7j4BEu9K9Dwt1Xoa35XoEb0R0TN6I6Jn4Y2InsS3PT5BxJ3sxsOiWa/7mx9PC4wfanJD6Y2cA9INzW/kHGh6I6fHuf38Rk7PRPvbeJTrrF6/6bmsYJtrol+/6RVRR3r9pke5Q+gvcn/KS6AnURjoYdW0v77ot1n6kd0fcWQ38QDFLZQHoVxmrwSirzVO3+1cnUDsD5UCnay2O1Sr4sTxb8yJPy+n/7Lc7TASTvwo+sUtjgRE+XLmlkYCUhgJgONVzm0YCcDoWuZ2GAlwlOvcVirYygq2uSY8EkARdWzjVeItjoQB5V3rWqC2VPasV1TR6PqO9UqB3FeV/epa5ejU9qtLua0Ko2GRvV3fjK4LjDarkg61nehapiSpbEQ/qHeT4QaxH0XP6JH4qfKe411gp/Gnynskf6q8V+inyntGP1Xes/BT5XeDDR1WNXdoQ4Cwdj1dhsYuc2OXlcYuZWOXubHLamOXorFL0dhlamy8T7gMTV/mpvNScaDhkfhRRBSEygNxEnM48uNw4jow4mE4CRyi9Cg8YggWXuJTIDhs6hJ/kMKvIh1FRLGr/CYSiTl2+ReRiOvY5d9DYoFjl34OKWKIXfgxpBgIjp264VCk/FsRR0rgOI79UoQsImJa+Z0IqVbiW/mVCC2nWOsfiVAixj3/RIQKZeqD6g9EDAXKrW3oA0MUfeMq7ibmiJtEsTauo2wyx9cEjqwJMab5ccAS7/1TIDiC6t7/QdqtF+hu/57QDfp2OLF/ZBOO/FqpDavoNq+iW7mKLosTrkq6MUocKoU3Rgk95PNsBaKK1h9etvJW45nQuN7pVuNQ03SrUfGHSgW2NU4N+s4TSljfcZvUrcYsQXvoVmOmD/K0W02pHaMPO1u+OXcWMdcfb84N1cSbc4Qe8nm2AlGF649L29Hd/Kxy3UmGJpCyqSoP1aps6wo1j+XcysrG+zbfgmQB2hRuQTJ7ECfbKkZ1H3myXC42+jt65+cRla9hbhsaicddjSTCqVmy/Y3EfZMjCb7TkYSy3ZGw7XmM/DKjK4EqYSiPCjMvj/2SMiTGs4ivc8nrXcNn95mLajSV6jWV6jVjvdTUeyn+sTDSKj3Y1HqwqfXgp4xEh95UMjDeAmP1c0ZzgSrxnI/FbV6P27wSm3ktNvNabP47I1HqdjQGC4EqDV6Mf5H9ZbXI7zISbVxWzrmsJO1ytC7LyvhuBaqcuB3r3bbeu22ld9OfqSO51vltJaxtpYlfBCrTDNnMv3PRlUCVAK0qPbMa7RkRsXXlBOvKCdZVO12Pnlqh0Q90AlXq2o1lS1fPlq6SLXxdxXItW7qaVXR6ItwvNC7ivL/JyN+XN7TNpWyRwRyaT9K3XKNvlQh/q2TDNzEL6b99apiXXXiReRy/YZMRrLnya2ht5TW0tv4a2heo6Ol5If7wy9A2fIRO8EV/9/AttXeHol4Ty+tIh8dt9FWmnrm6aD7dly89/P+H8wtTywf7Vc5RxrbSyVqopvjKlf6YW5jQLK+UFu0hl1hX8MgJ1yMnRGtT6nerM/TFYZithlX8sR/F20IrXLsDEjeJVrRiBxpvDa3COh0Y3fFZweociL0wb2h4Ke4UwGGwnBvZLdoOg3QFyzUg2B7A3hyAQ2uAlMYAKm1xFA9CRzlfhINUz0XtY4PXOdmtqU7sYBkOhm8FMuQbEjwP8CH4Tr5g3M2SPPCwT8HL7b7zzA/CuZx32JwuhaLTXdapLutSl9HsCULuyy735TBXOviKzduGg6HmQLyKAyx31GAoGqLxaJwGpXE1Mk3Mw9MkGqPGeaCawKPVhDhkDdO49RubmBx8u/M8Yh/GRmJiGBbZYVpKEVNinhimnDBOiQGvGDNR47aIi0x0o+ToNzFaQMHuA4UsM4mOYDjagmPhDSZGg7Db1KmD2SoKz35RFDcHI8o54KEnhaLTUVVGIp54sqJTRftKetzJvJJI0Wbg3j6FZptJdB1xy35Qwt9qIkTWk/5SE3NlPfIPNZFE1pP/TBMLbD3przQRJuuhP9J0yjRYT8FuPUZiPhkW+WRayidTYj4ZpowxThkDuxKYKNMo4iIT3ShpPSZG6ynYraeQZSbRegxH63EsrMfEaD32Mn7qYLYe8beR6BPuLkaU9cA7AxSKTkdVWY94YYAVnSraetLbAswriRStB14VoNBsM4nWI94TKEp4eo0GFAW2oaiyGUVVWlIsIowpFmB7imoyqSgnq4oyGVYU2baCSplKWrSwIIKRRU45GkWVqbFEzteoU9ZGkbMzqpyjQa1yaVmhyKLGx0KhrTAWIUMMIthi4MsaJ4uMIhklicouYxEyzSB+qaRXMtCgChsNOvhl5NJS+VUiGVK21/qLRLrEd1KXDXfsLSJdZjS9kwWrV4ik9rUS4m2NkzXXXh866HlrldhSVd5kGD7C129DobTqJw7foPfWrdPkTRy/obLhci3+lHhS8Hukp5ZP6BEhVfhOHxu0hXM42v/NXCbxbqBjcauviB8Smdr910L2fz7+WSRlpiUMM2RUyo3e4Q7uaRYH5XBf+OswzR/M5ivO7YDKhA4ovoPtHF+8dmpvWzvyt6ed+evizso+Zif26rShkghPrU1Rb3Kb/N16QLpNjWxTfHXecW5qI5raiKa28SjXua1UsJUVbHNN0nvtrog6+hvsjnLs6fd2N2XdeG5H8f2+TVghOorLQufpLcBNWAA68lWfM1/qOSvrOye2qDNUEuqJtckXfRtMKEBxG88mJRTwtL7dcEIBjmvdTUgoYLSq3QwJBUe5zm2lgq2sYJtrkn6GxxVRR//RHUdxQb3BhDoE/2GC2/ceMPiA4va9hxR84Ol52wMHH3DcNPcQgg+Mtu9ti6v6kY1mR+aqjsTOli27qtO4h2UbXdUZbcLZoqs6iRtStji9HhmKBZrcKNqytM226lw2SuxI2gZbdSTayhuOtsVW/SjXWe0n2rKtOs010duFttFWncWNQdtgq4Z8+TJEHx9InBCiNqUHEsxVg9UDCZao6fmBBAvcHfxAgjHFIS1dKRaiKOekcUpM5yOxECnqUs5T0ypxShlrQi1OrUCVBsosNrHeEM5n4zqpTa61hdPbeKVvU6LDK9OQ60gpAihRxqOk4oN6DhGqFCWUOBKocceiFscAKhQq9bsaOVj6AzweUKIhEaTxYImBEdQ8NlCuxzKNENRGYtlqWm+7HC2ojzaQxwxKethgiZE28uBBqZ4UPIS+lfXquR3FNfi3sF4FlNfg33i9ChTXq47j0vxbXK86o6X5t7Je9aNc57ZSwVZWsM01SetVV0Qdfb3qKF4rfMOZiBHVP81EiYvGqZkoSbkf0kzEnFubZyISWoEqDZS9pGYiliqVrXRanolYoO5LM9HAy/0qhaiJxqkPnYumm5ib7lLuQ9MoLMa53SZwHxahFajSQNmHJtYbwn1oXPehybW2cB8apz7EX0GqYWpq0Kg/oyZCEQrkcEQ5923QKWRB47gEkfsZxbaCR4Ig+zwUGG8o933QdP+HImNt5TwIGuTCv/73/wO+9kRf"'),_s=JSON.parse('"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"'),$s=JSON.parse('"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"'),eu=JSON.parse('"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"'),tu=JSON.parse('"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"'),ru="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",nu="eJxtmNtu20YQhl+F4FULyMGeD7pz3AY1ChtG7NpFA18w1NomIlECSRcxgrx7SVk7+wOdG8H5OJydf2Z2d5gf9cV+t0v9VK/r+6vXsXlOlbHe28paq229qj/t++m62aXZ4J/m8PRb1z9/baZxefK63Z6eXN5dVMvTCh83u277xr/6kLrnl2XNq7TpXnczuZyabdee98/b2VzM/x4/dd/T5qab2pd6PQ2vaVVfvDRD005puE3Lu7eH1HbN9hTjx4/77/X6y5lcnUmjVzHIVVDicVX/1W/SsO36dLMfu6nb9/X6TAoBD+5euvZbn8axXtuZ36dhPJrVQqgPQoh5hev91LWLkIv94W1Ygq9+aX+tZAx2tfz64284/sblN/rqfLP/mqrbt3FKu7G67Nv9cNgPzZQ2H6rz7bb6vLgZq89pTMO/M/xfEqturJpqSM/d7GJIm2oamk3aNcO3av80O5xh3yyKmm1193ZIT02bqovTKjP+MAf++7zsZvZ3276kYyWWXB0z99S18/PbafPHQ71W4fjn/fxnFO+ZvkrT0LVzTr78qB/+nk38bHM9exgP8zr1z9U7jt6840YW5uSJKcZOCaBBnKgm5mU8MVNYyMwWFvO7Ukagkmgg6sDWQ5yFFqjzUrLEaQ3BEmiwNsMSaZS0vgWfOkPHWQowNeTUc0kumnxZvsgPxlGai6VTGUqAVCTQ6QkWnc77DKEiLktSUBJKqHIQZ86d8gCpHYoiEzMsb1ubYy8vW50DChB5ZhGqrijD0EqUIeiaEHIfCg5Kpuu0ApiToaGPSY0uaQsyr65L2oKi1yFt1PLaQ3lzfXTgXodGoJYzglndSLDMPg1sTPJpQJHJigw0QrGERqD9YhyTOgONQDUyuF1zaxuokc/BW2ztXCMrGZ9WMW1oQZHIXWNBkSCfRZEL5BMUiZw6CzVSFCfUSGZFNjIldoKDkonTKQiJIGzWmFd3BizJJ9SINoLDriOfUCOZS+zg+KGD1qGiLNMLxtJD1/ns00ON6EzyUCM6vbxhoBKaqbG3DFQCNiL1iHccBPV0DHhQH/JW8EW90dkyFKGywCJU0WkVSvSGeiSUODWFFD0HYdPQVoiRgfPMA+/nnRgiAyNYSjpWNQcNSMrtFCUH4ZIRpSCWocFCSuhCEY6hoUClc0WC52BJlCYYLQdhN+hygRRRlo5BKRRLS6oihSqh+ZzzRGG1Mo4Iz1LoP0qsxDGFzk0JE42ji0jCPejomJKCuwil4m5CiRMEUMVSzVLDUstSx1Juc0oVWMpqY295qVltmtWmWW2a1aZZbZrVplltmtWmWW2G1WZYbYbVZlhthtVmWG2G1WZYbYbVZlhtltVmWW2W1WZZbZbVZlltltVmWW2W1QYjQCh7E2aAQHeGhCFgPoNoy8KNb2wxBhmGKBxoUZXlLGsLI6AsftEDHV0wIURVbANLcTKlGGBIKPOAxCmhePCKUwFzAmpDFRQvjA9R06Hq8TONvshgKDCuRAZTXigUxjxNFfKRo3CLhnIJBMFRvMZpqpNBMlQJzGT5WFQMVQI/AikPMIhEU1aDjqJvQwmjSHB05cC9jbYwc5UtAHNLhDw41ha+lEqF4JaH3gmB61SYcqInxTDmQK8v08vjqv4zDf1N0w3Lf4A8/vwPpfK11w==";var iu={Courier:Xs,"Courier-Bold":Ls,"Courier-Oblique":Hs,"Courier-BoldOblique":Gs,Helvetica:Qs,"Helvetica-Bold":Zs,"Helvetica-Oblique":Js,"Helvetica-BoldOblique":Ys,"Times-Roman":tu,"Times-Bold":_s,"Times-Italic":eu,"Times-BoldItalic":$s,Symbol:ru,ZapfDingbats:nu},rn;(function(r){r.Courier="Courier",r.CourierBold="Courier-Bold",r.CourierOblique="Courier-Oblique",r.CourierBoldOblique="Courier-BoldOblique",r.Helvetica="Helvetica",r.HelveticaBold="Helvetica-Bold",r.HelveticaOblique="Helvetica-Oblique",r.HelveticaBoldOblique="Helvetica-BoldOblique",r.TimesRoman="Times-Roman",r.TimesRomanBold="Times-Bold",r.TimesRomanItalic="Times-Italic",r.TimesRomanBoldItalic="Times-BoldItalic",r.Symbol="Symbol",r.ZapfDingbats="ZapfDingbats"})(rn||(rn={}));var Ca={},au=function(){function r(){var e=this;this.getWidthOfGlyph=function(t){return e.CharWidths[t]},this.getXAxisKerningForPair=function(t,n){return(e.KernPairXAmounts[t]||{})[n]}}return r.load=function(e){var t=Ca[e];if(t)return t;var n=po(iu[e]),i=Object.assign(new r,JSON.parse(n));return i.CharWidths=i.CharMetrics.reduce(function(a,o){return a[o.N]=o.WX,a},{}),i.KernPairXAmounts=i.KernPairs.reduce(function(a,o){var s=o[0],u=o[1],f=o[2];return a[s]||(a[s]={}),a[s][u]=f,a},{}),Ca[e]=i,i},r}();const ou="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";var su=po(ou),xi=JSON.parse(su),wi=function(){function r(e,t){var n=this;this.canEncodeUnicodeCodePoint=function(i){return i in n.unicodeMappings},this.encodeUnicodeCodePoint=function(i){var a=n.unicodeMappings[i];if(!a){var o=String.fromCharCode(i),s="0x"+Ks(i.toString(16),4,"0"),u=n.name+' cannot encode "'+o+'" ('+s+")";throw new Error(u)}return{code:a[0],name:a[1]}},this.name=e,this.supportedCodePoints=Object.keys(t).map(Number).sort(function(i,a){return i-a}),this.unicodeMappings=t}return r}(),mn={Symbol:new wi("Symbol",xi.symbol),ZapfDingbats:new wi("ZapfDingbats",xi.zapfdingbats),WinAnsi:new wi("WinAnsi",xi.win1252)},Yn=function(r){return Object.keys(r).map(function(e){return r[e]})},uu=Yn(rn),Ta=function(r){return uu.includes(r)},xn=function(r,e){return r.x===e.x&&r.y===e.y&&r.width===e.width&&r.height===e.height},Ye=function(r){return"`"+r+"`"},fu=function(r){return"'"+r+"'"},Pa=function(r){var e=typeof r;return e==="string"?fu(r):e==="undefined"?Ye(r):r},cu=function(r,e,t){for(var n=new Array(t.length),i=0,a=t.length;i<a;i++){var o=t[i];n[i]=Pa(o)}var s=n.join(" or ");return Ye(e)+" must be one of "+s+", but was actually "+Pa(r)},er=function(r,e,t){Array.isArray(t)||(t=Yn(t));for(var n=0,i=t.length;n<i;n++)if(r===t[n])return;throw new TypeError(cu(r,e,t))},kt=function(r,e,t){Array.isArray(t)||(t=Yn(t)),er(r,e,t.concat(void 0))},lu=function(r,e,t){Array.isArray(t)||(t=Yn(t));for(var n=0,i=r.length;n<i;n++)er(r[n],e,t)},hu=function(r){return r===null?"null":r===void 0?"undefined":typeof r=="string"?"string":isNaN(r)?"NaN":typeof r=="number"?"number":typeof r=="boolean"?"boolean":typeof r=="symbol"?"symbol":typeof r=="bigint"?"bigint":r.constructor&&r.constructor.name?r.constructor.name:r.name?r.name:r.constructor?String(r.constructor):String(r)},du=function(r,e){return e==="null"?r===null:e==="undefined"?r===void 0:e==="string"?typeof r=="string":e==="number"?typeof r=="number"&&!isNaN(r):e==="boolean"?typeof r=="boolean":e==="symbol"?typeof r=="symbol":e==="bigint"?typeof r=="bigint":e===Date?r instanceof Date:e===Array?r instanceof Array:e===Uint8Array?r instanceof Uint8Array:e===ArrayBuffer?r instanceof ArrayBuffer:e===Function?r instanceof Function:r instanceof e[0]},vu=function(r,e,t){for(var n=new Array(t.length),i=0,a=t.length;i<a;i++){var o=t[i];o==="null"&&(n[i]=Ye("null")),o==="undefined"&&(n[i]=Ye("undefined")),o==="string"?n[i]=Ye("string"):o==="number"?n[i]=Ye("number"):o==="boolean"?n[i]=Ye("boolean"):o==="symbol"?n[i]=Ye("symbol"):o==="bigint"?n[i]=Ye("bigint"):o===Array?n[i]=Ye("Array"):o===Uint8Array?n[i]=Ye("Uint8Array"):o===ArrayBuffer?n[i]=Ye("ArrayBuffer"):n[i]=Ye(o[1])}var s=n.join(" or ");return Ye(e)+" must be of type "+s+", but was actually of type "+Ye(hu(r))},A=function(r,e,t){for(var n=0,i=t.length;n<i;n++)if(du(r,t[n]))return;throw new TypeError(vu(r,e,t))},G=function(r,e,t){A(r,e,t.concat("undefined"))},go=function(r,e,t){for(var n=0,i=r.length;n<i;n++)A(r[n],e,t)},vt=function(r,e,t,n){if(A(r,e,["number"]),A(t,"min",["number"]),A(n,"max",["number"]),n=Math.max(t,n),r<t||r>n)throw new Error(Ye(e)+" must be at least "+t+" and at most "+n+", but was actually "+r)},Nt=function(r,e,t,n){A(r,e,["number","undefined"]),typeof r=="number"&&vt(r,e,t,n)},yo=function(r,e,t){if(A(r,e,["number"]),r%t!==0)throw new Error(Ye(e)+" must be a multiple of "+t+", but was actually "+r)},pu=function(r,e){if(!Number.isInteger(r))throw new Error(Ye(e)+" must be an integer, but was actually "+r)},Jn=function(r,e){if(![1,0].includes(Math.sign(r)))throw new Error(Ye(e)+" must be a positive number or 0, but was actually "+r)},de=new Uint16Array(256);for(var wn=0;wn<256;wn++)de[wn]=wn;de[22]=le("");de[24]=le("˘");de[25]=le("ˇ");de[26]=le("ˆ");de[27]=le("˙");de[28]=le("˝");de[29]=le("˛");de[30]=le("˚");de[31]=le("˜");de[127]=le("�");de[128]=le("•");de[129]=le("†");de[130]=le("‡");de[131]=le("…");de[132]=le("—");de[133]=le("–");de[134]=le("ƒ");de[135]=le("⁄");de[136]=le("‹");de[137]=le("›");de[138]=le("−");de[139]=le("‰");de[140]=le("„");de[141]=le("“");de[142]=le("”");de[143]=le("‘");de[144]=le("’");de[145]=le("‚");de[146]=le("™");de[147]=le("ﬁ");de[148]=le("ﬂ");de[149]=le("Ł");de[150]=le("Œ");de[151]=le("Š");de[152]=le("Ÿ");de[153]=le("Ž");de[154]=le("ı");de[155]=le("ł");de[156]=le("œ");de[157]=le("š");de[158]=le("ž");de[159]=le("�");de[160]=le("€");de[173]=le("�");var bo=function(r){for(var e=new Array(r.length),t=0,n=r.length;t<n;t++)e[t]=de[r[t]];return String.fromCodePoint.apply(String,e)},Gt=function(){function r(e){this.populate=e,this.value=void 0}return r.prototype.getValue=function(){return this.value},r.prototype.access=function(){return this.value||(this.value=this.populate()),this.value},r.prototype.invalidate=function(){this.value=void 0},r.populatedBy=function(e){return new r(e)},r}(),Pt=function(r){X(e,r);function e(t,n){var i=this,a="Method "+t+"."+n+"() not implemented";return i=r.call(this,a)||this,i}return e}(Error),Mi=function(r){X(e,r);function e(t){var n=this,i="Cannot construct "+t+" - it has a private constructor";return n=r.call(this,i)||this,n}return e}(Error),zn=function(r){X(e,r);function e(t,n){var i=this,a=function(u){var f,l;return(f=u==null?void 0:u.name)!==null&&f!==void 0?f:(l=u==null?void 0:u.constructor)===null||l===void 0?void 0:l.name},o=Array.isArray(t)?t.map(a):[a(t)],s="Expected instance of "+o.join(" or ")+", "+("but got instance of "+(n&&a(n)));return i=r.call(this,s)||this,i}return e}(Error),gu=function(r){X(e,r);function e(t){var n=this,i=t+" stream encoding not supported";return n=r.call(this,i)||this,n}return e}(Error),Ii=function(r){X(e,r);function e(t,n){var i=this,a="Cannot call "+t+"."+n+"() more than once";return i=r.call(this,a)||this,i}return e}(Error);(function(r){X(e,r);function e(t){var n=this,i="Missing catalog (ref="+t+")";return n=r.call(this,i)||this,n}return e})(Error);var yu=function(r){X(e,r);function e(){var t=this,n="Can't embed page with missing Contents";return t=r.call(this,n)||this,t}return e}(Error),bu=function(r){X(e,r);function e(t){var n,i,a,o=this,s=(a=(i=(n=t==null?void 0:t.contructor)===null||n===void 0?void 0:n.name)!==null&&i!==void 0?i:t==null?void 0:t.name)!==null&&a!==void 0?a:t,u="Unrecognized stream type: "+s;return o=r.call(this,u)||this,o}return e}(Error),mu=function(r){X(e,r);function e(){var t=this,n="Found mismatched contexts while embedding pages. All pages in the array passed to `PDFDocument.embedPages()` must be from the same document.";return t=r.call(this,n)||this,t}return e}(Error),xu=function(r){X(e,r);function e(t){var n=this,i="Attempted to convert PDFArray with "+t+" elements to rectangle, but must have exactly 4 elements.";return n=r.call(this,i)||this,n}return e}(Error),mo=function(r){X(e,r);function e(t){var n=this,i='Attempted to convert "'+t+'" to a date, but it does not match the PDF date string format.';return n=r.call(this,i)||this,n}return e}(Error),Da=function(r){X(e,r);function e(t,n){var i=this,a="Invalid targetIndex specified: targetIndex="+t+" must be less than Count="+n;return i=r.call(this,a)||this,i}return e}(Error),Aa=function(r){X(e,r);function e(t,n){var i=this,a="Failed to "+n+" at targetIndex="+t+" due to corrupt page tree: It is likely that one or more 'Count' entries are invalid";return i=r.call(this,a)||this,i}return e}(Error),jn=function(r){X(e,r);function e(t,n,i){var a=this,o="index should be at least "+n+" and at most "+i+", but was actually "+t;return a=r.call(this,o)||this,a}return e}(Error),Ui=function(r){X(e,r);function e(){var t=this,n="Attempted to set invalid field value";return t=r.call(this,n)||this,t}return e}(Error),wu=function(r){X(e,r);function e(){var t=this,n="Attempted to select multiple values for single-select field";return t=r.call(this,n)||this,t}return e}(Error),Su=function(r){X(e,r);function e(t){var n=this,i="No /DA (default appearance) entry found for field: "+t;return n=r.call(this,i)||this,n}return e}(Error),Fu=function(r){X(e,r);function e(t){var n=this,i="No Tf operator found for DA of field: "+t;return n=r.call(this,i)||this,n}return e}(Error),Ra=function(r){X(e,r);function e(t,n){var i=this,a="Failed to parse number "+("(line:"+t.line+" col:"+t.column+" offset="+t.offset+'): "'+n+'"');return i=r.call(this,a)||this,i}return e}(Error),sr=function(r){X(e,r);function e(t,n){var i=this,a="Failed to parse PDF document "+("(line:"+t.line+" col:"+t.column+" offset="+t.offset+"): "+n);return i=r.call(this,a)||this,i}return e}(Error),ku=function(r){X(e,r);function e(t,n,i){var a=this,o="Expected next byte to be "+n+" but it was actually "+i;return a=r.call(this,t,o)||this,a}return e}(sr),Cu=function(r){X(e,r);function e(t,n){var i=this,a="Failed to parse PDF object starting with the following byte: "+n;return i=r.call(this,t,a)||this,i}return e}(sr),Tu=function(r){X(e,r);function e(t){var n=this,i="Failed to parse invalid PDF object";return n=r.call(this,t,i)||this,n}return e}(sr),Pu=function(r){X(e,r);function e(t){var n=this,i="Failed to parse PDF stream";return n=r.call(this,t,i)||this,n}return e}(sr),Du=function(r){X(e,r);function e(t){var n=this,i="Failed to parse PDF literal string due to unbalanced parenthesis";return n=r.call(this,t,i)||this,n}return e}(sr),Au=function(r){X(e,r);function e(t){var n=this,i="Parser stalled";return n=r.call(this,t,i)||this,n}return e}(sr),Ru=function(r){X(e,r);function e(t){var n=this,i="No PDF header found";return n=r.call(this,t,i)||this,n}return e}(sr),Ou=function(r){X(e,r);function e(t,n){var i=this,a="Did not find expected keyword '"+ro(n)+"'";return i=r.call(this,t,a)||this,i}return e}(sr),x;(function(r){r[r.Null=0]="Null",r[r.Backspace=8]="Backspace",r[r.Tab=9]="Tab",r[r.Newline=10]="Newline",r[r.FormFeed=12]="FormFeed",r[r.CarriageReturn=13]="CarriageReturn",r[r.Space=32]="Space",r[r.ExclamationPoint=33]="ExclamationPoint",r[r.Hash=35]="Hash",r[r.Percent=37]="Percent",r[r.LeftParen=40]="LeftParen",r[r.RightParen=41]="RightParen",r[r.Plus=43]="Plus",r[r.Minus=45]="Minus",r[r.Dash=45]="Dash",r[r.Period=46]="Period",r[r.ForwardSlash=47]="ForwardSlash",r[r.Zero=48]="Zero",r[r.One=49]="One",r[r.Two=50]="Two",r[r.Three=51]="Three",r[r.Four=52]="Four",r[r.Five=53]="Five",r[r.Six=54]="Six",r[r.Seven=55]="Seven",r[r.Eight=56]="Eight",r[r.Nine=57]="Nine",r[r.LessThan=60]="LessThan",r[r.GreaterThan=62]="GreaterThan",r[r.A=65]="A",r[r.D=68]="D",r[r.E=69]="E",r[r.F=70]="F",r[r.O=79]="O",r[r.P=80]="P",r[r.R=82]="R",r[r.LeftSquareBracket=91]="LeftSquareBracket",r[r.BackSlash=92]="BackSlash",r[r.RightSquareBracket=93]="RightSquareBracket",r[r.a=97]="a",r[r.b=98]="b",r[r.d=100]="d",r[r.e=101]="e",r[r.f=102]="f",r[r.i=105]="i",r[r.j=106]="j",r[r.l=108]="l",r[r.m=109]="m",r[r.n=110]="n",r[r.o=111]="o",r[r.r=114]="r",r[r.s=115]="s",r[r.t=116]="t",r[r.u=117]="u",r[r.x=120]="x",r[r.LeftCurly=123]="LeftCurly",r[r.RightCurly=125]="RightCurly",r[r.Tilde=126]="Tilde"})(x||(x={}));var Qn=function(){function r(e,t){this.major=String(e),this.minor=String(t)}return r.prototype.toString=function(){var e=Lt(129);return"%PDF-"+this.major+"."+this.minor+`
%`+e+e+e+e},r.prototype.sizeInBytes=function(){return 12+this.major.length+this.minor.length},r.prototype.copyBytesInto=function(e,t){var n=t;return e[t++]=x.Percent,e[t++]=x.P,e[t++]=x.D,e[t++]=x.F,e[t++]=x.Dash,t+=rt(this.major,e,t),e[t++]=x.Period,t+=rt(this.minor,e,t),e[t++]=x.Newline,e[t++]=x.Percent,e[t++]=129,e[t++]=129,e[t++]=129,e[t++]=129,t-n},r.forVersion=function(e,t){return new r(e,t)},r}(),ut=function(){function r(){}return r.prototype.clone=function(e){throw new Pt(this.constructor.name,"clone")},r.prototype.toString=function(){throw new Pt(this.constructor.name,"toString")},r.prototype.sizeInBytes=function(){throw new Pt(this.constructor.name,"sizeInBytes")},r.prototype.copyBytesInto=function(e,t){throw new Pt(this.constructor.name,"copyBytesInto")},r}(),fe=function(r){X(e,r);function e(t){var n=r.call(this)||this;return n.numberValue=t,n.stringValue=Rs(t),n}return e.prototype.asNumber=function(){return this.numberValue},e.prototype.value=function(){return this.numberValue},e.prototype.clone=function(){return e.of(this.numberValue)},e.prototype.toString=function(){return this.stringValue},e.prototype.sizeInBytes=function(){return this.stringValue.length},e.prototype.copyBytesInto=function(t,n){return n+=rt(this.stringValue,t,n),this.stringValue.length},e.of=function(t){return new e(t)},e}(ut),Ae=function(r){X(e,r);function e(t){var n=r.call(this)||this;return n.array=[],n.context=t,n}return e.prototype.size=function(){return this.array.length},e.prototype.push=function(t){this.array.push(t)},e.prototype.insert=function(t,n){this.array.splice(t,0,n)},e.prototype.indexOf=function(t){var n=this.array.indexOf(t);return n===-1?void 0:n},e.prototype.remove=function(t){this.array.splice(t,1)},e.prototype.set=function(t,n){this.array[t]=n},e.prototype.get=function(t){return this.array[t]},e.prototype.lookupMaybe=function(t){for(var n,i=[],a=1;a<arguments.length;a++)i[a-1]=arguments[a];return(n=this.context).lookupMaybe.apply(n,ke([this.get(t)],i))},e.prototype.lookup=function(t){for(var n,i=[],a=1;a<arguments.length;a++)i[a-1]=arguments[a];return(n=this.context).lookup.apply(n,ke([this.get(t)],i))},e.prototype.asRectangle=function(){if(this.size()!==4)throw new xu(this.size());var t=this.lookup(0,fe).asNumber(),n=this.lookup(1,fe).asNumber(),i=this.lookup(2,fe).asNumber(),a=this.lookup(3,fe).asNumber(),o=t,s=n,u=i-t,f=a-n;return{x:o,y:s,width:u,height:f}},e.prototype.asArray=function(){return this.array.slice()},e.prototype.clone=function(t){for(var n=e.withContext(t||this.context),i=0,a=this.size();i<a;i++)n.push(this.array[i]);return n},e.prototype.toString=function(){for(var t="[ ",n=0,i=this.size();n<i;n++)t+=this.get(n).toString(),t+=" ";return t+="]",t},e.prototype.sizeInBytes=function(){for(var t=3,n=0,i=this.size();n<i;n++)t+=this.get(n).sizeInBytes()+1;return t},e.prototype.copyBytesInto=function(t,n){var i=n;t[n++]=x.LeftSquareBracket,t[n++]=x.Space;for(var a=0,o=this.size();a<o;a++)n+=this.get(a).copyBytesInto(t,n),t[n++]=x.Space;return t[n++]=x.RightSquareBracket,n-i},e.prototype.scalePDFNumbers=function(t,n){for(var i=0,a=this.size();i<a;i++){var o=this.lookup(i);if(o instanceof fe){var s=i%2===0?t:n;this.set(i,fe.of(o.asNumber()*s))}}},e.withContext=function(t){return new e(t)},e}(ut),Si={},nn=function(r){X(e,r);function e(t,n){var i=this;if(t!==Si)throw new Mi("PDFBool");return i=r.call(this)||this,i.value=n,i}return e.prototype.asBoolean=function(){return this.value},e.prototype.clone=function(){return this},e.prototype.toString=function(){return String(this.value)},e.prototype.sizeInBytes=function(){return this.value?4:5},e.prototype.copyBytesInto=function(t,n){return this.value?(t[n++]=x.t,t[n++]=x.r,t[n++]=x.u,t[n++]=x.e,4):(t[n++]=x.f,t[n++]=x.a,t[n++]=x.l,t[n++]=x.s,t[n++]=x.e,5)},e.True=new e(Si,!0),e.False=new e(Si,!1),e}(ut),Ot=new Uint8Array(256);Ot[x.LeftParen]=1;Ot[x.RightParen]=1;Ot[x.LessThan]=1;Ot[x.GreaterThan]=1;Ot[x.LeftSquareBracket]=1;Ot[x.RightSquareBracket]=1;Ot[x.LeftCurly]=1;Ot[x.RightCurly]=1;Ot[x.ForwardSlash]=1;Ot[x.Percent]=1;var Xt=new Uint8Array(256);Xt[x.Null]=1;Xt[x.Tab]=1;Xt[x.Newline]=1;Xt[x.FormFeed]=1;Xt[x.CarriageReturn]=1;Xt[x.Space]=1;var Vi=new Uint8Array(256);for(var Vr=0,Eu=256;Vr<Eu;Vr++)Vi[Vr]=Xt[Vr]||Ot[Vr]?1:0;Vi[x.Hash]=1;var Bu=function(r){return r.replace(/#([\dABCDEF]{2})/g,function(e,t){return cs(t)})},Nu=function(r){return r>=x.ExclamationPoint&&r<=x.Tilde&&!Vi[r]},Oa={},Ea=new Map,m=function(r){X(e,r);function e(t,n){var i=this;if(t!==Oa)throw new Mi("PDFName");i=r.call(this)||this;for(var a="/",o=0,s=n.length;o<s;o++){var u=n[o],f=le(u);a+=Nu(f)?u:"#"+Xn(f)}return i.encodedName=a,i}return e.prototype.asBytes=function(){for(var t=[],n="",i=!1,a=function(h){h!==void 0&&t.push(h),i=!1},o=1,s=this.encodedName.length;o<s;o++){var u=this.encodedName[o],f=le(u),l=this.encodedName[o+1];i?f>=x.Zero&&f<=x.Nine||f>=x.a&&f<=x.f||f>=x.A&&f<=x.F?(n+=u,(n.length===2||!(l>="0"&&l<="9"||l>="a"&&l<="f"||l>="A"&&l<="F"))&&(a(parseInt(n,16)),n="")):a(f):f===x.Hash?i=!0:a(f)}return new Uint8Array(t)},e.prototype.decodeText=function(){var t=this.asBytes();return String.fromCharCode.apply(String,Array.from(t))},e.prototype.asString=function(){return this.encodedName},e.prototype.value=function(){return this.encodedName},e.prototype.clone=function(){return this},e.prototype.toString=function(){return this.encodedName},e.prototype.sizeInBytes=function(){return this.encodedName.length},e.prototype.copyBytesInto=function(t,n){return n+=rt(this.encodedName,t,n),this.encodedName.length},e.of=function(t){var n=Bu(t),i=Ea.get(n);return i||(i=new e(Oa,n),Ea.set(n,i)),i},e.Length=e.of("Length"),e.FlateDecode=e.of("FlateDecode"),e.Resources=e.of("Resources"),e.Font=e.of("Font"),e.XObject=e.of("XObject"),e.ExtGState=e.of("ExtGState"),e.Contents=e.of("Contents"),e.Type=e.of("Type"),e.Parent=e.of("Parent"),e.MediaBox=e.of("MediaBox"),e.Page=e.of("Page"),e.Annots=e.of("Annots"),e.TrimBox=e.of("TrimBox"),e.ArtBox=e.of("ArtBox"),e.BleedBox=e.of("BleedBox"),e.CropBox=e.of("CropBox"),e.Rotate=e.of("Rotate"),e.Title=e.of("Title"),e.Author=e.of("Author"),e.Subject=e.of("Subject"),e.Creator=e.of("Creator"),e.Keywords=e.of("Keywords"),e.Producer=e.of("Producer"),e.CreationDate=e.of("CreationDate"),e.ModDate=e.of("ModDate"),e}(ut),zu=function(r){X(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.asNull=function(){return null},e.prototype.clone=function(){return this},e.prototype.toString=function(){return"null"},e.prototype.sizeInBytes=function(){return 4},e.prototype.copyBytesInto=function(t,n){return t[n++]=x.n,t[n++]=x.u,t[n++]=x.l,t[n++]=x.l,4},e}(ut);const ht=new zu;var ve=function(r){X(e,r);function e(t,n){var i=r.call(this)||this;return i.dict=t,i.context=n,i}return e.prototype.keys=function(){return Array.from(this.dict.keys())},e.prototype.values=function(){return Array.from(this.dict.values())},e.prototype.entries=function(){return Array.from(this.dict.entries())},e.prototype.set=function(t,n){this.dict.set(t,n)},e.prototype.get=function(t,n){n===void 0&&(n=!1);var i=this.dict.get(t);if(!(i===ht&&!n))return i},e.prototype.has=function(t){var n=this.dict.get(t);return n!==void 0&&n!==ht},e.prototype.lookupMaybe=function(t){for(var n,i=[],a=1;a<arguments.length;a++)i[a-1]=arguments[a];var o=i.includes(ht),s=(n=this.context).lookupMaybe.apply(n,ke([this.get(t,o)],i));if(!(s===ht&&!o))return s},e.prototype.lookup=function(t){for(var n,i=[],a=1;a<arguments.length;a++)i[a-1]=arguments[a];var o=i.includes(ht),s=(n=this.context).lookup.apply(n,ke([this.get(t,o)],i));if(!(s===ht&&!o))return s},e.prototype.delete=function(t){return this.dict.delete(t)},e.prototype.asMap=function(){return new Map(this.dict)},e.prototype.uniqueKey=function(t){t===void 0&&(t="");for(var n=this.keys(),i=m.of(this.context.addRandomSuffix(t,10));n.includes(i);)i=m.of(this.context.addRandomSuffix(t,10));return i},e.prototype.clone=function(t){for(var n=e.withContext(t||this.context),i=this.entries(),a=0,o=i.length;a<o;a++){var s=i[a],u=s[0],f=s[1];n.set(u,f)}return n},e.prototype.toString=function(){for(var t=`<<
`,n=this.entries(),i=0,a=n.length;i<a;i++){var o=n[i],s=o[0],u=o[1];t+=s.toString()+" "+u.toString()+`
`}return t+=">>",t},e.prototype.sizeInBytes=function(){for(var t=5,n=this.entries(),i=0,a=n.length;i<a;i++){var o=n[i],s=o[0],u=o[1];t+=s.sizeInBytes()+u.sizeInBytes()+2}return t},e.prototype.copyBytesInto=function(t,n){var i=n;t[n++]=x.LessThan,t[n++]=x.LessThan,t[n++]=x.Newline;for(var a=this.entries(),o=0,s=a.length;o<s;o++){var u=a[o],f=u[0],l=u[1];n+=f.copyBytesInto(t,n),t[n++]=x.Space,n+=l.copyBytesInto(t,n),t[n++]=x.Newline}return t[n++]=x.GreaterThan,t[n++]=x.GreaterThan,n-i},e.withContext=function(t){return new e(new Map,t)},e.fromMapWithContext=function(t,n){return new e(t,n)},e}(ut),gt=function(r){X(e,r);function e(t){var n=r.call(this)||this;return n.dict=t,n}return e.prototype.clone=function(t){throw new Pt(this.constructor.name,"clone")},e.prototype.getContentsString=function(){throw new Pt(this.constructor.name,"getContentsString")},e.prototype.getContents=function(){throw new Pt(this.constructor.name,"getContents")},e.prototype.getContentsSize=function(){throw new Pt(this.constructor.name,"getContentsSize")},e.prototype.updateDict=function(){var t=this.getContentsSize();this.dict.set(m.Length,fe.of(t))},e.prototype.sizeInBytes=function(){return this.updateDict(),this.dict.sizeInBytes()+this.getContentsSize()+18},e.prototype.toString=function(){this.updateDict();var t=this.dict.toString();return t+=`
stream
`,t+=this.getContentsString(),t+=`
endstream`,t},e.prototype.copyBytesInto=function(t,n){this.updateDict();var i=n;n+=this.dict.copyBytesInto(t,n),t[n++]=x.Newline,t[n++]=x.s,t[n++]=x.t,t[n++]=x.r,t[n++]=x.e,t[n++]=x.a,t[n++]=x.m,t[n++]=x.Newline;for(var a=this.getContents(),o=0,s=a.length;o<s;o++)t[n++]=a[o];return t[n++]=x.Newline,t[n++]=x.e,t[n++]=x.n,t[n++]=x.d,t[n++]=x.s,t[n++]=x.t,t[n++]=x.r,t[n++]=x.e,t[n++]=x.a,t[n++]=x.m,n-i},e}(ut),an=function(r){X(e,r);function e(t,n){var i=r.call(this,t)||this;return i.contents=n,i}return e.prototype.asUint8Array=function(){return this.contents.slice()},e.prototype.clone=function(t){return e.of(this.dict.clone(t),this.contents.slice())},e.prototype.getContentsString=function(){return ro(this.contents)},e.prototype.getContents=function(){return this.contents},e.prototype.getContentsSize=function(){return this.contents.length},e.of=function(t,n){return new e(t,n)},e}(gt),Ba={},Na=new Map,Ee=function(r){X(e,r);function e(t,n,i){var a=this;if(t!==Ba)throw new Mi("PDFRef");return a=r.call(this)||this,a.objectNumber=n,a.generationNumber=i,a.tag=n+" "+i+" R",a}return e.prototype.clone=function(){return this},e.prototype.toString=function(){return this.tag},e.prototype.sizeInBytes=function(){return this.tag.length},e.prototype.copyBytesInto=function(t,n){return n+=rt(this.tag,t,n),this.tag.length},e.of=function(t,n){n===void 0&&(n=0);var i=t+" "+n+" R",a=Na.get(i);return a||(a=new e(Ba,t,n),Na.set(i,a)),a},e}(ut),Fe=function(){function r(e,t){this.name=e,this.args=t||[]}return r.prototype.clone=function(e){for(var t=new Array(this.args.length),n=0,i=t.length;n<i;n++){var a=this.args[n];t[n]=a instanceof ut?a.clone(e):a}return r.of(this.name,t)},r.prototype.toString=function(){for(var e="",t=0,n=this.args.length;t<n;t++)e+=String(this.args[t])+" ";return e+=this.name,e},r.prototype.sizeInBytes=function(){for(var e=0,t=0,n=this.args.length;t<n;t++){var i=this.args[t];e+=(i instanceof ut?i.sizeInBytes():i.length)+1}return e+=this.name.length,e},r.prototype.copyBytesInto=function(e,t){for(var n=t,i=0,a=this.args.length;i<a;i++){var o=this.args[i];o instanceof ut?t+=o.copyBytesInto(e,t):t+=rt(o,e,t),e[t++]=x.Space}return t+=rt(this.name,e,t),t-n},r.of=function(e,t){return new r(e,t)},r}(),xe;(function(r){r.NonStrokingColor="sc",r.NonStrokingColorN="scn",r.NonStrokingColorRgb="rg",r.NonStrokingColorGray="g",r.NonStrokingColorCmyk="k",r.NonStrokingColorspace="cs",r.StrokingColor="SC",r.StrokingColorN="SCN",r.StrokingColorRgb="RG",r.StrokingColorGray="G",r.StrokingColorCmyk="K",r.StrokingColorspace="CS",r.BeginMarkedContentSequence="BDC",r.BeginMarkedContent="BMC",r.EndMarkedContent="EMC",r.MarkedContentPointWithProps="DP",r.MarkedContentPoint="MP",r.DrawObject="Do",r.ConcatTransformationMatrix="cm",r.PopGraphicsState="Q",r.PushGraphicsState="q",r.SetFlatness="i",r.SetGraphicsStateParams="gs",r.SetLineCapStyle="J",r.SetLineDashPattern="d",r.SetLineJoinStyle="j",r.SetLineMiterLimit="M",r.SetLineWidth="w",r.SetTextMatrix="Tm",r.SetRenderingIntent="ri",r.AppendRectangle="re",r.BeginInlineImage="BI",r.BeginInlineImageData="ID",r.EndInlineImage="EI",r.ClipEvenOdd="W*",r.ClipNonZero="W",r.CloseAndStroke="s",r.CloseFillEvenOddAndStroke="b*",r.CloseFillNonZeroAndStroke="b",r.ClosePath="h",r.AppendBezierCurve="c",r.CurveToReplicateFinalPoint="y",r.CurveToReplicateInitialPoint="v",r.EndPath="n",r.FillEvenOddAndStroke="B*",r.FillEvenOdd="f*",r.FillNonZeroAndStroke="B",r.FillNonZero="f",r.LegacyFillNonZero="F",r.LineTo="l",r.MoveTo="m",r.ShadingFill="sh",r.StrokePath="S",r.BeginText="BT",r.EndText="ET",r.MoveText="Td",r.MoveTextSetLeading="TD",r.NextLine="T*",r.SetCharacterSpacing="Tc",r.SetFontAndSize="Tf",r.SetTextHorizontalScaling="Tz",r.SetTextLineHeight="TL",r.SetTextRenderingMode="Tr",r.SetTextRise="Ts",r.SetWordSpacing="Tw",r.ShowText="Tj",r.ShowTextAdjusted="TJ",r.ShowTextLine="'",r.ShowTextLineAndSpace='"',r.Type3D0="d0",r.Type3D1="d1",r.BeginCompatibilitySection="BX",r.EndCompatibilitySection="EX"})(xe||(xe={}));var qi=function(r){X(e,r);function e(t,n){var i=r.call(this,t)||this;return i.computeContents=function(){var a=i.getUnencodedContents();return i.encode?Zn.deflate(a):a},i.encode=n,n&&t.set(m.of("Filter"),m.of("FlateDecode")),i.contentsCache=Gt.populatedBy(i.computeContents),i}return e.prototype.getContents=function(){return this.contentsCache.access()},e.prototype.getContentsSize=function(){return this.contentsCache.access().length},e.prototype.getUnencodedContents=function(){throw new Pt(this.constructor.name,"getUnencodedContents")},e}(gt),Qr=function(r){X(e,r);function e(t,n,i){i===void 0&&(i=!0);var a=r.call(this,t,i)||this;return a.operators=n,a}return e.prototype.push=function(){for(var t,n=[],i=0;i<arguments.length;i++)n[i]=arguments[i];(t=this.operators).push.apply(t,n)},e.prototype.clone=function(t){for(var n=new Array(this.operators.length),i=0,a=this.operators.length;i<a;i++)n[i]=this.operators[i].clone(t);var o=this,s=o.dict,u=o.encode;return e.of(s.clone(t),n,u)},e.prototype.getContentsString=function(){for(var t="",n=0,i=this.operators.length;n<i;n++)t+=this.operators[n]+`
`;return t},e.prototype.getUnencodedContents=function(){for(var t=new Uint8Array(this.getUnencodedContentsSize()),n=0,i=0,a=this.operators.length;i<a;i++)n+=this.operators[i].copyBytesInto(t,n),t[n++]=x.Newline;return t},e.prototype.getUnencodedContentsSize=function(){for(var t=0,n=0,i=this.operators.length;n<i;n++)t+=this.operators[n].sizeInBytes()+1;return t},e.of=function(t,n,i){return i===void 0&&(i=!0),new e(t,n,i)},e}(qi),ju=function(){function r(e){this.seed=e}return r.prototype.nextInt=function(){var e=Math.sin(this.seed++)*1e4;return e-Math.floor(e)},r.withSeed=function(e){return new r(e)},r}(),Mu=function(r,e){var t=r[0],n=e[0];return t.objectNumber-n.objectNumber},Pi=function(){function r(){this.largestObjectNumber=0,this.header=Qn.forVersion(1,7),this.trailerInfo={},this.indirectObjects=new Map,this.rng=ju.withSeed(1)}return r.prototype.assign=function(e,t){this.indirectObjects.set(e,t),e.objectNumber>this.largestObjectNumber&&(this.largestObjectNumber=e.objectNumber)},r.prototype.nextRef=function(){return this.largestObjectNumber+=1,Ee.of(this.largestObjectNumber)},r.prototype.register=function(e){var t=this.nextRef();return this.assign(t,e),t},r.prototype.delete=function(e){return this.indirectObjects.delete(e)},r.prototype.lookupMaybe=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var i=t.includes(ht),a=e instanceof Ee?this.indirectObjects.get(e):e;if(!(!a||a===ht&&!i)){for(var o=0,s=t.length;o<s;o++){var u=t[o];if(u===ht){if(a===ht)return a}else if(a instanceof u)return a}throw new zn(t,a)}},r.prototype.lookup=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var i=e instanceof Ee?this.indirectObjects.get(e):e;if(t.length===0)return i;for(var a=0,o=t.length;a<o;a++){var s=t[a];if(s===ht){if(i===ht)return i}else if(i instanceof s)return i}throw new zn(t,i)},r.prototype.getObjectRef=function(e){for(var t=Array.from(this.indirectObjects.entries()),n=0,i=t.length;n<i;n++){var a=t[n],o=a[0],s=a[1];if(s===e)return o}},r.prototype.enumerateIndirectObjects=function(){return Array.from(this.indirectObjects.entries()).sort(Mu)},r.prototype.obj=function(e){if(e instanceof ut)return e;if(e==null)return ht;if(typeof e=="string")return m.of(e);if(typeof e=="number")return fe.of(e);if(typeof e=="boolean")return e?nn.True:nn.False;if(Array.isArray(e)){for(var t=Ae.withContext(this),n=0,i=e.length;n<i;n++)t.push(this.obj(e[n]));return t}else{for(var a=ve.withContext(this),o=Object.keys(e),n=0,i=o.length;n<i;n++){var s=o[n],u=e[s];u!==void 0&&a.set(m.of(s),this.obj(u))}return a}},r.prototype.stream=function(e,t){return t===void 0&&(t={}),an.of(this.obj(t),Ti(e))},r.prototype.flateStream=function(e,t){return t===void 0&&(t={}),this.stream(Zn.deflate(Ti(e)),he(he({},t),{Filter:"FlateDecode"}))},r.prototype.contentStream=function(e,t){return t===void 0&&(t={}),Qr.of(this.obj(t),e)},r.prototype.formXObject=function(e,t){return t===void 0&&(t={}),this.contentStream(e,he(he({BBox:this.obj([0,0,0,0]),Matrix:this.obj([1,0,0,1,0,0])},t),{Type:"XObject",Subtype:"Form"}))},r.prototype.getPushGraphicsStateContentStream=function(){if(this.pushGraphicsStateContentStreamRef)return this.pushGraphicsStateContentStreamRef;var e=this.obj({}),t=Fe.of(xe.PushGraphicsState),n=Qr.of(e,[t]);return this.pushGraphicsStateContentStreamRef=this.register(n),this.pushGraphicsStateContentStreamRef},r.prototype.getPopGraphicsStateContentStream=function(){if(this.popGraphicsStateContentStreamRef)return this.popGraphicsStateContentStreamRef;var e=this.obj({}),t=Fe.of(xe.PopGraphicsState),n=Qr.of(e,[t]);return this.popGraphicsStateContentStreamRef=this.register(n),this.popGraphicsStateContentStreamRef},r.prototype.addRandomSuffix=function(e,t){return t===void 0&&(t=4),e+"-"+Math.floor(this.rng.nextInt()*Math.pow(10,t))},r.create=function(){return new r},r}(),Ht=function(r){X(e,r);function e(t,n,i){i===void 0&&(i=!0);var a=r.call(this,t,n)||this;return a.normalized=!1,a.autoNormalizeCTM=i,a}return e.prototype.clone=function(t){for(var n=e.fromMapWithContext(new Map,t||this.context,this.autoNormalizeCTM),i=this.entries(),a=0,o=i.length;a<o;a++){var s=i[a],u=s[0],f=s[1];n.set(u,f)}return n},e.prototype.Parent=function(){return this.lookupMaybe(m.Parent,ve)},e.prototype.Contents=function(){return this.lookup(m.of("Contents"))},e.prototype.Annots=function(){return this.lookupMaybe(m.Annots,Ae)},e.prototype.BleedBox=function(){return this.lookupMaybe(m.BleedBox,Ae)},e.prototype.TrimBox=function(){return this.lookupMaybe(m.TrimBox,Ae)},e.prototype.ArtBox=function(){return this.lookupMaybe(m.ArtBox,Ae)},e.prototype.Resources=function(){var t=this.getInheritableAttribute(m.Resources);return this.context.lookupMaybe(t,ve)},e.prototype.MediaBox=function(){var t=this.getInheritableAttribute(m.MediaBox);return this.context.lookup(t,Ae)},e.prototype.CropBox=function(){var t=this.getInheritableAttribute(m.CropBox);return this.context.lookupMaybe(t,Ae)},e.prototype.Rotate=function(){var t=this.getInheritableAttribute(m.Rotate);return this.context.lookupMaybe(t,fe)},e.prototype.getInheritableAttribute=function(t){var n;return this.ascend(function(i){n||(n=i.get(t))}),n},e.prototype.setParent=function(t){this.set(m.Parent,t)},e.prototype.addContentStream=function(t){var n=this.normalizedEntries().Contents||this.context.obj([]);this.set(m.Contents,n),n.push(t)},e.prototype.wrapContentStreams=function(t,n){var i=this.Contents();return i instanceof Ae?(i.insert(0,t),i.push(n),!0):!1},e.prototype.addAnnot=function(t){var n=this.normalizedEntries().Annots;n.push(t)},e.prototype.removeAnnot=function(t){var n=this.normalizedEntries().Annots,i=n.indexOf(t);i!==void 0&&n.remove(i)},e.prototype.setFontDictionary=function(t,n){var i=this.normalizedEntries().Font;i.set(t,n)},e.prototype.newFontDictionaryKey=function(t){var n=this.normalizedEntries().Font;return n.uniqueKey(t)},e.prototype.newFontDictionary=function(t,n){var i=this.newFontDictionaryKey(t);return this.setFontDictionary(i,n),i},e.prototype.setXObject=function(t,n){var i=this.normalizedEntries().XObject;i.set(t,n)},e.prototype.newXObjectKey=function(t){var n=this.normalizedEntries().XObject;return n.uniqueKey(t)},e.prototype.newXObject=function(t,n){var i=this.newXObjectKey(t);return this.setXObject(i,n),i},e.prototype.setExtGState=function(t,n){var i=this.normalizedEntries().ExtGState;i.set(t,n)},e.prototype.newExtGStateKey=function(t){var n=this.normalizedEntries().ExtGState;return n.uniqueKey(t)},e.prototype.newExtGState=function(t,n){var i=this.newExtGStateKey(t);return this.setExtGState(i,n),i},e.prototype.ascend=function(t){t(this);var n=this.Parent();n&&n.ascend(t)},e.prototype.normalize=function(){if(!this.normalized){var t=this.context,n=this.get(m.Contents),i=this.context.lookup(n);i instanceof gt&&this.set(m.Contents,t.obj([n])),this.autoNormalizeCTM&&this.wrapContentStreams(this.context.getPushGraphicsStateContentStream(),this.context.getPopGraphicsStateContentStream());var a=this.getInheritableAttribute(m.Resources),o=t.lookupMaybe(a,ve)||t.obj({});this.set(m.Resources,o);var s=o.lookupMaybe(m.Font,ve)||t.obj({});o.set(m.Font,s);var u=o.lookupMaybe(m.XObject,ve)||t.obj({});o.set(m.XObject,u);var f=o.lookupMaybe(m.ExtGState,ve)||t.obj({});o.set(m.ExtGState,f);var l=this.Annots()||t.obj([]);this.set(m.Annots,l),this.normalized=!0}},e.prototype.normalizedEntries=function(){this.normalize();var t=this.Annots(),n=this.Resources(),i=this.Contents();return{Annots:t,Resources:n,Contents:i,Font:n.lookup(m.Font,ve),XObject:n.lookup(m.XObject,ve),ExtGState:n.lookup(m.ExtGState,ve)}},e.InheritableEntries=["Resources","MediaBox","CropBox","Rotate"],e.withContextAndParent=function(t,n){var i=new Map;return i.set(m.Type,m.Page),i.set(m.Parent,n),i.set(m.Resources,t.obj({})),i.set(m.MediaBox,t.obj([0,0,612,792])),new e(i,t,!1)},e.fromMapWithContext=function(t,n,i){return i===void 0&&(i=!0),new e(t,n,i)},e}(ve),za=function(){function r(e,t){var n=this;this.traversedObjects=new Map,this.copy=function(i){return i instanceof Ht?n.copyPDFPage(i):i instanceof ve?n.copyPDFDict(i):i instanceof Ae?n.copyPDFArray(i):i instanceof gt?n.copyPDFStream(i):i instanceof Ee?n.copyPDFIndirectObject(i):i.clone()},this.copyPDFPage=function(i){for(var a=i.clone(),o=Ht.InheritableEntries,s=0,u=o.length;s<u;s++){var f=m.of(o[s]),l=a.getInheritableAttribute(f);!a.get(f)&&l&&a.set(f,l)}return a.delete(m.of("Parent")),n.copyPDFDict(a)},this.copyPDFDict=function(i){if(n.traversedObjects.has(i))return n.traversedObjects.get(i);var a=i.clone(n.dest);n.traversedObjects.set(i,a);for(var o=i.entries(),s=0,u=o.length;s<u;s++){var f=o[s],l=f[0],h=f[1];a.set(l,n.copy(h))}return a},this.copyPDFArray=function(i){if(n.traversedObjects.has(i))return n.traversedObjects.get(i);var a=i.clone(n.dest);n.traversedObjects.set(i,a);for(var o=0,s=i.size();o<s;o++){var u=i.get(o);a.set(o,n.copy(u))}return a},this.copyPDFStream=function(i){if(n.traversedObjects.has(i))return n.traversedObjects.get(i);var a=i.clone(n.dest);n.traversedObjects.set(i,a);for(var o=i.dict.entries(),s=0,u=o.length;s<u;s++){var f=o[s],l=f[0],h=f[1];a.dict.set(l,n.copy(h))}return a},this.copyPDFIndirectObject=function(i){var a=n.traversedObjects.has(i);if(!a){var o=n.dest.nextRef();n.traversedObjects.set(i,o);var s=n.src.lookup(i);if(s){var u=n.copy(s);n.dest.assign(o,u)}}return n.traversedObjects.get(i)},this.src=e,this.dest=t}return r.for=function(e,t){return new r(e,t)},r}(),xo=function(){function r(e){this.subsections=e?[[e]]:[],this.chunkIdx=0,this.chunkLength=e?1:0}return r.prototype.addEntry=function(e,t){this.append({ref:e,offset:t,deleted:!1})},r.prototype.addDeletedEntry=function(e,t){this.append({ref:e,offset:t,deleted:!0})},r.prototype.toString=function(){for(var e=`xref
`,t=0,n=this.subsections.length;t<n;t++){var i=this.subsections[t];e+=i[0].ref.objectNumber+" "+i.length+`
`;for(var a=0,o=i.length;a<o;a++){var s=i[a];e+=jt(String(s.offset),10,"0"),e+=" ",e+=jt(String(s.ref.generationNumber),5,"0"),e+=" ",e+=s.deleted?"f":"n",e+=` 
`}}return e},r.prototype.sizeInBytes=function(){for(var e=5,t=0,n=this.subsections.length;t<n;t++){var i=this.subsections[t],a=i.length,o=i[0];e+=2,e+=String(o.ref.objectNumber).length,e+=String(a).length,e+=20*a}return e},r.prototype.copyBytesInto=function(e,t){var n=t;return e[t++]=x.x,e[t++]=x.r,e[t++]=x.e,e[t++]=x.f,e[t++]=x.Newline,t+=this.copySubsectionsIntoBuffer(this.subsections,e,t),t-n},r.prototype.copySubsectionsIntoBuffer=function(e,t,n){for(var i=n,a=e.length,o=0;o<a;o++){var s=this.subsections[o],u=String(s[0].ref.objectNumber);n+=rt(u,t,n),t[n++]=x.Space;var f=String(s.length);n+=rt(f,t,n),t[n++]=x.Newline,n+=this.copyEntriesIntoBuffer(s,t,n)}return n-i},r.prototype.copyEntriesIntoBuffer=function(e,t,n){for(var i=e.length,a=0;a<i;a++){var o=e[a],s=jt(String(o.offset),10,"0");n+=rt(s,t,n),t[n++]=x.Space;var u=jt(String(o.ref.generationNumber),5,"0");n+=rt(u,t,n),t[n++]=x.Space,t[n++]=o.deleted?x.f:x.n,t[n++]=x.Space,t[n++]=x.Newline}return 20*i},r.prototype.append=function(e){if(this.chunkLength===0){this.subsections.push([e]),this.chunkIdx=0,this.chunkLength=1;return}var t=this.subsections[this.chunkIdx],n=t[this.chunkLength-1];e.ref.objectNumber-n.ref.objectNumber>1?(this.subsections.push([e]),this.chunkIdx+=1,this.chunkLength=1):(t.push(e),this.chunkLength+=1)},r.create=function(){return new r({ref:Ee.of(0,65535),offset:0,deleted:!0})},r.createEmpty=function(){return new r},r}(),Wi=function(){function r(e){this.lastXRefOffset=String(e)}return r.prototype.toString=function(){return`startxref
`+this.lastXRefOffset+`
%%EOF`},r.prototype.sizeInBytes=function(){return 16+this.lastXRefOffset.length},r.prototype.copyBytesInto=function(e,t){var n=t;return e[t++]=x.s,e[t++]=x.t,e[t++]=x.a,e[t++]=x.r,e[t++]=x.t,e[t++]=x.x,e[t++]=x.r,e[t++]=x.e,e[t++]=x.f,e[t++]=x.Newline,t+=rt(this.lastXRefOffset,e,t),e[t++]=x.Newline,e[t++]=x.Percent,e[t++]=x.Percent,e[t++]=x.E,e[t++]=x.O,e[t++]=x.F,t-n},r.forLastCrossRefSectionOffset=function(e){return new r(e)},r}(),Iu=function(){function r(e){this.dict=e}return r.prototype.toString=function(){return`trailer
`+this.dict.toString()},r.prototype.sizeInBytes=function(){return 8+this.dict.sizeInBytes()},r.prototype.copyBytesInto=function(e,t){var n=t;return e[t++]=x.t,e[t++]=x.r,e[t++]=x.a,e[t++]=x.i,e[t++]=x.l,e[t++]=x.e,e[t++]=x.r,e[t++]=x.Newline,t+=this.dict.copyBytesInto(e,t),t-n},r.of=function(e){return new r(e)},r}(),wo=function(r){X(e,r);function e(t,n,i){i===void 0&&(i=!0);var a=r.call(this,t.obj({}),i)||this;return a.objects=n,a.offsets=a.computeObjectOffsets(),a.offsetsString=a.computeOffsetsString(),a.dict.set(m.of("Type"),m.of("ObjStm")),a.dict.set(m.of("N"),fe.of(a.objects.length)),a.dict.set(m.of("First"),fe.of(a.offsetsString.length)),a}return e.prototype.getObjectsCount=function(){return this.objects.length},e.prototype.clone=function(t){return e.withContextAndObjects(t||this.dict.context,this.objects.slice(),this.encode)},e.prototype.getContentsString=function(){for(var t=this.offsetsString,n=0,i=this.objects.length;n<i;n++){var a=this.objects[n],o=a[1];t+=o+`
`}return t},e.prototype.getUnencodedContents=function(){for(var t=new Uint8Array(this.getUnencodedContentsSize()),n=rt(this.offsetsString,t,0),i=0,a=this.objects.length;i<a;i++){var o=this.objects[i],s=o[1];n+=s.copyBytesInto(t,n),t[n++]=x.Newline}return t},e.prototype.getUnencodedContentsSize=function(){return this.offsetsString.length+Nn(this.offsets)[1]+Nn(this.objects)[1].sizeInBytes()+1},e.prototype.computeOffsetsString=function(){for(var t="",n=0,i=this.offsets.length;n<i;n++){var a=this.offsets[n],o=a[0],s=a[1];t+=o+" "+s+" "}return t},e.prototype.computeObjectOffsets=function(){for(var t=0,n=new Array(this.objects.length),i=0,a=this.objects.length;i<a;i++){var o=this.objects[i],s=o[0],u=o[1];n[i]=[s.objectNumber,t],t+=u.sizeInBytes()+1}return n},e.withContextAndObjects=function(t,n,i){return i===void 0&&(i=!0),new e(t,n,i)},e}(qi),So=function(){function r(e,t){var n=this;this.parsedObjects=0,this.shouldWaitForTick=function(i){return n.parsedObjects+=i,n.parsedObjects%n.objectsPerTick===0},this.context=e,this.objectsPerTick=t}return r.prototype.serializeToBuffer=function(){return pe(this,void 0,void 0,function(){var e,t,n,i,a,o,s,u,f,l,h,d,v,y,w,F,S;return ge(this,function(R){switch(R.label){case 0:return[4,this.computeBufferSize()];case 1:e=R.sent(),t=e.size,n=e.header,i=e.indirectObjects,a=e.xref,o=e.trailerDict,s=e.trailer,u=0,f=new Uint8Array(t),u+=n.copyBytesInto(f,u),f[u++]=x.Newline,f[u++]=x.Newline,l=0,h=i.length,R.label=2;case 2:return l<h?(d=i[l],v=d[0],y=d[1],w=String(v.objectNumber),u+=rt(w,f,u),f[u++]=x.Space,F=String(v.generationNumber),u+=rt(F,f,u),f[u++]=x.Space,f[u++]=x.o,f[u++]=x.b,f[u++]=x.j,f[u++]=x.Newline,u+=y.copyBytesInto(f,u),f[u++]=x.Newline,f[u++]=x.e,f[u++]=x.n,f[u++]=x.d,f[u++]=x.o,f[u++]=x.b,f[u++]=x.j,f[u++]=x.Newline,f[u++]=x.Newline,S=y instanceof wo?y.getObjectsCount():1,this.shouldWaitForTick(S)?[4,Er()]:[3,4]):[3,5];case 3:R.sent(),R.label=4;case 4:return l++,[3,2];case 5:return a&&(u+=a.copyBytesInto(f,u),f[u++]=x.Newline),o&&(u+=o.copyBytesInto(f,u),f[u++]=x.Newline,f[u++]=x.Newline),u+=s.copyBytesInto(f,u),[2,f]}})})},r.prototype.computeIndirectObjectSize=function(e){var t=e[0],n=e[1],i=t.sizeInBytes()+3,a=n.sizeInBytes()+9;return i+a},r.prototype.createTrailerDict=function(){return this.context.obj({Size:this.context.largestObjectNumber+1,Root:this.context.trailerInfo.Root,Encrypt:this.context.trailerInfo.Encrypt,Info:this.context.trailerInfo.Info,ID:this.context.trailerInfo.ID})},r.prototype.computeBufferSize=function(){return pe(this,void 0,void 0,function(){var e,t,n,i,a,o,s,u,f,l,h;return ge(this,function(d){switch(d.label){case 0:e=Qn.forVersion(1,7),t=e.sizeInBytes()+2,n=xo.create(),i=this.context.enumerateIndirectObjects(),a=0,o=i.length,d.label=1;case 1:return a<o?(s=i[a],u=s[0],n.addEntry(u,t),t+=this.computeIndirectObjectSize(s),this.shouldWaitForTick(1)?[4,Er()]:[3,3]):[3,4];case 2:d.sent(),d.label=3;case 3:return a++,[3,1];case 4:return f=t,t+=n.sizeInBytes()+1,l=Iu.of(this.createTrailerDict()),t+=l.sizeInBytes()+2,h=Wi.forLastCrossRefSectionOffset(f),t+=h.sizeInBytes(),[2,{size:t,header:e,indirectObjects:i,xref:n,trailerDict:l,trailer:h}]}})})},r.forContext=function(e,t){return new r(e,t)},r}(),Fo=function(r){X(e,r);function e(t){var n=r.call(this)||this;return n.data=t,n}return e.prototype.clone=function(){return e.of(this.data.slice())},e.prototype.toString=function(){return"PDFInvalidObject("+this.data.length+" bytes)"},e.prototype.sizeInBytes=function(){return this.data.length},e.prototype.copyBytesInto=function(t,n){for(var i=this.data.length,a=0;a<i;a++)t[n++]=this.data[a];return i},e.of=function(t){return new e(t)},e}(ut),$t;(function(r){r[r.Deleted=0]="Deleted",r[r.Uncompressed=1]="Uncompressed",r[r.Compressed=2]="Compressed"})($t||($t={}));var Uu=function(r){X(e,r);function e(t,n,i){i===void 0&&(i=!0);var a=r.call(this,t,i)||this;return a.computeIndex=function(){for(var o=[],s=0,u=0,f=a.entries.length;u<f;u++){var l=a.entries[u],h=a.entries[u-1];u===0?o.push(l.ref.objectNumber):l.ref.objectNumber-h.ref.objectNumber>1&&(o.push(s),o.push(l.ref.objectNumber),s=0),s+=1}return o.push(s),o},a.computeEntryTuples=function(){for(var o=new Array(a.entries.length),s=0,u=a.entries.length;s<u;s++){var f=a.entries[s];if(f.type===$t.Deleted){var l=f.type,h=f.nextFreeObjectNumber,d=f.ref;o[s]=[l,h,d.generationNumber]}if(f.type===$t.Uncompressed){var l=f.type,v=f.offset,d=f.ref;o[s]=[l,v,d.generationNumber]}if(f.type===$t.Compressed){var l=f.type,y=f.objectStreamRef,w=f.index;o[s]=[l,y.objectNumber,w]}}return o},a.computeMaxEntryByteWidths=function(){for(var o=a.entryTuplesCache.access(),s=[0,0,0],u=0,f=o.length;u<f;u++){var l=o[u],h=l[0],d=l[1],v=l[2],y=Dn(h),w=Dn(d),F=Dn(v);y>s[0]&&(s[0]=y),w>s[1]&&(s[1]=w),F>s[2]&&(s[2]=F)}return s},a.entries=n||[],a.entryTuplesCache=Gt.populatedBy(a.computeEntryTuples),a.maxByteWidthsCache=Gt.populatedBy(a.computeMaxEntryByteWidths),a.indexCache=Gt.populatedBy(a.computeIndex),t.set(m.of("Type"),m.of("XRef")),a}return e.prototype.addDeletedEntry=function(t,n){var i=$t.Deleted;this.entries.push({type:i,ref:t,nextFreeObjectNumber:n}),this.entryTuplesCache.invalidate(),this.maxByteWidthsCache.invalidate(),this.indexCache.invalidate(),this.contentsCache.invalidate()},e.prototype.addUncompressedEntry=function(t,n){var i=$t.Uncompressed;this.entries.push({type:i,ref:t,offset:n}),this.entryTuplesCache.invalidate(),this.maxByteWidthsCache.invalidate(),this.indexCache.invalidate(),this.contentsCache.invalidate()},e.prototype.addCompressedEntry=function(t,n,i){var a=$t.Compressed;this.entries.push({type:a,ref:t,objectStreamRef:n,index:i}),this.entryTuplesCache.invalidate(),this.maxByteWidthsCache.invalidate(),this.indexCache.invalidate(),this.contentsCache.invalidate()},e.prototype.clone=function(t){var n=this,i=n.dict,a=n.entries,o=n.encode;return e.of(i.clone(t),a.slice(),o)},e.prototype.getContentsString=function(){for(var t=this.entryTuplesCache.access(),n=this.maxByteWidthsCache.access(),i="",a=0,o=t.length;a<o;a++){for(var s=t[a],u=s[0],f=s[1],l=s[2],h=Fr(kr(u)),d=Fr(kr(f)),v=Fr(kr(l)),y=n[0]-1;y>=0;y--)i+=(h[y]||0).toString(2);for(var y=n[1]-1;y>=0;y--)i+=(d[y]||0).toString(2);for(var y=n[2]-1;y>=0;y--)i+=(v[y]||0).toString(2)}return i},e.prototype.getUnencodedContents=function(){for(var t=this.entryTuplesCache.access(),n=this.maxByteWidthsCache.access(),i=new Uint8Array(this.getUnencodedContentsSize()),a=0,o=0,s=t.length;o<s;o++){for(var u=t[o],f=u[0],l=u[1],h=u[2],d=Fr(kr(f)),v=Fr(kr(l)),y=Fr(kr(h)),w=n[0]-1;w>=0;w--)i[a++]=d[w]||0;for(var w=n[1]-1;w>=0;w--)i[a++]=v[w]||0;for(var w=n[2]-1;w>=0;w--)i[a++]=y[w]||0}return i},e.prototype.getUnencodedContentsSize=function(){var t=this.maxByteWidthsCache.access(),n=ws(t);return n*this.entries.length},e.prototype.updateDict=function(){r.prototype.updateDict.call(this);var t=this.maxByteWidthsCache.access(),n=this.indexCache.access(),i=this.dict.context;this.dict.set(m.of("W"),i.obj(t)),this.dict.set(m.of("Index"),i.obj(n))},e.create=function(t,n){n===void 0&&(n=!0);var i=new e(t,[],n);return i.addDeletedEntry(Ee.of(0,65535),0),i},e.of=function(t,n,i){return i===void 0&&(i=!0),new e(t,n,i)},e}(qi),Vu=function(r){X(e,r);function e(t,n,i,a){var o=r.call(this,t,n)||this;return o.encodeStreams=i,o.objectsPerStream=a,o}return e.prototype.computeBufferSize=function(){return pe(this,void 0,void 0,function(){var t,n,i,a,o,s,u,f,y,w,l,S,h,d,F,v,y,w,F,S,R,C,B,E;return ge(this,function(D){switch(D.label){case 0:t=this.context.largestObjectNumber+1,n=Qn.forVersion(1,7),i=n.sizeInBytes()+2,a=Uu.create(this.createTrailerDict(),this.encodeStreams),o=[],s=[],u=[],f=this.context.enumerateIndirectObjects(),y=0,w=f.length,D.label=1;case 1:return y<w?(l=f[y],S=l[0],h=l[1],d=S===this.context.trailerInfo.Encrypt||h instanceof gt||h instanceof Fo||S.generationNumber!==0,d?(o.push(l),a.addUncompressedEntry(S,i),i+=this.computeIndirectObjectSize(l),this.shouldWaitForTick(1)?[4,Er()]:[3,3]):[3,4]):[3,6];case 2:D.sent(),D.label=3;case 3:return[3,5];case 4:F=Nn(s),v=Nn(u),(!F||F.length%this.objectsPerStream===0)&&(F=[],s.push(F),v=Ee.of(t++),u.push(v)),a.addCompressedEntry(S,v,F.length),F.push(l),D.label=5;case 5:return y++,[3,1];case 6:y=0,w=s.length,D.label=7;case 7:return y<w?(F=s[y],S=u[y],R=wo.withContextAndObjects(this.context,F,this.encodeStreams),a.addUncompressedEntry(S,i),i+=this.computeIndirectObjectSize([S,R]),o.push([S,R]),this.shouldWaitForTick(F.length)?[4,Er()]:[3,9]):[3,10];case 8:D.sent(),D.label=9;case 9:return y++,[3,7];case 10:return C=Ee.of(t++),a.dict.set(m.of("Size"),fe.of(t)),a.addUncompressedEntry(C,i),B=i,i+=this.computeIndirectObjectSize([C,a]),o.push([C,a]),E=Wi.forLastCrossRefSectionOffset(B),i+=E.sizeInBytes(),[2,{size:i,header:n,indirectObjects:o,trailer:E}]}})})},e.forContext=function(t,n,i,a){return i===void 0&&(i=!0),a===void 0&&(a=50),new e(t,n,i,a)},e}(So),oe=function(r){X(e,r);function e(t){var n=r.call(this)||this;return n.value=t,n}return e.prototype.asBytes=function(){for(var t=this.value+(this.value.length%2===1?"0":""),n=t.length,i=new Uint8Array(t.length/2),a=0,o=0;a<n;){var s=parseInt(t.substring(a,a+2),16);i[o]=s,a+=2,o+=1}return i},e.prototype.decodeText=function(){var t=this.asBytes();return uo(t)?ao(t):bo(t)},e.prototype.decodeDate=function(){var t=this.decodeText(),n=to(t);if(!n)throw new mo(t);return n},e.prototype.asString=function(){return this.value},e.prototype.clone=function(){return e.of(this.value)},e.prototype.toString=function(){return"<"+this.value+">"},e.prototype.sizeInBytes=function(){return this.value.length+2},e.prototype.copyBytesInto=function(t,n){return t[n++]=x.LessThan,n+=rt(this.value,t,n),t[n++]=x.GreaterThan,this.value.length+2},e.of=function(t){return new e(t)},e.fromText=function(t){for(var n=Cs(t),i="",a=0,o=n.length;a<o;a++)i+=un(n[a],4);return new e(i)},e}(ut),Mn=function(){function r(e,t){this.encoding=e===rn.ZapfDingbats?mn.ZapfDingbats:e===rn.Symbol?mn.Symbol:mn.WinAnsi,this.font=au.load(e),this.fontName=this.font.FontName,this.customName=t}return r.prototype.encodeText=function(e){for(var t=this.encodeTextAsGlyphs(e),n=new Array(t.length),i=0,a=t.length;i<a;i++)n[i]=Xn(t[i].code);return oe.of(n.join(""))},r.prototype.widthOfTextAtSize=function(e,t){for(var n=this.encodeTextAsGlyphs(e),i=0,a=0,o=n.length;a<o;a++){var s=n[a].name,u=(n[a+1]||{}).name,f=this.font.getXAxisKerningForPair(s,u)||0;i+=this.widthOfGlyph(s)+f}var l=t/1e3;return i*l},r.prototype.heightOfFontAtSize=function(e,t){t===void 0&&(t={});var n=t.descender,i=n===void 0?!0:n,a=this.font,o=a.Ascender,s=a.Descender,u=a.FontBBox,f=o||u[3],l=s||u[1],h=f-l;return i||(h+=s||0),h/1e3*e},r.prototype.sizeOfFontAtHeight=function(e){var t=this.font,n=t.Ascender,i=t.Descender,a=t.FontBBox,o=n||a[3],s=i||a[1];return 1e3*e/(o-s)},r.prototype.embedIntoContext=function(e,t){var n=e.obj({Type:"Font",Subtype:"Type1",BaseFont:this.customName||this.fontName,Encoding:this.encoding===mn.WinAnsi?"WinAnsiEncoding":void 0});return t?(e.assign(t,n),t):e.register(n)},r.prototype.widthOfGlyph=function(e){return this.font.getWidthOfGlyph(e)||250},r.prototype.encodeTextAsGlyphs=function(e){for(var t=Array.from(e),n=new Array(t.length),i=0,a=t.length;i<a;i++){var o=fs(t[i]);n[i]=this.encoding.encodeUnicodeCodePoint(o)}return n},r.for=function(e,t){return new r(e,t)},r}(),qu=function(r,e){for(var t=new Array(r.length),n=0,i=r.length;n<i;n++){var a=r[n],o=ja(An(e(a))),s=ja.apply(void 0,a.codePoints.map(Ku));t[n]=[o,s]}return Wu(t)},Wu=function(r){return`/CIDInit /ProcSet findresource begin
12 dict begin
begincmap
/CIDSystemInfo <<
  /Registry (Adobe)
  /Ordering (UCS)
  /Supplement 0
>> def
/CMapName /Adobe-Identity-UCS def
/CMapType 2 def
1 begincodespacerange
<0000><ffff>
endcodespacerange
`+r.length+` beginbfchar
`+r.map(function(e){var t=e[0],n=e[1];return t+" "+n}).join(`
`)+`
endbfchar
endcmap
CMapName currentdict /CMap defineresource pop
end
end`},ja=function(){for(var r=[],e=0;e<arguments.length;e++)r[e]=arguments[e];return"<"+r.join("")+">"},An=function(r){return un(r,4)},Ku=function(r){if(Ts(r))return An(r);if(Ps(r)){var e=no(r),t=io(r);return""+An(e)+An(t)}var n=Xn(r),i="0x"+n+" is not a valid UTF-8 or UTF-16 codepoint.";throw new Error(i)},Lu=function(r){var e=0,t=function(n){e|=1<<n-1};return r.fixedPitch&&t(1),r.serif&&t(2),t(3),r.script&&t(4),r.nonsymbolic&&t(6),r.italic&&t(7),r.allCap&&t(17),r.smallCap&&t(18),r.forceBold&&t(19),e},Gu=function(r){var e=r["OS/2"]?r["OS/2"].sFamilyClass:0,t=Lu({fixedPitch:r.post.isFixedPitch,serif:1<=e&&e<=7,script:e===10,italic:r.head.macStyle.italic});return t},Oe=function(r){X(e,r);function e(t){var n=r.call(this)||this;return n.value=t,n}return e.prototype.asBytes=function(){for(var t=[],n="",i=!1,a=function(h){h!==void 0&&t.push(h),i=!1},o=0,s=this.value.length;o<s;o++){var u=this.value[o],f=le(u),l=this.value[o+1];i?f===x.Newline||f===x.CarriageReturn?a():f===x.n?a(x.Newline):f===x.r?a(x.CarriageReturn):f===x.t?a(x.Tab):f===x.b?a(x.Backspace):f===x.f?a(x.FormFeed):f===x.LeftParen?a(x.LeftParen):f===x.RightParen?a(x.RightParen):f===x.Backspace?a(x.BackSlash):f>=x.Zero&&f<=x.Seven?(n+=u,(n.length===3||!(l>="0"&&l<="7"))&&(a(parseInt(n,8)),n="")):a(f):f===x.BackSlash?i=!0:a(f)}return new Uint8Array(t)},e.prototype.decodeText=function(){var t=this.asBytes();return uo(t)?ao(t):bo(t)},e.prototype.decodeDate=function(){var t=this.decodeText(),n=to(t);if(!n)throw new mo(t);return n},e.prototype.asString=function(){return this.value},e.prototype.clone=function(){return e.of(this.value)},e.prototype.toString=function(){return"("+this.value+")"},e.prototype.sizeInBytes=function(){return this.value.length+2},e.prototype.copyBytesInto=function(t,n){return t[n++]=x.LeftParen,n+=rt(this.value,t,n),t[n++]=x.RightParen,this.value.length+2},e.of=function(t){return new e(t)},e.fromDate=function(t){var n=jt(String(t.getUTCFullYear()),4,"0"),i=jt(String(t.getUTCMonth()+1),2,"0"),a=jt(String(t.getUTCDate()),2,"0"),o=jt(String(t.getUTCHours()),2,"0"),s=jt(String(t.getUTCMinutes()),2,"0"),u=jt(String(t.getUTCSeconds()),2,"0");return new e("D:"+n+i+a+o+s+u+"Z")},e}(ut),Ki=function(){function r(e,t,n,i){var a=this;this.allGlyphsInFontSortedById=function(){for(var o=new Array(a.font.characterSet.length),s=0,u=o.length;s<u;s++){var f=a.font.characterSet[s];o[s]=a.font.glyphForCodePoint(f)}return xs(o.sort(ms),function(l){return l.id})},this.font=e,this.scale=1e3/this.font.unitsPerEm,this.fontData=t,this.fontName=this.font.postscriptName||"Font",this.customName=n,this.fontFeatures=i,this.baseFontName="",this.glyphCache=Gt.populatedBy(this.allGlyphsInFontSortedById)}return r.for=function(e,t,n,i){return pe(this,void 0,void 0,function(){var a;return ge(this,function(o){switch(o.label){case 0:return[4,e.create(t)];case 1:return a=o.sent(),[2,new r(a,t,n,i)]}})})},r.prototype.encodeText=function(e){for(var t=this.font.layout(e,this.fontFeatures).glyphs,n=new Array(t.length),i=0,a=t.length;i<a;i++)n[i]=un(t[i].id,4);return oe.of(n.join(""))},r.prototype.widthOfTextAtSize=function(e,t){for(var n=this.font.layout(e,this.fontFeatures).glyphs,i=0,a=0,o=n.length;a<o;a++)i+=n[a].advanceWidth*this.scale;var s=t/1e3;return i*s},r.prototype.heightOfFontAtSize=function(e,t){t===void 0&&(t={});var n=t.descender,i=n===void 0?!0:n,a=this.font,o=a.ascent,s=a.descent,u=a.bbox,f=(o||u.maxY)*this.scale,l=(s||u.minY)*this.scale,h=f-l;return i||(h-=Math.abs(s)||0),h/1e3*e},r.prototype.sizeOfFontAtHeight=function(e){var t=this.font,n=t.ascent,i=t.descent,a=t.bbox,o=(n||a.maxY)*this.scale,s=(i||a.minY)*this.scale;return 1e3*e/(o-s)},r.prototype.embedIntoContext=function(e,t){return this.baseFontName=this.customName||e.addRandomSuffix(this.fontName),this.embedFontDict(e,t)},r.prototype.embedFontDict=function(e,t){return pe(this,void 0,void 0,function(){var n,i,a;return ge(this,function(o){switch(o.label){case 0:return[4,this.embedCIDFontDict(e)];case 1:return n=o.sent(),i=this.embedUnicodeCmap(e),a=e.obj({Type:"Font",Subtype:"Type0",BaseFont:this.baseFontName,Encoding:"Identity-H",DescendantFonts:[n],ToUnicode:i}),t?(e.assign(t,a),[2,t]):[2,e.register(a)]}})})},r.prototype.isCFF=function(){return this.font.cff},r.prototype.embedCIDFontDict=function(e){return pe(this,void 0,void 0,function(){var t,n;return ge(this,function(i){switch(i.label){case 0:return[4,this.embedFontDescriptor(e)];case 1:return t=i.sent(),n=e.obj({Type:"Font",Subtype:this.isCFF()?"CIDFontType0":"CIDFontType2",CIDToGIDMap:"Identity",BaseFont:this.baseFontName,CIDSystemInfo:{Registry:Oe.of("Adobe"),Ordering:Oe.of("Identity"),Supplement:0},FontDescriptor:t,W:this.computeWidths()}),[2,e.register(n)]}})})},r.prototype.embedFontDescriptor=function(e){return pe(this,void 0,void 0,function(){var t,n,i,a,o,s,u,f,l,h,d,v,y,w,F;return ge(this,function(S){switch(S.label){case 0:return[4,this.embedFontStream(e)];case 1:return t=S.sent(),n=this.scale,i=this.font,a=i.italicAngle,o=i.ascent,s=i.descent,u=i.capHeight,f=i.xHeight,l=this.font.bbox,h=l.minX,d=l.minY,v=l.maxX,y=l.maxY,w=e.obj((F={Type:"FontDescriptor",FontName:this.baseFontName,Flags:Gu(this.font),FontBBox:[h*n,d*n,v*n,y*n],ItalicAngle:a,Ascent:o*n,Descent:s*n,CapHeight:(u||o)*n,XHeight:(f||0)*n,StemV:0},F[this.isCFF()?"FontFile3":"FontFile2"]=t,F)),[2,e.register(w)]}})})},r.prototype.serializeFont=function(){return pe(this,void 0,void 0,function(){return ge(this,function(e){return[2,this.fontData]})})},r.prototype.embedFontStream=function(e){return pe(this,void 0,void 0,function(){var t,n,i;return ge(this,function(a){switch(a.label){case 0:return i=(n=e).flateStream,[4,this.serializeFont()];case 1:return t=i.apply(n,[a.sent(),{Subtype:this.isCFF()?"CIDFontType0C":void 0}]),[2,e.register(t)]}})})},r.prototype.embedUnicodeCmap=function(e){var t=qu(this.glyphCache.access(),this.glyphId.bind(this)),n=e.flateStream(t);return e.register(n)},r.prototype.glyphId=function(e){return e?e.id:-1},r.prototype.computeWidths=function(){for(var e=this.glyphCache.access(),t=[],n=[],i=0,a=e.length;i<a;i++){var o=e[i],s=e[i-1],u=this.glyphId(o),f=this.glyphId(s);i===0?t.push(u):u-f!==1&&(t.push(n),t.push(u),n=[]),n.push(o.advanceWidth*this.scale)}return t.push(n),t},r}(),Hu=function(r){X(e,r);function e(t,n,i,a){var o=r.call(this,t,n,i,a)||this;return o.subset=o.font.createSubset(),o.glyphs=[],o.glyphCache=Gt.populatedBy(function(){return o.glyphs}),o.glyphIdMap=new Map,o}return e.for=function(t,n,i,a){return pe(this,void 0,void 0,function(){var o;return ge(this,function(s){switch(s.label){case 0:return[4,t.create(n)];case 1:return o=s.sent(),[2,new e(o,n,i,a)]}})})},e.prototype.encodeText=function(t){for(var n=this.font.layout(t,this.fontFeatures).glyphs,i=new Array(n.length),a=0,o=n.length;a<o;a++){var s=n[a],u=this.subset.includeGlyph(s);this.glyphs[u-1]=s,this.glyphIdMap.set(s.id,u),i[a]=un(u,4)}return this.glyphCache.invalidate(),oe.of(i.join(""))},e.prototype.isCFF=function(){return this.subset.cff},e.prototype.glyphId=function(t){return t?this.glyphIdMap.get(t.id):-1},e.prototype.serializeFont=function(){var t=this;return new Promise(function(n,i){var a=[];t.subset.encodeStream().on("data",function(o){return a.push(o)}).on("end",function(){return n(bs(a))}).on("error",function(o){return i(o)})})},e}(Ki),Di;(function(r){r.Source="Source",r.Data="Data",r.Alternative="Alternative",r.Supplement="Supplement",r.EncryptedPayload="EncryptedPayload",r.FormData="EncryptedPayload",r.Schema="Schema",r.Unspecified="Unspecified"})(Di||(Di={}));var Xu=function(){function r(e,t,n){n===void 0&&(n={}),this.fileData=e,this.fileName=t,this.options=n}return r.for=function(e,t,n){return n===void 0&&(n={}),new r(e,t,n)},r.prototype.embedIntoContext=function(e,t){return pe(this,void 0,void 0,function(){var n,i,a,o,s,u,f,l,h;return ge(this,function(d){return n=this.options,i=n.mimeType,a=n.description,o=n.creationDate,s=n.modificationDate,u=n.afRelationship,f=e.flateStream(this.fileData,{Type:"EmbeddedFile",Subtype:i??void 0,Params:{Size:this.fileData.length,CreationDate:o?Oe.fromDate(o):void 0,ModDate:s?Oe.fromDate(s):void 0}}),l=e.register(f),h=e.obj({Type:"Filespec",F:Oe.of(this.fileName),UF:oe.fromText(this.fileName),EF:{F:l},Desc:a?oe.fromText(a):void 0,AFRelationship:u??void 0}),t?(e.assign(t,h),[2,t]):[2,e.register(h)]})})},r}(),Ma=[65472,65473,65474,65475,65477,65478,65479,65480,65481,65482,65483,65484,65485,65486,65487],Or;(function(r){r.DeviceGray="DeviceGray",r.DeviceRGB="DeviceRGB",r.DeviceCMYK="DeviceCMYK"})(Or||(Or={}));var Zu={1:Or.DeviceGray,3:Or.DeviceRGB,4:Or.DeviceCMYK},ko=function(){function r(e,t,n,i,a){this.imageData=e,this.bitsPerComponent=t,this.width=n,this.height=i,this.colorSpace=a}return r.for=function(e){return pe(this,void 0,void 0,function(){var t,n,i,a,o,s,u,f,l,h;return ge(this,function(d){if(t=new DataView(e.buffer),n=t.getUint16(0),n!==65496)throw new Error("SOI not found in JPEG");for(i=2;i<t.byteLength&&(a=t.getUint16(i),i+=2,!Ma.includes(a));)i+=t.getUint16(i);if(!Ma.includes(a))throw new Error("Invalid JPEG");if(i+=2,o=t.getUint8(i++),s=t.getUint16(i),i+=2,u=t.getUint16(i),i+=2,f=t.getUint8(i++),l=Zu[f],!l)throw new Error("Unknown JPEG channel.");return h=l,[2,new r(e,o,u,s,h)]})})},r.prototype.embedIntoContext=function(e,t){return pe(this,void 0,void 0,function(){var n;return ge(this,function(i){return n=e.stream(this.imageData,{Type:"XObject",Subtype:"Image",BitsPerComponent:this.bitsPerComponent,Width:this.width,Height:this.height,ColorSpace:this.colorSpace,Filter:"DCTDecode",Decode:this.colorSpace===Or.DeviceCMYK?[1,0,1,0,1,0,1,0]:void 0}),t?(e.assign(t,n),[2,t]):[2,e.register(n)]})})},r}(),U={};U.toRGBA8=function(r){var e=r.width,t=r.height;if(r.tabs.acTL==null)return[U.toRGBA8.decodeImage(r.data,e,t,r).buffer];var n=[];r.frames[0].data==null&&(r.frames[0].data=r.data);for(var i=e*t*4,a=new Uint8Array(i),o=new Uint8Array(i),s=new Uint8Array(i),u=0;u<r.frames.length;u++){var f=r.frames[u],l=f.rect.x,h=f.rect.y,d=f.rect.width,v=f.rect.height,y=U.toRGBA8.decodeImage(f.data,d,v,r);if(u!=0)for(var w=0;w<i;w++)s[w]=a[w];if(f.blend==0?U._copyTile(y,d,v,a,e,t,l,h,0):f.blend==1&&U._copyTile(y,d,v,a,e,t,l,h,1),n.push(a.buffer.slice(0)),f.dispose!=0){if(f.dispose==1)U._copyTile(o,d,v,a,e,t,l,h,0);else if(f.dispose==2)for(var w=0;w<i;w++)a[w]=s[w]}}return n};U.toRGBA8.decodeImage=function(r,e,t,n){var i=e*t,a=U.decode._getBPP(n),o=Math.ceil(e*a/8),s=new Uint8Array(i*4),u=new Uint32Array(s.buffer),f=n.ctype,l=n.depth,h=U._bin.readUshort;if(f==6){var d=i<<2;if(l==8)for(var v=0;v<d;v+=4)s[v]=r[v],s[v+1]=r[v+1],s[v+2]=r[v+2],s[v+3]=r[v+3];if(l==16)for(var v=0;v<d;v++)s[v]=r[v<<1]}else if(f==2){var y=n.tabs.tRNS;if(y==null){if(l==8)for(var v=0;v<i;v++){var w=v*3;u[v]=255<<24|r[w+2]<<16|r[w+1]<<8|r[w]}if(l==16)for(var v=0;v<i;v++){var w=v*6;u[v]=255<<24|r[w+4]<<16|r[w+2]<<8|r[w]}}else{var F=y[0],S=y[1],R=y[2];if(l==8)for(var v=0;v<i;v++){var C=v<<2,w=v*3;u[v]=255<<24|r[w+2]<<16|r[w+1]<<8|r[w],r[w]==F&&r[w+1]==S&&r[w+2]==R&&(s[C+3]=0)}if(l==16)for(var v=0;v<i;v++){var C=v<<2,w=v*6;u[v]=255<<24|r[w+4]<<16|r[w+2]<<8|r[w],h(r,w)==F&&h(r,w+2)==S&&h(r,w+4)==R&&(s[C+3]=0)}}}else if(f==3){var B=n.tabs.PLTE,E=n.tabs.tRNS,D=E?E.length:0;if(l==1)for(var P=0;P<t;P++)for(var I=P*o,V=P*e,v=0;v<e;v++){var C=V+v<<2,q=r[I+(v>>3)]>>7-((v&7)<<0)&1,L=3*q;s[C]=B[L],s[C+1]=B[L+1],s[C+2]=B[L+2],s[C+3]=q<D?E[q]:255}if(l==2)for(var P=0;P<t;P++)for(var I=P*o,V=P*e,v=0;v<e;v++){var C=V+v<<2,q=r[I+(v>>2)]>>6-((v&3)<<1)&3,L=3*q;s[C]=B[L],s[C+1]=B[L+1],s[C+2]=B[L+2],s[C+3]=q<D?E[q]:255}if(l==4)for(var P=0;P<t;P++)for(var I=P*o,V=P*e,v=0;v<e;v++){var C=V+v<<2,q=r[I+(v>>1)]>>4-((v&1)<<2)&15,L=3*q;s[C]=B[L],s[C+1]=B[L+1],s[C+2]=B[L+2],s[C+3]=q<D?E[q]:255}if(l==8)for(var v=0;v<i;v++){var C=v<<2,q=r[v],L=3*q;s[C]=B[L],s[C+1]=B[L+1],s[C+2]=B[L+2],s[C+3]=q<D?E[q]:255}}else if(f==4){if(l==8)for(var v=0;v<i;v++){var C=v<<2,Z=v<<1,j=r[Z];s[C]=j,s[C+1]=j,s[C+2]=j,s[C+3]=r[Z+1]}if(l==16)for(var v=0;v<i;v++){var C=v<<2,Z=v<<2,j=r[Z];s[C]=j,s[C+1]=j,s[C+2]=j,s[C+3]=r[Z+2]}}else if(f==0)for(var F=n.tabs.tRNS?n.tabs.tRNS:-1,P=0;P<t;P++){var Q=P*o,te=P*e;if(l==1)for(var Y=0;Y<e;Y++){var j=255*(r[Q+(Y>>>3)]>>>7-(Y&7)&1),J=j==F*255?0:255;u[te+Y]=J<<24|j<<16|j<<8|j}else if(l==2)for(var Y=0;Y<e;Y++){var j=85*(r[Q+(Y>>>2)]>>>6-((Y&3)<<1)&3),J=j==F*85?0:255;u[te+Y]=J<<24|j<<16|j<<8|j}else if(l==4)for(var Y=0;Y<e;Y++){var j=17*(r[Q+(Y>>>1)]>>>4-((Y&1)<<2)&15),J=j==F*17?0:255;u[te+Y]=J<<24|j<<16|j<<8|j}else if(l==8)for(var Y=0;Y<e;Y++){var j=r[Q+Y],J=j==F?0:255;u[te+Y]=J<<24|j<<16|j<<8|j}else if(l==16)for(var Y=0;Y<e;Y++){var j=r[Q+(Y<<1)],J=h(r,Q+(Y<<v))==F?0:255;u[te+Y]=J<<24|j<<16|j<<8|j}}return s};U.decode=function(r){for(var e=new Uint8Array(r),t=8,n=U._bin,i=n.readUshort,a=n.readUint,o={tabs:{},frames:[]},s=new Uint8Array(e.length),u=0,f,l=0,h=[137,80,78,71,13,10,26,10],d=0;d<8;d++)if(e[d]!=h[d])throw"The input is not a PNG file!";for(;t<e.length;){var v=n.readUint(e,t);t+=4;var y=n.readASCII(e,t,4);if(t+=4,y=="IHDR")U.decode._IHDR(e,t,o);else if(y=="IDAT"){for(var d=0;d<v;d++)s[u+d]=e[t+d];u+=v}else if(y=="acTL")o.tabs[y]={num_frames:a(e,t),num_plays:a(e,t+4)},f=new Uint8Array(e.length);else if(y=="fcTL"){if(l!=0){var w=o.frames[o.frames.length-1];w.data=U.decode._decompress(o,f.slice(0,l),w.rect.width,w.rect.height),l=0}var F={x:a(e,t+12),y:a(e,t+16),width:a(e,t+4),height:a(e,t+8)},S=i(e,t+22);S=i(e,t+20)/(S==0?100:S);var R={rect:F,delay:Math.round(S*1e3),dispose:e[t+24],blend:e[t+25]};o.frames.push(R)}else if(y=="fdAT"){for(var d=0;d<v-4;d++)f[l+d]=e[t+d+4];l+=v-4}else if(y=="pHYs")o.tabs[y]=[n.readUint(e,t),n.readUint(e,t+4),e[t+8]];else if(y=="cHRM"){o.tabs[y]=[];for(var d=0;d<8;d++)o.tabs[y].push(n.readUint(e,t+d*4))}else if(y=="tEXt"){o.tabs[y]==null&&(o.tabs[y]={});var C=n.nextZero(e,t),B=n.readASCII(e,t,C-t),E=n.readASCII(e,C+1,t+v-C-1);o.tabs[y][B]=E}else if(y=="iTXt"){o.tabs[y]==null&&(o.tabs[y]={});var C=0,D=t;C=n.nextZero(e,D);var B=n.readASCII(e,D,C-D);D=C+1,e[D],e[D+1],D+=2,C=n.nextZero(e,D),n.readASCII(e,D,C-D),D=C+1,C=n.nextZero(e,D),n.readUTF8(e,D,C-D),D=C+1;var E=n.readUTF8(e,D,v-(D-t));o.tabs[y][B]=E}else if(y=="PLTE")o.tabs[y]=n.readBytes(e,t,v);else if(y=="hIST"){var P=o.tabs.PLTE.length/3;o.tabs[y]=[];for(var d=0;d<P;d++)o.tabs[y].push(i(e,t+d*2))}else if(y=="tRNS")o.ctype==3?o.tabs[y]=n.readBytes(e,t,v):o.ctype==0?o.tabs[y]=i(e,t):o.ctype==2&&(o.tabs[y]=[i(e,t),i(e,t+2),i(e,t+4)]);else if(y=="gAMA")o.tabs[y]=n.readUint(e,t)/1e5;else if(y=="sRGB")o.tabs[y]=e[t];else if(y=="bKGD")o.ctype==0||o.ctype==4?o.tabs[y]=[i(e,t)]:o.ctype==2||o.ctype==6?o.tabs[y]=[i(e,t),i(e,t+2),i(e,t+4)]:o.ctype==3&&(o.tabs[y]=e[t]);else if(y=="IEND")break;t+=v,n.readUint(e,t),t+=4}if(l!=0){var w=o.frames[o.frames.length-1];w.data=U.decode._decompress(o,f.slice(0,l),w.rect.width,w.rect.height),l=0}return o.data=U.decode._decompress(o,s,o.width,o.height),delete o.compress,delete o.interlace,delete o.filter,o};U.decode._decompress=function(r,e,t,n){var i=U.decode._getBPP(r),a=Math.ceil(t*i/8),o=new Uint8Array((a+1+r.interlace)*n);return e=U.decode._inflate(e,o),r.interlace==0?e=U.decode._filterZero(e,r,0,t,n):r.interlace==1&&(e=U.decode._readInterlace(e,r)),e};U.decode._inflate=function(r,e){var t=U.inflateRaw(new Uint8Array(r.buffer,2,r.length-6),e);return t};U.inflateRaw=function(){var r={};return r.H={},r.H.N=function(e,t){var n=Uint8Array,i=0,a=0,o=0,s=0,u=0,f=0,l=0,h=0,d=0,v,y;if(e[0]==3&&e[1]==0)return t||new n(0);var w=r.H,F=w.b,S=w.e,R=w.R,C=w.n,B=w.A,E=w.Z,D=w.m,P=t==null;for(P&&(t=new n(e.length>>>2<<3));i==0;){if(i=F(e,d,1),a=F(e,d+1,2),d+=3,a==0){(d&7)!=0&&(d+=8-(d&7));var I=(d>>>3)+4,V=e[I-4]|e[I-3]<<8;P&&(t=r.H.W(t,h+V)),t.set(new n(e.buffer,e.byteOffset+I,V),h),d=I+V<<3,h+=V;continue}if(P&&(t=r.H.W(t,h+(1<<17))),a==1&&(v=D.J,y=D.h,f=511,l=31),a==2){o=S(e,d,5)+257,s=S(e,d+5,5)+1,u=S(e,d+10,4)+4,d+=14;for(var q=1,L=0;L<38;L+=2)D.Q[L]=0,D.Q[L+1]=0;for(var L=0;L<u;L++){var Z=S(e,d+L*3,3);D.Q[(D.X[L]<<1)+1]=Z,Z>q&&(q=Z)}d+=3*u,C(D.Q,q),B(D.Q,q,D.u),v=D.w,y=D.d,d=R(D.u,(1<<q)-1,o+s,e,d,D.v);var j=w.V(D.v,0,o,D.C);f=(1<<j)-1;var Q=w.V(D.v,o,s,D.D);l=(1<<Q)-1,C(D.C,j),B(D.C,j,v),C(D.D,Q),B(D.D,Q,y)}for(;;){var te=v[E(e,d)&f];d+=te&15;var Y=te>>>4;if(!(Y>>>8))t[h++]=Y;else{if(Y==256)break;var J=h+Y-254;if(Y>264){var Te=D.q[Y-257];J=h+(Te>>>3)+S(e,d,Te&7),d+=Te&7}var be=y[E(e,d)&l];d+=be&15;var Ge=be>>>4,ze=D.c[Ge],Ne=(ze>>>4)+F(e,d,ze&15);for(d+=ze&15;h<J;)t[h]=t[h++-Ne],t[h]=t[h++-Ne],t[h]=t[h++-Ne],t[h]=t[h++-Ne];h=J}}}return t.length==h?t:t.slice(0,h)},r.H.W=function(e,t){var n=e.length;if(t<=n)return e;var i=new Uint8Array(n<<1);return i.set(e,0),i},r.H.R=function(e,t,n,i,a,o){for(var s=r.H.e,u=r.H.Z,f=0;f<n;){var l=e[u(i,a)&t];a+=l&15;var h=l>>>4;if(h<=15)o[f]=h,f++;else{var d=0,v=0;h==16?(v=3+s(i,a,2),a+=2,d=o[f-1]):h==17?(v=3+s(i,a,3),a+=3):h==18&&(v=11+s(i,a,7),a+=7);for(var y=f+v;f<y;)o[f]=d,f++}}return a},r.H.V=function(e,t,n,i){for(var a=0,o=0,s=i.length>>>1;o<n;){var u=e[o+t];i[o<<1]=0,i[(o<<1)+1]=u,u>a&&(a=u),o++}for(;o<s;)i[o<<1]=0,i[(o<<1)+1]=0,o++;return a},r.H.n=function(e,t){for(var n=r.H.m,i=e.length,a,o,s,u,f,l=n.j,u=0;u<=t;u++)l[u]=0;for(u=1;u<i;u+=2)l[e[u]]++;var h=n.K;for(a=0,l[0]=0,o=1;o<=t;o++)a=a+l[o-1]<<1,h[o]=a;for(s=0;s<i;s+=2)f=e[s+1],f!=0&&(e[s]=h[f],h[f]++)},r.H.A=function(e,t,n){for(var i=e.length,a=r.H.m,o=a.r,s=0;s<i;s+=2)if(e[s+1]!=0)for(var u=s>>1,f=e[s+1],l=u<<4|f,h=t-f,d=e[s]<<h,v=d+(1<<h);d!=v;){var y=o[d]>>>15-t;n[y]=l,d++}},r.H.l=function(e,t){for(var n=r.H.m.r,i=15-t,a=0;a<e.length;a+=2){var o=e[a]<<t-e[a+1];e[a]=n[o]>>>i}},r.H.M=function(e,t,n){n=n<<(t&7);var i=t>>>3;e[i]|=n,e[i+1]|=n>>>8},r.H.I=function(e,t,n){n=n<<(t&7);var i=t>>>3;e[i]|=n,e[i+1]|=n>>>8,e[i+2]|=n>>>16},r.H.e=function(e,t,n){return(e[t>>>3]|e[(t>>>3)+1]<<8)>>>(t&7)&(1<<n)-1},r.H.b=function(e,t,n){return(e[t>>>3]|e[(t>>>3)+1]<<8|e[(t>>>3)+2]<<16)>>>(t&7)&(1<<n)-1},r.H.Z=function(e,t){return(e[t>>>3]|e[(t>>>3)+1]<<8|e[(t>>>3)+2]<<16)>>>(t&7)},r.H.i=function(e,t){return(e[t>>>3]|e[(t>>>3)+1]<<8|e[(t>>>3)+2]<<16|e[(t>>>3)+3]<<24)>>>(t&7)},r.H.m=function(){var e=Uint16Array,t=Uint32Array;return{K:new e(16),j:new e(16),X:[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],S:[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,999,999,999],T:[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0],q:new e(32),p:[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,65535,65535],z:[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0],c:new t(32),J:new e(512),_:[],h:new e(32),$:[],w:new e(32768),C:[],v:[],d:new e(32768),D:[],u:new e(512),Q:[],r:new e(32768),s:new t(286),Y:new t(30),a:new t(19),t:new t(15e3),k:new e(65536),g:new e(32768)}}(),function(){for(var e=r.H.m,t=32768,n=0;n<t;n++){var i=n;i=(i&2863311530)>>>1|(i&1431655765)<<1,i=(i&3435973836)>>>2|(i&858993459)<<2,i=(i&4042322160)>>>4|(i&252645135)<<4,i=(i&4278255360)>>>8|(i&16711935)<<8,e.r[n]=(i>>>16|i<<16)>>>17}function a(o,s,u){for(;s--!=0;)o.push(0,u)}for(var n=0;n<32;n++)e.q[n]=e.S[n]<<3|e.T[n],e.c[n]=e.p[n]<<4|e.z[n];a(e._,144,8),a(e._,112,9),a(e._,24,7),a(e._,8,8),r.H.n(e._,9),r.H.A(e._,9,e.J),r.H.l(e._,9),a(e.$,32,5),r.H.n(e.$,5),r.H.A(e.$,5,e.h),r.H.l(e.$,5),a(e.Q,19,0),a(e.C,286,0),a(e.D,30,0),a(e.v,320,0)}(),r.H.N}();U.decode._readInterlace=function(r,e){for(var t=e.width,n=e.height,i=U.decode._getBPP(e),a=i>>3,o=Math.ceil(t*i/8),s=new Uint8Array(n*o),u=0,f=[0,0,4,0,2,0,1],l=[0,4,0,2,0,1,0],h=[8,8,8,4,4,2,2],d=[8,8,4,4,2,2,1],v=0;v<7;){for(var y=h[v],w=d[v],F=0,S=0,R=f[v];R<n;)R+=y,S++;for(var C=l[v];C<t;)C+=w,F++;var B=Math.ceil(F*i/8);U.decode._filterZero(r,e,u,F,S);for(var E=0,D=f[v];D<n;){for(var P=l[v],I=u+E*B<<3;P<t;){if(i==1){var V=r[I>>3];V=V>>7-(I&7)&1,s[D*o+(P>>3)]|=V<<7-((P&7)<<0)}if(i==2){var V=r[I>>3];V=V>>6-(I&7)&3,s[D*o+(P>>2)]|=V<<6-((P&3)<<1)}if(i==4){var V=r[I>>3];V=V>>4-(I&7)&15,s[D*o+(P>>1)]|=V<<4-((P&1)<<2)}if(i>=8)for(var q=D*o+P*a,L=0;L<a;L++)s[q+L]=r[(I>>3)+L];I+=i,P+=w}E++,D+=y}F*S!=0&&(u+=S*(1+B)),v=v+1}return s};U.decode._getBPP=function(r){var e=[1,null,3,1,2,null,4][r.ctype];return e*r.depth};U.decode._filterZero=function(r,e,t,n,i){var a=U.decode._getBPP(e),o=Math.ceil(n*a/8),s=U.decode._paeth;a=Math.ceil(a/8);var u=0,f=1,l=r[t],h=0;if(l>1&&(r[t]=[0,0,1][l-2]),l==3)for(h=a;h<o;h++)r[h+1]=r[h+1]+(r[h+1-a]>>>1)&255;for(var d=0;d<i;d++)if(u=t+d*o,f=u+d+1,l=r[f-1],h=0,l==0)for(;h<o;h++)r[u+h]=r[f+h];else if(l==1){for(;h<a;h++)r[u+h]=r[f+h];for(;h<o;h++)r[u+h]=r[f+h]+r[u+h-a]}else if(l==2)for(;h<o;h++)r[u+h]=r[f+h]+r[u+h-o];else if(l==3){for(;h<a;h++)r[u+h]=r[f+h]+(r[u+h-o]>>>1);for(;h<o;h++)r[u+h]=r[f+h]+(r[u+h-o]+r[u+h-a]>>>1)}else{for(;h<a;h++)r[u+h]=r[f+h]+s(0,r[u+h-o],0);for(;h<o;h++)r[u+h]=r[f+h]+s(r[u+h-a],r[u+h-o],r[u+h-a-o])}return r};U.decode._paeth=function(r,e,t){var n=r+e-t,i=n-r,a=n-e,o=n-t;return i*i<=a*a&&i*i<=o*o?r:a*a<=o*o?e:t};U.decode._IHDR=function(r,e,t){var n=U._bin;t.width=n.readUint(r,e),e+=4,t.height=n.readUint(r,e),e+=4,t.depth=r[e],e++,t.ctype=r[e],e++,t.compress=r[e],e++,t.filter=r[e],e++,t.interlace=r[e],e++};U._bin={nextZero:function(r,e){for(;r[e]!=0;)e++;return e},readUshort:function(r,e){return r[e]<<8|r[e+1]},writeUshort:function(r,e,t){r[e]=t>>8&255,r[e+1]=t&255},readUint:function(r,e){return r[e]*(256*256*256)+(r[e+1]<<16|r[e+2]<<8|r[e+3])},writeUint:function(r,e,t){r[e]=t>>24&255,r[e+1]=t>>16&255,r[e+2]=t>>8&255,r[e+3]=t&255},readASCII:function(r,e,t){for(var n="",i=0;i<t;i++)n+=String.fromCharCode(r[e+i]);return n},writeASCII:function(r,e,t){for(var n=0;n<t.length;n++)r[e+n]=t.charCodeAt(n)},readBytes:function(r,e,t){for(var n=[],i=0;i<t;i++)n.push(r[e+i]);return n},pad:function(r){return r.length<2?"0"+r:r},readUTF8:function(r,e,t){for(var n="",i,a=0;a<t;a++)n+="%"+U._bin.pad(r[e+a].toString(16));try{i=decodeURIComponent(n)}catch{return U._bin.readASCII(r,e,t)}return i}};U._copyTile=function(r,e,t,n,i,a,o,s,u){for(var f=Math.min(e,i),l=Math.min(t,a),h=0,d=0,v=0;v<l;v++)for(var y=0;y<f;y++)if(o>=0&&s>=0?(h=v*e+y<<2,d=(s+v)*i+o+y<<2):(h=(-s+v)*e-o+y<<2,d=v*i+y<<2),u==0)n[d]=r[h],n[d+1]=r[h+1],n[d+2]=r[h+2],n[d+3]=r[h+3];else if(u==1){var w=r[h+3]*.00392156862745098,F=r[h]*w,S=r[h+1]*w,R=r[h+2]*w,C=n[d+3]*(1/255),B=n[d]*C,E=n[d+1]*C,D=n[d+2]*C,P=1-w,I=w+C*P,V=I==0?0:1/I;n[d+3]=255*I,n[d+0]=(F+B*P)*V,n[d+1]=(S+E*P)*V,n[d+2]=(R+D*P)*V}else if(u==2){var w=r[h+3],F=r[h],S=r[h+1],R=r[h+2],C=n[d+3],B=n[d],E=n[d+1],D=n[d+2];w==C&&F==B&&S==E&&R==D?(n[d]=0,n[d+1]=0,n[d+2]=0,n[d+3]=0):(n[d]=F,n[d+1]=S,n[d+2]=R,n[d+3]=w)}else if(u==3){var w=r[h+3],F=r[h],S=r[h+1],R=r[h+2],C=n[d+3],B=n[d],E=n[d+1],D=n[d+2];if(w==C&&F==B&&S==E&&R==D)continue;if(w<220&&C>20)return!1}return!0};U.encode=function(r,e,t,n,i,a,o){n==null&&(n=0),o==null&&(o=!1);var s=U.encode.compress(r,e,t,n,[!1,!1,!1,0,o]);return U.encode.compressPNG(s,-1),U.encode._main(s,e,t,i,a)};U.encodeLL=function(r,e,t,n,i,a,o,s){for(var u={ctype:0+(n==1?0:2)+(i==0?0:4),depth:a,frames:[]},f=(n+i)*a,l=f*e,h=0;h<r.length;h++)u.frames.push({rect:{x:0,y:0,width:e,height:t},img:new Uint8Array(r[h]),blend:0,dispose:1,bpp:Math.ceil(f/8),bpl:Math.ceil(l/8)});U.encode.compressPNG(u,0,!0);var d=U.encode._main(u,e,t,o,s);return d};U.encode._main=function(r,e,t,n,i){i==null&&(i={});var a=U.crc.crc,o=U._bin.writeUint,s=U._bin.writeUshort,u=U._bin.writeASCII,f=8,l=r.frames.length>1,h=!1,d=33+(l?20:0);if(i.sRGB!=null&&(d+=13),i.pHYs!=null&&(d+=21),r.ctype==3){for(var v=r.plte.length,y=0;y<v;y++)r.plte[y]>>>24!=255&&(h=!0);d+=8+v*3+4+(h?8+v*1+4:0)}for(var w=0;w<r.frames.length;w++){var F=r.frames[w];l&&(d+=38),d+=F.cimg.length+12,w!=0&&(d+=4)}d+=12;for(var S=new Uint8Array(d),R=[137,80,78,71,13,10,26,10],y=0;y<8;y++)S[y]=R[y];if(o(S,f,13),f+=4,u(S,f,"IHDR"),f+=4,o(S,f,e),f+=4,o(S,f,t),f+=4,S[f]=r.depth,f++,S[f]=r.ctype,f++,S[f]=0,f++,S[f]=0,f++,S[f]=0,f++,o(S,f,a(S,f-17,17)),f+=4,i.sRGB!=null&&(o(S,f,1),f+=4,u(S,f,"sRGB"),f+=4,S[f]=i.sRGB,f++,o(S,f,a(S,f-5,5)),f+=4),i.pHYs!=null&&(o(S,f,9),f+=4,u(S,f,"pHYs"),f+=4,o(S,f,i.pHYs[0]),f+=4,o(S,f,i.pHYs[1]),f+=4,S[f]=i.pHYs[2],f++,o(S,f,a(S,f-13,13)),f+=4),l&&(o(S,f,8),f+=4,u(S,f,"acTL"),f+=4,o(S,f,r.frames.length),f+=4,o(S,f,i.loop!=null?i.loop:0),f+=4,o(S,f,a(S,f-12,12)),f+=4),r.ctype==3){var v=r.plte.length;o(S,f,v*3),f+=4,u(S,f,"PLTE"),f+=4;for(var y=0;y<v;y++){var C=y*3,B=r.plte[y],E=B&255,D=B>>>8&255,P=B>>>16&255;S[f+C+0]=E,S[f+C+1]=D,S[f+C+2]=P}if(f+=v*3,o(S,f,a(S,f-v*3-4,v*3+4)),f+=4,h){o(S,f,v),f+=4,u(S,f,"tRNS"),f+=4;for(var y=0;y<v;y++)S[f+y]=r.plte[y]>>>24&255;f+=v,o(S,f,a(S,f-v-4,v+4)),f+=4}}for(var I=0,w=0;w<r.frames.length;w++){var F=r.frames[w];l&&(o(S,f,26),f+=4,u(S,f,"fcTL"),f+=4,o(S,f,I++),f+=4,o(S,f,F.rect.width),f+=4,o(S,f,F.rect.height),f+=4,o(S,f,F.rect.x),f+=4,o(S,f,F.rect.y),f+=4,s(S,f,n[w]),f+=2,s(S,f,1e3),f+=2,S[f]=F.dispose,f++,S[f]=F.blend,f++,o(S,f,a(S,f-30,30)),f+=4);var V=F.cimg,v=V.length;o(S,f,v+(w==0?0:4)),f+=4;var q=f;u(S,f,w==0?"IDAT":"fdAT"),f+=4,w!=0&&(o(S,f,I++),f+=4),S.set(V,f),f+=v,o(S,f,a(S,q,f-q)),f+=4}return o(S,f,0),f+=4,u(S,f,"IEND"),f+=4,o(S,f,a(S,f-4,4)),f+=4,S.buffer};U.encode.compressPNG=function(r,e,t){for(var n=0;n<r.frames.length;n++){var i=r.frames[n];i.rect.width;var a=i.rect.height,o=new Uint8Array(a*i.bpl+a);i.cimg=U.encode._filterZero(i.img,a,i.bpp,i.bpl,o,e,t)}};U.encode.compress=function(r,e,t,n,i){for(var a=i[0],o=i[1],s=i[2],u=i[3],f=i[4],l=6,h=8,d=255,v=0;v<r.length;v++)for(var y=new Uint8Array(r[v]),w=y.length,F=0;F<w;F+=4)d&=y[F+3];var S=d!=255,R=U.encode.framize(r,e,t,a,o,s),C={},B=[],E=[];if(n!=0){for(var D=[],F=0;F<R.length;F++)D.push(R[F].img.buffer);for(var P=U.encode.concatRGBA(D),I=U.quantize(P,n),V=0,q=new Uint8Array(I.abuf),F=0;F<R.length;F++){var L=R[F].img,Z=L.length;E.push(new Uint8Array(I.inds.buffer,V>>2,Z>>2));for(var v=0;v<Z;v+=4)L[v]=q[V+v],L[v+1]=q[V+v+1],L[v+2]=q[V+v+2],L[v+3]=q[V+v+3];V+=Z}for(var F=0;F<I.plte.length;F++)B.push(I.plte[F].est.rgba)}else for(var v=0;v<R.length;v++){var j=R[v],Q=new Uint32Array(j.img.buffer),te=j.rect.width,w=Q.length,Y=new Uint8Array(w);E.push(Y);for(var F=0;F<w;F++){var J=Q[F];if(F!=0&&J==Q[F-1])Y[F]=Y[F-1];else if(F>te&&J==Q[F-te])Y[F]=Y[F-te];else{var Te=C[J];if(Te==null&&(C[J]=Te=B.length,B.push(J),B.length>=300))break;Y[F]=Te}}}var be=B.length;be<=256&&f==!1&&(be<=2?h=1:be<=4?h=2:be<=16?h=4:h=8,h=Math.max(h,u));for(var v=0;v<R.length;v++){var j=R[v];j.rect.x,j.rect.y;var te=j.rect.width,Ge=j.rect.height,ze=j.img;new Uint32Array(ze.buffer);var Ne=4*te,Ue=4;if(be<=256&&f==!1){Ne=Math.ceil(h*te/8);for(var Ve=new Uint8Array(Ne*Ge),qe=E[v],Pe=0;Pe<Ge;Pe++){var F=Pe*Ne,Ie=Pe*te;if(h==8)for(var ae=0;ae<te;ae++)Ve[F+ae]=qe[Ie+ae];else if(h==4)for(var ae=0;ae<te;ae++)Ve[F+(ae>>1)]|=qe[Ie+ae]<<4-(ae&1)*4;else if(h==2)for(var ae=0;ae<te;ae++)Ve[F+(ae>>2)]|=qe[Ie+ae]<<6-(ae&3)*2;else if(h==1)for(var ae=0;ae<te;ae++)Ve[F+(ae>>3)]|=qe[Ie+ae]<<7-(ae&7)*1}ze=Ve,l=3,Ue=1}else if(S==!1&&R.length==1){for(var Ve=new Uint8Array(te*Ge*3),$e=te*Ge,F=0;F<$e;F++){var L=F*3,it=F*4;Ve[L]=ze[it],Ve[L+1]=ze[it+1],Ve[L+2]=ze[it+2]}ze=Ve,l=2,Ue=3,Ne=3*te}j.img=ze,j.bpl=Ne,j.bpp=Ue}return{ctype:l,depth:h,plte:B,frames:R}};U.encode.framize=function(r,e,t,n,i,a){for(var o=[],s=0;s<r.length;s++){var u=new Uint8Array(r[s]),f=new Uint32Array(u.buffer),l,h=0,d=0,v=e,y=t,w=n?1:0;if(s!=0){for(var F=a||n||s==1||o[s-2].dispose!=0?1:2,S=0,R=1e9,C=0;C<F;C++){for(var j=new Uint8Array(r[s-1-C]),B=new Uint32Array(r[s-1-C]),E=e,D=t,P=-1,I=-1,V=0;V<t;V++)for(var q=0;q<e;q++){var L=V*e+q;f[L]!=B[L]&&(q<E&&(E=q),q>P&&(P=q),V<D&&(D=V),V>I&&(I=V))}P==-1&&(E=D=P=I=0),i&&((E&1)==1&&E--,(D&1)==1&&D--);var Z=(P-E+1)*(I-D+1);Z<R&&(R=Z,S=C,h=E,d=D,v=P-E+1,y=I-D+1)}var j=new Uint8Array(r[s-1-S]);S==1&&(o[s-1].dispose=2),l=new Uint8Array(v*y*4),U._copyTile(j,e,t,l,v,y,-h,-d,0),w=U._copyTile(u,e,t,l,v,y,-h,-d,3)?1:0,w==1?U.encode._prepareDiff(u,e,t,l,{x:h,y:d,width:v,height:y}):U._copyTile(u,e,t,l,v,y,-h,-d,0)}else l=u.slice(0);o.push({rect:{x:h,y:d,width:v,height:y},img:l,blend:w,dispose:0})}if(n)for(var s=0;s<o.length;s++){var Q=o[s];if(Q.blend!=1){var te=Q.rect,Y=o[s-1].rect,J=Math.min(te.x,Y.x),Te=Math.min(te.y,Y.y),be=Math.max(te.x+te.width,Y.x+Y.width),Ge=Math.max(te.y+te.height,Y.y+Y.height),ze={x:J,y:Te,width:be-J,height:Ge-Te};o[s-1].dispose=1,s-1!=0&&U.encode._updateFrame(r,e,t,o,s-1,ze,i),U.encode._updateFrame(r,e,t,o,s,ze,i)}}var Ne=0;if(r.length!=1)for(var L=0;L<o.length;L++){var Q=o[L];Ne+=Q.rect.width*Q.rect.height}return o};U.encode._updateFrame=function(r,e,t,n,i,a,o){for(var s=Uint8Array,u=Uint32Array,f=new s(r[i-1]),l=new u(r[i-1]),h=i+1<r.length?new s(r[i+1]):null,d=new s(r[i]),v=new u(d.buffer),y=e,w=t,F=-1,S=-1,R=0;R<a.height;R++)for(var C=0;C<a.width;C++){var B=a.x+C,E=a.y+R,D=E*e+B,P=v[D];P==0||n[i-1].dispose==0&&l[D]==P&&(h==null||h[D*4+3]!=0)||(B<y&&(y=B),B>F&&(F=B),E<w&&(w=E),E>S&&(S=E))}F==-1&&(y=w=F=S=0),o&&((y&1)==1&&y--,(w&1)==1&&w--),a={x:y,y:w,width:F-y+1,height:S-w+1};var I=n[i];I.rect=a,I.blend=1,I.img=new Uint8Array(a.width*a.height*4),n[i-1].dispose==0?(U._copyTile(f,e,t,I.img,a.width,a.height,-a.x,-a.y,0),U.encode._prepareDiff(d,e,t,I.img,a)):U._copyTile(d,e,t,I.img,a.width,a.height,-a.x,-a.y,0)};U.encode._prepareDiff=function(r,e,t,n,i){U._copyTile(r,e,t,n,i.width,i.height,-i.x,-i.y,2)};U.encode._filterZero=function(r,e,t,n,i,a,o){var s=[],u=[0,1,2,3,4];a!=-1?u=[a]:(e*n>5e5||t==1)&&(u=[0]);var f;o&&(f={level:0});for(var l=o&&UZIP!=null?UZIP:Zn,h=0;h<u.length;h++){for(var d=0;d<e;d++)U.encode._filterLine(i,r,d,n,t,u[h]);s.push(l.deflate(i,f))}for(var v,y=1e9,h=0;h<s.length;h++)s[h].length<y&&(v=h,y=s[h].length);return s[v]};U.encode._filterLine=function(r,e,t,n,i,a){var o=t*n,s=o+t,u=U.decode._paeth;if(r[s]=a,s++,a==0)if(n<500)for(var f=0;f<n;f++)r[s+f]=e[o+f];else r.set(new Uint8Array(e.buffer,o,n),s);else if(a==1){for(var f=0;f<i;f++)r[s+f]=e[o+f];for(var f=i;f<n;f++)r[s+f]=e[o+f]-e[o+f-i]+256&255}else if(t==0){for(var f=0;f<i;f++)r[s+f]=e[o+f];if(a==2)for(var f=i;f<n;f++)r[s+f]=e[o+f];if(a==3)for(var f=i;f<n;f++)r[s+f]=e[o+f]-(e[o+f-i]>>1)+256&255;if(a==4)for(var f=i;f<n;f++)r[s+f]=e[o+f]-u(e[o+f-i],0,0)+256&255}else{if(a==2)for(var f=0;f<n;f++)r[s+f]=e[o+f]+256-e[o+f-n]&255;if(a==3){for(var f=0;f<i;f++)r[s+f]=e[o+f]+256-(e[o+f-n]>>1)&255;for(var f=i;f<n;f++)r[s+f]=e[o+f]+256-(e[o+f-n]+e[o+f-i]>>1)&255}if(a==4){for(var f=0;f<i;f++)r[s+f]=e[o+f]+256-u(0,e[o+f-n],0)&255;for(var f=i;f<n;f++)r[s+f]=e[o+f]+256-u(e[o+f-i],e[o+f-n],e[o+f-i-n])&255}}};U.crc={table:function(){for(var r=new Uint32Array(256),e=0;e<256;e++){for(var t=e,n=0;n<8;n++)t&1?t=3988292384^t>>>1:t=t>>>1;r[e]=t}return r}(),update:function(r,e,t,n){for(var i=0;i<n;i++)r=U.crc.table[(r^e[t+i])&255]^r>>>8;return r},crc:function(r,e,t){return U.crc.update(4294967295,r,e,t)^4294967295}};U.quantize=function(r,e){var t=new Uint8Array(r),n=t.slice(0),i=new Uint32Array(n.buffer),a=U.quantize.getKDtree(n,e),o=a[0],s=a[1];U.quantize.planeDst;for(var u=t,f=i,l=u.length,h=new Uint8Array(t.length>>2),d=0;d<l;d+=4){var v=u[d]*.00392156862745098,y=u[d+1]*(1/255),w=u[d+2]*(1/255),F=u[d+3]*(1/255),S=U.quantize.getNearest(o,v,y,w,F);h[d>>2]=S.ind,f[d>>2]=S.est.rgba}return{abuf:n.buffer,inds:h,plte:s}};U.quantize.getKDtree=function(r,e,t){t==null&&(t=1e-4);var n=new Uint32Array(r.buffer),i={i0:0,i1:r.length,bst:null,est:null,tdst:0,left:null,right:null};i.bst=U.quantize.stats(r,i.i0,i.i1),i.est=U.quantize.estats(i.bst);for(var a=[i];a.length<e;){for(var o=0,s=0,u=0;u<a.length;u++)a[u].est.L>o&&(o=a[u].est.L,s=u);if(o<t)break;var f=a[s],l=U.quantize.splitPixels(r,n,f.i0,f.i1,f.est.e,f.est.eMq255),h=f.i0>=l||f.i1<=l;if(h){f.est.L=0;continue}var d={i0:f.i0,i1:l,bst:null,est:null,tdst:0,left:null,right:null};d.bst=U.quantize.stats(r,d.i0,d.i1),d.est=U.quantize.estats(d.bst);var v={i0:l,i1:f.i1,bst:null,est:null,tdst:0,left:null,right:null};v.bst={R:[],m:[],N:f.bst.N-d.bst.N};for(var u=0;u<16;u++)v.bst.R[u]=f.bst.R[u]-d.bst.R[u];for(var u=0;u<4;u++)v.bst.m[u]=f.bst.m[u]-d.bst.m[u];v.est=U.quantize.estats(v.bst),f.left=d,f.right=v,a[s]=d,a.push(v)}a.sort(function(y,w){return w.bst.N-y.bst.N});for(var u=0;u<a.length;u++)a[u].ind=u;return[i,a]};U.quantize.getNearest=function(r,e,t,n,i){if(r.left==null)return r.tdst=U.quantize.dist(r.est.q,e,t,n,i),r;var a=U.quantize.planeDst(r.est,e,t,n,i),o=r.left,s=r.right;a>0&&(o=r.right,s=r.left);var u=U.quantize.getNearest(o,e,t,n,i);if(u.tdst<=a*a)return u;var f=U.quantize.getNearest(s,e,t,n,i);return f.tdst<u.tdst?f:u};U.quantize.planeDst=function(r,e,t,n,i){var a=r.e;return a[0]*e+a[1]*t+a[2]*n+a[3]*i-r.eMq};U.quantize.dist=function(r,e,t,n,i){var a=e-r[0],o=t-r[1],s=n-r[2],u=i-r[3];return a*a+o*o+s*s+u*u};U.quantize.splitPixels=function(r,e,t,n,i,a){var o=U.quantize.vecDot;for(n-=4;t<n;){for(;o(r,t,i)<=a;)t+=4;for(;o(r,n,i)>a;)n-=4;if(t>=n)break;var s=e[t>>2];e[t>>2]=e[n>>2],e[n>>2]=s,t+=4,n-=4}for(;o(r,t,i)>a;)t-=4;return t+4};U.quantize.vecDot=function(r,e,t){return r[e]*t[0]+r[e+1]*t[1]+r[e+2]*t[2]+r[e+3]*t[3]};U.quantize.stats=function(r,e,t){for(var n=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],i=[0,0,0,0],a=t-e>>2,o=e;o<t;o+=4){var s=r[o]*.00392156862745098,u=r[o+1]*(1/255),f=r[o+2]*(1/255),l=r[o+3]*(1/255);i[0]+=s,i[1]+=u,i[2]+=f,i[3]+=l,n[0]+=s*s,n[1]+=s*u,n[2]+=s*f,n[3]+=s*l,n[5]+=u*u,n[6]+=u*f,n[7]+=u*l,n[10]+=f*f,n[11]+=f*l,n[15]+=l*l}return n[4]=n[1],n[8]=n[2],n[9]=n[6],n[12]=n[3],n[13]=n[7],n[14]=n[11],{R:n,m:i,N:a}};U.quantize.estats=function(r){var e=r.R,t=r.m,n=r.N,i=t[0],a=t[1],o=t[2],s=t[3],u=n==0?0:1/n,f=[e[0]-i*i*u,e[1]-i*a*u,e[2]-i*o*u,e[3]-i*s*u,e[4]-a*i*u,e[5]-a*a*u,e[6]-a*o*u,e[7]-a*s*u,e[8]-o*i*u,e[9]-o*a*u,e[10]-o*o*u,e[11]-o*s*u,e[12]-s*i*u,e[13]-s*a*u,e[14]-s*o*u,e[15]-s*s*u],l=f,h=U.M4,d=[.5,.5,.5,.5],v=0,y=0;if(n!=0)for(var w=0;w<10&&(d=h.multVec(l,d),y=Math.sqrt(h.dot(d,d)),d=h.sml(1/y,d),!(Math.abs(y-v)<1e-9));w++)v=y;var F=[i*u,a*u,o*u,s*u],S=h.dot(h.sml(255,F),d);return{Cov:f,q:F,e:d,L:v,eMq255:S,eMq:h.dot(d,F),rgba:(Math.round(255*F[3])<<24|Math.round(255*F[2])<<16|Math.round(255*F[1])<<8|Math.round(255*F[0])<<0)>>>0}};U.M4={multVec:function(r,e){return[r[0]*e[0]+r[1]*e[1]+r[2]*e[2]+r[3]*e[3],r[4]*e[0]+r[5]*e[1]+r[6]*e[2]+r[7]*e[3],r[8]*e[0]+r[9]*e[1]+r[10]*e[2]+r[11]*e[3],r[12]*e[0]+r[13]*e[1]+r[14]*e[2]+r[15]*e[3]]},dot:function(r,e){return r[0]*e[0]+r[1]*e[1]+r[2]*e[2]+r[3]*e[3]},sml:function(r,e){return[r*e[0],r*e[1],r*e[2],r*e[3]]}};U.encode.concatRGBA=function(r){for(var e=0,t=0;t<r.length;t++)e+=r[t].byteLength;for(var n=new Uint8Array(e),i=0,t=0;t<r.length;t++){for(var a=new Uint8Array(r[t]),o=a.length,s=0;s<o;s+=4){var u=a[s],f=a[s+1],l=a[s+2],h=a[s+3];h==0&&(u=f=l=0),n[i+s]=u,n[i+s+1]=f,n[i+s+2]=l,n[i+s+3]=h}i+=o}return n.buffer};var Yu=function(r){if(r===0)return dr.Greyscale;if(r===2)return dr.Truecolour;if(r===3)return dr.IndexedColour;if(r===4)return dr.GreyscaleWithAlpha;if(r===6)return dr.TruecolourWithAlpha;throw new Error("Unknown color type: "+r)},Ju=function(r){for(var e=Math.floor(r.length/4),t=new Uint8Array(e*3),n=new Uint8Array(e*1),i=0,a=0,o=0;i<r.length;)t[a++]=r[i++],t[a++]=r[i++],t[a++]=r[i++],n[o++]=r[i++];return{rgbChannel:t,alphaChannel:n}},dr;(function(r){r.Greyscale="Greyscale",r.Truecolour="Truecolour",r.IndexedColour="IndexedColour",r.GreyscaleWithAlpha="GreyscaleWithAlpha",r.TruecolourWithAlpha="TruecolourWithAlpha"})(dr||(dr={}));var Qu=function(){function r(e){var t=U.decode(e),n=U.toRGBA8(t);if(n.length>1)throw new Error("Animated PNGs are not supported");var i=new Uint8Array(n[0]),a=Ju(i),o=a.rgbChannel,s=a.alphaChannel;this.rgbChannel=o;var u=s.some(function(f){return f<255});u&&(this.alphaChannel=s),this.type=Yu(t.ctype),this.width=t.width,this.height=t.height,this.bitsPerComponent=8}return r.load=function(e){return new r(e)},r}(),Co=function(){function r(e){this.image=e,this.bitsPerComponent=e.bitsPerComponent,this.width=e.width,this.height=e.height,this.colorSpace="DeviceRGB"}return r.for=function(e){return pe(this,void 0,void 0,function(){var t;return ge(this,function(n){return t=Qu.load(e),[2,new r(t)]})})},r.prototype.embedIntoContext=function(e,t){return pe(this,void 0,void 0,function(){var n,i;return ge(this,function(a){return n=this.embedAlphaChannel(e),i=e.flateStream(this.image.rgbChannel,{Type:"XObject",Subtype:"Image",BitsPerComponent:this.image.bitsPerComponent,Width:this.image.width,Height:this.image.height,ColorSpace:this.colorSpace,SMask:n}),t?(e.assign(t,i),[2,t]):[2,e.register(i)]})})},r.prototype.embedAlphaChannel=function(e){if(this.image.alphaChannel){var t=e.flateStream(this.image.alphaChannel,{Type:"XObject",Subtype:"Image",Height:this.image.height,Width:this.image.width,BitsPerComponent:this.image.bitsPerComponent,ColorSpace:"DeviceGray",Decode:[0,1]});return e.register(t)}},r}(),To=function(){function r(e,t,n){this.bytes=e,this.start=t||0,this.pos=this.start,this.end=t&&n?t+n:this.bytes.length}return Object.defineProperty(r.prototype,"length",{get:function(){return this.end-this.start},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"isEmpty",{get:function(){return this.length===0},enumerable:!1,configurable:!0}),r.prototype.getByte=function(){return this.pos>=this.end?-1:this.bytes[this.pos++]},r.prototype.getUint16=function(){var e=this.getByte(),t=this.getByte();return e===-1||t===-1?-1:(e<<8)+t},r.prototype.getInt32=function(){var e=this.getByte(),t=this.getByte(),n=this.getByte(),i=this.getByte();return(e<<24)+(t<<16)+(n<<8)+i},r.prototype.getBytes=function(e,t){t===void 0&&(t=!1);var n=this.bytes,i=this.pos,a=this.end;if(e){var s=i+e;s>a&&(s=a),this.pos=s;var o=n.subarray(i,s);return t?new Uint8ClampedArray(o):o}else{var o=n.subarray(i,a);return t?new Uint8ClampedArray(o):o}},r.prototype.peekByte=function(){var e=this.getByte();return this.pos--,e},r.prototype.peekBytes=function(e,t){t===void 0&&(t=!1);var n=this.getBytes(e,t);return this.pos-=n.length,n},r.prototype.skip=function(e){e||(e=1),this.pos+=e},r.prototype.reset=function(){this.pos=this.start},r.prototype.moveStart=function(){this.start=this.pos},r.prototype.makeSubStream=function(e,t){return new r(this.bytes,e,t)},r.prototype.decode=function(){return this.bytes},r}(),_u=new Uint8Array(0),ln=function(){function r(e){if(this.pos=0,this.bufferLength=0,this.eof=!1,this.buffer=_u,this.minBufferLength=512,e)for(;this.minBufferLength<e;)this.minBufferLength*=2}return Object.defineProperty(r.prototype,"isEmpty",{get:function(){for(;!this.eof&&this.bufferLength===0;)this.readBlock();return this.bufferLength===0},enumerable:!1,configurable:!0}),r.prototype.getByte=function(){for(var e=this.pos;this.bufferLength<=e;){if(this.eof)return-1;this.readBlock()}return this.buffer[this.pos++]},r.prototype.getUint16=function(){var e=this.getByte(),t=this.getByte();return e===-1||t===-1?-1:(e<<8)+t},r.prototype.getInt32=function(){var e=this.getByte(),t=this.getByte(),n=this.getByte(),i=this.getByte();return(e<<24)+(t<<16)+(n<<8)+i},r.prototype.getBytes=function(e,t){t===void 0&&(t=!1);var n,i=this.pos;if(e){for(this.ensureBuffer(i+e),n=i+e;!this.eof&&this.bufferLength<n;)this.readBlock();var a=this.bufferLength;n>a&&(n=a)}else{for(;!this.eof;)this.readBlock();n=this.bufferLength}this.pos=n;var o=this.buffer.subarray(i,n);return t&&!(o instanceof Uint8ClampedArray)?new Uint8ClampedArray(o):o},r.prototype.peekByte=function(){var e=this.getByte();return this.pos--,e},r.prototype.peekBytes=function(e,t){t===void 0&&(t=!1);var n=this.getBytes(e,t);return this.pos-=n.length,n},r.prototype.skip=function(e){e||(e=1),this.pos+=e},r.prototype.reset=function(){this.pos=0},r.prototype.makeSubStream=function(e,t){for(var n=e+t;this.bufferLength<=n&&!this.eof;)this.readBlock();return new To(this.buffer,e,t)},r.prototype.decode=function(){for(;!this.eof;)this.readBlock();return this.buffer.subarray(0,this.bufferLength)},r.prototype.readBlock=function(){throw new Pt(this.constructor.name,"readBlock")},r.prototype.ensureBuffer=function(e){var t=this.buffer;if(e<=t.byteLength)return t;for(var n=this.minBufferLength;n<e;)n*=2;var i=new Uint8Array(n);return i.set(t),this.buffer=i},r}(),Ia=function(r){return r===32||r===9||r===13||r===10},$u=function(r){X(e,r);function e(t,n){var i=r.call(this,n)||this;return i.stream=t,i.input=new Uint8Array(5),n&&(n=.8*n),i}return e.prototype.readBlock=function(){for(var t=126,n=122,i=-1,a=this.stream,o=a.getByte();Ia(o);)o=a.getByte();if(o===i||o===t){this.eof=!0;return}var s=this.bufferLength,u,f;if(o===n){for(u=this.ensureBuffer(s+4),f=0;f<4;++f)u[s+f]=0;this.bufferLength+=4}else{var l=this.input;for(l[0]=o,f=1;f<5;++f){for(o=a.getByte();Ia(o);)o=a.getByte();if(l[f]=o,o===i||o===t)break}if(u=this.ensureBuffer(s+f-1),this.bufferLength+=f-1,f<5){for(;f<5;++f)l[f]=117;this.eof=!0}var h=0;for(f=0;f<5;++f)h=h*85+(l[f]-33);for(f=3;f>=0;--f)u[s+f]=h&255,h>>=8}},e}(ln),ef=function(r){X(e,r);function e(t,n){var i=r.call(this,n)||this;return i.stream=t,i.firstDigit=-1,n&&(n=.5*n),i}return e.prototype.readBlock=function(){var t=8e3,n=this.stream.getBytes(t);if(!n.length){this.eof=!0;return}for(var i=n.length+1>>1,a=this.ensureBuffer(this.bufferLength+i),o=this.bufferLength,s=this.firstDigit,u=0,f=n.length;u<f;u++){var l=n[u],h=void 0;if(l>=48&&l<=57)h=l&15;else if(l>=65&&l<=70||l>=97&&l<=102)h=(l&15)+9;else if(l===62){this.eof=!0;break}else continue;s<0?s=h:(a[o++]=s<<4|h,s=-1)}s>=0&&this.eof&&(a[o++]=s<<4,s=-1),this.firstDigit=s,this.bufferLength=o},e}(ln),Ua=new Int32Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),tf=new Int32Array([3,4,5,6,7,8,9,10,65547,65549,65551,65553,131091,131095,131099,131103,196643,196651,196659,196667,262211,262227,262243,262259,327811,327843,327875,327907,258,258,258]),rf=new Int32Array([1,2,3,4,65541,65543,131081,131085,196625,196633,262177,262193,327745,327777,393345,393409,459009,459137,524801,525057,590849,591361,657409,658433,724993,727041,794625,798721,868353,876545]),nf=[new Int32Array([459008,524368,524304,524568,459024,524400,524336,590016,459016,524384,524320,589984,524288,524416,524352,590048,459012,524376,524312,589968,459028,524408,524344,590032,459020,524392,524328,59e4,524296,524424,524360,590064,459010,524372,524308,524572,459026,524404,524340,590024,459018,524388,524324,589992,524292,524420,524356,590056,459014,524380,524316,589976,459030,524412,524348,590040,459022,524396,524332,590008,524300,524428,524364,590072,459009,524370,524306,524570,459025,524402,524338,590020,459017,524386,524322,589988,524290,524418,524354,590052,459013,524378,524314,589972,459029,524410,524346,590036,459021,524394,524330,590004,524298,524426,524362,590068,459011,524374,524310,524574,459027,524406,524342,590028,459019,524390,524326,589996,524294,524422,524358,590060,459015,524382,524318,589980,459031,524414,524350,590044,459023,524398,524334,590012,524302,524430,524366,590076,459008,524369,524305,524569,459024,524401,524337,590018,459016,524385,524321,589986,524289,524417,524353,590050,459012,524377,524313,589970,459028,524409,524345,590034,459020,524393,524329,590002,524297,524425,524361,590066,459010,524373,524309,524573,459026,524405,524341,590026,459018,524389,524325,589994,524293,524421,524357,590058,459014,524381,524317,589978,459030,524413,524349,590042,459022,524397,524333,590010,524301,524429,524365,590074,459009,524371,524307,524571,459025,524403,524339,590022,459017,524387,524323,589990,524291,524419,524355,590054,459013,524379,524315,589974,459029,524411,524347,590038,459021,524395,524331,590006,524299,524427,524363,590070,459011,524375,524311,524575,459027,524407,524343,590030,459019,524391,524327,589998,524295,524423,524359,590062,459015,524383,524319,589982,459031,524415,524351,590046,459023,524399,524335,590014,524303,524431,524367,590078,459008,524368,524304,524568,459024,524400,524336,590017,459016,524384,524320,589985,524288,524416,524352,590049,459012,524376,524312,589969,459028,524408,524344,590033,459020,524392,524328,590001,524296,524424,524360,590065,459010,524372,524308,524572,459026,524404,524340,590025,459018,524388,524324,589993,524292,524420,524356,590057,459014,524380,524316,589977,459030,524412,524348,590041,459022,524396,524332,590009,524300,524428,524364,590073,459009,524370,524306,524570,459025,524402,524338,590021,459017,524386,524322,589989,524290,524418,524354,590053,459013,524378,524314,589973,459029,524410,524346,590037,459021,524394,524330,590005,524298,524426,524362,590069,459011,524374,524310,524574,459027,524406,524342,590029,459019,524390,524326,589997,524294,524422,524358,590061,459015,524382,524318,589981,459031,524414,524350,590045,459023,524398,524334,590013,524302,524430,524366,590077,459008,524369,524305,524569,459024,524401,524337,590019,459016,524385,524321,589987,524289,524417,524353,590051,459012,524377,524313,589971,459028,524409,524345,590035,459020,524393,524329,590003,524297,524425,524361,590067,459010,524373,524309,524573,459026,524405,524341,590027,459018,524389,524325,589995,524293,524421,524357,590059,459014,524381,524317,589979,459030,524413,524349,590043,459022,524397,524333,590011,524301,524429,524365,590075,459009,524371,524307,524571,459025,524403,524339,590023,459017,524387,524323,589991,524291,524419,524355,590055,459013,524379,524315,589975,459029,524411,524347,590039,459021,524395,524331,590007,524299,524427,524363,590071,459011,524375,524311,524575,459027,524407,524343,590031,459019,524391,524327,589999,524295,524423,524359,590063,459015,524383,524319,589983,459031,524415,524351,590047,459023,524399,524335,590015,524303,524431,524367,590079]),9],af=[new Int32Array([327680,327696,327688,327704,327684,327700,327692,327708,327682,327698,327690,327706,327686,327702,327694,0,327681,327697,327689,327705,327685,327701,327693,327709,327683,327699,327691,327707,327687,327703,327695,0]),5],of=function(r){X(e,r);function e(t,n){var i=r.call(this,n)||this;i.stream=t;var a=t.getByte(),o=t.getByte();if(a===-1||o===-1)throw new Error("Invalid header in flate stream: "+a+", "+o);if((a&15)!==8)throw new Error("Unknown compression method in flate stream: "+a+", "+o);if(((a<<8)+o)%31!==0)throw new Error("Bad FCHECK in flate stream: "+a+", "+o);if(o&32)throw new Error("FDICT bit set in flate stream: "+a+", "+o);return i.codeSize=0,i.codeBuf=0,i}return e.prototype.readBlock=function(){var t,n,i=this.stream,a=this.getBits(3);if(a&1&&(this.eof=!0),a>>=1,a===0){var o=void 0;if((o=i.getByte())===-1)throw new Error("Bad block header in flate stream");var s=o;if((o=i.getByte())===-1)throw new Error("Bad block header in flate stream");if(s|=o<<8,(o=i.getByte())===-1)throw new Error("Bad block header in flate stream");var u=o;if((o=i.getByte())===-1)throw new Error("Bad block header in flate stream");if(u|=o<<8,u!==(~s&65535)&&(s!==0||u!==0))throw new Error("Bad uncompressed block length in flate stream");this.codeBuf=0,this.codeSize=0;var f=this.bufferLength;t=this.ensureBuffer(f+s);var l=f+s;if(this.bufferLength=l,s===0)i.peekByte()===-1&&(this.eof=!0);else for(var h=f;h<l;++h){if((o=i.getByte())===-1){this.eof=!0;break}t[h]=o}return}var d,v;if(a===1)d=nf,v=af;else if(a===2){var y=this.getBits(5)+257,w=this.getBits(5)+1,F=this.getBits(4)+4,S=new Uint8Array(Ua.length),R=void 0;for(R=0;R<F;++R)S[Ua[R]]=this.getBits(3);var C=this.generateHuffmanTable(S);n=0,R=0;for(var B=y+w,E=new Uint8Array(B),D=void 0,P=void 0,I=void 0;R<B;){var V=this.getCode(C);if(V===16)D=2,P=3,I=n;else if(V===17)D=3,P=3,I=n=0;else if(V===18)D=7,P=11,I=n=0;else{E[R++]=n=V;continue}for(var q=this.getBits(D)+P;q-- >0;)E[R++]=I}d=this.generateHuffmanTable(E.subarray(0,y)),v=this.generateHuffmanTable(E.subarray(y,B))}else throw new Error("Unknown block type in flate stream");t=this.buffer;for(var L=t?t.length:0,Z=this.bufferLength;;){var j=this.getCode(d);if(j<256){Z+1>=L&&(t=this.ensureBuffer(Z+1),L=t.length),t[Z++]=j;continue}if(j===256){this.bufferLength=Z;return}j-=257,j=tf[j];var Q=j>>16;Q>0&&(Q=this.getBits(Q)),n=(j&65535)+Q,j=this.getCode(v),j=rf[j],Q=j>>16,Q>0&&(Q=this.getBits(Q));var te=(j&65535)+Q;Z+n>=L&&(t=this.ensureBuffer(Z+n),L=t.length);for(var Y=0;Y<n;++Y,++Z)t[Z]=t[Z-te]}},e.prototype.getBits=function(t){for(var n=this.stream,i=this.codeSize,a=this.codeBuf,o;i<t;){if((o=n.getByte())===-1)throw new Error("Bad encoding in flate stream");a|=o<<i,i+=8}return o=a&(1<<t)-1,this.codeBuf=a>>t,this.codeSize=i-=t,o},e.prototype.getCode=function(t){for(var n=this.stream,i=t[0],a=t[1],o=this.codeSize,s=this.codeBuf,u;o<a&&(u=n.getByte())!==-1;)s|=u<<o,o+=8;var f=i[s&(1<<a)-1];typeof i=="number"&&console.log("FLATE:",f);var l=f>>16,h=f&65535;if(l<1||o<l)throw new Error("Bad encoding in flate stream");return this.codeBuf=s>>l,this.codeSize=o-l,h},e.prototype.generateHuffmanTable=function(t){var n=t.length,i=0,a;for(a=0;a<n;++a)t[a]>i&&(i=t[a]);for(var o=1<<i,s=new Int32Array(o),u=1,f=0,l=2;u<=i;++u,f<<=1,l<<=1)for(var h=0;h<n;++h)if(t[h]===u){var d=0,v=f;for(a=0;a<u;++a)d=d<<1|v&1,v>>=1;for(a=d;a<o;a+=l)s[a]=u<<16|h;++f}return[s,i]},e}(ln),sf=function(r){X(e,r);function e(t,n,i){var a=r.call(this,n)||this;a.stream=t,a.cachedData=0,a.bitsCached=0;for(var o=4096,s={earlyChange:i,codeLength:9,nextCode:258,dictionaryValues:new Uint8Array(o),dictionaryLengths:new Uint16Array(o),dictionaryPrevCodes:new Uint16Array(o),currentSequence:new Uint8Array(o),currentSequenceLength:0},u=0;u<256;++u)s.dictionaryValues[u]=u,s.dictionaryLengths[u]=1;return a.lzwState=s,a}return e.prototype.readBlock=function(){var t=512,n=t*2,i=t,a,o,s,u=this.lzwState;if(u){var f=u.earlyChange,l=u.nextCode,h=u.dictionaryValues,d=u.dictionaryLengths,v=u.dictionaryPrevCodes,y=u.codeLength,w=u.prevCode,F=u.currentSequence,S=u.currentSequenceLength,R=0,C=this.bufferLength,B=this.ensureBuffer(this.bufferLength+n);for(a=0;a<t;a++){var E=this.readBits(y),D=S>0;if(!E||E<256)F[0]=E,S=1;else if(E>=258)if(E<l)for(S=d[E],o=S-1,s=E;o>=0;o--)F[o]=h[s],s=v[s];else F[S++]=F[0];else if(E===256){y=9,l=258,S=0;continue}else{this.eof=!0,delete this.lzwState;break}if(D&&(v[l]=w,d[l]=d[w]+1,h[l]=F[0],l++,y=l+f&l+f-1?y:Math.min(Math.log(l+f)/.6931471805599453+1,12)|0),w=E,R+=S,n<R){do n+=i;while(n<R);B=this.ensureBuffer(this.bufferLength+n)}for(o=0;o<S;o++)B[C++]=F[o]}u.nextCode=l,u.codeLength=y,u.prevCode=w,u.currentSequenceLength=S,this.bufferLength=C}},e.prototype.readBits=function(t){for(var n=this.bitsCached,i=this.cachedData;n<t;){var a=this.stream.getByte();if(a===-1)return this.eof=!0,null;i=i<<8|a,n+=8}return this.bitsCached=n-=t,this.cachedData=i,i>>>n&(1<<t)-1},e}(ln),uf=function(r){X(e,r);function e(t,n){var i=r.call(this,n)||this;return i.stream=t,i}return e.prototype.readBlock=function(){var t=this.stream.getBytes(2);if(!t||t.length<2||t[0]===128){this.eof=!0;return}var n,i=this.bufferLength,a=t[0];if(a<128){if(n=this.ensureBuffer(i+a+1),n[i++]=t[1],a>0){var o=this.stream.getBytes(a);n.set(o,i),i+=a}}else{a=257-a;var s=t[1];n=this.ensureBuffer(i+a+1);for(var u=0;u<a;u++)n[i++]=s}this.bufferLength=i},e}(ln),Va=function(r,e,t){if(e===m.of("FlateDecode"))return new of(r);if(e===m.of("LZWDecode")){var n=1;if(t instanceof ve){var i=t.lookup(m.of("EarlyChange"));i instanceof fe&&(n=i.asNumber())}return new sf(r,void 0,n)}if(e===m.of("ASCII85Decode"))return new $u(r);if(e===m.of("ASCIIHexDecode"))return new ef(r);if(e===m.of("RunLengthDecode"))return new uf(r);throw new gu(e.asString())},Po=function(r){var e=r.dict,t=r.contents,n=new To(t),i=e.lookup(m.of("Filter")),a=e.lookup(m.of("DecodeParms"));if(i instanceof m)n=Va(n,i,a);else if(i instanceof Ae)for(var o=0,s=i.size();o<s;o++)n=Va(n,i.lookup(o,m),a&&a.lookupMaybe(o,ve));else if(i)throw new zn([m,Ae],i);return n},ff=function(r){var e=r.MediaBox(),t=e.lookup(2,fe).asNumber()-e.lookup(0,fe).asNumber(),n=e.lookup(3,fe).asNumber()-e.lookup(1,fe).asNumber();return{left:0,bottom:0,right:t,top:n}},cf=function(r){return[1,0,0,1,-r.left,-r.bottom]},Do=function(){function r(e,t,n){this.page=e;var i=t??ff(e);this.width=i.right-i.left,this.height=i.top-i.bottom,this.boundingBox=i,this.transformationMatrix=n??cf(i)}return r.for=function(e,t,n){return pe(this,void 0,void 0,function(){return ge(this,function(i){return[2,new r(e,t,n)]})})},r.prototype.embedIntoContext=function(e,t){return pe(this,void 0,void 0,function(){var n,i,a,o,s,u,f,l,h,d;return ge(this,function(v){if(n=this.page.normalizedEntries(),i=n.Contents,a=n.Resources,!i)throw new yu;return o=this.decodeContents(i),s=this.boundingBox,u=s.left,f=s.bottom,l=s.right,h=s.top,d=e.flateStream(o,{Type:"XObject",Subtype:"Form",FormType:1,BBox:[u,f,l,h],Matrix:this.transformationMatrix,Resources:a}),t?(e.assign(t,d),[2,t]):[2,e.register(d)]})})},r.prototype.decodeContents=function(e){for(var t=Uint8Array.of(x.Newline),n=[],i=0,a=e.size();i<a;i++){var o=e.lookup(i,gt),s=void 0;if(o instanceof an)s=Po(o).decode();else if(o instanceof Qr)s=o.getUnencodedContents();else throw new bu(o);n.push(s,t)}return ys.apply(void 0,n)},r}(),Sn=function(r,e){if(r!==void 0)return e[r]},_r;(function(r){r.UseNone="UseNone",r.UseOutlines="UseOutlines",r.UseThumbs="UseThumbs",r.UseOC="UseOC"})(_r||(_r={}));var $r;(function(r){r.L2R="L2R",r.R2L="R2L"})($r||($r={}));var en;(function(r){r.None="None",r.AppDefault="AppDefault"})(en||(en={}));var In;(function(r){r.Simplex="Simplex",r.DuplexFlipShortEdge="DuplexFlipShortEdge",r.DuplexFlipLongEdge="DuplexFlipLongEdge"})(In||(In={}));var qa=function(){function r(e){this.dict=e}return r.prototype.lookupBool=function(e){var t=this.dict.lookup(m.of(e));if(t instanceof nn)return t},r.prototype.lookupName=function(e){var t=this.dict.lookup(m.of(e));if(t instanceof m)return t},r.prototype.HideToolbar=function(){return this.lookupBool("HideToolbar")},r.prototype.HideMenubar=function(){return this.lookupBool("HideMenubar")},r.prototype.HideWindowUI=function(){return this.lookupBool("HideWindowUI")},r.prototype.FitWindow=function(){return this.lookupBool("FitWindow")},r.prototype.CenterWindow=function(){return this.lookupBool("CenterWindow")},r.prototype.DisplayDocTitle=function(){return this.lookupBool("DisplayDocTitle")},r.prototype.NonFullScreenPageMode=function(){return this.lookupName("NonFullScreenPageMode")},r.prototype.Direction=function(){return this.lookupName("Direction")},r.prototype.PrintScaling=function(){return this.lookupName("PrintScaling")},r.prototype.Duplex=function(){return this.lookupName("Duplex")},r.prototype.PickTrayByPDFSize=function(){return this.lookupBool("PickTrayByPDFSize")},r.prototype.PrintPageRange=function(){var e=this.dict.lookup(m.of("PrintPageRange"));if(e instanceof Ae)return e},r.prototype.NumCopies=function(){var e=this.dict.lookup(m.of("NumCopies"));if(e instanceof fe)return e},r.prototype.getHideToolbar=function(){var e,t;return(t=(e=this.HideToolbar())===null||e===void 0?void 0:e.asBoolean())!==null&&t!==void 0?t:!1},r.prototype.getHideMenubar=function(){var e,t;return(t=(e=this.HideMenubar())===null||e===void 0?void 0:e.asBoolean())!==null&&t!==void 0?t:!1},r.prototype.getHideWindowUI=function(){var e,t;return(t=(e=this.HideWindowUI())===null||e===void 0?void 0:e.asBoolean())!==null&&t!==void 0?t:!1},r.prototype.getFitWindow=function(){var e,t;return(t=(e=this.FitWindow())===null||e===void 0?void 0:e.asBoolean())!==null&&t!==void 0?t:!1},r.prototype.getCenterWindow=function(){var e,t;return(t=(e=this.CenterWindow())===null||e===void 0?void 0:e.asBoolean())!==null&&t!==void 0?t:!1},r.prototype.getDisplayDocTitle=function(){var e,t;return(t=(e=this.DisplayDocTitle())===null||e===void 0?void 0:e.asBoolean())!==null&&t!==void 0?t:!1},r.prototype.getNonFullScreenPageMode=function(){var e,t,n=(e=this.NonFullScreenPageMode())===null||e===void 0?void 0:e.decodeText();return(t=Sn(n,_r))!==null&&t!==void 0?t:_r.UseNone},r.prototype.getReadingDirection=function(){var e,t,n=(e=this.Direction())===null||e===void 0?void 0:e.decodeText();return(t=Sn(n,$r))!==null&&t!==void 0?t:$r.L2R},r.prototype.getPrintScaling=function(){var e,t,n=(e=this.PrintScaling())===null||e===void 0?void 0:e.decodeText();return(t=Sn(n,en))!==null&&t!==void 0?t:en.AppDefault},r.prototype.getDuplex=function(){var e,t=(e=this.Duplex())===null||e===void 0?void 0:e.decodeText();return Sn(t,In)},r.prototype.getPickTrayByPDFSize=function(){var e;return(e=this.PickTrayByPDFSize())===null||e===void 0?void 0:e.asBoolean()},r.prototype.getPrintPageRange=function(){var e=this.PrintPageRange();if(!e)return[];for(var t=[],n=0;n<e.size();n+=2){var i=e.lookup(n,fe).asNumber(),a=e.lookup(n+1,fe).asNumber();t.push({start:i,end:a})}return t},r.prototype.getNumCopies=function(){var e,t;return(t=(e=this.NumCopies())===null||e===void 0?void 0:e.asNumber())!==null&&t!==void 0?t:1},r.prototype.setHideToolbar=function(e){var t=this.dict.context.obj(e);this.dict.set(m.of("HideToolbar"),t)},r.prototype.setHideMenubar=function(e){var t=this.dict.context.obj(e);this.dict.set(m.of("HideMenubar"),t)},r.prototype.setHideWindowUI=function(e){var t=this.dict.context.obj(e);this.dict.set(m.of("HideWindowUI"),t)},r.prototype.setFitWindow=function(e){var t=this.dict.context.obj(e);this.dict.set(m.of("FitWindow"),t)},r.prototype.setCenterWindow=function(e){var t=this.dict.context.obj(e);this.dict.set(m.of("CenterWindow"),t)},r.prototype.setDisplayDocTitle=function(e){var t=this.dict.context.obj(e);this.dict.set(m.of("DisplayDocTitle"),t)},r.prototype.setNonFullScreenPageMode=function(e){er(e,"nonFullScreenPageMode",_r);var t=m.of(e);this.dict.set(m.of("NonFullScreenPageMode"),t)},r.prototype.setReadingDirection=function(e){er(e,"readingDirection",$r);var t=m.of(e);this.dict.set(m.of("Direction"),t)},r.prototype.setPrintScaling=function(e){er(e,"printScaling",en);var t=m.of(e);this.dict.set(m.of("PrintScaling"),t)},r.prototype.setDuplex=function(e){er(e,"duplex",In);var t=m.of(e);this.dict.set(m.of("Duplex"),t)},r.prototype.setPickTrayByPDFSize=function(e){var t=this.dict.context.obj(e);this.dict.set(m.of("PickTrayByPDFSize"),t)},r.prototype.setPrintPageRange=function(e){Array.isArray(e)||(e=[e]);for(var t=[],n=0,i=e.length;n<i;n++)t.push(e[n].start),t.push(e[n].end);go(t,"printPageRange",["number"]);var a=this.dict.context.obj(t);this.dict.set(m.of("PrintPageRange"),a)},r.prototype.setNumCopies=function(e){vt(e,"numCopies",1,Number.MAX_VALUE),pu(e,"numCopies");var t=this.dict.context.obj(e);this.dict.set(m.of("NumCopies"),t)},r.fromDict=function(e){return new r(e)},r.create=function(e){var t=e.obj({});return new r(t)},r}(),lf=/\/([^\0\t\n\f\r\ ]+)[\0\t\n\f\r\ ]*(\d*\.\d+|\d+)?[\0\t\n\f\r\ ]+Tf/,Ao=function(){function r(e,t){this.dict=e,this.ref=t}return r.prototype.T=function(){return this.dict.lookupMaybe(m.of("T"),Oe,oe)},r.prototype.Ff=function(){var e=this.getInheritableAttribute(m.of("Ff"));return this.dict.context.lookupMaybe(e,fe)},r.prototype.V=function(){var e=this.getInheritableAttribute(m.of("V"));return this.dict.context.lookup(e)},r.prototype.Kids=function(){return this.dict.lookupMaybe(m.of("Kids"),Ae)},r.prototype.DA=function(){var e=this.dict.lookup(m.of("DA"));if(e instanceof Oe||e instanceof oe)return e},r.prototype.setKids=function(e){this.dict.set(m.of("Kids"),this.dict.context.obj(e))},r.prototype.getParent=function(){var e=this.dict.get(m.of("Parent"));if(e instanceof Ee){var t=this.dict.lookup(m.of("Parent"),ve);return new r(t,e)}},r.prototype.setParent=function(e){e?this.dict.set(m.of("Parent"),e):this.dict.delete(m.of("Parent"))},r.prototype.getFullyQualifiedName=function(){var e=this.getParent();return e?e.getFullyQualifiedName()+"."+this.getPartialName():this.getPartialName()},r.prototype.getPartialName=function(){var e;return(e=this.T())===null||e===void 0?void 0:e.decodeText()},r.prototype.setPartialName=function(e){e?this.dict.set(m.of("T"),oe.fromText(e)):this.dict.delete(m.of("T"))},r.prototype.setDefaultAppearance=function(e){this.dict.set(m.of("DA"),Oe.of(e))},r.prototype.getDefaultAppearance=function(){var e=this.DA();return e instanceof oe?e.decodeText():e==null?void 0:e.asString()},r.prototype.setFontSize=function(e){var t,n=(t=this.getFullyQualifiedName())!==null&&t!==void 0?t:"",i=this.getDefaultAppearance();if(!i)throw new Su(n);var a=zi(i,lf);if(!a.match)throw new Fu(n);var o=i.slice(0,a.pos-a.match[0].length),s=a.pos<=i.length?i.slice(a.pos):"",u=a.match[1],f=o+" /"+u+" "+e+" Tf "+s;this.setDefaultAppearance(f)},r.prototype.getFlags=function(){var e,t;return(t=(e=this.Ff())===null||e===void 0?void 0:e.asNumber())!==null&&t!==void 0?t:0},r.prototype.setFlags=function(e){this.dict.set(m.of("Ff"),fe.of(e))},r.prototype.hasFlag=function(e){var t=this.getFlags();return(t&e)!==0},r.prototype.setFlag=function(e){var t=this.getFlags();this.setFlags(t|e)},r.prototype.clearFlag=function(e){var t=this.getFlags();this.setFlags(t&~e)},r.prototype.setFlagTo=function(e,t){t?this.setFlag(e):this.clearFlag(e)},r.prototype.getInheritableAttribute=function(e){var t;return this.ascend(function(n){t||(t=n.dict.get(e))}),t},r.prototype.ascend=function(e){e(this);var t=this.getParent();t&&t.ascend(e)},r}(),Fi=function(){function r(e){this.dict=e}return r.prototype.W=function(){var e=this.dict.lookup(m.of("W"));if(e instanceof fe)return e},r.prototype.getWidth=function(){var e,t;return(t=(e=this.W())===null||e===void 0?void 0:e.asNumber())!==null&&t!==void 0?t:1},r.prototype.setWidth=function(e){var t=this.dict.context.obj(e);this.dict.set(m.of("W"),t)},r.fromDict=function(e){return new r(e)},r}(),hf=function(){function r(e){this.dict=e}return r.prototype.Rect=function(){return this.dict.lookup(m.of("Rect"),Ae)},r.prototype.AP=function(){return this.dict.lookupMaybe(m.of("AP"),ve)},r.prototype.F=function(){var e=this.dict.lookup(m.of("F"));return this.dict.context.lookupMaybe(e,fe)},r.prototype.getRectangle=function(){var e,t=this.Rect();return(e=t==null?void 0:t.asRectangle())!==null&&e!==void 0?e:{x:0,y:0,width:0,height:0}},r.prototype.setRectangle=function(e){var t=e.x,n=e.y,i=e.width,a=e.height,o=this.dict.context.obj([t,n,t+i,n+a]);this.dict.set(m.of("Rect"),o)},r.prototype.getAppearanceState=function(){var e=this.dict.lookup(m.of("AS"));if(e instanceof m)return e},r.prototype.setAppearanceState=function(e){this.dict.set(m.of("AS"),e)},r.prototype.setAppearances=function(e){this.dict.set(m.of("AP"),e)},r.prototype.ensureAP=function(){var e=this.AP();return e||(e=this.dict.context.obj({}),this.dict.set(m.of("AP"),e)),e},r.prototype.getNormalAppearance=function(){var e=this.ensureAP(),t=e.get(m.of("N"));if(t instanceof Ee||t instanceof ve)return t;throw new Error("Unexpected N type: "+(t==null?void 0:t.constructor.name))},r.prototype.setNormalAppearance=function(e){var t=this.ensureAP();t.set(m.of("N"),e)},r.prototype.setRolloverAppearance=function(e){var t=this.ensureAP();t.set(m.of("R"),e)},r.prototype.setDownAppearance=function(e){var t=this.ensureAP();t.set(m.of("D"),e)},r.prototype.removeRolloverAppearance=function(){var e=this.AP();e==null||e.delete(m.of("R"))},r.prototype.removeDownAppearance=function(){var e=this.AP();e==null||e.delete(m.of("D"))},r.prototype.getAppearances=function(){var e=this.AP();if(e){var t=e.lookup(m.of("N"),ve,gt),n=e.lookupMaybe(m.of("R"),ve,gt),i=e.lookupMaybe(m.of("D"),ve,gt);return{normal:t,rollover:n,down:i}}},r.prototype.getFlags=function(){var e,t;return(t=(e=this.F())===null||e===void 0?void 0:e.asNumber())!==null&&t!==void 0?t:0},r.prototype.setFlags=function(e){this.dict.set(m.of("F"),fe.of(e))},r.prototype.hasFlag=function(e){var t=this.getFlags();return(t&e)!==0},r.prototype.setFlag=function(e){var t=this.getFlags();this.setFlags(t|e)},r.prototype.clearFlag=function(e){var t=this.getFlags();this.setFlags(t&~e)},r.prototype.setFlagTo=function(e,t){t?this.setFlag(e):this.clearFlag(e)},r.fromDict=function(e){return new r(e)},r}(),ki=function(){function r(e){this.dict=e}return r.prototype.R=function(){var e=this.dict.lookup(m.of("R"));if(e instanceof fe)return e},r.prototype.BC=function(){var e=this.dict.lookup(m.of("BC"));if(e instanceof Ae)return e},r.prototype.BG=function(){var e=this.dict.lookup(m.of("BG"));if(e instanceof Ae)return e},r.prototype.CA=function(){var e=this.dict.lookup(m.of("CA"));if(e instanceof oe||e instanceof Oe)return e},r.prototype.RC=function(){var e=this.dict.lookup(m.of("RC"));if(e instanceof oe||e instanceof Oe)return e},r.prototype.AC=function(){var e=this.dict.lookup(m.of("AC"));if(e instanceof oe||e instanceof Oe)return e},r.prototype.getRotation=function(){var e;return(e=this.R())===null||e===void 0?void 0:e.asNumber()},r.prototype.getBorderColor=function(){var e=this.BC();if(e){for(var t=[],n=0,i=e==null?void 0:e.size();n<i;n++){var a=e.get(n);a instanceof fe&&t.push(a.asNumber())}return t}},r.prototype.getBackgroundColor=function(){var e=this.BG();if(e){for(var t=[],n=0,i=e==null?void 0:e.size();n<i;n++){var a=e.get(n);a instanceof fe&&t.push(a.asNumber())}return t}},r.prototype.getCaptions=function(){var e=this.CA(),t=this.RC(),n=this.AC();return{normal:e==null?void 0:e.decodeText(),rollover:t==null?void 0:t.decodeText(),down:n==null?void 0:n.decodeText()}},r.prototype.setRotation=function(e){var t=this.dict.context.obj(e);this.dict.set(m.of("R"),t)},r.prototype.setBorderColor=function(e){var t=this.dict.context.obj(e);this.dict.set(m.of("BC"),t)},r.prototype.setBackgroundColor=function(e){var t=this.dict.context.obj(e);this.dict.set(m.of("BG"),t)},r.prototype.setCaptions=function(e){var t=oe.fromText(e.normal);if(this.dict.set(m.of("CA"),t),e.rollover){var n=oe.fromText(e.rollover);this.dict.set(m.of("RC"),n)}else this.dict.delete(m.of("RC"));if(e.down){var i=oe.fromText(e.down);this.dict.set(m.of("AC"),i)}else this.dict.delete(m.of("AC"))},r.fromDict=function(e){return new r(e)},r}(),Ai=function(r){X(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.MK=function(){var t=this.dict.lookup(m.of("MK"));if(t instanceof ve)return t},e.prototype.BS=function(){var t=this.dict.lookup(m.of("BS"));if(t instanceof ve)return t},e.prototype.DA=function(){var t=this.dict.lookup(m.of("DA"));if(t instanceof Oe||t instanceof oe)return t},e.prototype.P=function(){var t=this.dict.get(m.of("P"));if(t instanceof Ee)return t},e.prototype.setP=function(t){this.dict.set(m.of("P"),t)},e.prototype.setDefaultAppearance=function(t){this.dict.set(m.of("DA"),Oe.of(t))},e.prototype.getDefaultAppearance=function(){var t=this.DA();return t instanceof oe?t.decodeText():t==null?void 0:t.asString()},e.prototype.getAppearanceCharacteristics=function(){var t=this.MK();if(t)return ki.fromDict(t)},e.prototype.getOrCreateAppearanceCharacteristics=function(){var t=this.MK();if(t)return ki.fromDict(t);var n=ki.fromDict(this.dict.context.obj({}));return this.dict.set(m.of("MK"),n.dict),n},e.prototype.getBorderStyle=function(){var t=this.BS();if(t)return Fi.fromDict(t)},e.prototype.getOrCreateBorderStyle=function(){var t=this.BS();if(t)return Fi.fromDict(t);var n=Fi.fromDict(this.dict.context.obj({}));return this.dict.set(m.of("BS"),n.dict),n},e.prototype.getOnValue=function(){var t,n=(t=this.getAppearances())===null||t===void 0?void 0:t.normal;if(n instanceof ve)for(var i=n.keys(),a=0,o=i.length;a<o;a++){var s=i[a];if(s!==m.of("Off"))return s}},e.fromDict=function(t){return new e(t)},e.create=function(t,n){var i=t.obj({Type:"Annot",Subtype:"Widget",Rect:[0,0,0,0],Parent:n});return new e(i)},e}(hf),Nr=function(r){X(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.FT=function(){var t=this.getInheritableAttribute(m.of("FT"));return this.dict.context.lookup(t,m)},e.prototype.getWidgets=function(){var t=this.Kids();if(!t)return[Ai.fromDict(this.dict)];for(var n=new Array(t.size()),i=0,a=t.size();i<a;i++){var o=t.lookup(i,ve);n[i]=Ai.fromDict(o)}return n},e.prototype.addWidget=function(t){var n=this.normalizedEntries().Kids;n.push(t)},e.prototype.removeWidget=function(t){var n=this.Kids();if(n){if(t<0||t>n.size())throw new jn(t,0,n.size());n.remove(t)}else{if(t!==0)throw new jn(t,0,0);this.setKids([])}},e.prototype.normalizedEntries=function(){var t=this.Kids();return t||(t=this.dict.context.obj([this.ref]),this.dict.set(m.of("Kids"),t)),{Kids:t}},e.fromDict=function(t,n){return new e(t,n)},e}(Ao),Li=function(r){X(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.Opt=function(){return this.dict.lookupMaybe(m.of("Opt"),Oe,oe,Ae)},e.prototype.setOpt=function(t){this.dict.set(m.of("Opt"),this.dict.context.obj(t))},e.prototype.getExportValues=function(){var t=this.Opt();if(t){if(t instanceof Oe||t instanceof oe)return[t];for(var n=[],i=0,a=t.size();i<a;i++){var o=t.lookup(i);(o instanceof Oe||o instanceof oe)&&n.push(o)}return n}},e.prototype.removeExportValue=function(t){var n=this.Opt();if(n)if(n instanceof Oe||n instanceof oe){if(t!==0)throw new jn(t,0,0);this.setOpt([])}else{if(t<0||t>n.size())throw new jn(t,0,n.size());n.remove(t)}},e.prototype.normalizeExportValues=function(){for(var t,n,i,a,o=(t=this.getExportValues())!==null&&t!==void 0?t:[],s=[],u=this.getWidgets(),f=0,l=u.length;f<l;f++){var h=u[f],d=(n=o[f])!==null&&n!==void 0?n:oe.fromText((a=(i=h.getOnValue())===null||i===void 0?void 0:i.decodeText())!==null&&a!==void 0?a:"");s.push(d)}this.setOpt(s)},e.prototype.addOpt=function(t,n){var i;this.normalizeExportValues();var a=t.decodeText(),o;if(n)for(var s=(i=this.getExportValues())!==null&&i!==void 0?i:[],u=0,f=s.length;u<f;u++){var l=s[u];l.decodeText()===a&&(o=u)}var h=this.Opt();return h.push(t),o??h.size()-1},e.prototype.addWidgetWithOpt=function(t,n,i){var a=this.addOpt(n,i),o=m.of(String(a));return this.addWidget(t),o},e}(Nr),_n=function(r){X(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.setValue=function(t){var n,i=(n=this.getOnValue())!==null&&n!==void 0?n:m.of("Yes");if(t!==i&&t!==m.of("Off"))throw new Ui;this.dict.set(m.of("V"),t);for(var a=this.getWidgets(),o=0,s=a.length;o<s;o++){var u=a[o],f=u.getOnValue()===t?t:m.of("Off");u.setAppearanceState(f)}},e.prototype.getValue=function(){var t=this.V();return t instanceof m?t:m.of("Off")},e.prototype.getOnValue=function(){var t=this.getWidgets()[0];return t==null?void 0:t.getOnValue()},e.fromDict=function(t,n){return new e(t,n)},e.create=function(t){var n=t.obj({FT:"Btn",Kids:[]}),i=t.register(n);return new e(n,i)},e}(Li),Xe=function(r){return 1<<r},zt;(function(r){r[r.ReadOnly=Xe(0)]="ReadOnly",r[r.Required=Xe(1)]="Required",r[r.NoExport=Xe(2)]="NoExport"})(zt||(zt={}));var Ct;(function(r){r[r.NoToggleToOff=Xe(14)]="NoToggleToOff",r[r.Radio=Xe(15)]="Radio",r[r.PushButton=Xe(16)]="PushButton",r[r.RadiosInUnison=Xe(25)]="RadiosInUnison"})(Ct||(Ct={}));var Ke;(function(r){r[r.Multiline=Xe(12)]="Multiline",r[r.Password=Xe(13)]="Password",r[r.FileSelect=Xe(20)]="FileSelect",r[r.DoNotSpellCheck=Xe(22)]="DoNotSpellCheck",r[r.DoNotScroll=Xe(23)]="DoNotScroll",r[r.Comb=Xe(24)]="Comb",r[r.RichText=Xe(25)]="RichText"})(Ke||(Ke={}));var Re;(function(r){r[r.Combo=Xe(17)]="Combo",r[r.Edit=Xe(18)]="Edit",r[r.Sort=Xe(19)]="Sort",r[r.MultiSelect=Xe(21)]="MultiSelect",r[r.DoNotSpellCheck=Xe(22)]="DoNotSpellCheck",r[r.CommitOnSelChange=Xe(26)]="CommitOnSelChange"})(Re||(Re={}));var Ro=function(r){X(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.setValues=function(t){if(this.hasFlag(Re.Combo)&&!this.hasFlag(Re.Edit)&&!this.valuesAreValid(t))throw new Ui;if(t.length===0&&this.dict.delete(m.of("V")),t.length===1&&this.dict.set(m.of("V"),t[0]),t.length>1){if(!this.hasFlag(Re.MultiSelect))throw new wu;this.dict.set(m.of("V"),this.dict.context.obj(t))}this.updateSelectedIndices(t)},e.prototype.valuesAreValid=function(t){for(var n=this.getOptions(),i=function(u,f){var l=t[u].decodeText();if(!n.find(function(h){return l===(h.display||h.value).decodeText()}))return{value:!1}},a=0,o=t.length;a<o;a++){var s=i(a);if(typeof s=="object")return s.value}return!0},e.prototype.updateSelectedIndices=function(t){if(t.length>1){for(var n=new Array(t.length),i=this.getOptions(),a=function(u,f){var l=t[u].decodeText();n[u]=i.findIndex(function(h){return l===(h.display||h.value).decodeText()})},o=0,s=t.length;o<s;o++)a(o,s);this.dict.set(m.of("I"),this.dict.context.obj(n.sort()))}else this.dict.delete(m.of("I"))},e.prototype.getValues=function(){var t=this.V();if(t instanceof Oe||t instanceof oe)return[t];if(t instanceof Ae){for(var n=[],i=0,a=t.size();i<a;i++){var o=t.lookup(i);(o instanceof Oe||o instanceof oe)&&n.push(o)}return n}return[]},e.prototype.Opt=function(){return this.dict.lookupMaybe(m.of("Opt"),Oe,oe,Ae)},e.prototype.setOptions=function(t){for(var n=new Array(t.length),i=0,a=t.length;i<a;i++){var o=t[i],s=o.value,u=o.display;n[i]=this.dict.context.obj([s,u||s])}this.dict.set(m.of("Opt"),this.dict.context.obj(n))},e.prototype.getOptions=function(){var t=this.Opt();if(t instanceof Oe||t instanceof oe)return[{value:t,display:t}];if(t instanceof Ae){for(var n=[],i=0,a=t.size();i<a;i++){var o=t.lookup(i);if((o instanceof Oe||o instanceof oe)&&n.push({value:o,display:o}),o instanceof Ae&&o.size()>0){var s=o.lookup(0,Oe,oe),u=o.lookupMaybe(1,Oe,oe);n.push({value:s,display:u||s})}}return n}return[]},e}(Nr),$n=function(r){X(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.fromDict=function(t,n){return new e(t,n)},e.create=function(t){var n=t.obj({FT:"Ch",Ff:Re.Combo,Kids:[]}),i=t.register(n);return new e(n,i)},e}(Ro),Un=function(r){X(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.addField=function(t){var n=this.normalizedEntries().Kids;n==null||n.push(t)},e.prototype.normalizedEntries=function(){var t=this.Kids();return t||(t=this.dict.context.obj([]),this.dict.set(m.of("Kids"),t)),{Kids:t}},e.fromDict=function(t,n){return new e(t,n)},e.create=function(t){var n=t.obj({}),i=t.register(n);return new e(n,i)},e}(Ao),Gi=function(r){X(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.fromDict=function(t,n){return new e(t,n)},e}(Nr),ei=function(r){X(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.MaxLen=function(){var t=this.dict.lookup(m.of("MaxLen"));if(t instanceof fe)return t},e.prototype.Q=function(){var t=this.dict.lookup(m.of("Q"));if(t instanceof fe)return t},e.prototype.setMaxLength=function(t){this.dict.set(m.of("MaxLen"),fe.of(t))},e.prototype.removeMaxLength=function(){this.dict.delete(m.of("MaxLen"))},e.prototype.getMaxLength=function(){var t;return(t=this.MaxLen())===null||t===void 0?void 0:t.asNumber()},e.prototype.setQuadding=function(t){this.dict.set(m.of("Q"),fe.of(t))},e.prototype.getQuadding=function(){var t;return(t=this.Q())===null||t===void 0?void 0:t.asNumber()},e.prototype.setValue=function(t){this.dict.set(m.of("V"),t)},e.prototype.removeValue=function(){this.dict.delete(m.of("V"))},e.prototype.getValue=function(){var t=this.V();if(t instanceof Oe||t instanceof oe)return t},e.fromDict=function(t,n){return new e(t,n)},e.create=function(t){var n=t.obj({FT:"Tx",Kids:[]}),i=t.register(n);return new e(n,i)},e}(Nr),ti=function(r){X(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.fromDict=function(t,n){return new e(t,n)},e.create=function(t){var n=t.obj({FT:"Btn",Ff:Ct.PushButton,Kids:[]}),i=t.register(n);return new e(n,i)},e}(Li),ri=function(r){X(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.setValue=function(t){var n=this.getOnValues();if(!n.includes(t)&&t!==m.of("Off"))throw new Ui;this.dict.set(m.of("V"),t);for(var i=this.getWidgets(),a=0,o=i.length;a<o;a++){var s=i[a],u=s.getOnValue()===t?t:m.of("Off");s.setAppearanceState(u)}},e.prototype.getValue=function(){var t=this.V();return t instanceof m?t:m.of("Off")},e.prototype.getOnValues=function(){for(var t=this.getWidgets(),n=[],i=0,a=t.length;i<a;i++){var o=t[i].getOnValue();o&&n.push(o)}return n},e.fromDict=function(t,n){return new e(t,n)},e.create=function(t){var n=t.obj({FT:"Btn",Ff:Ct.Radio,Kids:[]}),i=t.register(n);return new e(n,i)},e}(Li),ni=function(r){X(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.fromDict=function(t,n){return new e(t,n)},e.create=function(t){var n=t.obj({FT:"Ch",Kids:[]}),i=t.register(n);return new e(n,i)},e}(Ro),Hi=function(r){if(!r)return[];for(var e=[],t=0,n=r.size();t<n;t++){var i=r.get(t),a=r.lookup(t);i instanceof Ee&&a instanceof ve&&e.push([Oo(a,i),i])}return e},Oo=function(r,e){var t=df(r);return t?Un.fromDict(r,e):vf(r,e)},df=function(r){var e=r.lookup(m.of("Kids"));if(e instanceof Ae)for(var t=0,n=e.size();t<n;t++){var i=e.lookup(t),a=i instanceof ve&&i.has(m.of("T"));if(a)return!0}return!1},vf=function(r,e){var t=Xi(r,m.of("FT")),n=r.context.lookup(t,m);return n===m.of("Btn")?pf(r,e):n===m.of("Ch")?gf(r,e):n===m.of("Tx")?ei.fromDict(r,e):n===m.of("Sig")?Gi.fromDict(r,e):Nr.fromDict(r,e)},pf=function(r,e){var t,n=Xi(r,m.of("Ff")),i=r.context.lookupMaybe(n,fe),a=(t=i==null?void 0:i.asNumber())!==null&&t!==void 0?t:0;return Ri(a,Ct.PushButton)?ti.fromDict(r,e):Ri(a,Ct.Radio)?ri.fromDict(r,e):_n.fromDict(r,e)},gf=function(r,e){var t,n=Xi(r,m.of("Ff")),i=r.context.lookupMaybe(n,fe),a=(t=i==null?void 0:i.asNumber())!==null&&t!==void 0?t:0;return Ri(a,Re.Combo)?$n.fromDict(r,e):ni.fromDict(r,e)},Ri=function(r,e){return(r&e)!==0},Xi=function(r,e){var t;return Eo(r,function(n){t||(t=n.get(e))}),t},Eo=function(r,e){e(r);var t=r.lookupMaybe(m.of("Parent"),ve);t&&Eo(t,e)},Vn=function(){function r(e){this.dict=e}return r.prototype.Fields=function(){var e=this.dict.lookup(m.of("Fields"));if(e instanceof Ae)return e},r.prototype.getFields=function(){for(var e=this.normalizedEntries().Fields,t=new Array(e.size()),n=0,i=e.size();n<i;n++){var a=e.get(n),o=e.lookup(n,ve);t[n]=[Oo(o,a),a]}return t},r.prototype.getAllFields=function(){var e=[],t=function(n){if(n)for(var i=0,a=n.length;i<a;i++){var o=n[i];e.push(o);var s=o[0];s instanceof Un&&t(Hi(s.Kids()))}};return t(this.getFields()),e},r.prototype.addField=function(e){var t=this.normalizedEntries().Fields;t==null||t.push(e)},r.prototype.removeField=function(e){var t=e.getParent(),n=t===void 0?this.normalizedEntries().Fields:t.Kids(),i=n==null?void 0:n.indexOf(e.ref);if(n===void 0||i===void 0)throw new Error("Tried to remove inexistent field "+e.getFullyQualifiedName());n.remove(i),t!==void 0&&n.size()===0&&this.removeField(t)},r.prototype.normalizedEntries=function(){var e=this.Fields();return e||(e=this.dict.context.obj([]),this.dict.set(m.of("Fields"),e)),{Fields:e}},r.fromDict=function(e){return new r(e)},r.create=function(e){var t=e.obj({Fields:[]});return new r(t)},r}(),Bo=function(r){X(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.Pages=function(){return this.lookup(m.of("Pages"),ve)},e.prototype.AcroForm=function(){return this.lookupMaybe(m.of("AcroForm"),ve)},e.prototype.getAcroForm=function(){var t=this.AcroForm();if(t)return Vn.fromDict(t)},e.prototype.getOrCreateAcroForm=function(){var t=this.getAcroForm();if(!t){t=Vn.create(this.context);var n=this.context.register(t.dict);this.set(m.of("AcroForm"),n)}return t},e.prototype.ViewerPreferences=function(){return this.lookupMaybe(m.of("ViewerPreferences"),ve)},e.prototype.getViewerPreferences=function(){var t=this.ViewerPreferences();if(t)return qa.fromDict(t)},e.prototype.getOrCreateViewerPreferences=function(){var t=this.getViewerPreferences();if(!t){t=qa.create(this.context);var n=this.context.register(t.dict);this.set(m.of("ViewerPreferences"),n)}return t},e.prototype.insertLeafNode=function(t,n){var i=this.get(m.of("Pages")),a=this.Pages().insertLeafNode(t,n);return a||i},e.prototype.removeLeafNode=function(t){this.Pages().removeLeafNode(t)},e.withContextAndPages=function(t,n){var i=new Map;return i.set(m.of("Type"),m.of("Catalog")),i.set(m.of("Pages"),n),new e(i,t)},e.fromMapWithContext=function(t,n){return new e(t,n)},e}(ve),No=function(r){X(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.Parent=function(){return this.lookup(m.of("Parent"))},e.prototype.Kids=function(){return this.lookup(m.of("Kids"),Ae)},e.prototype.Count=function(){return this.lookup(m.of("Count"),fe)},e.prototype.pushTreeNode=function(t){var n=this.Kids();n.push(t)},e.prototype.pushLeafNode=function(t){var n=this.Kids();this.insertLeafKid(n.size(),t)},e.prototype.insertLeafNode=function(t,n){var i=this.Kids(),a=this.Count().asNumber();if(n>a)throw new Da(n,a);for(var o=n,s=0,u=i.size();s<u;s++){if(o===0){this.insertLeafKid(s,t);return}var f=i.get(s),l=this.context.lookup(f);if(l instanceof e){if(l.Count().asNumber()>o)return l.insertLeafNode(t,o)||f;o-=l.Count().asNumber()}l instanceof Ht&&(o-=1)}if(o===0){this.insertLeafKid(i.size(),t);return}throw new Aa(n,"insertLeafNode")},e.prototype.removeLeafNode=function(t,n){n===void 0&&(n=!0);var i=this.Kids(),a=this.Count().asNumber();if(t>=a)throw new Da(t,a);for(var o=t,s=0,u=i.size();s<u;s++){var f=i.get(s),l=this.context.lookup(f);if(l instanceof e)if(l.Count().asNumber()>o){l.removeLeafNode(o,n),n&&l.Kids().size()===0&&i.remove(s);return}else o-=l.Count().asNumber();if(l instanceof Ht)if(o===0){this.removeKid(s);return}else o-=1}throw new Aa(t,"removeLeafNode")},e.prototype.ascend=function(t){t(this);var n=this.Parent();n&&n.ascend(t)},e.prototype.traverse=function(t){for(var n=this.Kids(),i=0,a=n.size();i<a;i++){var o=n.get(i),s=this.context.lookup(o);s instanceof e&&s.traverse(t),t(s,o)}},e.prototype.insertLeafKid=function(t,n){var i=this.Kids();this.ascend(function(a){var o=a.Count().asNumber()+1;a.set(m.of("Count"),fe.of(o))}),i.insert(t,n)},e.prototype.removeKid=function(t){var n=this.Kids(),i=n.lookup(t);i instanceof Ht&&this.ascend(function(a){var o=a.Count().asNumber()-1;a.set(m.of("Count"),fe.of(o))}),n.remove(t)},e.withContext=function(t,n){var i=new Map;return i.set(m.of("Type"),m.of("Pages")),i.set(m.of("Kids"),t.obj([])),i.set(m.of("Count"),t.obj(0)),n&&i.set(m.of("Parent"),n),new e(i,t)},e.fromMapWithContext=function(t,n){return new e(t,n)},e}(ve),st=new Uint8Array(256);st[x.Zero]=1;st[x.One]=1;st[x.Two]=1;st[x.Three]=1;st[x.Four]=1;st[x.Five]=1;st[x.Six]=1;st[x.Seven]=1;st[x.Eight]=1;st[x.Nine]=1;var ii=new Uint8Array(256);ii[x.Period]=1;ii[x.Plus]=1;ii[x.Minus]=1;var Zi=new Uint8Array(256);for(var qr=0,yf=256;qr<yf;qr++)Zi[qr]=st[qr]||ii[qr]?1:0;var Wa=x.Newline,Ka=x.CarriageReturn,bf=function(){function r(e,t){t===void 0&&(t=!1),this.bytes=e,this.capNumbers=t}return r.prototype.parseRawInt=function(){for(var e="";!this.bytes.done();){var t=this.bytes.peek();if(!st[t])break;e+=Lt(this.bytes.next())}var n=Number(e);if(!e||!isFinite(n))throw new Ra(this.bytes.position(),e);return n},r.prototype.parseRawNumber=function(){for(var e="";!this.bytes.done();){var t=this.bytes.peek();if(!Zi[t]||(e+=Lt(this.bytes.next()),t===x.Period))break}for(;!this.bytes.done();){var t=this.bytes.peek();if(!st[t])break;e+=Lt(this.bytes.next())}var n=Number(e);if(!e||!isFinite(n))throw new Ra(this.bytes.position(),e);if(n>Number.MAX_SAFE_INTEGER)if(this.capNumbers){var i="Parsed number that is too large for some PDF readers: "+e+", using Number.MAX_SAFE_INTEGER instead.";return console.warn(i),Number.MAX_SAFE_INTEGER}else{var i="Parsed number that is too large for some PDF readers: "+e+", not capping.";console.warn(i)}return n},r.prototype.skipWhitespace=function(){for(;!this.bytes.done()&&Xt[this.bytes.peek()];)this.bytes.next()},r.prototype.skipLine=function(){for(;!this.bytes.done();){var e=this.bytes.peek();if(e===Wa||e===Ka)return;this.bytes.next()}},r.prototype.skipComment=function(){if(this.bytes.peek()!==x.Percent)return!1;for(;!this.bytes.done();){var e=this.bytes.peek();if(e===Wa||e===Ka)return!0;this.bytes.next()}return!0},r.prototype.skipWhitespaceAndComments=function(){for(this.skipWhitespace();this.skipComment();)this.skipWhitespace()},r.prototype.matchKeyword=function(e){for(var t=this.bytes.offset(),n=0,i=e.length;n<i;n++)if(this.bytes.done()||this.bytes.next()!==e[n])return this.bytes.moveTo(t),!1;return!0},r}(),ai=function(){function r(e){this.idx=0,this.line=0,this.column=0,this.bytes=e,this.length=this.bytes.length}return r.prototype.moveTo=function(e){this.idx=e},r.prototype.next=function(){var e=this.bytes[this.idx++];return e===x.Newline?(this.line+=1,this.column=0):this.column+=1,e},r.prototype.assertNext=function(e){if(this.peek()!==e)throw new ku(this.position(),e,this.peek());return this.next()},r.prototype.peek=function(){return this.bytes[this.idx]},r.prototype.peekAhead=function(e){return this.bytes[this.idx+e]},r.prototype.peekAt=function(e){return this.bytes[e]},r.prototype.done=function(){return this.idx>=this.length},r.prototype.offset=function(){return this.idx},r.prototype.slice=function(e,t){return this.bytes.slice(e,t)},r.prototype.position=function(){return{line:this.line,column:this.column,offset:this.idx}},r.of=function(e){return new r(e)},r.fromPDFRawStream=function(e){return r.of(Po(e).decode())},r}(),mf=x.Space,Wr=x.CarriageReturn,Kr=x.Newline,Lr=[x.s,x.t,x.r,x.e,x.a,x.m],Fn=[x.e,x.n,x.d,x.s,x.t,x.r,x.e,x.a,x.m],Me={header:[x.Percent,x.P,x.D,x.F,x.Dash],eof:[x.Percent,x.Percent,x.E,x.O,x.F],obj:[x.o,x.b,x.j],endobj:[x.e,x.n,x.d,x.o,x.b,x.j],xref:[x.x,x.r,x.e,x.f],trailer:[x.t,x.r,x.a,x.i,x.l,x.e,x.r],startxref:[x.s,x.t,x.a,x.r,x.t,x.x,x.r,x.e,x.f],true:[x.t,x.r,x.u,x.e],false:[x.f,x.a,x.l,x.s,x.e],null:[x.n,x.u,x.l,x.l],stream:Lr,streamEOF1:ke(Lr,[mf,Wr,Kr]),streamEOF2:ke(Lr,[Wr,Kr]),streamEOF3:ke(Lr,[Wr]),streamEOF4:ke(Lr,[Kr]),endstream:Fn,EOF1endstream:ke([Wr,Kr],Fn),EOF2endstream:ke([Wr],Fn),EOF3endstream:ke([Kr],Fn)},zo=function(r){X(e,r);function e(t,n,i){i===void 0&&(i=!1);var a=r.call(this,t,i)||this;return a.context=n,a}return e.prototype.parseObject=function(){if(this.skipWhitespaceAndComments(),this.matchKeyword(Me.true))return nn.True;if(this.matchKeyword(Me.false))return nn.False;if(this.matchKeyword(Me.null))return ht;var t=this.bytes.peek();if(t===x.LessThan&&this.bytes.peekAhead(1)===x.LessThan)return this.parseDictOrStream();if(t===x.LessThan)return this.parseHexString();if(t===x.LeftParen)return this.parseString();if(t===x.ForwardSlash)return this.parseName();if(t===x.LeftSquareBracket)return this.parseArray();if(Zi[t])return this.parseNumberOrRef();throw new Cu(this.bytes.position(),t)},e.prototype.parseNumberOrRef=function(){var t=this.parseRawNumber();this.skipWhitespaceAndComments();var n=this.bytes.offset();if(st[this.bytes.peek()]){var i=this.parseRawNumber();if(this.skipWhitespaceAndComments(),this.bytes.peek()===x.R)return this.bytes.assertNext(x.R),Ee.of(t,i)}return this.bytes.moveTo(n),fe.of(t)},e.prototype.parseHexString=function(){var t="";for(this.bytes.assertNext(x.LessThan);!this.bytes.done()&&this.bytes.peek()!==x.GreaterThan;)t+=Lt(this.bytes.next());return this.bytes.assertNext(x.GreaterThan),oe.of(t)},e.prototype.parseString=function(){for(var t=0,n=!1,i="";!this.bytes.done();){var a=this.bytes.next();if(i+=Lt(a),n||(a===x.LeftParen&&(t+=1),a===x.RightParen&&(t-=1)),a===x.BackSlash?n=!n:n&&(n=!1),t===0)return Oe.of(i.substring(1,i.length-1))}throw new Du(this.bytes.position())},e.prototype.parseName=function(){this.bytes.assertNext(x.ForwardSlash);for(var t="";!this.bytes.done();){var n=this.bytes.peek();if(Xt[n]||Ot[n])break;t+=Lt(n),this.bytes.next()}return m.of(t)},e.prototype.parseArray=function(){this.bytes.assertNext(x.LeftSquareBracket),this.skipWhitespaceAndComments();for(var t=Ae.withContext(this.context);this.bytes.peek()!==x.RightSquareBracket;){var n=this.parseObject();t.push(n),this.skipWhitespaceAndComments()}return this.bytes.assertNext(x.RightSquareBracket),t},e.prototype.parseDict=function(){this.bytes.assertNext(x.LessThan),this.bytes.assertNext(x.LessThan),this.skipWhitespaceAndComments();for(var t=new Map;!this.bytes.done()&&this.bytes.peek()!==x.GreaterThan&&this.bytes.peekAhead(1)!==x.GreaterThan;){var n=this.parseName(),i=this.parseObject();t.set(n,i),this.skipWhitespaceAndComments()}this.skipWhitespaceAndComments(),this.bytes.assertNext(x.GreaterThan),this.bytes.assertNext(x.GreaterThan);var a=t.get(m.of("Type"));return a===m.of("Catalog")?Bo.fromMapWithContext(t,this.context):a===m.of("Pages")?No.fromMapWithContext(t,this.context):a===m.of("Page")?Ht.fromMapWithContext(t,this.context):ve.fromMapWithContext(t,this.context)},e.prototype.parseDictOrStream=function(){var t=this.bytes.position(),n=this.parseDict();if(this.skipWhitespaceAndComments(),!this.matchKeyword(Me.streamEOF1)&&!this.matchKeyword(Me.streamEOF2)&&!this.matchKeyword(Me.streamEOF3)&&!this.matchKeyword(Me.streamEOF4)&&!this.matchKeyword(Me.stream))return n;var i=this.bytes.offset(),a,o=n.get(m.of("Length"));o instanceof fe?(a=i+o.asNumber(),this.bytes.moveTo(a),this.skipWhitespaceAndComments(),this.matchKeyword(Me.endstream)||(this.bytes.moveTo(i),a=this.findEndOfStreamFallback(t))):a=this.findEndOfStreamFallback(t);var s=this.bytes.slice(i,a);return an.of(n,s)},e.prototype.findEndOfStreamFallback=function(t){for(var n=1,i=this.bytes.offset();!this.bytes.done()&&(i=this.bytes.offset(),this.matchKeyword(Me.stream)?n+=1:this.matchKeyword(Me.EOF1endstream)||this.matchKeyword(Me.EOF2endstream)||this.matchKeyword(Me.EOF3endstream)||this.matchKeyword(Me.endstream)?n-=1:this.bytes.next(),n!==0););if(n!==0)throw new Pu(t);return i},e.forBytes=function(t,n,i){return new e(ai.of(t),n,i)},e.forByteStream=function(t,n,i){return i===void 0&&(i=!1),new e(t,n,i)},e}(bf),xf=function(r){X(e,r);function e(t,n){var i=r.call(this,ai.fromPDFRawStream(t),t.dict.context)||this,a=t.dict;return i.alreadyParsed=!1,i.shouldWaitForTick=n||function(){return!1},i.firstOffset=a.lookup(m.of("First"),fe).asNumber(),i.objectCount=a.lookup(m.of("N"),fe).asNumber(),i}return e.prototype.parseIntoContext=function(){return pe(this,void 0,void 0,function(){var t,n,i,a,o,s,u,f;return ge(this,function(l){switch(l.label){case 0:if(this.alreadyParsed)throw new Ii("PDFObjectStreamParser","parseIntoContext");this.alreadyParsed=!0,t=this.parseOffsetsAndObjectNumbers(),n=0,i=t.length,l.label=1;case 1:return n<i?(a=t[n],o=a.objectNumber,s=a.offset,this.bytes.moveTo(this.firstOffset+s),u=this.parseObject(),f=Ee.of(o,0),this.context.assign(f,u),this.shouldWaitForTick()?[4,Er()]:[3,3]):[3,4];case 2:l.sent(),l.label=3;case 3:return n++,[3,1];case 4:return[2]}})})},e.prototype.parseOffsetsAndObjectNumbers=function(){for(var t=[],n=0,i=this.objectCount;n<i;n++){this.skipWhitespaceAndComments();var a=this.parseRawInt();this.skipWhitespaceAndComments();var o=this.parseRawInt();t.push({objectNumber:a,offset:o})}return t},e.forStream=function(t,n){return new e(t,n)},e}(zo),wf=function(){function r(e){this.alreadyParsed=!1,this.dict=e.dict,this.bytes=ai.fromPDFRawStream(e),this.context=this.dict.context;var t=this.dict.lookup(m.of("Size"),fe),n=this.dict.lookup(m.of("Index"));if(n instanceof Ae){this.subsections=[];for(var i=0,a=n.size();i<a;i+=2){var o=n.lookup(i+0,fe).asNumber(),s=n.lookup(i+1,fe).asNumber();this.subsections.push({firstObjectNumber:o,length:s})}}else this.subsections=[{firstObjectNumber:0,length:t.asNumber()}];var u=this.dict.lookup(m.of("W"),Ae);this.byteWidths=[-1,-1,-1];for(var i=0,a=u.size();i<a;i++)this.byteWidths[i]=u.lookup(i,fe).asNumber()}return r.prototype.parseIntoContext=function(){if(this.alreadyParsed)throw new Ii("PDFXRefStreamParser","parseIntoContext");this.alreadyParsed=!0,this.context.trailerInfo={Root:this.dict.get(m.of("Root")),Encrypt:this.dict.get(m.of("Encrypt")),Info:this.dict.get(m.of("Info")),ID:this.dict.get(m.of("ID"))};var e=this.parseEntries();return e},r.prototype.parseEntries=function(){for(var e=[],t=this.byteWidths,n=t[0],i=t[1],a=t[2],o=0,s=this.subsections.length;o<s;o++)for(var u=this.subsections[o],f=u.firstObjectNumber,l=u.length,h=0;h<l;h++){for(var d=0,v=0,y=n;v<y;v++)d=d<<8|this.bytes.next();for(var w=0,v=0,y=i;v<y;v++)w=w<<8|this.bytes.next();for(var F=0,v=0,y=a;v<y;v++)F=F<<8|this.bytes.next();n===0&&(d=1);var S=f+h,R={ref:Ee.of(S,F),offset:w,deleted:d===0,inObjectStream:d===2};e.push(R)}return e},r.forStream=function(e){return new r(e)},r}(),Sf=function(r){X(e,r);function e(t,n,i,a){n===void 0&&(n=1/0),i===void 0&&(i=!1),a===void 0&&(a=!1);var o=r.call(this,ai.of(t),Pi.create(),a)||this;return o.alreadyParsed=!1,o.parsedObjects=0,o.shouldWaitForTick=function(){return o.parsedObjects+=1,o.parsedObjects%o.objectsPerTick===0},o.objectsPerTick=n,o.throwOnInvalidObject=i,o}return e.prototype.parseDocument=function(){return pe(this,void 0,void 0,function(){var t,n;return ge(this,function(i){switch(i.label){case 0:if(this.alreadyParsed)throw new Ii("PDFParser","parseDocument");this.alreadyParsed=!0,this.context.header=this.parseHeader(),i.label=1;case 1:return this.bytes.done()?[3,3]:[4,this.parseDocumentSection()];case 2:if(i.sent(),n=this.bytes.offset(),n===t)throw new Au(this.bytes.position());return t=n,[3,1];case 3:return this.maybeRecoverRoot(),this.context.lookup(Ee.of(0))&&(console.warn("Removing parsed object: 0 0 R"),this.context.delete(Ee.of(0))),[2,this.context]}})})},e.prototype.maybeRecoverRoot=function(){var t=function(l){return l instanceof ve&&l.lookup(m.of("Type"))===m.of("Catalog")},n=this.context.lookup(this.context.trailerInfo.Root);if(!t(n))for(var i=this.context.enumerateIndirectObjects(),a=0,o=i.length;a<o;a++){var s=i[a],u=s[0],f=s[1];t(f)&&(this.context.trailerInfo.Root=u)}},e.prototype.parseHeader=function(){for(;!this.bytes.done();){if(this.matchKeyword(Me.header)){var t=this.parseRawInt();this.bytes.assertNext(x.Period);var n=this.parseRawInt(),i=Qn.forVersion(t,n);return this.skipBinaryHeaderComment(),i}this.bytes.next()}throw new Ru(this.bytes.position())},e.prototype.parseIndirectObjectHeader=function(){this.skipWhitespaceAndComments();var t=this.parseRawInt();this.skipWhitespaceAndComments();var n=this.parseRawInt();if(this.skipWhitespaceAndComments(),!this.matchKeyword(Me.obj))throw new Ou(this.bytes.position(),Me.obj);return Ee.of(t,n)},e.prototype.matchIndirectObjectHeader=function(){var t=this.bytes.offset();try{return this.parseIndirectObjectHeader(),!0}catch{return this.bytes.moveTo(t),!1}},e.prototype.parseIndirectObject=function(){return pe(this,void 0,void 0,function(){var t,n;return ge(this,function(i){switch(i.label){case 0:return t=this.parseIndirectObjectHeader(),this.skipWhitespaceAndComments(),n=this.parseObject(),this.skipWhitespaceAndComments(),this.matchKeyword(Me.endobj),n instanceof an&&n.dict.lookup(m.of("Type"))===m.of("ObjStm")?[4,xf.forStream(n,this.shouldWaitForTick).parseIntoContext()]:[3,2];case 1:return i.sent(),[3,3];case 2:n instanceof an&&n.dict.lookup(m.of("Type"))===m.of("XRef")?wf.forStream(n).parseIntoContext():this.context.assign(t,n),i.label=3;case 3:return[2,t]}})})},e.prototype.tryToParseInvalidIndirectObject=function(){var t=this.bytes.position(),n="Trying to parse invalid object: "+JSON.stringify(t)+")";if(this.throwOnInvalidObject)throw new Error(n);console.warn(n);var i=this.parseIndirectObjectHeader();console.warn("Invalid object ref: "+i),this.skipWhitespaceAndComments();for(var a=this.bytes.offset(),o=!0;!this.bytes.done()&&(this.matchKeyword(Me.endobj)&&(o=!1),!!o);)this.bytes.next();if(o)throw new Tu(t);var s=this.bytes.offset()-Me.endobj.length,u=Fo.of(this.bytes.slice(a,s));return this.context.assign(i,u),i},e.prototype.parseIndirectObjects=function(){return pe(this,void 0,void 0,function(){var t;return ge(this,function(n){switch(n.label){case 0:this.skipWhitespaceAndComments(),n.label=1;case 1:if(!(!this.bytes.done()&&st[this.bytes.peek()]))return[3,8];t=this.bytes.offset(),n.label=2;case 2:return n.trys.push([2,4,,5]),[4,this.parseIndirectObject()];case 3:return n.sent(),[3,5];case 4:return n.sent(),this.bytes.moveTo(t),this.tryToParseInvalidIndirectObject(),[3,5];case 5:return this.skipWhitespaceAndComments(),this.skipJibberish(),this.shouldWaitForTick()?[4,Er()]:[3,7];case 6:n.sent(),n.label=7;case 7:return[3,1];case 8:return[2]}})})},e.prototype.maybeParseCrossRefSection=function(){if(this.skipWhitespaceAndComments(),!!this.matchKeyword(Me.xref)){this.skipWhitespaceAndComments();for(var t=-1,n=xo.createEmpty();!this.bytes.done()&&st[this.bytes.peek()];){var i=this.parseRawInt();this.skipWhitespaceAndComments();var a=this.parseRawInt();this.skipWhitespaceAndComments();var o=this.bytes.peek();if(o===x.n||o===x.f){var s=Ee.of(t,a);this.bytes.next()===x.n?n.addEntry(s,i):n.addDeletedEntry(s,i),t+=1}else t=i;this.skipWhitespaceAndComments()}return n}},e.prototype.maybeParseTrailerDict=function(){if(this.skipWhitespaceAndComments(),!!this.matchKeyword(Me.trailer)){this.skipWhitespaceAndComments();var t=this.parseDict(),n=this.context;n.trailerInfo={Root:t.get(m.of("Root"))||n.trailerInfo.Root,Encrypt:t.get(m.of("Encrypt"))||n.trailerInfo.Encrypt,Info:t.get(m.of("Info"))||n.trailerInfo.Info,ID:t.get(m.of("ID"))||n.trailerInfo.ID}}},e.prototype.maybeParseTrailer=function(){if(this.skipWhitespaceAndComments(),!!this.matchKeyword(Me.startxref)){this.skipWhitespaceAndComments();var t=this.parseRawInt();return this.skipWhitespace(),this.matchKeyword(Me.eof),this.skipWhitespaceAndComments(),this.matchKeyword(Me.eof),this.skipWhitespaceAndComments(),Wi.forLastCrossRefSectionOffset(t)}},e.prototype.parseDocumentSection=function(){return pe(this,void 0,void 0,function(){return ge(this,function(t){switch(t.label){case 0:return[4,this.parseIndirectObjects()];case 1:return t.sent(),this.maybeParseCrossRefSection(),this.maybeParseTrailerDict(),this.maybeParseTrailer(),this.skipJibberish(),[2]}})})},e.prototype.skipJibberish=function(){for(this.skipWhitespaceAndComments();!this.bytes.done();){var t=this.bytes.offset(),n=this.bytes.peek(),i=n>=x.Space&&n<=x.Tilde;if(i&&(this.matchKeyword(Me.xref)||this.matchKeyword(Me.trailer)||this.matchKeyword(Me.startxref)||this.matchIndirectObjectHeader())){this.bytes.moveTo(t);break}this.bytes.next()}},e.prototype.skipBinaryHeaderComment=function(){this.skipWhitespaceAndComments();try{var t=this.bytes.offset();this.parseIndirectObjectHeader(),this.bytes.moveTo(t)}catch{this.bytes.next(),this.skipWhitespaceAndComments()}},e.forBytesWithOptions=function(t,n,i,a){return new e(t,n,i,a)},e}(zo),Ut=function(r){return 1<<r},tn;(function(r){r[r.Invisible=Ut(0)]="Invisible",r[r.Hidden=Ut(1)]="Hidden",r[r.Print=Ut(2)]="Print",r[r.NoZoom=Ut(3)]="NoZoom",r[r.NoRotate=Ut(4)]="NoRotate",r[r.NoView=Ut(5)]="NoView",r[r.ReadOnly=Ut(6)]="ReadOnly",r[r.Locked=Ut(7)]="Locked",r[r.ToggleNoView=Ut(8)]="ToggleNoView",r[r.LockedContents=Ut(9)]="LockedContents"})(tn||(tn={}));var oi=function(r){return r instanceof m?r:m.of(r)},ce=function(r){return r instanceof fe?r:fe.of(r)},ye=function(r){return r instanceof fe?r.asNumber():r},on;(function(r){r.Degrees="degrees",r.Radians="radians"})(on||(on={}));var ue=function(r){return A(r,"degreeAngle",["number"]),{type:on.Degrees,angle:r}},jo=on.Radians,Mo=on.Degrees,Io=function(r){return r*Math.PI/180},Ff=function(r){return r*180/Math.PI},nt=function(r){return r.type===jo?r.angle:r.type===Mo?Io(r.angle):cn("Invalid rotation: "+JSON.stringify(r))},Uo=function(r){return r.type===jo?Ff(r.angle):r.type===Mo?r.angle:cn("Invalid rotation: "+JSON.stringify(r))},Zt=function(r){r===void 0&&(r=0);var e=r/90%4;return e===0?0:e===1?90:e===2?180:e===3?270:0},pr=function(r,e){e===void 0&&(e=0);var t=Zt(e);return t===90||t===270?{width:r.height,height:r.width}:{width:r.width,height:r.height}},kf=function(r,e,t){e===void 0&&(e=0),t===void 0&&(t=0);var n=r.x,i=r.y,a=r.width,o=r.height,s=Zt(t),u=e/2;return s===0?{x:n-u,y:i-u,width:a,height:o}:s===90?{x:n-o+u,y:i-u,width:o,height:a}:s===180?{x:n-a+u,y:i-o+u,width:a,height:o}:s===270?{x:n-u,y:i-a+u,width:o,height:a}:{x:n-u,y:i-u,width:a,height:o}},Vo=function(){return Fe.of(xe.ClipNonZero)},qn=Math.cos,Wn=Math.sin,Kn=Math.tan,si=function(r,e,t,n,i,a){return Fe.of(xe.ConcatTransformationMatrix,[ce(r),ce(e),ce(t),ce(n),ce(i),ce(a)])},Dt=function(r,e){return si(1,0,0,1,r,e)},sn=function(r,e){return si(r,0,0,e,0,0)},zr=function(r){return si(qn(ye(r)),Wn(ye(r)),-Wn(ye(r)),qn(ye(r)),0,0)},kn=function(r){return zr(Io(ye(r)))},Yi=function(r,e){return si(1,Kn(ye(r)),Kn(ye(e)),1,0,0)},ui=function(r,e){return Fe.of(xe.SetLineDashPattern,["["+r.map(ce).join(" ")+"]",ce(e)])},Rr;(function(r){r[r.Butt=0]="Butt",r[r.Round=1]="Round",r[r.Projecting=2]="Projecting"})(Rr||(Rr={}));var fi=function(r){return Fe.of(xe.SetLineCapStyle,[ce(r)])},La;(function(r){r[r.Miter=0]="Miter",r[r.Round=1]="Round",r[r.Bevel=2]="Bevel"})(La||(La={}));var gr=function(r){return Fe.of(xe.SetGraphicsStateParams,[oi(r)])},Qe=function(){return Fe.of(xe.PushGraphicsState)},_e=function(){return Fe.of(xe.PopGraphicsState)},hn=function(r){return Fe.of(xe.SetLineWidth,[ce(r)])},pt=function(r,e,t,n,i,a){return Fe.of(xe.AppendBezierCurve,[ce(r),ce(e),ce(t),ce(n),ce(i),ce(a)])},Cn=function(r,e,t,n){return Fe.of(xe.CurveToReplicateInitialPoint,[ce(r),ce(e),ce(t),ce(n)])},nr=function(){return Fe.of(xe.ClosePath)},Vt=function(r,e){return Fe.of(xe.MoveTo,[ce(r),ce(e)])},et=function(r,e){return Fe.of(xe.LineTo,[ce(r),ce(e)])},dn=function(){return Fe.of(xe.StrokePath)},Ji=function(){return Fe.of(xe.FillNonZero)},Qi=function(){return Fe.of(xe.FillNonZeroAndStroke)},qo=function(){return Fe.of(xe.EndPath)},Cf=function(){return Fe.of(xe.NextLine)},Wo=function(r){return Fe.of(xe.ShowText,[r])},Ko=function(){return Fe.of(xe.BeginText)},Lo=function(){return Fe.of(xe.EndText)},_i=function(r,e){return Fe.of(xe.SetFontAndSize,[oi(r),ce(e)])},Tf=function(r){return Fe.of(xe.SetTextLineHeight,[ce(r)])},Ga;(function(r){r[r.Fill=0]="Fill",r[r.Outline=1]="Outline",r[r.FillAndOutline=2]="FillAndOutline",r[r.Invisible=3]="Invisible",r[r.FillAndClip=4]="FillAndClip",r[r.OutlineAndClip=5]="OutlineAndClip",r[r.FillAndOutlineAndClip=6]="FillAndOutlineAndClip",r[r.Clip=7]="Clip"})(Ga||(Ga={}));var Pf=function(r,e,t,n,i,a){return Fe.of(xe.SetTextMatrix,[ce(r),ce(e),ce(t),ce(n),ce(i),ce(a)])},Go=function(r,e,t,n,i){return Pf(qn(ye(r)),Wn(ye(r))+Kn(ye(e)),-Wn(ye(r))+Kn(ye(t)),qn(ye(r)),n,i)},$i=function(r){return Fe.of(xe.DrawObject,[oi(r)])},Df=function(r){return Fe.of(xe.NonStrokingColorGray,[ce(r)])},Af=function(r){return Fe.of(xe.StrokingColorGray,[ce(r)])},Rf=function(r,e,t){return Fe.of(xe.NonStrokingColorRgb,[ce(r),ce(e),ce(t)])},Of=function(r,e,t){return Fe.of(xe.StrokingColorRgb,[ce(r),ce(e),ce(t)])},Ef=function(r,e,t,n){return Fe.of(xe.NonStrokingColorCmyk,[ce(r),ce(e),ce(t),ce(n)])},Bf=function(r,e,t,n){return Fe.of(xe.StrokingColorCmyk,[ce(r),ce(e),ce(t),ce(n)])},Ho=function(r){return Fe.of(xe.BeginMarkedContent,[oi(r)])},Xo=function(){return Fe.of(xe.EndMarkedContent)},ir;(function(r){r.Grayscale="Grayscale",r.RGB="RGB",r.CMYK="CMYK"})(ir||(ir={}));var Zo=function(r){return vt(r,"gray",0,1),{type:ir.Grayscale,gray:r}},Be=function(r,e,t){return vt(r,"red",0,1),vt(e,"green",0,1),vt(t,"blue",0,1),{type:ir.RGB,red:r,green:e,blue:t}},Yo=function(r,e,t,n){return vt(r,"cyan",0,1),vt(e,"magenta",0,1),vt(t,"yellow",0,1),vt(n,"key",0,1),{type:ir.CMYK,cyan:r,magenta:e,yellow:t,key:n}},ea=ir.Grayscale,ta=ir.RGB,ra=ir.CMYK,yr=function(r){return r.type===ea?Df(r.gray):r.type===ta?Rf(r.red,r.green,r.blue):r.type===ra?Ef(r.cyan,r.magenta,r.yellow,r.key):cn("Invalid color: "+JSON.stringify(r))},vn=function(r){return r.type===ea?Af(r.gray):r.type===ta?Of(r.red,r.green,r.blue):r.type===ra?Bf(r.cyan,r.magenta,r.yellow,r.key):cn("Invalid color: "+JSON.stringify(r))},ft=function(r,e){return e===void 0&&(e=1),(r==null?void 0:r.length)===1?Zo(r[0]*e):(r==null?void 0:r.length)===3?Be(r[0]*e,r[1]*e,r[2]*e):(r==null?void 0:r.length)===4?Yo(r[0]*e,r[1]*e,r[2]*e,r[3]*e):void 0},Ha=function(r){return r.type===ea?[r.gray]:r.type===ta?[r.red,r.green,r.blue]:r.type===ra?[r.cyan,r.magenta,r.yellow,r.key]:cn("Invalid color: "+JSON.stringify(r))},ne=0,ie=0,we=0,Se=0,Xr=0,Zr=0,Xa=new Map([["A",7],["a",7],["C",6],["c",6],["H",1],["h",1],["L",2],["l",2],["M",2],["m",2],["Q",4],["q",4],["S",4],["s",4],["T",2],["t",2],["V",1],["v",1],["Z",0],["z",0]]),Nf=function(r){for(var e,t=[],n=[],i="",a=!1,o=0,s=0,u=r;s<u.length;s++){var f=u[s];if(Xa.has(f))o=Xa.get(f),e&&(i.length>0&&(n[n.length]=+i),t[t.length]={cmd:e,args:n},n=[],i="",a=!1),e=f;else if([" ",","].includes(f)||f==="-"&&i.length>0&&i[i.length-1]!=="e"||f==="."&&a){if(i.length===0)continue;n.length===o?(t[t.length]={cmd:e,args:n},n=[+i],e==="M"&&(e="L"),e==="m"&&(e="l")):n[n.length]=+i,a=f===".",i=["-","."].includes(f)?f:""}else i+=f,f==="."&&(a=!0)}return i.length>0&&(n.length===o?(t[t.length]={cmd:e,args:n},n=[+i],e==="M"&&(e="L"),e==="m"&&(e="l")):n[n.length]=+i),t[t.length]={cmd:e,args:n},t},zf=function(r){ne=ie=we=Se=Xr=Zr=0;for(var e=[],t=0;t<r.length;t++){var n=r[t];if(n.cmd&&typeof Za[n.cmd]=="function"){var i=Za[n.cmd](n.args);Array.isArray(i)?e=e.concat(i):e.push(i)}}return e},Za={M:function(r){return ne=r[0],ie=r[1],we=Se=null,Xr=ne,Zr=ie,Vt(ne,ie)},m:function(r){return ne+=r[0],ie+=r[1],we=Se=null,Xr=ne,Zr=ie,Vt(ne,ie)},C:function(r){return ne=r[4],ie=r[5],we=r[2],Se=r[3],pt(r[0],r[1],r[2],r[3],r[4],r[5])},c:function(r){var e=pt(r[0]+ne,r[1]+ie,r[2]+ne,r[3]+ie,r[4]+ne,r[5]+ie);return we=ne+r[2],Se=ie+r[3],ne+=r[4],ie+=r[5],e},S:function(r){(we===null||Se===null)&&(we=ne,Se=ie);var e=pt(ne-(we-ne),ie-(Se-ie),r[0],r[1],r[2],r[3]);return we=r[0],Se=r[1],ne=r[2],ie=r[3],e},s:function(r){(we===null||Se===null)&&(we=ne,Se=ie);var e=pt(ne-(we-ne),ie-(Se-ie),ne+r[0],ie+r[1],ne+r[2],ie+r[3]);return we=ne+r[0],Se=ie+r[1],ne+=r[2],ie+=r[3],e},Q:function(r){return we=r[0],Se=r[1],ne=r[2],ie=r[3],Cn(r[0],r[1],ne,ie)},q:function(r){var e=Cn(r[0]+ne,r[1]+ie,r[2]+ne,r[3]+ie);return we=ne+r[0],Se=ie+r[1],ne+=r[2],ie+=r[3],e},T:function(r){we===null||Se===null?(we=ne,Se=ie):(we=ne-(we-ne),Se=ie-(Se-ie));var e=Cn(we,Se,r[0],r[1]);return we=ne-(we-ne),Se=ie-(Se-ie),ne=r[0],ie=r[1],e},t:function(r){we===null||Se===null?(we=ne,Se=ie):(we=ne-(we-ne),Se=ie-(Se-ie));var e=Cn(we,Se,ne+r[0],ie+r[1]);return ne+=r[0],ie+=r[1],e},A:function(r){var e=Ya(ne,ie,r);return ne=r[5],ie=r[6],e},a:function(r){r[5]+=ne,r[6]+=ie;var e=Ya(ne,ie,r);return ne=r[5],ie=r[6],e},L:function(r){return ne=r[0],ie=r[1],we=Se=null,et(ne,ie)},l:function(r){return ne+=r[0],ie+=r[1],we=Se=null,et(ne,ie)},H:function(r){return ne=r[0],we=Se=null,et(ne,ie)},h:function(r){return ne+=r[0],we=Se=null,et(ne,ie)},V:function(r){return ie=r[0],we=Se=null,et(ne,ie)},v:function(r){return ie+=r[0],we=Se=null,et(ne,ie)},Z:function(){var r=nr();return ne=Xr,ie=Zr,r},z:function(){var r=nr();return ne=Xr,ie=Zr,r}},Ya=function(r,e,t){for(var n=t[0],i=t[1],a=t[2],o=t[3],s=t[4],u=t[5],f=t[6],l=jf(u,f,n,i,o,s,a,r,e),h=[],d=0,v=l;d<v.length;d++){var y=v[d],w=Mf.apply(void 0,y);h.push(pt.apply(void 0,w))}return h},jf=function(r,e,t,n,i,a,o,s,u){var f=o*(Math.PI/180),l=Math.sin(f),h=Math.cos(f);t=Math.abs(t),n=Math.abs(n),we=h*(s-r)*.5+l*(u-e)*.5,Se=h*(u-e)*.5-l*(s-r)*.5;var d=we*we/(t*t)+Se*Se/(n*n);d>1&&(d=Math.sqrt(d),t*=d,n*=d);var v=h/t,y=l/t,w=-l/n,F=h/n,S=v*s+y*u,R=w*s+F*u,C=v*r+y*e,B=w*r+F*e,E=(C-S)*(C-S)+(B-R)*(B-R),D=1/E-.25;D<0&&(D=0);var P=Math.sqrt(D);a===i&&(P=-P);var I=.5*(S+C)-P*(B-R),V=.5*(R+B)+P*(C-S),q=Math.atan2(R-V,S-I),L=Math.atan2(B-V,C-I),Z=L-q;Z<0&&a===1?Z+=2*Math.PI:Z>0&&a===0&&(Z-=2*Math.PI);for(var j=Math.ceil(Math.abs(Z/(Math.PI*.5+.001))),Q=[],te=0;te<j;te++){var Y=q+te*Z/j,J=q+(te+1)*Z/j;Q[te]=[I,V,Y,J,t,n,l,h]}return Q},Mf=function(r,e,t,n,i,a,o,s){var u=s*i,f=-o*a,l=o*i,h=s*a,d=.5*(n-t),v=8/3*Math.sin(d*.5)*Math.sin(d*.5)/Math.sin(d),y=r+Math.cos(t)-v*Math.sin(t),w=e+Math.sin(t)+v*Math.cos(t),F=r+Math.cos(n),S=e+Math.sin(n),R=F+v*Math.sin(n),C=S-v*Math.cos(n),B=[u*y+f*w,l*y+h*w,u*R+f*C,l*R+h*C,u*F+f*S,l*F+h*S];return B},If=function(r){return zf(Nf(r))},Uf=function(r,e){for(var t=[Qe(),e.graphicsState&&gr(e.graphicsState),Ko(),yr(e.color),_i(e.font,e.size),Tf(e.lineHeight),Go(nt(e.rotate),nt(e.xSkew),nt(e.ySkew),e.x,e.y)].filter(Boolean),n=0,i=r.length;n<i;n++)t.push(Wo(r[n]),Cf());return t.push(Lo(),_e()),t},Jo=function(r,e){return[Qe(),e.graphicsState&&gr(e.graphicsState),Dt(e.x,e.y),zr(nt(e.rotate)),sn(e.width,e.height),Yi(nt(e.xSkew),nt(e.ySkew)),$i(r),_e()].filter(Boolean)},Vf=function(r,e){return[Qe(),e.graphicsState&&gr(e.graphicsState),Dt(e.x,e.y),zr(nt(e.rotate)),sn(e.xScale,e.yScale),Yi(nt(e.xSkew),nt(e.ySkew)),$i(r),_e()].filter(Boolean)},qf=function(r){var e,t;return[Qe(),r.graphicsState&&gr(r.graphicsState),r.color&&vn(r.color),hn(r.thickness),ui((e=r.dashArray)!==null&&e!==void 0?e:[],(t=r.dashPhase)!==null&&t!==void 0?t:0),Vt(r.start.x,r.start.y),r.lineCap&&fi(r.lineCap),Vt(r.start.x,r.start.y),et(r.end.x,r.end.y),dn(),_e()].filter(Boolean)},Br=function(r){var e,t;return[Qe(),r.graphicsState&&gr(r.graphicsState),r.color&&yr(r.color),r.borderColor&&vn(r.borderColor),hn(r.borderWidth),r.borderLineCap&&fi(r.borderLineCap),ui((e=r.borderDashArray)!==null&&e!==void 0?e:[],(t=r.borderDashPhase)!==null&&t!==void 0?t:0),Dt(r.x,r.y),zr(nt(r.rotate)),Yi(nt(r.xSkew),nt(r.ySkew)),Vt(0,0),et(0,r.height),et(r.width,r.height),et(r.width,0),nr(),r.color&&r.borderWidth?Qi():r.color?Ji():r.borderColor?dn():nr(),_e()].filter(Boolean)},Ln=4*((Math.sqrt(2)-1)/3),Wf=function(r){var e=ye(r.x),t=ye(r.y),n=ye(r.xScale),i=ye(r.yScale);e-=n,t-=i;var a=n*Ln,o=i*Ln,s=e+n*2,u=t+i*2,f=e+n,l=t+i;return[Qe(),Vt(e,l),pt(e,l-o,f-a,t,f,t),pt(f+a,t,s,l-o,s,l),pt(s,l+o,f+a,u,f,u),pt(f-a,u,e,l+o,e,l),_e()]},Kf=function(r){var e=ye(r.x),t=ye(r.y),n=ye(r.xScale),i=ye(r.yScale),a=-n,o=-i,s=n*Ln,u=i*Ln,f=a+n*2,l=o+i*2,h=a+n,d=o+i;return[Dt(e,t),zr(nt(r.rotate)),Vt(a,d),pt(a,d-u,h-s,o,h,o),pt(h+s,o,f,d-u,f,d),pt(f,d+u,h+s,l,h,l),pt(h-s,l,a,d+u,a,d)]},Oi=function(r){var e,t,n;return ke([Qe(),r.graphicsState&&gr(r.graphicsState),r.color&&yr(r.color),r.borderColor&&vn(r.borderColor),hn(r.borderWidth),r.borderLineCap&&fi(r.borderLineCap),ui((e=r.borderDashArray)!==null&&e!==void 0?e:[],(t=r.borderDashPhase)!==null&&t!==void 0?t:0)],r.rotate===void 0?Wf({x:r.x,y:r.y,xScale:r.xScale,yScale:r.yScale}):Kf({x:r.x,y:r.y,xScale:r.xScale,yScale:r.yScale,rotate:(n=r.rotate)!==null&&n!==void 0?n:ue(0)}),[r.color&&r.borderWidth?Qi():r.color?Ji():r.borderColor?dn():nr(),_e()]).filter(Boolean)},Lf=function(r,e){var t,n,i;return ke([Qe(),e.graphicsState&&gr(e.graphicsState),Dt(e.x,e.y),zr(nt((t=e.rotate)!==null&&t!==void 0?t:ue(0))),e.scale?sn(e.scale,-e.scale):sn(1,-1),e.color&&yr(e.color),e.borderColor&&vn(e.borderColor),e.borderWidth&&hn(e.borderWidth),e.borderLineCap&&fi(e.borderLineCap),ui((n=e.borderDashArray)!==null&&n!==void 0?n:[],(i=e.borderDashPhase)!==null&&i!==void 0?i:0)],If(r),[e.color&&e.borderWidth?Qi():e.color?Ji():e.borderColor?dn():nr(),_e()]).filter(Boolean)},Gf=function(r){var e=ye(r.size),t=-1+.75,n=-1+.51,i=1-.525,a=1-.31,o=-1+.325,s=.3995/(i-n)+n;return[Qe(),r.color&&vn(r.color),hn(r.thickness),Dt(r.x,r.y),Vt(o*e,s*e),et(t*e,n*e),et(a*e,i*e),dn(),_e()].filter(Boolean)},ur=function(r){return r.rotation===0?[Dt(0,0),kn(0)]:r.rotation===90?[Dt(r.width,0),kn(90)]:r.rotation===180?[Dt(r.width,r.height),kn(180)]:r.rotation===270?[Dt(0,r.height),kn(270)]:[]},Tn=function(r){var e=Br({x:r.x,y:r.y,width:r.width,height:r.height,borderWidth:r.borderWidth,color:r.color,borderColor:r.borderColor,rotate:ue(0),xSkew:ue(0),ySkew:ue(0)});if(!r.filled)return e;var t=ye(r.width),n=ye(r.height),i=Math.min(t,n)/2,a=Gf({x:t/2,y:n/2,size:i,thickness:r.thickness,color:r.markColor});return ke([Qe()],e,a,[_e()])},Pn=function(r){var e=ye(r.width),t=ye(r.height),n=Math.min(e,t)/2,i=Oi({x:r.x,y:r.y,xScale:n,yScale:n,color:r.color,borderColor:r.borderColor,borderWidth:r.borderWidth});if(!r.filled)return i;var a=Oi({x:r.x,y:r.y,xScale:n*.45,yScale:n*.45,color:r.dotColor,borderColor:void 0,borderWidth:0});return ke([Qe()],i,a,[_e()])},Ja=function(r){var e=ye(r.x),t=ye(r.y),n=ye(r.width),i=ye(r.height),a=Br({x:e,y:t,width:n,height:i,borderWidth:r.borderWidth,color:r.color,borderColor:r.borderColor,rotate:ue(0),xSkew:ue(0),ySkew:ue(0)}),o=na(r.textLines,{color:r.textColor,font:r.font,size:r.fontSize,rotate:ue(0),xSkew:ue(0),ySkew:ue(0)});return ke([Qe()],a,o,[_e()])},na=function(r,e){for(var t=[Ko(),yr(e.color),_i(e.font,e.size)],n=0,i=r.length;n<i;n++){var a=r[n],o=a.encoded,s=a.x,u=a.y;t.push(Go(nt(e.rotate),nt(e.xSkew),nt(e.ySkew),s,u),Wo(o))}return t.push(Lo()),t},Qo=function(r){var e=ye(r.x),t=ye(r.y),n=ye(r.width),i=ye(r.height),a=ye(r.borderWidth),o=ye(r.padding),s=e+a/2+o,u=t+a/2+o,f=n-(a/2+o)*2,l=i-(a/2+o)*2,h=[Vt(s,u),et(s,u+l),et(s+f,u+l),et(s+f,u),nr(),Vo(),qo()],d=Br({x:e,y:t,width:n,height:i,borderWidth:r.borderWidth,color:r.color,borderColor:r.borderColor,rotate:ue(0),xSkew:ue(0),ySkew:ue(0)}),v=na(r.textLines,{color:r.textColor,font:r.font,size:r.fontSize,rotate:ue(0),xSkew:ue(0),ySkew:ue(0)}),y=ke([Ho("Tx"),Qe()],v,[_e(),Xo()]);return ke([Qe()],d,h,y,[_e()])},Hf=function(r){for(var e=ye(r.x),t=ye(r.y),n=ye(r.width),i=ye(r.height),a=ye(r.lineHeight),o=ye(r.borderWidth),s=ye(r.padding),u=e+o/2+s,f=t+o/2+s,l=n-(o/2+s)*2,h=i-(o/2+s)*2,d=[Vt(u,f),et(u,f+h),et(u+l,f+h),et(u+l,f),nr(),Vo(),qo()],v=Br({x:e,y:t,width:n,height:i,borderWidth:r.borderWidth,color:r.color,borderColor:r.borderColor,rotate:ue(0),xSkew:ue(0),ySkew:ue(0)}),y=[],w=0,F=r.selectedLines.length;w<F;w++){var S=r.textLines[r.selectedLines[w]];y.push.apply(y,Br({x:S.x-s,y:S.y-(a-S.height)/2,width:n-o,height:S.height+(a-S.height)/2,borderWidth:0,color:r.selectedColor,borderColor:void 0,rotate:ue(0),xSkew:ue(0),ySkew:ue(0)}))}var R=na(r.textLines,{color:r.textColor,font:r.font,size:r.fontSize,rotate:ue(0),xSkew:ue(0),ySkew:ue(0)}),C=ke([Ho("Tx"),Qe()],R,[_e(),Xo()]);return ke([Qe()],v,y,d,C,[_e()])},Xf=function(r){X(e,r);function e(){var t=this,n="Input document to `PDFDocument.load` is encrypted. You can use `PDFDocument.load(..., { ignoreEncryption: true })` if you wish to load the document anyways.";return t=r.call(this,n)||this,t}return e}(Error),Zf=function(r){X(e,r);function e(){var t=this,n="Input to `PDFDocument.embedFont` was a custom font, but no `fontkit` instance was found. You must register a `fontkit` instance with `PDFDocument.registerFontkit(...)` before embedding custom fonts.";return t=r.call(this,n)||this,t}return e}(Error),Yf=function(r){X(e,r);function e(){var t=this,n="A `page` passed to `PDFDocument.addPage` or `PDFDocument.insertPage` was from a different (foreign) PDF document. If you want to copy pages from one PDFDocument to another, you must use `PDFDocument.copyPages(...)` to copy the pages before adding or inserting them.";return t=r.call(this,n)||this,t}return e}(Error),Jf=function(r){X(e,r);function e(){var t=this,n="PDFDocument has no pages so `PDFDocument.removePage` cannot be called";return t=r.call(this,n)||this,t}return e}(Error),Qf=function(r){X(e,r);function e(t){var n=this,i='PDFDocument has no form field with the name "'+t+'"';return n=r.call(this,i)||this,n}return e}(Error),hr=function(r){X(e,r);function e(t,n,i){var a,o,s=this,u=n==null?void 0:n.name,f=(o=(a=i==null?void 0:i.constructor)===null||a===void 0?void 0:a.name)!==null&&o!==void 0?o:i,l='Expected field "'+t+'" to be of type '+u+", "+("but it is actually of type "+f);return s=r.call(this,l)||this,s}return e}(Error);(function(r){X(e,r);function e(t){var n=this,i='Failed to select check box due to missing onValue: "'+t+'"';return n=r.call(this,i)||this,n}return e})(Error);var _o=function(r){X(e,r);function e(t){var n=this,i='A field already exists with the specified name: "'+t+'"';return n=r.call(this,i)||this,n}return e}(Error),_f=function(r){X(e,r);function e(t){var n=this,i='Field name contains invalid component: "'+t+'"';return n=r.call(this,i)||this,n}return e}(Error);(function(r){X(e,r);function e(t){var n=this,i='A non-terminal field already exists with the specified name: "'+t+'"';return n=r.call(this,i)||this,n}return e})(Error);var $f=function(r){X(e,r);function e(t){var n=this,i="Reading rich text fields is not supported: Attempted to read rich text field: "+t;return n=r.call(this,i)||this,n}return e}(Error),ec=function(r){X(e,r);function e(t,n){var i=this,a="Failed to layout combed text as lineLength="+t+" is greater than cellCount="+n;return i=r.call(this,a)||this,i}return e}(Error),tc=function(r){X(e,r);function e(t,n,i){var a=this,o="Attempted to set text with length="+t+" for TextField with maxLength="+n+" and name="+i;return a=r.call(this,o)||this,a}return e}(Error),rc=function(r){X(e,r);function e(t,n,i){var a=this,o="Attempted to set maxLength="+n+", which is less than "+t+", the length of this field's current value (name="+i+")";return a=r.call(this,o)||this,a}return e}(Error),Je;(function(r){r[r.Left=0]="Left",r[r.Center=1]="Center",r[r.Right=2]="Right"})(Je||(Je={}));var $o=4,es=500,ts=function(r,e,t,n){n===void 0&&(n=!1);for(var i=$o;i<es;){for(var a=0,o=0,s=r.length;o<s;o++){a+=1;for(var u=r[o],f=u.split(" "),l=t.width,h=0,d=f.length;h<d;h++){var v=h===d-1,y=v?f[h]:f[h]+" ",w=e.widthOfTextAtSize(y,i);l-=w,l<=0&&(a+=1,l=t.width-w)}}if(!n&&a>r.length)return i-1;var F=e.heightAtSize(i),S=F+F*.2,R=S*a;if(R>Math.abs(t.height))return i-1;i+=1}return i},nc=function(r,e,t,n){for(var i=t.width/n,a=t.height,o=$o,s=ds(r);o<es;){for(var u=0,f=s.length;u<f;u++){var l=s[u],h=e.widthOfTextAtSize(l,o)>i*.75;if(h)return o-1}var d=e.heightAtSize(o,{descender:!1});if(d>a)return o-1;o+=1}return o},ic=function(r){for(var e=r.length;e>0;e--)if(/\s/.test(r[e]))return e},ac=function(r,e,t,n){for(var i,a=r.length;a>0;){var o=r.substring(0,a),s=t.encodeText(o),u=t.widthOfTextAtSize(o,n);if(u<e){var f=r.substring(a)||void 0;return{line:o,encoded:s,width:u,remainder:f}}a=(i=ic(o))!==null&&i!==void 0?i:0}return{line:r,encoded:t.encodeText(r),width:t.widthOfTextAtSize(r,n),remainder:void 0}},rs=function(r,e){var t=e.alignment,n=e.fontSize,i=e.font,a=e.bounds,o=_a(fn(r));(n===void 0||n===0)&&(n=ts(o,i,a,!0));for(var s=i.heightAtSize(n),u=s+s*.2,f=[],l=a.x,h=a.y,d=a.x+a.width,v=a.y+a.height,y=a.y+a.height,w=0,F=o.length;w<F;w++)for(var S=o[w];S!==void 0;){var R=ac(S,a.width,i,n),C=R.line,B=R.encoded,E=R.width,D=R.remainder,P=t===Je.Left?a.x:t===Je.Center?a.x+a.width/2-E/2:t===Je.Right?a.x+a.width-E:a.x;y-=u,P<l&&(l=P),y<h&&(h=y),P+E>d&&(d=P+E),y+s>v&&(v=y+s),f.push({text:C,encoded:B,width:E,height:s,x:P,y}),S=D==null?void 0:D.trim()}return{fontSize:n,lineHeight:u,lines:f,bounds:{x:l,y:h,width:d-l,height:v-h}}},oc=function(r,e){var t=e.fontSize,n=e.font,i=e.bounds,a=e.cellCount,o=$a(fn(r));if(o.length>a)throw new ec(o.length,a);(t===void 0||t===0)&&(t=nc(o,n,i,a));for(var s=i.width/a,u=n.heightAtSize(t,{descender:!1}),f=i.y+(i.height/2-u/2),l=[],h=i.x,d=i.y,v=i.x+i.width,y=i.y+i.height,w=0,F=0;w<a;){var S=eo(o,F),R=S[0],C=S[1],B=n.encodeText(R),E=n.widthOfTextAtSize(R,t),D=i.x+(s*w+s/2),P=D-E/2;P<h&&(h=P),f<d&&(d=f),P+E>v&&(v=P+E),f+u>y&&(y=f+u),l.push({text:o,encoded:B,width:E,height:u,x:P,y:f}),w+=1,F+=C}return{fontSize:t,cells:l,bounds:{x:h,y:d,width:v-h,height:y-d}}},Gn=function(r,e){var t=e.alignment,n=e.fontSize,i=e.font,a=e.bounds,o=$a(fn(r));(n===void 0||n===0)&&(n=ts([o],i,a));var s=i.encodeText(o),u=i.widthOfTextAtSize(o,n),f=i.heightAtSize(n,{descender:!1}),l=t===Je.Left?a.x:t===Je.Center?a.x+a.width/2-u/2:t===Je.Right?a.x+a.width-u:a.x,h=a.y+(a.height/2-f/2);return{fontSize:n,line:{text:o,encoded:s,width:u,height:f,x:l,y:h},bounds:{x:l,y:h,width:u,height:f}}},jr=function(r){return"normal"in r?r:{normal:r}},sc=/\/([^\0\t\n\f\r\ ]+)[\0\t\n\f\r\ ]+(\d*\.\d+|\d+)[\0\t\n\f\r\ ]+Tf/,ar=function(r){var e,t,n=(e=r.getDefaultAppearance())!==null&&e!==void 0?e:"",i=(t=zi(n,sc).match)!==null&&t!==void 0?t:[],a=Number(i[2]);return isFinite(a)?a:void 0},uc=/(\d*\.\d+|\d+)[\0\t\n\f\r\ ]*(\d*\.\d+|\d+)?[\0\t\n\f\r\ ]*(\d*\.\d+|\d+)?[\0\t\n\f\r\ ]*(\d*\.\d+|\d+)?[\0\t\n\f\r\ ]+(g|rg|k)/,At=function(r){var e,t=(e=r.getDefaultAppearance())!==null&&e!==void 0?e:"",n=zi(t,uc).match,i=n??[],a=i[1],o=i[2],s=i[3],u=i[4],f=i[5];if(f==="g"&&a)return Zo(Number(a));if(f==="rg"&&a&&o&&s)return Be(Number(a),Number(o),Number(s));if(f==="k"&&a&&o&&s&&u)return Yo(Number(a),Number(o),Number(s),Number(u))},Rt=function(r,e,t,n){var i;n===void 0&&(n=0);var a=[yr(e).toString(),_i((i=t==null?void 0:t.name)!==null&&i!==void 0?i:"dummy__noop",n).toString()].join(`
`);r.setDefaultAppearance(a)},fc=function(r,e){var t,n,i,a=At(e),o=At(r.acroField),s=e.getRectangle(),u=e.getAppearanceCharacteristics(),f=e.getBorderStyle(),l=(t=f==null?void 0:f.getWidth())!==null&&t!==void 0?t:0,h=Zt(u==null?void 0:u.getRotation()),d=pr(s,h),v=d.width,y=d.height,w=ur(he(he({},s),{rotation:h})),F=Be(0,0,0),S=(n=ft(u==null?void 0:u.getBorderColor()))!==null&&n!==void 0?n:F,R=ft(u==null?void 0:u.getBackgroundColor()),C=ft(u==null?void 0:u.getBackgroundColor(),.8),B=(i=a??o)!==null&&i!==void 0?i:F;Rt(a?e:r.acroField,B);var E={x:0+l/2,y:0+l/2,width:v-l,height:y-l,thickness:1.5,borderWidth:l,borderColor:S,markColor:B};return{normal:{on:ke(w,Tn(he(he({},E),{color:R,filled:!0}))),off:ke(w,Tn(he(he({},E),{color:R,filled:!1})))},down:{on:ke(w,Tn(he(he({},E),{color:C,filled:!0}))),off:ke(w,Tn(he(he({},E),{color:C,filled:!1})))}}},cc=function(r,e){var t,n,i,a=At(e),o=At(r.acroField),s=e.getRectangle(),u=e.getAppearanceCharacteristics(),f=e.getBorderStyle(),l=(t=f==null?void 0:f.getWidth())!==null&&t!==void 0?t:0,h=Zt(u==null?void 0:u.getRotation()),d=pr(s,h),v=d.width,y=d.height,w=ur(he(he({},s),{rotation:h})),F=Be(0,0,0),S=(n=ft(u==null?void 0:u.getBorderColor()))!==null&&n!==void 0?n:F,R=ft(u==null?void 0:u.getBackgroundColor()),C=ft(u==null?void 0:u.getBackgroundColor(),.8),B=(i=a??o)!==null&&i!==void 0?i:F;Rt(a?e:r.acroField,B);var E={x:v/2,y:y/2,width:v-l,height:y-l,borderWidth:l,borderColor:S,dotColor:B};return{normal:{on:ke(w,Pn(he(he({},E),{color:R,filled:!0}))),off:ke(w,Pn(he(he({},E),{color:R,filled:!1})))},down:{on:ke(w,Pn(he(he({},E),{color:C,filled:!0}))),off:ke(w,Pn(he(he({},E),{color:C,filled:!1})))}}},lc=function(r,e,t){var n,i,a,o,s,u=At(e),f=At(r.acroField),l=ar(e),h=ar(r.acroField),d=e.getRectangle(),v=e.getAppearanceCharacteristics(),y=e.getBorderStyle(),w=v==null?void 0:v.getCaptions(),F=(n=w==null?void 0:w.normal)!==null&&n!==void 0?n:"",S=(a=(i=w==null?void 0:w.down)!==null&&i!==void 0?i:F)!==null&&a!==void 0?a:"",R=(o=y==null?void 0:y.getWidth())!==null&&o!==void 0?o:0,C=Zt(v==null?void 0:v.getRotation()),B=pr(d,C),E=B.width,D=B.height,P=ur(he(he({},d),{rotation:C})),I=Be(0,0,0),V=ft(v==null?void 0:v.getBorderColor()),q=ft(v==null?void 0:v.getBackgroundColor()),L=ft(v==null?void 0:v.getBackgroundColor(),.8),Z={x:R,y:R,width:E-R*2,height:D-R*2},j=Gn(F,{alignment:Je.Center,fontSize:l??h,font:t,bounds:Z}),Q=Gn(S,{alignment:Je.Center,fontSize:l??h,font:t,bounds:Z}),te=Math.min(j.fontSize,Q.fontSize),Y=(s=u??f)!==null&&s!==void 0?s:I;Rt(u||l!==void 0?e:r.acroField,Y,t,te);var J={x:0+R/2,y:0+R/2,width:E-R,height:D-R,borderWidth:R,borderColor:V,textColor:Y,font:t.name,fontSize:te};return{normal:ke(P,Ja(he(he({},J),{color:q,textLines:[j.line]}))),down:ke(P,Ja(he(he({},J),{color:L,textLines:[Q.line]})))}},hc=function(r,e,t){var n,i,a,o,s=At(e),u=At(r.acroField),f=ar(e),l=ar(r.acroField),h=e.getRectangle(),d=e.getAppearanceCharacteristics(),v=e.getBorderStyle(),y=(n=r.getText())!==null&&n!==void 0?n:"",w=(i=v==null?void 0:v.getWidth())!==null&&i!==void 0?i:0,F=Zt(d==null?void 0:d.getRotation()),S=pr(h,F),R=S.width,C=S.height,B=ur(he(he({},h),{rotation:F})),E=Be(0,0,0),D=ft(d==null?void 0:d.getBorderColor()),P=ft(d==null?void 0:d.getBackgroundColor()),I,V,q=r.isCombed()?0:1,L={x:w+q,y:w+q,width:R-(w+q)*2,height:C-(w+q)*2};if(r.isMultiline()){var Z=rs(y,{alignment:r.getAlignment(),fontSize:f??l,font:t,bounds:L});I=Z.lines,V=Z.fontSize}else if(r.isCombed()){var Z=oc(y,{fontSize:f??l,font:t,bounds:L,cellCount:(a=r.getMaxLength())!==null&&a!==void 0?a:0});I=Z.cells,V=Z.fontSize}else{var Z=Gn(y,{alignment:r.getAlignment(),fontSize:f??l,font:t,bounds:L});I=[Z.line],V=Z.fontSize}var j=(o=s??u)!==null&&o!==void 0?o:E;Rt(s||f!==void 0?e:r.acroField,j,t,V);var Q={x:0+w/2,y:0+w/2,width:R-w,height:C-w,borderWidth:w??0,borderColor:D,textColor:j,font:t.name,fontSize:V,color:P,textLines:I,padding:q};return ke(B,Qo(Q))},dc=function(r,e,t){var n,i,a,o=At(e),s=At(r.acroField),u=ar(e),f=ar(r.acroField),l=e.getRectangle(),h=e.getAppearanceCharacteristics(),d=e.getBorderStyle(),v=(n=r.getSelected()[0])!==null&&n!==void 0?n:"",y=(i=d==null?void 0:d.getWidth())!==null&&i!==void 0?i:0,w=Zt(h==null?void 0:h.getRotation()),F=pr(l,w),S=F.width,R=F.height,C=ur(he(he({},l),{rotation:w})),B=Be(0,0,0),E=ft(h==null?void 0:h.getBorderColor()),D=ft(h==null?void 0:h.getBackgroundColor()),P=1,I={x:y+P,y:y+P,width:S-(y+P)*2,height:R-(y+P)*2},V=Gn(v,{alignment:Je.Left,fontSize:u??f,font:t,bounds:I}),q=V.line,L=V.fontSize,Z=(a=o??s)!==null&&a!==void 0?a:B;Rt(o||u!==void 0?e:r.acroField,Z,t,L);var j={x:0+y/2,y:0+y/2,width:S-y,height:R-y,borderWidth:y??0,borderColor:E,textColor:Z,font:t.name,fontSize:L,color:D,textLines:[q],padding:P};return ke(C,Qo(j))},vc=function(r,e,t){var n,i,a=At(e),o=At(r.acroField),s=ar(e),u=ar(r.acroField),f=e.getRectangle(),l=e.getAppearanceCharacteristics(),h=e.getBorderStyle(),d=(n=h==null?void 0:h.getWidth())!==null&&n!==void 0?n:0,v=Zt(l==null?void 0:l.getRotation()),y=pr(f,v),w=y.width,F=y.height,S=ur(he(he({},f),{rotation:v})),R=Be(0,0,0),C=ft(l==null?void 0:l.getBorderColor()),B=ft(l==null?void 0:l.getBackgroundColor()),E=r.getOptions(),D=r.getSelected();r.isSorted()&&E.sort();for(var P="",I=0,V=E.length;I<V;I++)P+=E[I],I<V-1&&(P+=`
`);for(var q=1,L={x:d+q,y:d+q,width:w-(d+q)*2,height:F-(d+q)*2},Z=rs(P,{alignment:Je.Left,fontSize:s??u,font:t,bounds:L}),j=Z.lines,Q=Z.fontSize,te=Z.lineHeight,Y=[],I=0,V=j.length;I<V;I++){var J=j[I];D.includes(J.text)&&Y.push(I)}var Te=Be(153/255,193/255,218/255),be=(i=a??o)!==null&&i!==void 0?i:R;return Rt(a||s!==void 0?e:r.acroField,be,t,Q),ke(S,Hf({x:0+d/2,y:0+d/2,width:w-d,height:F-d,borderWidth:d??0,borderColor:C,textColor:be,font:t.name,fontSize:Q,color:B,textLines:j,lineHeight:te,selectedColor:Te,selectedLines:Y,padding:q}))},ns=function(){function r(e,t,n){this.alreadyEmbedded=!1,A(e,"ref",[[Ee,"PDFRef"]]),A(t,"doc",[[vr,"PDFDocument"]]),A(n,"embedder",[[Do,"PDFPageEmbedder"]]),this.ref=e,this.doc=t,this.width=n.width,this.height=n.height,this.embedder=n}return r.prototype.scale=function(e){return A(e,"factor",["number"]),{width:this.width*e,height:this.height*e}},r.prototype.size=function(){return this.scale(1)},r.prototype.embed=function(){return pe(this,void 0,void 0,function(){return ge(this,function(e){switch(e.label){case 0:return this.alreadyEmbedded?[3,2]:[4,this.embedder.embedIntoContext(this.doc.context,this.ref)];case 1:e.sent(),this.alreadyEmbedded=!0,e.label=2;case 2:return[2]}})})},r.of=function(e,t,n){return new r(e,t,n)},r}(),yt=function(){function r(e,t,n){this.modified=!0,A(e,"ref",[[Ee,"PDFRef"]]),A(t,"doc",[[vr,"PDFDocument"]]),A(n,"embedder",[[Ki,"CustomFontEmbedder"],[Mn,"StandardFontEmbedder"]]),this.ref=e,this.doc=t,this.name=n.fontName,this.embedder=n}return r.prototype.encodeText=function(e){return A(e,"text",["string"]),this.modified=!0,this.embedder.encodeText(e)},r.prototype.widthOfTextAtSize=function(e,t){return A(e,"text",["string"]),A(t,"size",["number"]),this.embedder.widthOfTextAtSize(e,t)},r.prototype.heightAtSize=function(e,t){var n;return A(e,"size",["number"]),G(t==null?void 0:t.descender,"options.descender",["boolean"]),this.embedder.heightOfFontAtSize(e,{descender:(n=t==null?void 0:t.descender)!==null&&n!==void 0?n:!0})},r.prototype.sizeAtHeight=function(e){return A(e,"height",["number"]),this.embedder.sizeOfFontAtHeight(e)},r.prototype.getCharacterSet=function(){return this.embedder instanceof Mn?this.embedder.encoding.supportedCodePoints:this.embedder.font.characterSet},r.prototype.embed=function(){return pe(this,void 0,void 0,function(){return ge(this,function(e){switch(e.label){case 0:return this.modified?[4,this.embedder.embedIntoContext(this.doc.context,this.ref)]:[3,2];case 1:e.sent(),this.modified=!1,e.label=2;case 2:return[2]}})})},r.of=function(e,t,n){return new r(e,t,n)},r}(),Ei=function(){function r(e,t,n){A(e,"ref",[[Ee,"PDFRef"]]),A(t,"doc",[[vr,"PDFDocument"]]),A(n,"embedder",[[ko,"JpegEmbedder"],[Co,"PngEmbedder"]]),this.ref=e,this.doc=t,this.width=n.width,this.height=n.height,this.embedder=n}return r.prototype.scale=function(e){return A(e,"factor",["number"]),{width:this.width*e,height:this.height*e}},r.prototype.scaleToFit=function(e,t){A(e,"width",["number"]),A(t,"height",["number"]);var n=e/this.width,i=t/this.height,a=Math.min(n,i);return this.scale(a)},r.prototype.size=function(){return this.scale(1)},r.prototype.embed=function(){return pe(this,void 0,void 0,function(){var e,t,n;return ge(this,function(i){switch(i.label){case 0:return this.embedder?(this.embedTask||(e=this,t=e.doc,n=e.ref,this.embedTask=this.embedder.embedIntoContext(t.context,n)),[4,this.embedTask]):[2];case 1:return i.sent(),this.embedder=void 0,[2]}})})},r.of=function(e,t,n){return new r(e,t,n)},r}(),rr;(function(r){r[r.Left=0]="Left",r[r.Center=1]="Center",r[r.Right=2]="Right"})(rr||(rr={}));var Mr=function(r){G(r==null?void 0:r.x,"options.x",["number"]),G(r==null?void 0:r.y,"options.y",["number"]),G(r==null?void 0:r.width,"options.width",["number"]),G(r==null?void 0:r.height,"options.height",["number"]),G(r==null?void 0:r.textColor,"options.textColor",[[Object,"Color"]]),G(r==null?void 0:r.backgroundColor,"options.backgroundColor",[[Object,"Color"]]),G(r==null?void 0:r.borderColor,"options.borderColor",[[Object,"Color"]]),G(r==null?void 0:r.borderWidth,"options.borderWidth",["number"]),G(r==null?void 0:r.rotate,"options.rotate",[[Object,"Rotation"]])},br=function(){function r(e,t,n){A(e,"acroField",[[Nr,"PDFAcroTerminal"]]),A(t,"ref",[[Ee,"PDFRef"]]),A(n,"doc",[[vr,"PDFDocument"]]),this.acroField=e,this.ref=t,this.doc=n}return r.prototype.getName=function(){var e;return(e=this.acroField.getFullyQualifiedName())!==null&&e!==void 0?e:""},r.prototype.isReadOnly=function(){return this.acroField.hasFlag(zt.ReadOnly)},r.prototype.enableReadOnly=function(){this.acroField.setFlagTo(zt.ReadOnly,!0)},r.prototype.disableReadOnly=function(){this.acroField.setFlagTo(zt.ReadOnly,!1)},r.prototype.isRequired=function(){return this.acroField.hasFlag(zt.Required)},r.prototype.enableRequired=function(){this.acroField.setFlagTo(zt.Required,!0)},r.prototype.disableRequired=function(){this.acroField.setFlagTo(zt.Required,!1)},r.prototype.isExported=function(){return!this.acroField.hasFlag(zt.NoExport)},r.prototype.enableExporting=function(){this.acroField.setFlagTo(zt.NoExport,!1)},r.prototype.disableExporting=function(){this.acroField.setFlagTo(zt.NoExport,!0)},r.prototype.needsAppearancesUpdate=function(){throw new Pt(this.constructor.name,"needsAppearancesUpdate")},r.prototype.defaultUpdateAppearances=function(e){throw new Pt(this.constructor.name,"defaultUpdateAppearances")},r.prototype.markAsDirty=function(){this.doc.getForm().markFieldAsDirty(this.ref)},r.prototype.markAsClean=function(){this.doc.getForm().markFieldAsClean(this.ref)},r.prototype.isDirty=function(){return this.doc.getForm().fieldIsDirty(this.ref)},r.prototype.createWidget=function(e){var t,n=e.textColor,i=e.backgroundColor,a=e.borderColor,o=e.borderWidth,s=Uo(e.rotate),u=e.caption,f=e.x,l=e.y,h=e.width+o,d=e.height+o,v=!!e.hidden,y=e.page;yo(s,"degreesAngle",90);var w=Ai.create(this.doc.context,this.ref),F=kf({x:f,y:l,width:h,height:d},o,s);w.setRectangle(F),y&&w.setP(y);var S=w.getOrCreateAppearanceCharacteristics();i&&S.setBackgroundColor(Ha(i)),S.setRotation(s),u&&S.setCaptions({normal:u}),a&&S.setBorderColor(Ha(a));var R=w.getOrCreateBorderStyle();if(o!==void 0&&R.setWidth(o),w.setFlagTo(tn.Print,!0),w.setFlagTo(tn.Hidden,v),w.setFlagTo(tn.Invisible,!1),n){var C=(t=this.acroField.getDefaultAppearance())!==null&&t!==void 0?t:"",B=C+`
`+yr(n).toString();this.acroField.setDefaultAppearance(B)}return w},r.prototype.updateWidgetAppearanceWithFont=function(e,t,n){var i=n.normal,a=n.rollover,o=n.down;this.updateWidgetAppearances(e,{normal:this.createAppearanceStream(e,i,t),rollover:a&&this.createAppearanceStream(e,a,t),down:o&&this.createAppearanceStream(e,o,t)})},r.prototype.updateOnOffWidgetAppearance=function(e,t,n){var i=n.normal,a=n.rollover,o=n.down;this.updateWidgetAppearances(e,{normal:this.createAppearanceDict(e,i,t),rollover:a&&this.createAppearanceDict(e,a,t),down:o&&this.createAppearanceDict(e,o,t)})},r.prototype.updateWidgetAppearances=function(e,t){var n=t.normal,i=t.rollover,a=t.down;e.setNormalAppearance(n),i?e.setRolloverAppearance(i):e.removeRolloverAppearance(),a?e.setDownAppearance(a):e.removeDownAppearance()},r.prototype.createAppearanceStream=function(e,t,n){var i,a=this.acroField.dict.context,o=e.getRectangle(),s=o.width,u=o.height,f=n&&{Font:(i={},i[n.name]=n.ref,i)},l=a.formXObject(t,{Resources:f,BBox:a.obj([0,0,s,u]),Matrix:a.obj([1,0,0,1,0,0])}),h=a.register(l);return h},r.prototype.createImageAppearanceStream=function(e,t,n){var i,a,o=this.acroField.dict.context,s=e.getRectangle(),u=e.getAppearanceCharacteristics(),f=e.getBorderStyle(),l=(a=f==null?void 0:f.getWidth())!==null&&a!==void 0?a:0,h=Zt(u==null?void 0:u.getRotation()),d=ur(he(he({},s),{rotation:h})),v=pr(s,h),y=t.scaleToFit(v.width-l*2,v.height-l*2),w={x:l,y:l,width:y.width,height:y.height,rotate:ue(0),xSkew:ue(0),ySkew:ue(0)};n===rr.Center?(w.x+=(v.width-l*2)/2-y.width/2,w.y+=(v.height-l*2)/2-y.height/2):n===rr.Right&&(w.x=v.width-l-y.width,w.y=v.height-l-y.height);var F=this.doc.context.addRandomSuffix("Image",10),S=ke(d,Jo(F,w)),R={XObject:(i={},i[F]=t.ref,i)},C=o.formXObject(S,{Resources:R,BBox:o.obj([0,0,s.width,s.height]),Matrix:o.obj([1,0,0,1,0,0])});return o.register(C)},r.prototype.createAppearanceDict=function(e,t,n){var i=this.acroField.dict.context,a=this.createAppearanceStream(e,t.on),o=this.createAppearanceStream(e,t.off),s=i.obj({});return s.set(n,a),s.set(m.of("Off"),o),s},r}(),Yr=function(r){X(e,r);function e(t,n,i){var a=r.call(this,t,n,i)||this;return A(t,"acroCheckBox",[[_n,"PDFAcroCheckBox"]]),a.acroField=t,a}return e.prototype.check=function(){var t,n=(t=this.acroField.getOnValue())!==null&&t!==void 0?t:m.of("Yes");this.markAsDirty(),this.acroField.setValue(n)},e.prototype.uncheck=function(){this.markAsDirty(),this.acroField.setValue(m.of("Off"))},e.prototype.isChecked=function(){var t=this.acroField.getOnValue();return!!t&&t===this.acroField.getValue()},e.prototype.addToPage=function(t,n){var i,a,o,s,u,f;A(t,"page",[[Tt,"PDFPage"]]),Mr(n),n||(n={}),"textColor"in n||(n.textColor=Be(0,0,0)),"backgroundColor"in n||(n.backgroundColor=Be(1,1,1)),"borderColor"in n||(n.borderColor=Be(0,0,0)),"borderWidth"in n||(n.borderWidth=1);var l=this.createWidget({x:(i=n.x)!==null&&i!==void 0?i:0,y:(a=n.y)!==null&&a!==void 0?a:0,width:(o=n.width)!==null&&o!==void 0?o:50,height:(s=n.height)!==null&&s!==void 0?s:50,textColor:n.textColor,backgroundColor:n.backgroundColor,borderColor:n.borderColor,borderWidth:(u=n.borderWidth)!==null&&u!==void 0?u:0,rotate:(f=n.rotate)!==null&&f!==void 0?f:ue(0),hidden:n.hidden,page:t.ref}),h=this.doc.context.register(l.dict);this.acroField.addWidget(h),l.setAppearanceState(m.of("Off")),this.updateWidgetAppearance(l,m.of("Yes")),t.node.addAnnot(h)},e.prototype.needsAppearancesUpdate=function(){for(var t,n=this.acroField.getWidgets(),i=0,a=n.length;i<a;i++){var o=n[i],s=o.getAppearanceState(),u=(t=o.getAppearances())===null||t===void 0?void 0:t.normal;if(!(u instanceof ve)||s&&!u.has(s))return!0}return!1},e.prototype.defaultUpdateAppearances=function(){this.updateAppearances()},e.prototype.updateAppearances=function(t){var n;G(t,"provider",[Function]);for(var i=this.acroField.getWidgets(),a=0,o=i.length;a<o;a++){var s=i[a],u=(n=s.getOnValue())!==null&&n!==void 0?n:m.of("Yes");u&&this.updateWidgetAppearance(s,u,t)}this.markAsClean()},e.prototype.updateWidgetAppearance=function(t,n,i){var a=i??fc,o=jr(a(this,t));this.updateOnOffWidgetAppearance(t,n,o)},e.of=function(t,n,i){return new e(t,n,i)},e}(br),Rn=function(r){X(e,r);function e(t,n,i){var a=r.call(this,t,n,i)||this;return A(t,"acroComboBox",[[$n,"PDFAcroComboBox"]]),a.acroField=t,a}return e.prototype.getOptions=function(){for(var t=this.acroField.getOptions(),n=new Array(t.length),i=0,a=n.length;i<a;i++){var o=t[i],s=o.display,u=o.value;n[i]=(s??u).decodeText()}return n},e.prototype.getSelected=function(){for(var t=this.acroField.getValues(),n=new Array(t.length),i=0,a=t.length;i<a;i++)n[i]=t[i].decodeText();return n},e.prototype.setOptions=function(t){A(t,"options",[Array]);for(var n=new Array(t.length),i=0,a=t.length;i<a;i++)n[i]={value:oe.fromText(t[i])};this.acroField.setOptions(n)},e.prototype.addOptions=function(t){A(t,"options",["string",Array]);for(var n=Array.isArray(t)?t:[t],i=this.acroField.getOptions(),a=new Array(n.length),o=0,s=n.length;o<s;o++)a[o]={value:oe.fromText(n[o])};this.acroField.setOptions(i.concat(a))},e.prototype.select=function(t,n){n===void 0&&(n=!1),A(t,"options",["string",Array]),A(n,"merge",["boolean"]);var i=Array.isArray(t)?t:[t],a=this.getOptions(),o=i.find(function(h){return!a.includes(h)});o&&this.enableEditing(),this.markAsDirty(),(i.length>1||i.length===1&&n)&&this.enableMultiselect();for(var s=new Array(i.length),u=0,f=i.length;u<f;u++)s[u]=oe.fromText(i[u]);if(n){var l=this.acroField.getValues();this.acroField.setValues(l.concat(s))}else this.acroField.setValues(s)},e.prototype.clear=function(){this.markAsDirty(),this.acroField.setValues([])},e.prototype.setFontSize=function(t){Jn(t,"fontSize"),this.acroField.setFontSize(t),this.markAsDirty()},e.prototype.isEditable=function(){return this.acroField.hasFlag(Re.Edit)},e.prototype.enableEditing=function(){this.acroField.setFlagTo(Re.Edit,!0)},e.prototype.disableEditing=function(){this.acroField.setFlagTo(Re.Edit,!1)},e.prototype.isSorted=function(){return this.acroField.hasFlag(Re.Sort)},e.prototype.enableSorting=function(){this.acroField.setFlagTo(Re.Sort,!0)},e.prototype.disableSorting=function(){this.acroField.setFlagTo(Re.Sort,!1)},e.prototype.isMultiselect=function(){return this.acroField.hasFlag(Re.MultiSelect)},e.prototype.enableMultiselect=function(){this.acroField.setFlagTo(Re.MultiSelect,!0)},e.prototype.disableMultiselect=function(){this.acroField.setFlagTo(Re.MultiSelect,!1)},e.prototype.isSpellChecked=function(){return!this.acroField.hasFlag(Re.DoNotSpellCheck)},e.prototype.enableSpellChecking=function(){this.acroField.setFlagTo(Re.DoNotSpellCheck,!1)},e.prototype.disableSpellChecking=function(){this.acroField.setFlagTo(Re.DoNotSpellCheck,!0)},e.prototype.isSelectOnClick=function(){return this.acroField.hasFlag(Re.CommitOnSelChange)},e.prototype.enableSelectOnClick=function(){this.acroField.setFlagTo(Re.CommitOnSelChange,!0)},e.prototype.disableSelectOnClick=function(){this.acroField.setFlagTo(Re.CommitOnSelChange,!1)},e.prototype.addToPage=function(t,n){var i,a,o,s,u,f,l;A(t,"page",[[Tt,"PDFPage"]]),Mr(n),n||(n={}),"textColor"in n||(n.textColor=Be(0,0,0)),"backgroundColor"in n||(n.backgroundColor=Be(1,1,1)),"borderColor"in n||(n.borderColor=Be(0,0,0)),"borderWidth"in n||(n.borderWidth=1);var h=this.createWidget({x:(i=n.x)!==null&&i!==void 0?i:0,y:(a=n.y)!==null&&a!==void 0?a:0,width:(o=n.width)!==null&&o!==void 0?o:200,height:(s=n.height)!==null&&s!==void 0?s:50,textColor:n.textColor,backgroundColor:n.backgroundColor,borderColor:n.borderColor,borderWidth:(u=n.borderWidth)!==null&&u!==void 0?u:0,rotate:(f=n.rotate)!==null&&f!==void 0?f:ue(0),hidden:n.hidden,page:t.ref}),d=this.doc.context.register(h.dict);this.acroField.addWidget(d);var v=(l=n.font)!==null&&l!==void 0?l:this.doc.getForm().getDefaultFont();this.updateWidgetAppearance(h,v),t.node.addAnnot(d)},e.prototype.needsAppearancesUpdate=function(){var t;if(this.isDirty())return!0;for(var n=this.acroField.getWidgets(),i=0,a=n.length;i<a;i++){var o=n[i],s=((t=o.getAppearances())===null||t===void 0?void 0:t.normal)instanceof gt;if(!s)return!0}return!1},e.prototype.defaultUpdateAppearances=function(t){A(t,"font",[[yt,"PDFFont"]]),this.updateAppearances(t)},e.prototype.updateAppearances=function(t,n){A(t,"font",[[yt,"PDFFont"]]),G(n,"provider",[Function]);for(var i=this.acroField.getWidgets(),a=0,o=i.length;a<o;a++){var s=i[a];this.updateWidgetAppearance(s,t,n)}this.markAsClean()},e.prototype.updateWidgetAppearance=function(t,n,i){var a=i??dc,o=jr(a(this,t,n));this.updateWidgetAppearanceWithFont(t,n,o)},e.of=function(t,n,i){return new e(t,n,i)},e}(br),On=function(r){X(e,r);function e(t,n,i){var a=r.call(this,t,n,i)||this;return A(t,"acroListBox",[[ni,"PDFAcroListBox"]]),a.acroField=t,a}return e.prototype.getOptions=function(){for(var t=this.acroField.getOptions(),n=new Array(t.length),i=0,a=n.length;i<a;i++){var o=t[i],s=o.display,u=o.value;n[i]=(s??u).decodeText()}return n},e.prototype.getSelected=function(){for(var t=this.acroField.getValues(),n=new Array(t.length),i=0,a=t.length;i<a;i++)n[i]=t[i].decodeText();return n},e.prototype.setOptions=function(t){A(t,"options",[Array]),this.markAsDirty();for(var n=new Array(t.length),i=0,a=t.length;i<a;i++)n[i]={value:oe.fromText(t[i])};this.acroField.setOptions(n)},e.prototype.addOptions=function(t){A(t,"options",["string",Array]),this.markAsDirty();for(var n=Array.isArray(t)?t:[t],i=this.acroField.getOptions(),a=new Array(n.length),o=0,s=n.length;o<s;o++)a[o]={value:oe.fromText(n[o])};this.acroField.setOptions(i.concat(a))},e.prototype.select=function(t,n){n===void 0&&(n=!1),A(t,"options",["string",Array]),A(n,"merge",["boolean"]);var i=Array.isArray(t)?t:[t],a=this.getOptions();lu(i,"option",a),this.markAsDirty(),(i.length>1||i.length===1&&n)&&this.enableMultiselect();for(var o=new Array(i.length),s=0,u=i.length;s<u;s++)o[s]=oe.fromText(i[s]);if(n){var f=this.acroField.getValues();this.acroField.setValues(f.concat(o))}else this.acroField.setValues(o)},e.prototype.clear=function(){this.markAsDirty(),this.acroField.setValues([])},e.prototype.setFontSize=function(t){Jn(t,"fontSize"),this.acroField.setFontSize(t),this.markAsDirty()},e.prototype.isSorted=function(){return this.acroField.hasFlag(Re.Sort)},e.prototype.enableSorting=function(){this.acroField.setFlagTo(Re.Sort,!0)},e.prototype.disableSorting=function(){this.acroField.setFlagTo(Re.Sort,!1)},e.prototype.isMultiselect=function(){return this.acroField.hasFlag(Re.MultiSelect)},e.prototype.enableMultiselect=function(){this.acroField.setFlagTo(Re.MultiSelect,!0)},e.prototype.disableMultiselect=function(){this.acroField.setFlagTo(Re.MultiSelect,!1)},e.prototype.isSelectOnClick=function(){return this.acroField.hasFlag(Re.CommitOnSelChange)},e.prototype.enableSelectOnClick=function(){this.acroField.setFlagTo(Re.CommitOnSelChange,!0)},e.prototype.disableSelectOnClick=function(){this.acroField.setFlagTo(Re.CommitOnSelChange,!1)},e.prototype.addToPage=function(t,n){var i,a,o,s,u,f,l;A(t,"page",[[Tt,"PDFPage"]]),Mr(n),n||(n={}),"textColor"in n||(n.textColor=Be(0,0,0)),"backgroundColor"in n||(n.backgroundColor=Be(1,1,1)),"borderColor"in n||(n.borderColor=Be(0,0,0)),"borderWidth"in n||(n.borderWidth=1);var h=this.createWidget({x:(i=n.x)!==null&&i!==void 0?i:0,y:(a=n.y)!==null&&a!==void 0?a:0,width:(o=n.width)!==null&&o!==void 0?o:200,height:(s=n.height)!==null&&s!==void 0?s:100,textColor:n.textColor,backgroundColor:n.backgroundColor,borderColor:n.borderColor,borderWidth:(u=n.borderWidth)!==null&&u!==void 0?u:0,rotate:(f=n.rotate)!==null&&f!==void 0?f:ue(0),hidden:n.hidden,page:t.ref}),d=this.doc.context.register(h.dict);this.acroField.addWidget(d);var v=(l=n.font)!==null&&l!==void 0?l:this.doc.getForm().getDefaultFont();this.updateWidgetAppearance(h,v),t.node.addAnnot(d)},e.prototype.needsAppearancesUpdate=function(){var t;if(this.isDirty())return!0;for(var n=this.acroField.getWidgets(),i=0,a=n.length;i<a;i++){var o=n[i],s=((t=o.getAppearances())===null||t===void 0?void 0:t.normal)instanceof gt;if(!s)return!0}return!1},e.prototype.defaultUpdateAppearances=function(t){A(t,"font",[[yt,"PDFFont"]]),this.updateAppearances(t)},e.prototype.updateAppearances=function(t,n){A(t,"font",[[yt,"PDFFont"]]),G(n,"provider",[Function]);for(var i=this.acroField.getWidgets(),a=0,o=i.length;a<o;a++){var s=i[a];this.updateWidgetAppearance(s,t,n)}this.markAsClean()},e.prototype.updateWidgetAppearance=function(t,n,i){var a=i??vc,o=jr(a(this,t,n));this.updateWidgetAppearanceWithFont(t,n,o)},e.of=function(t,n,i){return new e(t,n,i)},e}(br),Jr=function(r){X(e,r);function e(t,n,i){var a=r.call(this,t,n,i)||this;return A(t,"acroRadioButton",[[ri,"PDFAcroRadioButton"]]),a.acroField=t,a}return e.prototype.getOptions=function(){var t=this.acroField.getExportValues();if(t){for(var n=new Array(t.length),i=0,a=t.length;i<a;i++)n[i]=t[i].decodeText();return n}for(var o=this.acroField.getOnValues(),s=new Array(o.length),i=0,a=s.length;i<a;i++)s[i]=o[i].decodeText();return s},e.prototype.getSelected=function(){var t=this.acroField.getValue();if(t!==m.of("Off")){var n=this.acroField.getExportValues();if(n){for(var i=this.acroField.getOnValues(),a=0,o=i.length;a<o;a++)if(i[a]===t)return n[a].decodeText()}return t.decodeText()}},e.prototype.select=function(t){A(t,"option",["string"]);var n=this.getOptions();er(t,"option",n),this.markAsDirty();var i=this.acroField.getOnValues(),a=this.acroField.getExportValues();if(a)for(var o=0,s=a.length;o<s;o++)a[o].decodeText()===t&&this.acroField.setValue(i[o]);else for(var o=0,s=i.length;o<s;o++){var u=i[o];u.decodeText()===t&&this.acroField.setValue(u)}},e.prototype.clear=function(){this.markAsDirty(),this.acroField.setValue(m.of("Off"))},e.prototype.isOffToggleable=function(){return!this.acroField.hasFlag(Ct.NoToggleToOff)},e.prototype.enableOffToggling=function(){this.acroField.setFlagTo(Ct.NoToggleToOff,!1)},e.prototype.disableOffToggling=function(){this.acroField.setFlagTo(Ct.NoToggleToOff,!0)},e.prototype.isMutuallyExclusive=function(){return!this.acroField.hasFlag(Ct.RadiosInUnison)},e.prototype.enableMutualExclusion=function(){this.acroField.setFlagTo(Ct.RadiosInUnison,!1)},e.prototype.disableMutualExclusion=function(){this.acroField.setFlagTo(Ct.RadiosInUnison,!0)},e.prototype.addOptionToPage=function(t,n,i){var a,o,s,u,f,l,h,d,v;A(t,"option",["string"]),A(n,"page",[[Tt,"PDFPage"]]),Mr(i);var y=this.createWidget({x:(a=i==null?void 0:i.x)!==null&&a!==void 0?a:0,y:(o=i==null?void 0:i.y)!==null&&o!==void 0?o:0,width:(s=i==null?void 0:i.width)!==null&&s!==void 0?s:50,height:(u=i==null?void 0:i.height)!==null&&u!==void 0?u:50,textColor:(f=i==null?void 0:i.textColor)!==null&&f!==void 0?f:Be(0,0,0),backgroundColor:(l=i==null?void 0:i.backgroundColor)!==null&&l!==void 0?l:Be(1,1,1),borderColor:(h=i==null?void 0:i.borderColor)!==null&&h!==void 0?h:Be(0,0,0),borderWidth:(d=i==null?void 0:i.borderWidth)!==null&&d!==void 0?d:1,rotate:(v=i==null?void 0:i.rotate)!==null&&v!==void 0?v:ue(0),hidden:i==null?void 0:i.hidden,page:n.ref}),w=this.doc.context.register(y.dict),F=this.acroField.addWidgetWithOpt(w,oe.fromText(t),!this.isMutuallyExclusive());y.setAppearanceState(m.of("Off")),this.updateWidgetAppearance(y,F),n.node.addAnnot(w)},e.prototype.needsAppearancesUpdate=function(){for(var t,n=this.acroField.getWidgets(),i=0,a=n.length;i<a;i++){var o=n[i],s=o.getAppearanceState(),u=(t=o.getAppearances())===null||t===void 0?void 0:t.normal;if(!(u instanceof ve)||s&&!u.has(s))return!0}return!1},e.prototype.defaultUpdateAppearances=function(){this.updateAppearances()},e.prototype.updateAppearances=function(t){G(t,"provider",[Function]);for(var n=this.acroField.getWidgets(),i=0,a=n.length;i<a;i++){var o=n[i],s=o.getOnValue();s&&this.updateWidgetAppearance(o,s,t)}},e.prototype.updateWidgetAppearance=function(t,n,i){var a=i??cc,o=jr(a(this,t));this.updateOnOffWidgetAppearance(t,n,o)},e.of=function(t,n,i){return new e(t,n,i)},e}(br),Bi=function(r){X(e,r);function e(t,n,i){var a=r.call(this,t,n,i)||this;return A(t,"acroSignature",[[Gi,"PDFAcroSignature"]]),a.acroField=t,a}return e.prototype.needsAppearancesUpdate=function(){return!1},e.of=function(t,n,i){return new e(t,n,i)},e}(br),En=function(r){X(e,r);function e(t,n,i){var a=r.call(this,t,n,i)||this;return A(t,"acroText",[[ei,"PDFAcroText"]]),a.acroField=t,a}return e.prototype.getText=function(){var t=this.acroField.getValue();if(!t&&this.isRichFormatted())throw new $f(this.getName());return t==null?void 0:t.decodeText()},e.prototype.setText=function(t){G(t,"text",["string"]);var n=this.getMaxLength();if(n!==void 0&&t&&t.length>n)throw new tc(t.length,n,this.getName());this.markAsDirty(),this.disableRichFormatting(),t?this.acroField.setValue(oe.fromText(t)):this.acroField.removeValue()},e.prototype.getAlignment=function(){var t=this.acroField.getQuadding();return t===0?Je.Left:t===1?Je.Center:t===2?Je.Right:Je.Left},e.prototype.setAlignment=function(t){er(t,"alignment",Je),this.markAsDirty(),this.acroField.setQuadding(t)},e.prototype.getMaxLength=function(){return this.acroField.getMaxLength()},e.prototype.setMaxLength=function(t){if(Nt(t,"maxLength",0,Number.MAX_SAFE_INTEGER),this.markAsDirty(),t===void 0)this.acroField.removeMaxLength();else{var n=this.getText();if(n&&n.length>t)throw new rc(n.length,t,this.getName());this.acroField.setMaxLength(t)}},e.prototype.removeMaxLength=function(){this.markAsDirty(),this.acroField.removeMaxLength()},e.prototype.setImage=function(t){for(var n=this.getAlignment(),i=n===Je.Center?rr.Center:n===Je.Right?rr.Right:rr.Left,a=this.acroField.getWidgets(),o=0,s=a.length;o<s;o++){var u=a[o],f=this.createImageAppearanceStream(u,t,i);this.updateWidgetAppearances(u,{normal:f})}this.markAsClean()},e.prototype.setFontSize=function(t){Jn(t,"fontSize"),this.acroField.setFontSize(t),this.markAsDirty()},e.prototype.isMultiline=function(){return this.acroField.hasFlag(Ke.Multiline)},e.prototype.enableMultiline=function(){this.markAsDirty(),this.acroField.setFlagTo(Ke.Multiline,!0)},e.prototype.disableMultiline=function(){this.markAsDirty(),this.acroField.setFlagTo(Ke.Multiline,!1)},e.prototype.isPassword=function(){return this.acroField.hasFlag(Ke.Password)},e.prototype.enablePassword=function(){this.acroField.setFlagTo(Ke.Password,!0)},e.prototype.disablePassword=function(){this.acroField.setFlagTo(Ke.Password,!1)},e.prototype.isFileSelector=function(){return this.acroField.hasFlag(Ke.FileSelect)},e.prototype.enableFileSelection=function(){this.acroField.setFlagTo(Ke.FileSelect,!0)},e.prototype.disableFileSelection=function(){this.acroField.setFlagTo(Ke.FileSelect,!1)},e.prototype.isSpellChecked=function(){return!this.acroField.hasFlag(Ke.DoNotSpellCheck)},e.prototype.enableSpellChecking=function(){this.acroField.setFlagTo(Ke.DoNotSpellCheck,!1)},e.prototype.disableSpellChecking=function(){this.acroField.setFlagTo(Ke.DoNotSpellCheck,!0)},e.prototype.isScrollable=function(){return!this.acroField.hasFlag(Ke.DoNotScroll)},e.prototype.enableScrolling=function(){this.acroField.setFlagTo(Ke.DoNotScroll,!1)},e.prototype.disableScrolling=function(){this.acroField.setFlagTo(Ke.DoNotScroll,!0)},e.prototype.isCombed=function(){return this.acroField.hasFlag(Ke.Comb)&&!this.isMultiline()&&!this.isPassword()&&!this.isFileSelector()&&this.getMaxLength()!==void 0},e.prototype.enableCombing=function(){if(this.getMaxLength()===void 0){var t="PDFTextFields must have a max length in order to be combed";console.warn(t)}this.markAsDirty(),this.disableMultiline(),this.disablePassword(),this.disableFileSelection(),this.acroField.setFlagTo(Ke.Comb,!0)},e.prototype.disableCombing=function(){this.markAsDirty(),this.acroField.setFlagTo(Ke.Comb,!1)},e.prototype.isRichFormatted=function(){return this.acroField.hasFlag(Ke.RichText)},e.prototype.enableRichFormatting=function(){this.acroField.setFlagTo(Ke.RichText,!0)},e.prototype.disableRichFormatting=function(){this.acroField.setFlagTo(Ke.RichText,!1)},e.prototype.addToPage=function(t,n){var i,a,o,s,u,f,l;A(t,"page",[[Tt,"PDFPage"]]),Mr(n),n||(n={}),"textColor"in n||(n.textColor=Be(0,0,0)),"backgroundColor"in n||(n.backgroundColor=Be(1,1,1)),"borderColor"in n||(n.borderColor=Be(0,0,0)),"borderWidth"in n||(n.borderWidth=1);var h=this.createWidget({x:(i=n.x)!==null&&i!==void 0?i:0,y:(a=n.y)!==null&&a!==void 0?a:0,width:(o=n.width)!==null&&o!==void 0?o:200,height:(s=n.height)!==null&&s!==void 0?s:50,textColor:n.textColor,backgroundColor:n.backgroundColor,borderColor:n.borderColor,borderWidth:(u=n.borderWidth)!==null&&u!==void 0?u:0,rotate:(f=n.rotate)!==null&&f!==void 0?f:ue(0),hidden:n.hidden,page:t.ref}),d=this.doc.context.register(h.dict);this.acroField.addWidget(d);var v=(l=n.font)!==null&&l!==void 0?l:this.doc.getForm().getDefaultFont();this.updateWidgetAppearance(h,v),t.node.addAnnot(d)},e.prototype.needsAppearancesUpdate=function(){var t;if(this.isDirty())return!0;for(var n=this.acroField.getWidgets(),i=0,a=n.length;i<a;i++){var o=n[i],s=((t=o.getAppearances())===null||t===void 0?void 0:t.normal)instanceof gt;if(!s)return!0}return!1},e.prototype.defaultUpdateAppearances=function(t){A(t,"font",[[yt,"PDFFont"]]),this.updateAppearances(t)},e.prototype.updateAppearances=function(t,n){A(t,"font",[[yt,"PDFFont"]]),G(n,"provider",[Function]);for(var i=this.acroField.getWidgets(),a=0,o=i.length;a<o;a++){var s=i[a];this.updateWidgetAppearance(s,t,n)}this.markAsClean()},e.prototype.updateWidgetAppearance=function(t,n,i){var a=i??hc,o=jr(a(this,t,n));this.updateWidgetAppearanceWithFont(t,n,o)},e.of=function(t,n,i){return new e(t,n,i)},e}(br),Hn;(function(r){r.Courier="Courier",r.CourierBold="Courier-Bold",r.CourierOblique="Courier-Oblique",r.CourierBoldOblique="Courier-BoldOblique",r.Helvetica="Helvetica",r.HelveticaBold="Helvetica-Bold",r.HelveticaOblique="Helvetica-Oblique",r.HelveticaBoldOblique="Helvetica-BoldOblique",r.TimesRoman="Times-Roman",r.TimesRomanBold="Times-Bold",r.TimesRomanItalic="Times-Italic",r.TimesRomanBoldItalic="Times-BoldItalic",r.Symbol="Symbol",r.ZapfDingbats="ZapfDingbats"})(Hn||(Hn={}));var pc=function(){function r(e,t){var n=this;this.embedDefaultFont=function(){return n.doc.embedStandardFont(Hn.Helvetica)},A(e,"acroForm",[[Vn,"PDFAcroForm"]]),A(t,"doc",[[vr,"PDFDocument"]]),this.acroForm=e,this.doc=t,this.dirtyFields=new Set,this.defaultFontCache=Gt.populatedBy(this.embedDefaultFont)}return r.prototype.hasXFA=function(){return this.acroForm.dict.has(m.of("XFA"))},r.prototype.deleteXFA=function(){this.acroForm.dict.delete(m.of("XFA"))},r.prototype.getFields=function(){for(var e=this.acroForm.getAllFields(),t=[],n=0,i=e.length;n<i;n++){var a=e[n],o=a[0],s=a[1],u=gc(o,s,this.doc);u&&t.push(u)}return t},r.prototype.getFieldMaybe=function(e){A(e,"name",["string"]);for(var t=this.getFields(),n=0,i=t.length;n<i;n++){var a=t[n];if(a.getName()===e)return a}},r.prototype.getField=function(e){A(e,"name",["string"]);var t=this.getFieldMaybe(e);if(t)return t;throw new Qf(e)},r.prototype.getButton=function(e){A(e,"name",["string"]);var t=this.getField(e);if(t instanceof Bn)return t;throw new hr(e,Bn,t)},r.prototype.getCheckBox=function(e){A(e,"name",["string"]);var t=this.getField(e);if(t instanceof Yr)return t;throw new hr(e,Yr,t)},r.prototype.getDropdown=function(e){A(e,"name",["string"]);var t=this.getField(e);if(t instanceof Rn)return t;throw new hr(e,Rn,t)},r.prototype.getOptionList=function(e){A(e,"name",["string"]);var t=this.getField(e);if(t instanceof On)return t;throw new hr(e,On,t)},r.prototype.getRadioGroup=function(e){A(e,"name",["string"]);var t=this.getField(e);if(t instanceof Jr)return t;throw new hr(e,Jr,t)},r.prototype.getSignature=function(e){A(e,"name",["string"]);var t=this.getField(e);if(t instanceof Bi)return t;throw new hr(e,Bi,t)},r.prototype.getTextField=function(e){A(e,"name",["string"]);var t=this.getField(e);if(t instanceof En)return t;throw new hr(e,En,t)},r.prototype.createButton=function(e){A(e,"name",["string"]);var t=Pr(e),n=this.findOrCreateNonTerminals(t.nonTerminal),i=ti.create(this.doc.context);return i.setPartialName(t.terminal),Dr(n,[i,i.ref],t.terminal),Bn.of(i,i.ref,this.doc)},r.prototype.createCheckBox=function(e){A(e,"name",["string"]);var t=Pr(e),n=this.findOrCreateNonTerminals(t.nonTerminal),i=_n.create(this.doc.context);return i.setPartialName(t.terminal),Dr(n,[i,i.ref],t.terminal),Yr.of(i,i.ref,this.doc)},r.prototype.createDropdown=function(e){A(e,"name",["string"]);var t=Pr(e),n=this.findOrCreateNonTerminals(t.nonTerminal),i=$n.create(this.doc.context);return i.setPartialName(t.terminal),Dr(n,[i,i.ref],t.terminal),Rn.of(i,i.ref,this.doc)},r.prototype.createOptionList=function(e){A(e,"name",["string"]);var t=Pr(e),n=this.findOrCreateNonTerminals(t.nonTerminal),i=ni.create(this.doc.context);return i.setPartialName(t.terminal),Dr(n,[i,i.ref],t.terminal),On.of(i,i.ref,this.doc)},r.prototype.createRadioGroup=function(e){A(e,"name",["string"]);var t=Pr(e),n=this.findOrCreateNonTerminals(t.nonTerminal),i=ri.create(this.doc.context);return i.setPartialName(t.terminal),Dr(n,[i,i.ref],t.terminal),Jr.of(i,i.ref,this.doc)},r.prototype.createTextField=function(e){A(e,"name",["string"]);var t=Pr(e),n=this.findOrCreateNonTerminals(t.nonTerminal),i=ei.create(this.doc.context);return i.setPartialName(t.terminal),Dr(n,[i,i.ref],t.terminal),En.of(i,i.ref,this.doc)},r.prototype.flatten=function(e){e===void 0&&(e={updateFieldAppearances:!0}),e.updateFieldAppearances&&this.updateFieldAppearances();for(var t=this.getFields(),n=0,i=t.length;n<i;n++){for(var a=t[n],o=a.acroField.getWidgets(),s=0,u=o.length;s<u;s++){var f=o[s],l=this.findWidgetPage(f),h=this.findWidgetAppearanceRef(a,f),d=l.node.newXObject("FlatWidget",h),v=f.getRectangle(),y=ke([Qe(),Dt(v.x,v.y)],ur(he(he({},v),{rotation:0})),[$i(d),_e()]).filter(Boolean);l.pushOperators.apply(l,y)}this.removeField(a)}},r.prototype.removeField=function(e){for(var t=e.acroField.getWidgets(),n=new Set,i=0,a=t.length;i<a;i++){var o=t[i],s=this.findWidgetAppearanceRef(e,o),u=this.findWidgetPage(o);n.add(u),u.node.removeAnnot(s)}n.forEach(function(v){return v.node.removeAnnot(e.ref)}),this.acroForm.removeField(e.acroField);for(var f=e.acroField.normalizedEntries().Kids,l=f.size(),h=0;h<l;h++){var d=f.get(h);d instanceof Ee&&this.doc.context.delete(d)}this.doc.context.delete(e.ref)},r.prototype.updateFieldAppearances=function(e){G(e,"font",[[yt,"PDFFont"]]),e=e??this.getDefaultFont();for(var t=this.getFields(),n=0,i=t.length;n<i;n++){var a=t[n];a.needsAppearancesUpdate()&&a.defaultUpdateAppearances(e)}},r.prototype.markFieldAsDirty=function(e){G(e,"fieldRef",[[Ee,"PDFRef"]]),this.dirtyFields.add(e)},r.prototype.markFieldAsClean=function(e){G(e,"fieldRef",[[Ee,"PDFRef"]]),this.dirtyFields.delete(e)},r.prototype.fieldIsDirty=function(e){return G(e,"fieldRef",[[Ee,"PDFRef"]]),this.dirtyFields.has(e)},r.prototype.getDefaultFont=function(){return this.defaultFontCache.access()},r.prototype.findWidgetPage=function(e){var t=e.P(),n=this.doc.getPages().find(function(a){return a.ref===t});if(n===void 0){var i=this.doc.context.getObjectRef(e.dict);if(i===void 0)throw new Error("Could not find PDFRef for PDFObject");if(n=this.doc.findPageForAnnotationRef(i),n===void 0)throw new Error("Could not find page for PDFRef "+i)}return n},r.prototype.findWidgetAppearanceRef=function(e,t){var n,i=t.getNormalAppearance();if(i instanceof ve&&(e instanceof Yr||e instanceof Jr)){var a=e.acroField.getValue(),o=(n=i.get(a))!==null&&n!==void 0?n:i.get(m.of("Off"));o instanceof Ee&&(i=o)}if(!(i instanceof Ee)){var s=e.getName();throw new Error("Failed to extract appearance ref for: "+s)}return i},r.prototype.findOrCreateNonTerminals=function(e){for(var t=[this.acroForm],n=0,i=e.length;n<i;n++){var a=e[n];if(!a)throw new _f(a);var o=t[0],s=t[1],u=this.findNonTerminal(a,o);if(u)t=u;else{var f=Un.create(this.doc.context);f.setPartialName(a),f.setParent(s);var l=this.doc.context.register(f.dict);o.addField(l),t=[f,l]}}return t},r.prototype.findNonTerminal=function(e,t){for(var n=t instanceof Vn?this.acroForm.getFields():Hi(t.Kids()),i=0,a=n.length;i<a;i++){var o=n[i],s=o[0],u=o[1];if(s.getPartialName()===e){if(s instanceof Un)return[s,u];throw new _o(e)}}},r.of=function(e,t){return new r(e,t)},r}(),gc=function(r,e,t){if(r instanceof ti)return Bn.of(r,e,t);if(r instanceof _n)return Yr.of(r,e,t);if(r instanceof $n)return Rn.of(r,e,t);if(r instanceof ni)return On.of(r,e,t);if(r instanceof ei)return En.of(r,e,t);if(r instanceof ri)return Jr.of(r,e,t);if(r instanceof Gi)return Bi.of(r,e,t)},Pr=function(r){if(r.length===0)throw new Error("PDF field names must not be empty strings");for(var e=r.split("."),t=0,n=e.length;t<n;t++)if(e[t]==="")throw new Error('Periods in PDF field names must be separated by at least one character: "'+r+'"');return e.length===1?{nonTerminal:[],terminal:e[0]}:{nonTerminal:e.slice(0,e.length-1),terminal:e[e.length-1]}},Dr=function(r,e,t){for(var n=r[0],i=r[1],a=e[0],o=e[1],s=n.normalizedEntries(),u=Hi("Kids"in s?s.Kids:s.Fields),f=0,l=u.length;f<l;f++)if(u[f][0].getPartialName()===t)throw new _o(t);n.addField(o),a.setParent(i)},yc={A4:[595.28,841.89]},Ni;(function(r){r[r.Fastest=1/0]="Fastest",r[r.Fast=1500]="Fast",r[r.Medium=500]="Medium",r[r.Slow=100]="Slow"})(Ni||(Ni={}));var bc=function(){function r(e,t,n){this.alreadyEmbedded=!1,this.ref=e,this.doc=t,this.embedder=n}return r.prototype.embed=function(){return pe(this,void 0,void 0,function(){var e,t,n,i,a;return ge(this,function(o){switch(o.label){case 0:return this.alreadyEmbedded?[3,2]:[4,this.embedder.embedIntoContext(this.doc.context,this.ref)];case 1:e=o.sent(),this.doc.catalog.has(m.of("Names"))||this.doc.catalog.set(m.of("Names"),this.doc.context.obj({})),t=this.doc.catalog.lookup(m.of("Names"),ve),t.has(m.of("EmbeddedFiles"))||t.set(m.of("EmbeddedFiles"),this.doc.context.obj({})),n=t.lookup(m.of("EmbeddedFiles"),ve),n.has(m.of("Names"))||n.set(m.of("Names"),this.doc.context.obj([])),i=n.lookup(m.of("Names"),Ae),i.push(oe.fromText(this.embedder.fileName)),i.push(e),this.doc.catalog.has(m.of("AF"))||this.doc.catalog.set(m.of("AF"),this.doc.context.obj([])),a=this.doc.catalog.lookup(m.of("AF"),Ae),a.push(e),this.alreadyEmbedded=!0,o.label=2;case 2:return[2]}})})},r.of=function(e,t,n){return new r(e,t,n)},r}(),mc=function(){function r(e,t,n){this.alreadyEmbedded=!1,this.ref=e,this.doc=t,this.embedder=n}return r.prototype.embed=function(){return pe(this,void 0,void 0,function(){var e,t,n,i,a,o,s;return ge(this,function(u){switch(u.label){case 0:return this.alreadyEmbedded?[3,2]:(e=this.doc,t=e.catalog,n=e.context,[4,this.embedder.embedIntoContext(this.doc.context,this.ref)]);case 1:i=u.sent(),t.has(m.of("Names"))||t.set(m.of("Names"),n.obj({})),a=t.lookup(m.of("Names"),ve),a.has(m.of("JavaScript"))||a.set(m.of("JavaScript"),n.obj({})),o=a.lookup(m.of("JavaScript"),ve),o.has(m.of("Names"))||o.set(m.of("Names"),n.obj([])),s=o.lookup(m.of("Names"),Ae),s.push(oe.fromText(this.embedder.scriptName)),s.push(i),this.alreadyEmbedded=!0,u.label=2;case 2:return[2]}})})},r.of=function(e,t,n){return new r(e,t,n)},r}(),xc=function(){function r(e,t){this.script=e,this.scriptName=t}return r.for=function(e,t){return new r(e,t)},r.prototype.embedIntoContext=function(e,t){return pe(this,void 0,void 0,function(){var n;return ge(this,function(i){return n=e.obj({Type:"Action",S:"JavaScript",JS:oe.fromText(this.script)}),t?(e.assign(t,n),[2,t]):[2,e.register(n)]})})},r}(),vr=function(){function r(e,t,n){var i=this;if(this.defaultWordBreaks=[" "],this.computePages=function(){var a=[];return i.catalog.Pages().traverse(function(o,s){if(o instanceof Ht){var u=i.pageMap.get(o);u||(u=Tt.of(o,s,i),i.pageMap.set(o,u)),a.push(u)}}),a},this.getOrCreateForm=function(){var a=i.catalog.getOrCreateAcroForm();return pc.of(a,i)},A(e,"context",[[Pi,"PDFContext"]]),A(t,"ignoreEncryption",["boolean"]),this.context=e,this.catalog=e.lookup(e.trailerInfo.Root),this.isEncrypted=!!e.lookup(e.trailerInfo.Encrypt),this.pageCache=Gt.populatedBy(this.computePages),this.pageMap=new Map,this.formCache=Gt.populatedBy(this.getOrCreateForm),this.fonts=[],this.images=[],this.embeddedPages=[],this.embeddedFiles=[],this.javaScripts=[],!t&&this.isEncrypted)throw new Xf;n&&this.updateInfoDict()}return r.load=function(e,t){return t===void 0&&(t={}),pe(this,void 0,void 0,function(){var n,i,a,o,s,u,f,l,h,d,v,y;return ge(this,function(w){switch(w.label){case 0:return n=t.ignoreEncryption,i=n===void 0?!1:n,a=t.parseSpeed,o=a===void 0?Ni.Slow:a,s=t.throwOnInvalidObject,u=s===void 0?!1:s,f=t.updateMetadata,l=f===void 0?!0:f,h=t.capNumbers,d=h===void 0?!1:h,A(e,"pdf",["string",Uint8Array,ArrayBuffer]),A(i,"ignoreEncryption",["boolean"]),A(o,"parseSpeed",["number"]),A(u,"throwOnInvalidObject",["boolean"]),v=Ir(e),[4,Sf.forBytesWithOptions(v,o,u,d).parseDocument()];case 1:return y=w.sent(),[2,new r(y,i,l)]}})})},r.create=function(e){return e===void 0&&(e={}),pe(this,void 0,void 0,function(){var t,n,i,a,o,s;return ge(this,function(u){return t=e.updateMetadata,n=t===void 0?!0:t,i=Pi.create(),a=No.withContext(i),o=i.register(a),s=Bo.withContextAndPages(i,o),i.trailerInfo.Root=i.register(s),[2,new r(i,!1,n)]})})},r.prototype.registerFontkit=function(e){this.fontkit=e},r.prototype.getForm=function(){var e=this.formCache.access();return e.hasXFA()&&(console.warn("Removing XFA form data as pdf-lib does not support reading or writing XFA"),e.deleteXFA()),e},r.prototype.getTitle=function(){var e=this.getInfoDict().lookup(m.Title);if(e)return _t(e),e.decodeText()},r.prototype.getAuthor=function(){var e=this.getInfoDict().lookup(m.Author);if(e)return _t(e),e.decodeText()},r.prototype.getSubject=function(){var e=this.getInfoDict().lookup(m.Subject);if(e)return _t(e),e.decodeText()},r.prototype.getKeywords=function(){var e=this.getInfoDict().lookup(m.Keywords);if(e)return _t(e),e.decodeText()},r.prototype.getCreator=function(){var e=this.getInfoDict().lookup(m.Creator);if(e)return _t(e),e.decodeText()},r.prototype.getProducer=function(){var e=this.getInfoDict().lookup(m.Producer);if(e)return _t(e),e.decodeText()},r.prototype.getCreationDate=function(){var e=this.getInfoDict().lookup(m.CreationDate);if(e)return _t(e),e.decodeDate()},r.prototype.getModificationDate=function(){var e=this.getInfoDict().lookup(m.ModDate);if(e)return _t(e),e.decodeDate()},r.prototype.setTitle=function(e,t){A(e,"title",["string"]);var n=m.of("Title");if(this.getInfoDict().set(n,oe.fromText(e)),t!=null&&t.showInWindowTitleBar){var i=this.catalog.getOrCreateViewerPreferences();i.setDisplayDocTitle(!0)}},r.prototype.setAuthor=function(e){A(e,"author",["string"]);var t=m.of("Author");this.getInfoDict().set(t,oe.fromText(e))},r.prototype.setSubject=function(e){A(e,"author",["string"]);var t=m.of("Subject");this.getInfoDict().set(t,oe.fromText(e))},r.prototype.setKeywords=function(e){A(e,"keywords",[Array]);var t=m.of("Keywords");this.getInfoDict().set(t,oe.fromText(e.join(" ")))},r.prototype.setCreator=function(e){A(e,"creator",["string"]);var t=m.of("Creator");this.getInfoDict().set(t,oe.fromText(e))},r.prototype.setProducer=function(e){A(e,"creator",["string"]);var t=m.of("Producer");this.getInfoDict().set(t,oe.fromText(e))},r.prototype.setLanguage=function(e){A(e,"language",["string"]);var t=m.of("Lang");this.catalog.set(t,Oe.of(e))},r.prototype.setCreationDate=function(e){A(e,"creationDate",[[Date,"Date"]]);var t=m.of("CreationDate");this.getInfoDict().set(t,Oe.fromDate(e))},r.prototype.setModificationDate=function(e){A(e,"modificationDate",[[Date,"Date"]]);var t=m.of("ModDate");this.getInfoDict().set(t,Oe.fromDate(e))},r.prototype.getPageCount=function(){return this.pageCount===void 0&&(this.pageCount=this.getPages().length),this.pageCount},r.prototype.getPages=function(){return this.pageCache.access()},r.prototype.getPage=function(e){var t=this.getPages();return vt(e,"index",0,t.length-1),t[e]},r.prototype.getPageIndices=function(){return Ss(0,this.getPageCount())},r.prototype.removePage=function(e){var t=this.getPageCount();if(this.pageCount===0)throw new Jf;vt(e,"index",0,t-1),this.catalog.removeLeafNode(e),this.pageCount=t-1},r.prototype.addPage=function(e){return A(e,"page",["undefined",[Tt,"PDFPage"],Array]),this.insertPage(this.getPageCount(),e)},r.prototype.insertPage=function(e,t){var n=this.getPageCount();if(vt(e,"index",0,n),A(t,"page",["undefined",[Tt,"PDFPage"],Array]),!t||Array.isArray(t)){var i=Array.isArray(t)?t:yc.A4;t=Tt.create(this),t.setSize.apply(t,i)}else if(t.doc!==this)throw new Yf;var a=this.catalog.insertLeafNode(t.ref,e);return t.node.setParent(a),this.pageMap.set(t.node,t),this.pageCache.invalidate(),this.pageCount=n+1,t},r.prototype.copyPages=function(e,t){return pe(this,void 0,void 0,function(){var n,i,a,o,s,u,f,l;return ge(this,function(h){switch(h.label){case 0:return A(e,"srcDoc",[[r,"PDFDocument"]]),A(t,"indices",[Array]),[4,e.flush()];case 1:for(h.sent(),n=za.for(e.context,this.context),i=e.getPages(),a=new Array(t.length),o=0,s=t.length;o<s;o++)u=i[t[o]],f=n.copy(u.node),l=this.context.register(f),a[o]=Tt.of(f,l,this);return[2,a]}})})},r.prototype.copy=function(){return pe(this,void 0,void 0,function(){var e,t,n,i;return ge(this,function(a){switch(a.label){case 0:return[4,r.create()];case 1:return e=a.sent(),[4,e.copyPages(this,this.getPageIndices())];case 2:for(t=a.sent(),n=0,i=t.length;n<i;n++)e.addPage(t[n]);return this.getAuthor()!==void 0&&e.setAuthor(this.getAuthor()),this.getCreationDate()!==void 0&&e.setCreationDate(this.getCreationDate()),this.getCreator()!==void 0&&e.setCreator(this.getCreator()),this.getModificationDate()!==void 0&&e.setModificationDate(this.getModificationDate()),this.getProducer()!==void 0&&e.setProducer(this.getProducer()),this.getSubject()!==void 0&&e.setSubject(this.getSubject()),this.getTitle()!==void 0&&e.setTitle(this.getTitle()),e.defaultWordBreaks=this.defaultWordBreaks,[2,e]}})})},r.prototype.addJavaScript=function(e,t){A(e,"name",["string"]),A(t,"script",["string"]);var n=xc.for(t,e),i=this.context.nextRef(),a=mc.of(i,this,n);this.javaScripts.push(a)},r.prototype.attach=function(e,t,n){return n===void 0&&(n={}),pe(this,void 0,void 0,function(){var i,a,o,s;return ge(this,function(u){return A(e,"attachment",["string",Uint8Array,ArrayBuffer]),A(t,"name",["string"]),G(n.mimeType,"mimeType",["string"]),G(n.description,"description",["string"]),G(n.creationDate,"options.creationDate",[Date]),G(n.modificationDate,"options.modificationDate",[Date]),kt(n.afRelationship,"options.afRelationship",Di),i=Ir(e),a=Xu.for(i,t,n),o=this.context.nextRef(),s=bc.of(o,this,a),this.embeddedFiles.push(s),[2]})})},r.prototype.embedFont=function(e,t){return t===void 0&&(t={}),pe(this,void 0,void 0,function(){var n,i,a,o,s,u,f,l,h,d;return ge(this,function(v){switch(v.label){case 0:return n=t.subset,i=n===void 0?!1:n,a=t.customName,o=t.features,A(e,"font",["string",Uint8Array,ArrayBuffer]),A(i,"subset",["boolean"]),Ta(e)?(s=Mn.for(e,a),[3,7]):[3,1];case 1:return ks(e)?(u=Ir(e),f=this.assertFontkit(),i?[4,Hu.for(f,u,a,o)]:[3,3]):[3,6];case 2:return l=v.sent(),[3,5];case 3:return[4,Ki.for(f,u,a,o)];case 4:l=v.sent(),v.label=5;case 5:return s=l,[3,7];case 6:throw new TypeError("`font` must be one of `StandardFonts | string | Uint8Array | ArrayBuffer`");case 7:return h=this.context.nextRef(),d=yt.of(h,this,s),this.fonts.push(d),[2,d]}})})},r.prototype.embedStandardFont=function(e,t){if(A(e,"font",["string"]),!Ta(e))throw new TypeError("`font` must be one of type `StandardFonts`");var n=Mn.for(e,t),i=this.context.nextRef(),a=yt.of(i,this,n);return this.fonts.push(a),a},r.prototype.embedJpg=function(e){return pe(this,void 0,void 0,function(){var t,n,i,a;return ge(this,function(o){switch(o.label){case 0:return A(e,"jpg",["string",Uint8Array,ArrayBuffer]),t=Ir(e),[4,ko.for(t)];case 1:return n=o.sent(),i=this.context.nextRef(),a=Ei.of(i,this,n),this.images.push(a),[2,a]}})})},r.prototype.embedPng=function(e){return pe(this,void 0,void 0,function(){var t,n,i,a;return ge(this,function(o){switch(o.label){case 0:return A(e,"png",["string",Uint8Array,ArrayBuffer]),t=Ir(e),[4,Co.for(t)];case 1:return n=o.sent(),i=this.context.nextRef(),a=Ei.of(i,this,n),this.images.push(a),[2,a]}})})},r.prototype.embedPdf=function(e,t){return t===void 0&&(t=[0]),pe(this,void 0,void 0,function(){var n,i,a;return ge(this,function(o){switch(o.label){case 0:return A(e,"pdf",["string",Uint8Array,ArrayBuffer,[r,"PDFDocument"]]),A(t,"indices",[Array]),e instanceof r?(i=e,[3,3]):[3,1];case 1:return[4,r.load(e)];case 2:i=o.sent(),o.label=3;case 3:return n=i,a=Fs(n.getPages(),t),[2,this.embedPages(a)]}})})},r.prototype.embedPage=function(e,t,n){return pe(this,void 0,void 0,function(){var i;return ge(this,function(a){switch(a.label){case 0:return A(e,"page",[[Tt,"PDFPage"]]),[4,this.embedPages([e],[t],[n])];case 1:return i=a.sent()[0],[2,i]}})})},r.prototype.embedPages=function(e,t,n){return t===void 0&&(t=[]),n===void 0&&(n=[]),pe(this,void 0,void 0,function(){var f,l,i,a,o,s,u,f,l,h,d,v,y,w,F;return ge(this,function(S){switch(S.label){case 0:if(e.length===0)return[2,[]];for(f=0,l=e.length-1;f<l;f++)if(i=e[f],a=e[f+1],i.node.context!==a.node.context)throw new mu;o=e[0].node.context,s=o===this.context?function(R){return R}:za.for(o,this.context).copy,u=new Array(e.length),f=0,l=e.length,S.label=1;case 1:return f<l?(h=s(e[f].node),d=t[f],v=n[f],[4,Do.for(h,d,v)]):[3,4];case 2:y=S.sent(),w=this.context.nextRef(),u[f]=ns.of(w,this,y),S.label=3;case 3:return f++,[3,1];case 4:return(F=this.embeddedPages).push.apply(F,u),[2,u]}})})},r.prototype.flush=function(){return pe(this,void 0,void 0,function(){return ge(this,function(e){switch(e.label){case 0:return[4,this.embedAll(this.fonts)];case 1:return e.sent(),[4,this.embedAll(this.images)];case 2:return e.sent(),[4,this.embedAll(this.embeddedPages)];case 3:return e.sent(),[4,this.embedAll(this.embeddedFiles)];case 4:return e.sent(),[4,this.embedAll(this.javaScripts)];case 5:return e.sent(),[2]}})})},r.prototype.save=function(e){return e===void 0&&(e={}),pe(this,void 0,void 0,function(){var t,n,i,a,o,s,u,f,l,h;return ge(this,function(d){switch(d.label){case 0:return t=e.useObjectStreams,n=t===void 0?!0:t,i=e.addDefaultPage,a=i===void 0?!0:i,o=e.objectsPerTick,s=o===void 0?50:o,u=e.updateFieldAppearances,f=u===void 0?!0:u,A(n,"useObjectStreams",["boolean"]),A(a,"addDefaultPage",["boolean"]),A(s,"objectsPerTick",["number"]),A(f,"updateFieldAppearances",["boolean"]),a&&this.getPageCount()===0&&this.addPage(),f&&(l=this.formCache.getValue(),l&&l.updateFieldAppearances()),[4,this.flush()];case 1:return d.sent(),h=n?Vu:So,[2,h.forContext(this.context,s).serializeToBuffer()]}})})},r.prototype.saveAsBase64=function(e){return e===void 0&&(e={}),pe(this,void 0,void 0,function(){var t,n,i,a,o;return ge(this,function(s){switch(s.label){case 0:return t=e.dataUri,n=t===void 0?!1:t,i=as(e,["dataUri"]),A(n,"dataUri",["boolean"]),[4,this.save(i)];case 1:return a=s.sent(),o=os(a),[2,n?"data:application/pdf;base64,"+o:o]}})})},r.prototype.findPageForAnnotationRef=function(e){for(var t=this.getPages(),n=0,i=t.length;n<i;n++){var a=t[n],o=a.node.Annots();if((o==null?void 0:o.indexOf(e))!==void 0)return a}},r.prototype.embedAll=function(e){return pe(this,void 0,void 0,function(){var t,n;return ge(this,function(i){switch(i.label){case 0:t=0,n=e.length,i.label=1;case 1:return t<n?[4,e[t].embed()]:[3,4];case 2:i.sent(),i.label=3;case 3:return t++,[3,1];case 4:return[2]}})})},r.prototype.updateInfoDict=function(){var e="pdf-lib (https://github.com/Hopding/pdf-lib)",t=new Date,n=this.getInfoDict();this.setProducer(e),this.setModificationDate(t),n.get(m.of("Creator"))||this.setCreator(e),n.get(m.of("CreationDate"))||this.setCreationDate(t)},r.prototype.getInfoDict=function(){var e=this.context.lookup(this.context.trailerInfo.Info);if(e instanceof ve)return e;var t=this.context.obj({});return this.context.trailerInfo.Info=this.context.register(t),t},r.prototype.assertFontkit=function(){if(!this.fontkit)throw new Zf;return this.fontkit},r}();function _t(r){if(!(r instanceof oe)&&!(r instanceof Oe))throw new zn([oe,Oe],r)}var Kt;(function(r){r.Normal="Normal",r.Multiply="Multiply",r.Screen="Screen",r.Overlay="Overlay",r.Darken="Darken",r.Lighten="Lighten",r.ColorDodge="ColorDodge",r.ColorBurn="ColorBurn",r.HardLight="HardLight",r.SoftLight="SoftLight",r.Difference="Difference",r.Exclusion="Exclusion"})(Kt||(Kt={}));var Tt=function(){function r(e,t,n){this.fontSize=24,this.fontColor=Be(0,0,0),this.lineHeight=24,this.x=0,this.y=0,A(e,"leafNode",[[Ht,"PDFPageLeaf"]]),A(t,"ref",[[Ee,"PDFRef"]]),A(n,"doc",[[vr,"PDFDocument"]]),this.node=e,this.ref=t,this.doc=n}return r.prototype.setRotation=function(e){var t=Uo(e);yo(t,"degreesAngle",90),this.node.set(m.of("Rotate"),this.doc.context.obj(t))},r.prototype.getRotation=function(){var e=this.node.Rotate();return ue(e?e.asNumber():0)},r.prototype.setSize=function(e,t){A(e,"width",["number"]),A(t,"height",["number"]);var n=this.getMediaBox();this.setMediaBox(n.x,n.y,e,t);var i=this.getCropBox(),a=this.getBleedBox(),o=this.getTrimBox(),s=this.getArtBox(),u=this.node.CropBox(),f=this.node.BleedBox(),l=this.node.TrimBox(),h=this.node.ArtBox();u&&xn(i,n)&&this.setCropBox(n.x,n.y,e,t),f&&xn(a,n)&&this.setBleedBox(n.x,n.y,e,t),l&&xn(o,n)&&this.setTrimBox(n.x,n.y,e,t),h&&xn(s,n)&&this.setArtBox(n.x,n.y,e,t)},r.prototype.setWidth=function(e){A(e,"width",["number"]),this.setSize(e,this.getSize().height)},r.prototype.setHeight=function(e){A(e,"height",["number"]),this.setSize(this.getSize().width,e)},r.prototype.setMediaBox=function(e,t,n,i){A(e,"x",["number"]),A(t,"y",["number"]),A(n,"width",["number"]),A(i,"height",["number"]);var a=this.doc.context.obj([e,t,e+n,t+i]);this.node.set(m.MediaBox,a)},r.prototype.setCropBox=function(e,t,n,i){A(e,"x",["number"]),A(t,"y",["number"]),A(n,"width",["number"]),A(i,"height",["number"]);var a=this.doc.context.obj([e,t,e+n,t+i]);this.node.set(m.CropBox,a)},r.prototype.setBleedBox=function(e,t,n,i){A(e,"x",["number"]),A(t,"y",["number"]),A(n,"width",["number"]),A(i,"height",["number"]);var a=this.doc.context.obj([e,t,e+n,t+i]);this.node.set(m.BleedBox,a)},r.prototype.setTrimBox=function(e,t,n,i){A(e,"x",["number"]),A(t,"y",["number"]),A(n,"width",["number"]),A(i,"height",["number"]);var a=this.doc.context.obj([e,t,e+n,t+i]);this.node.set(m.TrimBox,a)},r.prototype.setArtBox=function(e,t,n,i){A(e,"x",["number"]),A(t,"y",["number"]),A(n,"width",["number"]),A(i,"height",["number"]);var a=this.doc.context.obj([e,t,e+n,t+i]);this.node.set(m.ArtBox,a)},r.prototype.getSize=function(){var e=this.getMediaBox(),t=e.width,n=e.height;return{width:t,height:n}},r.prototype.getWidth=function(){return this.getSize().width},r.prototype.getHeight=function(){return this.getSize().height},r.prototype.getMediaBox=function(){var e=this.node.MediaBox();return e.asRectangle()},r.prototype.getCropBox=function(){var e,t=this.node.CropBox();return(e=t==null?void 0:t.asRectangle())!==null&&e!==void 0?e:this.getMediaBox()},r.prototype.getBleedBox=function(){var e,t=this.node.BleedBox();return(e=t==null?void 0:t.asRectangle())!==null&&e!==void 0?e:this.getCropBox()},r.prototype.getTrimBox=function(){var e,t=this.node.TrimBox();return(e=t==null?void 0:t.asRectangle())!==null&&e!==void 0?e:this.getCropBox()},r.prototype.getArtBox=function(){var e,t=this.node.ArtBox();return(e=t==null?void 0:t.asRectangle())!==null&&e!==void 0?e:this.getCropBox()},r.prototype.translateContent=function(e,t){A(e,"x",["number"]),A(t,"y",["number"]),this.node.normalize(),this.getContentStream();var n=this.createContentStream(Qe(),Dt(e,t)),i=this.doc.context.register(n),a=this.createContentStream(_e()),o=this.doc.context.register(a);this.node.wrapContentStreams(i,o)},r.prototype.scale=function(e,t){A(e,"x",["number"]),A(t,"y",["number"]),this.setSize(this.getWidth()*e,this.getHeight()*t),this.scaleContent(e,t),this.scaleAnnotations(e,t)},r.prototype.scaleContent=function(e,t){A(e,"x",["number"]),A(t,"y",["number"]),this.node.normalize(),this.getContentStream();var n=this.createContentStream(Qe(),sn(e,t)),i=this.doc.context.register(n),a=this.createContentStream(_e()),o=this.doc.context.register(a);this.node.wrapContentStreams(i,o)},r.prototype.scaleAnnotations=function(e,t){A(e,"x",["number"]),A(t,"y",["number"]);var n=this.node.Annots();if(n)for(var i=0;i<n.size();i++){var a=n.lookup(i);a instanceof ve&&this.scaleAnnot(a,e,t)}},r.prototype.resetPosition=function(){this.getContentStream(!1),this.x=0,this.y=0},r.prototype.setFont=function(e){A(e,"font",[[yt,"PDFFont"]]),this.font=e,this.fontKey=this.node.newFontDictionary(this.font.name,this.font.ref)},r.prototype.setFontSize=function(e){A(e,"fontSize",["number"]),this.fontSize=e},r.prototype.setFontColor=function(e){A(e,"fontColor",[[Object,"Color"]]),this.fontColor=e},r.prototype.setLineHeight=function(e){A(e,"lineHeight",["number"]),this.lineHeight=e},r.prototype.getPosition=function(){return{x:this.x,y:this.y}},r.prototype.getX=function(){return this.x},r.prototype.getY=function(){return this.y},r.prototype.moveTo=function(e,t){A(e,"x",["number"]),A(t,"y",["number"]),this.x=e,this.y=t},r.prototype.moveDown=function(e){A(e,"yDecrease",["number"]),this.y-=e},r.prototype.moveUp=function(e){A(e,"yIncrease",["number"]),this.y+=e},r.prototype.moveLeft=function(e){A(e,"xDecrease",["number"]),this.x-=e},r.prototype.moveRight=function(e){A(e,"xIncrease",["number"]),this.x+=e},r.prototype.pushOperators=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];go(e,"operator",[[Fe,"PDFOperator"]]);var n=this.getContentStream();n.push.apply(n,e)},r.prototype.drawText=function(e,t){var n,i,a,o,s,u,f;t===void 0&&(t={}),A(e,"text",["string"]),G(t.color,"options.color",[[Object,"Color"]]),Nt(t.opacity,"opacity.opacity",0,1),G(t.font,"options.font",[[yt,"PDFFont"]]),G(t.size,"options.size",["number"]),G(t.rotate,"options.rotate",[[Object,"Rotation"]]),G(t.xSkew,"options.xSkew",[[Object,"Rotation"]]),G(t.ySkew,"options.ySkew",[[Object,"Rotation"]]),G(t.x,"options.x",["number"]),G(t.y,"options.y",["number"]),G(t.lineHeight,"options.lineHeight",["number"]),G(t.maxWidth,"options.maxWidth",["number"]),G(t.wordBreaks,"options.wordBreaks",[Array]),kt(t.blendMode,"options.blendMode",Kt);for(var l=this.setOrEmbedFont(t.font),h=l.oldFont,d=l.newFont,v=l.newFontKey,y=t.size||this.fontSize,w=t.wordBreaks||this.doc.defaultWordBreaks,F=function(P){return d.widthOfTextAtSize(P,y)},S=t.maxWidth===void 0?_a(fn(e)):ps(e,w,t.maxWidth,F),R=new Array(S.length),C=0,B=S.length;C<B;C++)R[C]=d.encodeText(S[C]);var E=this.maybeEmbedGraphicsState({opacity:t.opacity,blendMode:t.blendMode}),D=this.getContentStream();D.push.apply(D,Uf(R,{color:(n=t.color)!==null&&n!==void 0?n:this.fontColor,font:v,size:y,rotate:(i=t.rotate)!==null&&i!==void 0?i:ue(0),xSkew:(a=t.xSkew)!==null&&a!==void 0?a:ue(0),ySkew:(o=t.ySkew)!==null&&o!==void 0?o:ue(0),x:(s=t.x)!==null&&s!==void 0?s:this.x,y:(u=t.y)!==null&&u!==void 0?u:this.y,lineHeight:(f=t.lineHeight)!==null&&f!==void 0?f:this.lineHeight,graphicsState:E})),t.font&&(h?this.setFont(h):this.resetFont())},r.prototype.drawImage=function(e,t){var n,i,a,o,s,u,f;t===void 0&&(t={}),A(e,"image",[[Ei,"PDFImage"]]),G(t.x,"options.x",["number"]),G(t.y,"options.y",["number"]),G(t.width,"options.width",["number"]),G(t.height,"options.height",["number"]),G(t.rotate,"options.rotate",[[Object,"Rotation"]]),G(t.xSkew,"options.xSkew",[[Object,"Rotation"]]),G(t.ySkew,"options.ySkew",[[Object,"Rotation"]]),Nt(t.opacity,"opacity.opacity",0,1),kt(t.blendMode,"options.blendMode",Kt);var l=this.node.newXObject("Image",e.ref),h=this.maybeEmbedGraphicsState({opacity:t.opacity,blendMode:t.blendMode}),d=this.getContentStream();d.push.apply(d,Jo(l,{x:(n=t.x)!==null&&n!==void 0?n:this.x,y:(i=t.y)!==null&&i!==void 0?i:this.y,width:(a=t.width)!==null&&a!==void 0?a:e.size().width,height:(o=t.height)!==null&&o!==void 0?o:e.size().height,rotate:(s=t.rotate)!==null&&s!==void 0?s:ue(0),xSkew:(u=t.xSkew)!==null&&u!==void 0?u:ue(0),ySkew:(f=t.ySkew)!==null&&f!==void 0?f:ue(0),graphicsState:h}))},r.prototype.drawPage=function(e,t){var n,i,a,o,s;t===void 0&&(t={}),A(e,"embeddedPage",[[ns,"PDFEmbeddedPage"]]),G(t.x,"options.x",["number"]),G(t.y,"options.y",["number"]),G(t.xScale,"options.xScale",["number"]),G(t.yScale,"options.yScale",["number"]),G(t.width,"options.width",["number"]),G(t.height,"options.height",["number"]),G(t.rotate,"options.rotate",[[Object,"Rotation"]]),G(t.xSkew,"options.xSkew",[[Object,"Rotation"]]),G(t.ySkew,"options.ySkew",[[Object,"Rotation"]]),Nt(t.opacity,"opacity.opacity",0,1),kt(t.blendMode,"options.blendMode",Kt);var u=this.node.newXObject("EmbeddedPdfPage",e.ref),f=this.maybeEmbedGraphicsState({opacity:t.opacity,blendMode:t.blendMode}),l=t.width!==void 0?t.width/e.width:t.xScale!==void 0?t.xScale:1,h=t.height!==void 0?t.height/e.height:t.yScale!==void 0?t.yScale:1,d=this.getContentStream();d.push.apply(d,Vf(u,{x:(n=t.x)!==null&&n!==void 0?n:this.x,y:(i=t.y)!==null&&i!==void 0?i:this.y,xScale:l,yScale:h,rotate:(a=t.rotate)!==null&&a!==void 0?a:ue(0),xSkew:(o=t.xSkew)!==null&&o!==void 0?o:ue(0),ySkew:(s=t.ySkew)!==null&&s!==void 0?s:ue(0),graphicsState:f}))},r.prototype.drawSvgPath=function(e,t){var n,i,a,o,s,u,f,l,h;t===void 0&&(t={}),A(e,"path",["string"]),G(t.x,"options.x",["number"]),G(t.y,"options.y",["number"]),G(t.scale,"options.scale",["number"]),G(t.rotate,"options.rotate",[[Object,"Rotation"]]),G(t.borderWidth,"options.borderWidth",["number"]),G(t.color,"options.color",[[Object,"Color"]]),Nt(t.opacity,"opacity.opacity",0,1),G(t.borderColor,"options.borderColor",[[Object,"Color"]]),G(t.borderDashArray,"options.borderDashArray",[Array]),G(t.borderDashPhase,"options.borderDashPhase",["number"]),kt(t.borderLineCap,"options.borderLineCap",Rr),Nt(t.borderOpacity,"options.borderOpacity",0,1),kt(t.blendMode,"options.blendMode",Kt);var d=this.maybeEmbedGraphicsState({opacity:t.opacity,borderOpacity:t.borderOpacity,blendMode:t.blendMode});!("color"in t)&&!("borderColor"in t)&&(t.borderColor=Be(0,0,0));var v=this.getContentStream();v.push.apply(v,Lf(e,{x:(n=t.x)!==null&&n!==void 0?n:this.x,y:(i=t.y)!==null&&i!==void 0?i:this.y,scale:t.scale,rotate:(a=t.rotate)!==null&&a!==void 0?a:ue(0),color:(o=t.color)!==null&&o!==void 0?o:void 0,borderColor:(s=t.borderColor)!==null&&s!==void 0?s:void 0,borderWidth:(u=t.borderWidth)!==null&&u!==void 0?u:0,borderDashArray:(f=t.borderDashArray)!==null&&f!==void 0?f:void 0,borderDashPhase:(l=t.borderDashPhase)!==null&&l!==void 0?l:void 0,borderLineCap:(h=t.borderLineCap)!==null&&h!==void 0?h:void 0,graphicsState:d}))},r.prototype.drawLine=function(e){var t,n,i,a,o;A(e.start,"options.start",[[Object,"{ x: number, y: number }"]]),A(e.end,"options.end",[[Object,"{ x: number, y: number }"]]),A(e.start.x,"options.start.x",["number"]),A(e.start.y,"options.start.y",["number"]),A(e.end.x,"options.end.x",["number"]),A(e.end.y,"options.end.y",["number"]),G(e.thickness,"options.thickness",["number"]),G(e.color,"options.color",[[Object,"Color"]]),G(e.dashArray,"options.dashArray",[Array]),G(e.dashPhase,"options.dashPhase",["number"]),kt(e.lineCap,"options.lineCap",Rr),Nt(e.opacity,"opacity.opacity",0,1),kt(e.blendMode,"options.blendMode",Kt);var s=this.maybeEmbedGraphicsState({borderOpacity:e.opacity,blendMode:e.blendMode});"color"in e||(e.color=Be(0,0,0));var u=this.getContentStream();u.push.apply(u,qf({start:e.start,end:e.end,thickness:(t=e.thickness)!==null&&t!==void 0?t:1,color:(n=e.color)!==null&&n!==void 0?n:void 0,dashArray:(i=e.dashArray)!==null&&i!==void 0?i:void 0,dashPhase:(a=e.dashPhase)!==null&&a!==void 0?a:void 0,lineCap:(o=e.lineCap)!==null&&o!==void 0?o:void 0,graphicsState:s}))},r.prototype.drawRectangle=function(e){var t,n,i,a,o,s,u,f,l,h,d,v,y;e===void 0&&(e={}),G(e.x,"options.x",["number"]),G(e.y,"options.y",["number"]),G(e.width,"options.width",["number"]),G(e.height,"options.height",["number"]),G(e.rotate,"options.rotate",[[Object,"Rotation"]]),G(e.xSkew,"options.xSkew",[[Object,"Rotation"]]),G(e.ySkew,"options.ySkew",[[Object,"Rotation"]]),G(e.borderWidth,"options.borderWidth",["number"]),G(e.color,"options.color",[[Object,"Color"]]),Nt(e.opacity,"opacity.opacity",0,1),G(e.borderColor,"options.borderColor",[[Object,"Color"]]),G(e.borderDashArray,"options.borderDashArray",[Array]),G(e.borderDashPhase,"options.borderDashPhase",["number"]),kt(e.borderLineCap,"options.borderLineCap",Rr),Nt(e.borderOpacity,"options.borderOpacity",0,1),kt(e.blendMode,"options.blendMode",Kt);var w=this.maybeEmbedGraphicsState({opacity:e.opacity,borderOpacity:e.borderOpacity,blendMode:e.blendMode});!("color"in e)&&!("borderColor"in e)&&(e.color=Be(0,0,0));var F=this.getContentStream();F.push.apply(F,Br({x:(t=e.x)!==null&&t!==void 0?t:this.x,y:(n=e.y)!==null&&n!==void 0?n:this.y,width:(i=e.width)!==null&&i!==void 0?i:150,height:(a=e.height)!==null&&a!==void 0?a:100,rotate:(o=e.rotate)!==null&&o!==void 0?o:ue(0),xSkew:(s=e.xSkew)!==null&&s!==void 0?s:ue(0),ySkew:(u=e.ySkew)!==null&&u!==void 0?u:ue(0),borderWidth:(f=e.borderWidth)!==null&&f!==void 0?f:0,color:(l=e.color)!==null&&l!==void 0?l:void 0,borderColor:(h=e.borderColor)!==null&&h!==void 0?h:void 0,borderDashArray:(d=e.borderDashArray)!==null&&d!==void 0?d:void 0,borderDashPhase:(v=e.borderDashPhase)!==null&&v!==void 0?v:void 0,graphicsState:w,borderLineCap:(y=e.borderLineCap)!==null&&y!==void 0?y:void 0}))},r.prototype.drawSquare=function(e){e===void 0&&(e={});var t=e.size;G(t,"size",["number"]),this.drawRectangle(he(he({},e),{width:t,height:t}))},r.prototype.drawEllipse=function(e){var t,n,i,a,o,s,u,f,l,h,d;e===void 0&&(e={}),G(e.x,"options.x",["number"]),G(e.y,"options.y",["number"]),G(e.xScale,"options.xScale",["number"]),G(e.yScale,"options.yScale",["number"]),G(e.rotate,"options.rotate",[[Object,"Rotation"]]),G(e.color,"options.color",[[Object,"Color"]]),Nt(e.opacity,"opacity.opacity",0,1),G(e.borderColor,"options.borderColor",[[Object,"Color"]]),Nt(e.borderOpacity,"options.borderOpacity",0,1),G(e.borderWidth,"options.borderWidth",["number"]),G(e.borderDashArray,"options.borderDashArray",[Array]),G(e.borderDashPhase,"options.borderDashPhase",["number"]),kt(e.borderLineCap,"options.borderLineCap",Rr),kt(e.blendMode,"options.blendMode",Kt);var v=this.maybeEmbedGraphicsState({opacity:e.opacity,borderOpacity:e.borderOpacity,blendMode:e.blendMode});!("color"in e)&&!("borderColor"in e)&&(e.color=Be(0,0,0));var y=this.getContentStream();y.push.apply(y,Oi({x:(t=e.x)!==null&&t!==void 0?t:this.x,y:(n=e.y)!==null&&n!==void 0?n:this.y,xScale:(i=e.xScale)!==null&&i!==void 0?i:100,yScale:(a=e.yScale)!==null&&a!==void 0?a:100,rotate:(o=e.rotate)!==null&&o!==void 0?o:void 0,color:(s=e.color)!==null&&s!==void 0?s:void 0,borderColor:(u=e.borderColor)!==null&&u!==void 0?u:void 0,borderWidth:(f=e.borderWidth)!==null&&f!==void 0?f:0,borderDashArray:(l=e.borderDashArray)!==null&&l!==void 0?l:void 0,borderDashPhase:(h=e.borderDashPhase)!==null&&h!==void 0?h:void 0,borderLineCap:(d=e.borderLineCap)!==null&&d!==void 0?d:void 0,graphicsState:v}))},r.prototype.drawCircle=function(e){e===void 0&&(e={});var t=e.size,n=t===void 0?100:t;G(n,"size",["number"]),this.drawEllipse(he(he({},e),{xScale:n,yScale:n}))},r.prototype.setOrEmbedFont=function(e){var t=this.font,n=this.fontKey;e?this.setFont(e):this.getFont();var i=this.font,a=this.fontKey;return{oldFont:t,oldFontKey:n,newFont:i,newFontKey:a}},r.prototype.getFont=function(){if(!this.font||!this.fontKey){var e=this.doc.embedStandardFont(Hn.Helvetica);this.setFont(e)}return[this.font,this.fontKey]},r.prototype.resetFont=function(){this.font=void 0,this.fontKey=void 0},r.prototype.getContentStream=function(e){return e===void 0&&(e=!0),e&&this.contentStream?this.contentStream:(this.contentStream=this.createContentStream(),this.contentStreamRef=this.doc.context.register(this.contentStream),this.node.addContentStream(this.contentStreamRef),this.contentStream)},r.prototype.createContentStream=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=this.doc.context.obj({}),i=Qr.of(n,e);return i},r.prototype.maybeEmbedGraphicsState=function(e){var t=e.opacity,n=e.borderOpacity,i=e.blendMode;if(!(t===void 0&&n===void 0&&i===void 0)){var a=this.doc.context.obj({Type:"ExtGState",ca:t,CA:n,BM:i}),o=this.node.newExtGState("GS",a);return o}},r.prototype.scaleAnnot=function(e,t,n){for(var i=["RD","CL","Vertices","QuadPoints","L","Rect"],a=0,o=i.length;a<o;a++){var s=e.lookup(m.of(i[a]));s instanceof Ae&&s.scalePDFNumbers(t,n)}var u=e.lookup(m.of("InkList"));if(u instanceof Ae)for(var a=0,o=u.size();a<o;a++){var f=u.lookup(a);f instanceof Ae&&f.scalePDFNumbers(t,n)}},r.of=function(e,t,n){return new r(e,t,n)},r.create=function(e){A(e,"doc",[[vr,"PDFDocument"]]);var t=Ee.of(-1),n=Ht.withContextAndParent(e.context,t),i=e.context.register(n);return new r(n,i,e)},r}(),Bn=function(r){X(e,r);function e(t,n,i){var a=r.call(this,t,n,i)||this;return A(t,"acroButton",[[ti,"PDFAcroPushButton"]]),a.acroField=t,a}return e.prototype.setImage=function(t,n){n===void 0&&(n=rr.Center);for(var i=this.acroField.getWidgets(),a=0,o=i.length;a<o;a++){var s=i[a],u=this.createImageAppearanceStream(s,t,n);this.updateWidgetAppearances(s,{normal:u})}this.markAsClean()},e.prototype.setFontSize=function(t){Jn(t,"fontSize"),this.acroField.setFontSize(t),this.markAsDirty()},e.prototype.addToPage=function(t,n,i){var a,o,s,u,f,l,h,d,v,y,w;G(t,"text",["string"]),G(n,"page",[[Tt,"PDFPage"]]),Mr(i);var F=this.createWidget({x:((a=i==null?void 0:i.x)!==null&&a!==void 0?a:0)-((o=i==null?void 0:i.borderWidth)!==null&&o!==void 0?o:0)/2,y:((s=i==null?void 0:i.y)!==null&&s!==void 0?s:0)-((u=i==null?void 0:i.borderWidth)!==null&&u!==void 0?u:0)/2,width:(f=i==null?void 0:i.width)!==null&&f!==void 0?f:100,height:(l=i==null?void 0:i.height)!==null&&l!==void 0?l:50,textColor:(h=i==null?void 0:i.textColor)!==null&&h!==void 0?h:Be(0,0,0),backgroundColor:(d=i==null?void 0:i.backgroundColor)!==null&&d!==void 0?d:Be(.75,.75,.75),borderColor:i==null?void 0:i.borderColor,borderWidth:(v=i==null?void 0:i.borderWidth)!==null&&v!==void 0?v:0,rotate:(y=i==null?void 0:i.rotate)!==null&&y!==void 0?y:ue(0),caption:t,hidden:i==null?void 0:i.hidden,page:n.ref}),S=this.doc.context.register(F.dict);this.acroField.addWidget(S);var R=(w=i==null?void 0:i.font)!==null&&w!==void 0?w:this.doc.getForm().getDefaultFont();this.updateWidgetAppearance(F,R),n.node.addAnnot(S)},e.prototype.needsAppearancesUpdate=function(){var t;if(this.isDirty())return!0;for(var n=this.acroField.getWidgets(),i=0,a=n.length;i<a;i++){var o=n[i],s=((t=o.getAppearances())===null||t===void 0?void 0:t.normal)instanceof gt;if(!s)return!0}return!1},e.prototype.defaultUpdateAppearances=function(t){A(t,"font",[[yt,"PDFFont"]]),this.updateAppearances(t)},e.prototype.updateAppearances=function(t,n){A(t,"font",[[yt,"PDFFont"]]),G(n,"provider",[Function]);for(var i=this.acroField.getWidgets(),a=0,o=i.length;a<o;a++){var s=i[a];this.updateWidgetAppearance(s,t,n)}},e.prototype.updateWidgetAppearance=function(t,n,i){var a=i??lc,o=jr(a(this,t,n));this.updateWidgetAppearanceWithFont(t,n,o)},e.of=function(t,n,i){return new e(t,n,i)},e}(br);export{Di as AFRelationship,Ct as AcroButtonFlags,Re as AcroChoiceFlags,zt as AcroFieldFlags,Ke as AcroTextFlags,tn as AnnotationFlags,ki as AppearanceCharacteristics,Kt as BlendMode,Gt as Cache,x as CharCodes,ir as ColorTypes,ec as CombedTextLayoutError,Aa as CorruptPageTreeError,Ki as CustomFontEmbedder,Hu as CustomFontSubsetEmbedder,In as Duplex,Xf as EncryptedPDFError,tc as ExceededMaxLengthError,_o as FieldAlreadyExistsError,Xu as FileEmbedder,Zf as FontkitNotRegisteredError,Yf as ForeignPageError,rr as ImageAlignment,jn as IndexOutOfBoundsError,Ui as InvalidAcroFieldValueError,_f as InvalidFieldNamePartError,rc as InvalidMaxLengthError,mo as InvalidPDFDateStringError,Da as InvalidTargetIndexError,ko as JpegEmbedder,Rr as LineCapStyle,La as LineJoinStyle,Pt as MethodNotImplementedError,Su as MissingDAEntryError,Ou as MissingKeywordError,Ru as MissingPDFHeaderError,yu as MissingPageContentsEmbeddingError,Fu as MissingTfOperatorError,wu as MultiSelectValueError,ku as NextByteAssertionError,Qf as NoSuchFieldError,_r as NonFullScreenPageMode,Ra as NumberParsingError,Li as PDFAcroButton,_n as PDFAcroCheckBox,Ro as PDFAcroChoice,$n as PDFAcroComboBox,Ao as PDFAcroField,Vn as PDFAcroForm,ni as PDFAcroListBox,Un as PDFAcroNonTerminal,ti as PDFAcroPushButton,ri as PDFAcroRadioButton,Gi as PDFAcroSignature,Nr as PDFAcroTerminal,ei as PDFAcroText,hf as PDFAnnotation,Ae as PDFArray,xu as PDFArrayIsNotRectangleError,nn as PDFBool,Bn as PDFButton,Bo as PDFCatalog,Yr as PDFCheckBox,Qr as PDFContentStream,Pi as PDFContext,xo as PDFCrossRefSection,Uu as PDFCrossRefStream,ve as PDFDict,vr as PDFDocument,Rn as PDFDropdown,ns as PDFEmbeddedPage,br as PDFField,qi as PDFFlateStream,yt as PDFFont,pc as PDFForm,Qn as PDFHeader,oe as PDFHexString,Ei as PDFImage,Fo as PDFInvalidObject,Tu as PDFInvalidObjectParsingError,mc as PDFJavaScript,m as PDFName,ht as PDFNull,fe as PDFNumber,ut as PDFObject,za as PDFObjectCopier,zo as PDFObjectParser,Cu as PDFObjectParsingError,wo as PDFObjectStream,xf as PDFObjectStreamParser,Fe as PDFOperator,xe as PDFOperatorNames,On as PDFOptionList,Tt as PDFPage,Do as PDFPageEmbedder,Ht as PDFPageLeaf,No as PDFPageTree,Sf as PDFParser,sr as PDFParsingError,Jr as PDFRadioGroup,an as PDFRawStream,Ee as PDFRef,Bi as PDFSignature,gt as PDFStream,Pu as PDFStreamParsingError,Vu as PDFStreamWriter,Oe as PDFString,En as PDFTextField,Wi as PDFTrailer,Iu as PDFTrailerDict,Ai as PDFWidgetAnnotation,So as PDFWriter,wf as PDFXRefStreamParser,mu as PageEmbeddingMismatchedContextError,yc as PageSizes,Ni as ParseSpeeds,Co as PngEmbedder,en as PrintScaling,Mi as PrivateConstructorError,$r as ReadingDirection,Jf as RemovePageFromEmptyDocumentError,Ii as ReparseError,$f as RichTextFieldReadError,on as RotationTypes,Au as StalledParserError,Mn as StandardFontEmbedder,uu as StandardFontValues,Hn as StandardFonts,Je as TextAlignment,Ga as TextRenderingMode,Du as UnbalancedParenthesisError,hr as UnexpectedFieldTypeError,zn as UnexpectedObjectTypeError,bu as UnrecognizedStreamTypeError,gu as UnsupportedEncodingError,qa as ViewerPreferences,pr as adjustDimsForRotation,pt as appendBezierCurve,Cn as appendQuadraticCurve,ro as arrayAsString,ye as asNumber,oi as asPDFName,ce as asPDFNumber,go as assertEachIs,pu as assertInteger,A as assertIs,er as assertIsOneOf,kt as assertIsOneOfOrUndefined,lu as assertIsSubset,yo as assertMultiple,G as assertOrUndefined,Jn as assertPositive,vt as assertRange,Nt as assertRangeOrUndefined,Ye as backtick,Ho as beginMarkedContent,Ko as beginText,ps as breakTextIntoLines,ms as byAscendingId,kr as bytesFor,ks as canBeConvertedToUint8Array,eo as charAtIndex,Lt as charFromCode,cs as charFromHexCode,ds as charSplit,fn as cleanText,Vo as clip,nr as closePath,Yo as cmyk,Ha as colorToComponents,ft as componentsToColor,si as concatTransformationMatrix,rt as copyStringIntoBuffer,Oo as createPDFAcroField,Hi as createPDFAcroFields,vu as createTypeErrorMsg,cu as createValueErrorMsg,aa as decodeFromBase64,us as decodeFromBase64DataUri,Po as decodePDFRawStream,lc as defaultButtonAppearanceProvider,fc as defaultCheckBoxAppearanceProvider,dc as defaultDropdownAppearanceProvider,vc as defaultOptionListAppearanceProvider,cc as defaultRadioGroupAppearanceProvider,hc as defaultTextFieldAppearanceProvider,ue as degrees,Io as degreesToRadians,Ja as drawButton,Tn as drawCheckBox,Gf as drawCheckMark,Oi as drawEllipse,Wf as drawEllipsePath,Jo as drawImage,qf as drawLine,Uf as drawLinesOfText,$i as drawObject,Hf as drawOptionList,Vf as drawPage,Pn as drawRadioButton,Br as drawRectangle,Lf as drawSvgPath,Qo as drawTextField,na as drawTextLines,os as encodeToBase64,Xo as endMarkedContent,qo as endPath,Lo as endText,cn as error,ls as escapeRegExp,hs as escapedNewlineChars,Ji as fill,Qi as fillAndStroke,zi as findLastMatch,hu as getType,Zo as grayscale,Ps as hasSurrogates,uo as hasUtf16BOM,no as highSurrogate,Qa as isNewlineChar,Ta as isStandardFont,du as isType,Ts as isWithinBMP,Nn as last,oc as layoutCombedText,rs as layoutMultilineText,Gn as layoutSinglelineText,_a as lineSplit,et as lineTo,io as lowSurrogate,ys as mergeIntoTypedArray,$a as mergeLines,bs as mergeUint8Arrays,Vt as moveTo,Cf as nextLine,jr as normalizeAppearance,Rs as numberToString,jt as padStart,to as parseDate,bo as pdfDocEncodingDecode,Fs as pluckIndices,_e as popGraphicsState,Qe as pushGraphicsState,Ff as radiansToDegrees,Ss as range,xn as rectanglesAreEqual,Zt as reduceRotation,Fr as reverseArray,Be as rgb,Go as rotateAndSkewTextRadiansAndTranslate,kn as rotateDegrees,ur as rotateInPlace,zr as rotateRadians,kf as rotateRectangle,sn as scale,ui as setDashPattern,Ef as setFillingCmykColor,yr as setFillingColor,Df as setFillingGrayscaleColor,Rf as setFillingRgbColor,_i as setFontAndSize,gr as setGraphicsState,fi as setLineCap,Tf as setLineHeight,hn as setLineWidth,Bf as setStrokingCmykColor,vn as setStrokingColor,Af as setStrokingGrayscaleColor,Of as setStrokingRgbColor,Pf as setTextMatrix,Wo as showText,fu as singleQuote,Dn as sizeInBytes,Yi as skewRadians,xs as sortedUniq,dn as stroke,ws as sum,le as toCharCode,fs as toCodePoint,Uo as toDegrees,Xn as toHexString,un as toHexStringOfMinLength,nt as toRadians,Ir as toUint8Array,Dt as translate,Ti as typedArrayFor,ao as utf16Decode,Cs as utf16Encode,Yn as values,Er as waitForTick};
