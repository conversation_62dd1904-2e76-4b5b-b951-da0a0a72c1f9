<?php

namespace App\Http\Controllers;

use App\Models\Inspector;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use App\Mail\InspectorInvitation;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\File;
use App\Models\McdStaffRole;
use App\Models\InspectorInvite;
use Illuminate\Support\Str;


class InspectorController extends Controller
{
    public function index()
    {
        $inspectors = Inspector::with('role')->latest()->get();
       
        return view('inspectors.index', compact('inspectors'));
    }

    // public function create()
    // {
           
        

    //      $roles = McdStaffRole::all();
    //     return view('inspectors.create',compact('roles'));
    // }

     public function create(Request $request)
    {
            $token = $request->query('token');

            $invite = InspectorInvite::where('token', $token)->first();

             // If not found or already used, redirect
            if (!$invite || $invite->is_used) {
                return redirect()->route('mcd.already.submitted.page');
            }

            $roles = McdStaffRole::all();
            return view('inspectors.create',compact('roles','token', 'invite'));
    }

    
      public function store(Request $request)
{
    $request->validate([
        'first_name' => 'required|string|max:50',
        'last_name' => 'required|string|max:50',
        'email' => 'required|email|unique:inspectors,email',
        'password' => 'required|string|min:8',
        'country_code' => 'required|string|max:5',
        'phone' => 'required|string|max:15|regex:/^[0-9\-\+\(\)]+$/',
        'designation' => 'required|string|max:50',
        'department' => 'required|string|max:50',
        'address' => 'nullable|string|max:500',
        'status' => 'required|in:active,inactive',
        'inspector_id' => 'nullable|string|max:50|unique:inspectors,inspector_id',
        'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
         'token' => 'required',
    ]);
    $invite = InspectorInvite::where('token', $request->token)->first();
            if (!$invite || $invite->is_used) {
                return redirect()->route('mcd.already.submitted.page');
            }


    $data = $request->except('password', 'image', 'country_code');
    $data['phone'] = $request->country_code . $request->phone;
    $data['password'] = Hash::make($request->password);

    if ($request->hasFile('image')) {
        $file = $request->file('image');
        $filename = time() . '_' . $file->getClientOriginalName();
        $destinationPath = public_path('inspector');

        if (!File::exists($destinationPath)) {
            File::makeDirectory($destinationPath, 0755, true);
        }

        $file->move($destinationPath, $filename);
        $data['image'] = url('public/inspector/' . $filename);
    }
          

    Inspector::create($data);
    $invite->is_used = true;
          $invite->save();

    return redirect()->route('mcd.inspector.thankyou.page')->with('success', 'Inspector added successfully.');
}

    public function show(Inspector $inspector)
    {
        return view('inspectors.show', compact('inspector'));
    }

    public function edit(Inspector $inspector)
    {
         $roles = McdStaffRole::all();
        return view('inspectors.edit', compact('inspector','roles'));
    }

    
    public function update(Request $request, Inspector $inspector)
{
    
    $request->validate([
        'first_name' => 'required|string|max:50',
        'last_name' => 'required|string|max:50',
        'email' => 'required|email|unique:inspectors,email,' . $inspector->id,
        'country_code' => 'required|string|max:5|regex:/^\+\d+$/',
        'phone' => 'required|string|max:15|regex:/^[0-9\-\(\)]+$/',
        'password' => 'nullable|string|min:8',
        'designation' => 'required|string|max:50',
        'department' => 'required|string|max:50',
        'address' => 'nullable|string|max:500',
        'status' => 'required|in:active,inactive',
        'inspector_id' => 'nullable|string|max:50|unique:inspectors,inspector_id,' . $inspector->id,
        'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
    ]);

    $data = $request->except(['password', 'image', 'country_code']);

    // Combine country code and phone number
    $data['phone'] = $request->country_code . $request->phone;

    if ($request->filled('password')) {
        $data['password'] = Hash::make($request->password);
    }

    if ($request->hasFile('image')) {
        if ($inspector->image && File::exists(public_path(parse_url($inspector->image, PHP_URL_PATH)))) {
            File::delete(public_path(parse_url($inspector->image, PHP_URL_PATH)));
        }

        $file = $request->file('image');
        $filename = time() . '_' . $file->getClientOriginalName();
        $destinationPath = public_path('inspector');

        if (!File::exists($destinationPath)) {
            File::makeDirectory($destinationPath, 0755, true);
        }

        $file->move($destinationPath, $filename);
        $data['image'] = url('public/inspector/' . $filename);
    }

    $inspector->update($data);

    return redirect()->route('inspectors.index')->with('success', 'Inspector updated successfully.');
}

    public function destroy(Inspector $inspector)
    {
        if ($inspector->image && File::exists(public_path(parse_url($inspector->image, PHP_URL_PATH)))) {
            File::delete(public_path(parse_url($inspector->image, PHP_URL_PATH)));
        }

        $inspector->delete();
        return redirect()->route('inspectors.index')->with('success', 'Inspector deleted successfully.');
    }

    public function invite(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'message' => 'nullable|string|max:1000',
        ]);


        $token = Str::random(64);

    // Store the invitation
    $invitation = InspectorInvite::create([
        'email' => $request->email,
        'token' => $token,
        'is_used' => false
    ]);

    // Generate invite URL with token
    $inviteUrl = route('inspectors.create', ['token' => $token]);

        $data = [
            'email' => $request->email,
            'message' => $request->message,
            'create_url' => $inviteUrl,
        ];
         try {

        Mail::to($request->email)->send(new InspectorInvitation($data));

          return redirect()->route('inspectors.index')->with('success', 'Invitation sent successfully.');
         }catch (\Exception $e) {
          return response()->json(['success' => false, 'message' => 'Failed to send invitation.'], 500);
        }
    }




   

    
}
