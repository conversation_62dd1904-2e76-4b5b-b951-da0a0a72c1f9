<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use App\Models\McdStaffRole;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationApiController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
    }

    // public function index(Request $request)
    // {
    //     $inspector = Auth::guard('api')->user();

    //     if (!$inspector) {
    //         return response()->json(['message' => 'Unauthorized'], 401);
    //     }

    //     // Eager load the project relationship with its related models
    //     $notifications = Notification::with([
    //         'project' => function ($query) {
    //             $query->with([
    //                 'inspector' => function ($query) {
    //                     $query->select('id', 'first_name', 'last_name', 'email', 'phone', 'designation', 'department', 'address', 'status', 'inspector_id', 'image');
    //                 },
    //                 'inspector.role' => function ($query) {
    //                     $query->select('id', 'name', 'description', 'is_active');
    //                 },
    //                 'architectCategory' => function ($query) {
    //                     $query->select('id', 'name');
    //                 },
    //                 'builderSubCategory' => function ($query) {
    //                     $query->select('id', 'sub_category_name');
    //                 },
    //                 'permitNumbers' => function ($query) {
    //                     $query->select('id', 'website_builder_project_id', 'permit_number');
    //                 }
    //             ]);
    //         }
    //     ])
    //         ->where('inspector_id', $inspector->id)
    //         ->orderBy('sent_at', 'desc')
    //         ->get();

    //     // Transform the notifications to include project details
    //     $notifications->each(function ($notification) {
    //         if ($notification->project) {
    //             // Transform department_trade into department_names
    //             $departmentIds = is_array($notification->project->department_trade)
    //                 ? $notification->project->department_trade
    //                 : json_decode($notification->project->department_trade, true) ?? explode(',', $notification->project->department_trade);
    //             $departmentIds = array_filter($departmentIds, 'is_numeric');
    //             $notification->project->department_names = McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();

    //             // Transform related fields
    //             $notification->project->architect_category_name = $notification->project->architectCategory->name ?? null;
    //             $notification->project->builder_sub_category_name = $notification->project->builderSubCategory->sub_category_name ?? null;
    //             $notification->project->permit_numbers = $notification->project->permitNumbers->pluck('permit_number')->toArray();

    //             // Unset original ID fields and relationships
    //             unset($notification->project->architect_category_id);
    //             unset($notification->project->builder_sub_category_id);
    //             unset($notification->project->architectCategory);
    //             unset($notification->project->builderSubCategory);
    //             unset($notification->project->permitNumbers);
    //         }
    //     });

    //     if ($notifications->isEmpty()) {
    //         return response()->json([
    //             'message' => 'No notifications found for this inspector',
    //             'inspector_id' => $inspector->id,
    //             'data' => [],
    //         ], 200);
    //     }

    //     return response()->json([
    //         'message' => 'Notifications retrieved successfully',
    //         'inspector_id' => $inspector->id,
    //         'data' => $notifications,
    //     ], 200);
    // }
 public function index(Request $request)
    {
        $inspector = Auth::guard('api')->user();

        if (!$inspector) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }

        $inspectorDepartmentId = $inspector->department;
        $inspectorId = (string) $inspector->id;

        $notifications = Notification::with([
            'project' => function ($query) use ($inspectorDepartmentId, $inspectorId) {
                $query->with([
                    'inspector' => function ($query) {
                        $query->select('id', 'first_name', 'last_name', 'email', 'phone', 'designation', 'department', 'address', 'status', 'inspector_id', 'image');
                    },
                    'inspector.role' => function ($query) {
                        $query->select('id', 'name', 'description', 'is_active');
                    },
                    'architectCategory' => function ($query) {
                        $query->select('id', 'name');
                    },
                    'builderSubCategory' => function ($query) {
                        $query->select('id', 'sub_category_name');
                    },
                    'permitNumbers' => function ($query) {
                        $query->select('id', 'website_builder_project_id', 'permit_number');
                    },
                    'departmentPermitNumbers' => function ($query) use ($inspectorDepartmentId) {
                        $query->where('mcd_staff_role_id', $inspectorDepartmentId)
                              ->select('id', 'website_builder_project_id', 'permit_number');
                    },
                    'physicalInspectionReports' => function ($query) use ($inspectorId) {
                        $query->whereJsonContains('inspectors', $inspectorId)
                              ->select('id', 'project_id', 'report_status', 'inspectors', 'inspection_done_at')
                              ->latest('inspection_done_at');
                    }
                ])
                ->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode($inspectorDepartmentId)]);
            }
        ])
        ->where('inspector_id', $inspector->id)
        ->orderBy('sent_at', 'desc')
        ->get();

        // Map report_status values to labels
        $statusMap = [
            0 => 'Approved',
            1 => 'Violation',
            2 => 'Pending'
        ];

        $notifications->each(function ($notification) use ($statusMap) {
            $notification->is_read = $notification->read_at !== null;

            if ($notification->project) {
                $departmentIds = is_array($notification->project->department_trade)
                    ? $notification->project->department_trade
                    : json_decode($notification->project->department_trade, true) ?? explode(',', $notification->project->department_trade);

                $departmentIds = array_filter($departmentIds, 'is_numeric');
                $notification->project->department_names = McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();

                $notification->project->architect_category_name = $notification->project->architectCategory->name ?? null;
                $notification->project->builder_sub_category_name = $notification->project->builderSubCategory->sub_category_name ?? null;
                $notification->project->PUID = $notification->project->permitNumbers->pluck('permit_number')->toArray();
                $notification->project->department_PermitNumber = $notification->project->departmentPermitNumbers->pluck('permit_number')->toArray();

                if ($notification->project->physicalInspectionReports->isNotEmpty()) {
                    $latestReport = $notification->project->physicalInspectionReports->first();
                    $notification->project->status = $statusMap[$latestReport->report_status] ?? $notification->project->status;
                } else {
                    $notification->project->status = 'Pending';
                }

                unset($notification->project->architect_category_id);
                unset($notification->project->builder_sub_category_id);
                unset($notification->project->architectCategory);
                unset($notification->project->builderSubCategory);
                unset($notification->project->permitNumbers);
                unset($notification->project->departmentPermitNumbers);
                unset($notification->project->physicalInspectionReports);
            }
        });

        $inspectorDetails = [
            'id' => $inspector->id,
            'first_name' => $inspector->first_name,
            'last_name' => $inspector->last_name,
            'email' => $inspector->email,
            'phone' => $inspector->phone,
            'designation' => $inspector->designation,
            'department' => $inspector->department,
            'address' => $inspector->address,
            'status' => $inspector->status,
            'inspector_id' => $inspector->inspector_id,
            'image' => $inspector->image,
            'role' => $inspector->role ? [
                'id' => $inspector->role->id,
                'name' => $inspector->role->name,
                'description' => $inspector->role->description,
                'is_active' => $inspector->role->is_active,
            ] : null,
        ];

        if ($notifications->isEmpty()) {
            return response()->json([
                'message' => 'No notifications found for this inspector',
                'inspector' => $inspectorDetails,
                'data' => [],
            ], 200);
        }

        return response()->json([
            'message' => 'Notifications retrieved successfully',
            'inspector' => $inspectorDetails,
            'data' => $notifications,
        ], 200);
    }

public function markAllAsRead(Request $request)
{
    $inspector = Auth::guard('api')->user();

    if (!$inspector) {
        return response()->json(['message' => 'Unauthorized'], 401);
    }

    Notification::where('inspector_id', $inspector->id)
        ->whereNull('read_at')
        ->update(['read_at' => now()]);

    return response()->json([
        'message' => 'All notifications marked as read.',
    ], 200);
}
public function delete($id)
{
    $inspector = Auth::guard('api')->user();

    if (!$inspector) {
        return response()->json(['message' => 'Unauthorized'], 401);
    }

    $notification = Notification::where('id', $id)
        ->where('inspector_id', $inspector->id)
        ->first();

    if (!$notification) {
        return response()->json(['message' => 'Notification not found'], 404);
    }

    $notification->delete();

    return response()->json(['message' => 'Notification deleted successfully.'], 200);
}

    public function show(Request $request, Notification $notification)
    {
        $inspector = Auth::guard('api')->user();

        if (!$inspector) {
            return response()->json(['message' => 'Unauthorized'], 401); 
        }

        if ($notification->inspector_id !== $inspector->id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Eager load the project relationship with its related models
        $notification->load([
            'project' => function ($query) {
                $query->with([
                    'inspector' => function ($query) {
                        $query->select('id', 'first_name', 'last_name', 'email', 'phone', 'designation', 'department', 'address', 'status', 'inspector_id', 'image');
                    },
                    'inspector.role' => function ($query) {
                        $query->select('id', 'name', 'description', 'is_active');
                    },
                    'architectCategory' => function ($query) {
                        $query->select('id', 'name');
                    },
                    'builderSubCategory' => function ($query) {
                        $query->select('id', 'sub_category_name');
                    },
                    'permitNumbers' => function ($query) {
                        $query->select('id', 'website_builder_project_id', 'permit_number');
                    }
                ]);
            }
        ]);

        // Transform the project details
        if ($notification->project) {
            $departmentIds = is_array($notification->project->department_trade)
                ? $notification->project->department_trade
                : json_decode($notification->project->department_trade, true) ?? explode(',', $notification->project->department_trade);
            $departmentIds = array_filter($departmentIds, 'is_numeric');
            $notification->project->department_names = McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();

            $notification->project->architect_category_name = $notification->project->architectCategory->name ?? null;
            $notification->project->builder_sub_category_name = $notification->project->builderSubCategory->sub_category_name ?? null;
            $notification->project->permit_numbers = $notification->project->permitNumbers->pluck('permit_number')->toArray();

            unset($notification->project->architect_category_id);
            unset($notification->project->builder_sub_category_id);
            unset($notification->project->architectCategory);
            unset($notification->project->builderSubCategory);
            unset($notification->project->permitNumbers);
        }

        return response()->json([
            'message' => 'Notification retrieved successfully',
            'data' => $notification,
        ], 200);
    }
}