@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <section class="section">
            <div class="section-body">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4>My Municipal > Assign Municipals to Projects</h4>
                            </div>
                            <form method="POST" action="{{ route('project_municipals.store') }}">
                                @csrf
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Project <span class="text-danger">*</span></label>
                                                <select name="project_id" id="project_id" class="form-control @error('project_id') is-invalid @enderror" required>
                                                    <option value="">Select Project</option>
                                                    @foreach ($projects as $project)
                                                        <option value="{{ $project->id }}" {{ old('project_id') == $project->id ? 'selected' : '' }}>{{ $project->project_name }}</option>
                                                    @endforeach
                                                </select>
                                                @error('project_id')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Municipal <span class="text-danger">*</span></label>
                                                <select name="municipal_id" id="municipal_id" class="form-control @error('municipal_id') is-invalid @enderror" required>
                                                    <option value="">Select Municipal</option>
                                                    @foreach ($municipals as $municipal)
                                                        <option value="{{ $municipal->id }}" {{ old('municipal_id') == $municipal->id ? 'selected' : '' }}>{{ $municipal->title }}</option>
                                                    @endforeach
                                                </select>
                                                @error('municipal_id')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Status (Optional)</label>
                                                <select name="status" id="status" class="form-control @error('status') is-invalid @enderror">
                                                    <option value="">Select Status</option>
                                                    <option value="Active" {{ old('status') == 'Active' ? 'selected' : '' }}>Active</option>
                                                    <option value="Inactive" {{ old('status') == 'Inactive' ? 'selected' : '' }}>Inactive</option>
                                                </select>
                                                @error('status')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Description (Optional)</label>
                                                <textarea name="description" id="description" class="form-control @error('description') is-invalid @enderror">{{ old('description') }}</textarea>
                                                @error('description')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer text-right">
                                    <button class="btn btn-primary mr-1" type="submit">Create</button>
                                    <a href="{{ route('project_municipals.index') }}" class="btn btn-secondary">Cancel</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
@endsection