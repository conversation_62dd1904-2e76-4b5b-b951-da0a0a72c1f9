<?php

namespace App\Http\Controllers;

use App\Models\Inspector;
use App\Models\Notification;
use App\Models\WebsiteBuilderProject;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use App\Mail\InspectorNotification;

class NotificationController extends Controller
{
    public function create()
    {
       
        $inspectors = Inspector::where('status', 'active')->get();
        $projects = WebsiteBuilderProject::all();
        return view('admin.notifications.create', compact('inspectors', 'projects'));
    }

   public function store(Request $request)
{
    $validated = $request->validate([
        'inspector_id' => 'required|exists:inspectors,id',
        'project_id' => 'required|exists:website_builder_projects,id',
        'title' => 'required|string|max:255',
        'message' => 'required|string|max:1000',
        'tag' => 'required|in:Approved,Violation,Message',
    ]);

    $notification = Notification::create([
        'inspector_id' => $validated['inspector_id'],
        'project_id' => $validated['project_id'],
        'title' => $validated['title'],
        'message' => $validated['message'],
        'tag' => $validated['tag'],
        'sent_at' => now(),
    ]);

    $inspector = Inspector::find($validated['inspector_id']);
    $project = WebsiteBuilderProject::find($validated['project_id']);

    if (!$project || !is_string($project->name)) {
        \Log::error('Invalid project name', [
            'project_id' => $validated['project_id'],
            'name' => $project ? $project->name : null,
        ]);
        return redirect()->route('notifications.index')->with('error', 'Invalid project name.');
    }

    $notificationData = [
        'title' => $validated['title'],
        'message' => $validated['message'],
        'tag' => $validated['tag'],
        'project' => $project->name,
        'sent_at' => now()->toDateTimeString(),
    ];

    Mail::to($inspector->email)->send(new InspectorNotification($notificationData));

    return redirect()->route('notifications.index')->with('success', 'Notification sent successfully!');
}

    public function index()
    {
        // Fetch all notifications with their inspectors and projects, ordered by sent date
        $notifications = Notification::with(['inspector', 'project'])->orderBy('sent_at', 'desc')->get();
        return view('admin.notifications.index', compact('notifications'));
    }

    public function show(Notification $notification)
    {
        // Load the inspector and project relationships
        $notification->load(['inspector', 'project']);
        return view('admin.notifications.show', compact('notification'));
    }

    public function destroy(Notification $notification)
    {
        $notification->delete();
        return redirect()->route('notifications.index')->with('success', 'Notification deleted successfully!');
    }
}