<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;
use App\Models\McdStaff;

class CheckPermission
{
    public function handle($request, Closure $next, $permission, $accessType = 'read')
    {
        $guard = Auth::guard('staff_mcd')->check() ? 'staff_mcd' : 'web';
        
        /** @var McdStaff $user */
        $user = Auth::guard($guard)->user();

        if ($user && method_exists($user, 'hasPermission') && $user->hasPermission($permission, $accessType)) {
            return $next($request);
        }

        abort(403, 'Unauthorized action.');
    }
}
