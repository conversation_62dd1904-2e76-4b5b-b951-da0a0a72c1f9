<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\NewBuilder;
use App\Models\Architect;
use App\Models\Inspector; // Add Inspector model
use Illuminate\Support\Facades\DB;
use App\Models\Message;
use Illuminate\Support\Facades\Auth;
use App\Models\Attachment;
use App\Http\Resources\MessageResource;
use App\Http\Resources\ArchitectResource;
use App\Http\Resources\BuilderResource;

class ShareController extends Controller
{
    public function builders(Request $request)
    {
        // No changes needed here as it doesn't involve sender logic
        try {
            $builderCount = DB::table('users')
                ->where('role_id', 2)
                ->select('email', 'name', DB::raw('"User" as source'))
                ->union(
                    DB::table('new_builders')
                        ->where('role_id', 2)
                        ->select('email', 'name', DB::raw('"Builder" as source'))
                )
                ->distinct('email')
                ->count();

            $builders = DB::table('users')
                ->where('role_id', 2)
                ->select(
                    'id',
                    'name',
                    'email',
                    DB::raw('created_at AS created_at'),
                    DB::raw('"User" AS source')
                )
                ->union(
                    DB::table('new_builders')
                        ->where('role_id', 2)
                        ->select(
                            'id',
                            'name',
                            'email',
                            DB::raw('created_at AS created_at'),
                            DB::raw('"Builder" AS source')
                        )
                )
                ->distinct('email')
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'count' => $builderCount,
                    'builders' => BuilderResource::collection($builders)
                ],
                'message' => 'Builders retrieved successfully'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving builders',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function architects(Request $request)
    {
        // No changes needed here as it doesn't involve sender logic
        try {
            $architectCount = DB::table('users')
                ->where('role_id', 3)
                ->select('email', 'name', DB::raw('"User" as source'))
                ->union(
                    DB::table('architects')
                        ->where('role_id', 3)
                        ->select('email', 'name', DB::raw('"Architect" as source'))
                )
                ->distinct('email')
                ->count();

            $architects = DB::table('users')
                ->where('role_id', 3)
                ->select(
                    'id',
                    'name',
                    'email',
                    DB::raw('created_at AS created_at'),
                    DB::raw('"User" AS source')
                )
                ->union(
                    DB::table('architects')
                        ->where('role_id', 3)
                        ->select(
                            'id',
                            'name',
                            'email',
                            DB::raw('created_at AS created_at'),
                            DB::raw('"Architect" AS source')
                        )
                )
                ->distinct('email')
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'count' => $architectCount,
                    'architects' => ArchitectResource::collection($architects)
                ],
                'message' => 'Architects retrieved successfully'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving architects',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function sendToBuilder(Request $request)
    {
        try {
            $request->validate([
                'receiver_email' => 'required|email|exists:users,email',
                'body' => 'required|string',
                'attachments.*' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:204800',
            ]);

            $receiver = User::where('email', $request->receiver_email)->firstOrFail();
            if ($receiver->role_id != 2) {
                return response()->json([
                    'success' => false,
                    'message' => 'Receiver is not a Builder.',
                ], 422);
            }

            $authUser = Auth::user();
            $messageData = [
                'receiver_id' => $receiver->id,
                'subject' => 'Message Sent to Builder',
                'body' => $request->body,
                'attachment_count' => $request->hasFile('attachments') ? count($request->file('attachments')) : 0,
            ];

            // Check if the authenticated user is an inspector
            if ($authUser instanceof Inspector) {
                $messageData['sender_id_inspector'] = $authUser->id;
            } else {
                $messageData['sender_id'] = $authUser->id;
            }

            $message = Message::create($messageData);

            if ($request->hasFile('attachments')) {
                foreach ($request->file('attachments') as $file) {
                    $path = $file->store('attachments', 'public');
                    Attachment::create([
                        'message_id' => $message->id,
                        'file_path' => $path,
                        'file_name' => $file->getClientOriginalName(),
                    ]);
                }
            }

            return response()->json([
                'success' => true,
                'data' => new MessageResource($message),
                'message' => 'Your message has been sent to the Builder successfully.'
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed: ' . implode(', ', $e->errors()[array_key_first($e->errors())]),
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Error in sendToBuilder: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while sending the message',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function sendToArchitect(Request $request)
    {
        try {
            $request->validate([
                'receiver_email' => 'required|email|exists:users,email',
                'body' => 'required|string',
                'attachments.*' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:204800',
            ]);

            $receiver = User::where('email', $request->receiver_email)->firstOrFail();
            if ($receiver->role_id != 3) {
                return response()->json([
                    'success' => false,
                    'message' => 'Receiver is not an Architect.',
                ], 422);
            }

            $authUser = Auth::user();
            $messageData = [
                'receiver_id' => $receiver->id,
                'subject' => 'Message Sent to Architect',
                'body' => $request->body,
                'attachment_count' => $request->hasFile('attachments') ? count($request->file('attachments')) : 0,
            ];

            // Check if the authenticated user is an inspector
            if ($authUser instanceof Inspector) {
                $messageData['sender_id_inspector'] = $authUser->id;
            } else {
                $messageData['sender_id'] = $authUser->id;
            }

            $message = Message::create($messageData);

            if ($request->hasFile('attachments')) {
                foreach ($request->file('attachments') as $file) {
                    $path = $file->store('attachments', 'public');
                    Attachment::create([
                        'message_id' => $message->id,
                        'file_path' => $path,
                        'file_name' => $file->getClientOriginalName(),
                    ]);
                }
            }

            return response()->json([
                'success' => true,
                'data' => new MessageResource($message),
                'message' => 'Your message has been sent to the Architect successfully.'
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed: ' . implode(', ', $e->errors()[array_key_first($e->errors())]),
            ], 422);
        } catch (\Exception $e) {
            \Log::error('Error in sendToArchitect: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while sending the message',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}