<?php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Mail\OtpMail;
use App\Models\Inspector;
use App\Models\Otp;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Laravel\Sanctum\PersonalAccessToken;

class AuthController extends Controller
{
    // public function login(Request $request)
    // {
    //     $request->validate([
    //         'email' => 'required|email|exists:inspectors,email',
    //         'password' => 'required|string',
    //     ]);

    //     $inspector = Inspector::where('email', $request->email)->first();

    //     if (!$inspector || !Hash::check($request->password, $inspector->password)) {
    //         return response()->json(['message' => 'Invalid credentials'], 401);
    //     }

    //     if ($inspector->status !== 'active') {
    //         return response()->json(['message' => 'Account is inactive'], 403);
    //     }

    //     // Generate token for the inspector
    //     $token = $inspector->createToken('auth_token')->plainTextToken;

    //     return response()->json([
    //         'message' => 'Login successful',
    //         'access_token' => $token,
    //         'token_type' => 'Bearer',
    //         'inspector' => $inspector,
    //     ], 200);
    // }
    public function login(Request $request)
{
    $request->validate([
        'email' => 'required|email|exists:inspectors,email',
        'password' => 'required|string',
    ]);

    $inspector = Inspector::where('email', $request->email)->with('role')->first();

    if (!$inspector || !Hash::check($request->password, $inspector->password)) {
        return response()->json(['message' => 'Invalid credentials'], 401);
    }

    if ($inspector->status !== 'active') {
        return response()->json(['message' => 'Account is inactive'], 403);
    }

    $token = $inspector->createToken('auth_token')->plainTextToken;

    $inspectorData = $inspector->toArray();
    $inspectorData['department_name'] = $inspector->role ? $inspector->role->name : null;
    unset($inspectorData['department']); // Remove the department ID if you only want the name
    unset($inspectorData['role']); 

    return response()->json([
        'message' => 'Login successful',
        'access_token' => $token,
        'token_type' => 'Bearer',
        'inspector' => $inspectorData,
    ], 200);
}

    public function forgotPassword(Request $request)
    {
        $request->validate([
            'email' => 'required|email|exists:inspectors,email',
        ]);

        $this->generateAndSendOtp($request->email, 'password_reset');

        return response()->json([
            'message' => 'OTP sent to your email',
            'email' => $request->email,
        ]);
    }
    public function resendOtp(Request $request)
    {
        $request->validate([
            'email' => 'required|email|exists:inspectors,email',
        ]);

        // Mark any existing unused OTPs for this email as used
        Otp::where('email', $request->email)
           ->where('type', 'password_reset')
           ->where('is_used', false)
           ->update(['is_used' => true]);

        // Generate and send a new OTP
        $this->generateAndSendOtp($request->email, 'password_reset');

        return response()->json([
            'message' => 'New OTP sent to your email',
            'email' => $request->email,
        ], 200);
    }

    public function verifyOtp(Request $request)
    {
        $request->validate([
            'email' => 'required|email|exists:inspectors,email',
            'code' => 'required|string|size:5',
        ]);

        $otp = Otp::where('email', $request->email)
                  ->where('is_used', false)
                  ->where('code', $request->code)
                  ->where('type', 'password_reset')
                  ->latest()
                  ->first();

        if (!$otp || $otp->expires_at->isPast()) {
            return response()->json(['message' => 'OTP is invalid or expired'], 400);
        }

        $otp->update(['is_used' => true]);

        $inspector = Inspector::where('email', $request->email)->first();
        $token = $inspector->createToken('password_reset_token')->plainTextToken;

        return response()->json([
            'message' => 'OTP verified successfully',
            'access_token' => $token,
            'token_type' => 'Bearer',
        ], 200);
    }

    public function resetPassword(Request $request)
    {
        $request->validate([
            'token' => 'required|string',
            'password' => 'required|string|min:8',
            'password_confirmation' => 'required|string|same:password',
        ]);

        // Verify the token
        $accessToken = PersonalAccessToken::findToken($request->token);
        if (!$accessToken || !$accessToken->tokenable || !($accessToken->tokenable instanceof Inspector)) {
            return response()->json(['message' => 'Invalid or unauthorized token'], 401);
        }

        // Get the inspector associated with the token
        $inspector = $accessToken->tokenable;

        // Update the password
        $inspector->update([
            'password' => Hash::make($request->password),
        ]);

        // Revoke the token after use
        $accessToken->delete();

        return response()->json(['message' => 'Password reset successful'], 200);
    }
    public function changePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required|string',
            'new_password' => 'required|string|min:8',
            'new_password_confirmation' => 'required|string|same:new_password',
        ]);

        // Get the authenticated inspector
        $inspector = $request->user();

        // Verify current password
        if (!Hash::check($request->current_password, $inspector->password)) {
            return response()->json(['message' => 'Current password is incorrect'], 401);
        }

        // Update the password
        $inspector->update([
            'password' => Hash::make($request->new_password),
        ]);

        return response()->json(['message' => 'Password changed successfully'], 200);
    }

    private function generateAndSendOtp($email, $type)
    {
        $code = str_pad(rand(0, 99999), 5, '0', STR_PAD_LEFT);
        $expiresAt = now()->addMinutes(10);

        $otp = Otp::create([
            'email' => $email,
            'code' => $code,
            'type' => $type,
            'expires_at' => $expiresAt,
        ]);

        Mail::to($email)->send(new OtpMail($code, $type));

        return $otp;
    }

    public function logout(Request $request)
    {
        // Revoke the user's current token
        $request->user()->currentAccessToken()->delete();

        return response()->json(['message' => 'Logged out successfully']);
    }
}