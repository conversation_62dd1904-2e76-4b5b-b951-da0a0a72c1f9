<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Notification extends Model
{
    protected $fillable = ['inspector_id', 'project_id', 'title', 'message', 'tag', 'sent_at'];

    public function inspector()
    {
        return $this->belongsTo(Inspector::class);
    }

    public function project()
    {
        return $this->belongsTo(WebsiteBuilderProject::class);
    }
}