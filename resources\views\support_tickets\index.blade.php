@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4>Support Tickets</h4>
                        <a href="{{ route('support_tickets.create') }}" class="btn btn-primary btn-sm">
                            <i class="fa fa-plus"></i> Create New Ticket
                        </a>
                    </div>
                    <div class="card-body">
                        @if ($tickets->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="table-1">
                                    <thead>
                                        <tr>
                                            <th>Sr No</th>
                                            <th>Subject</th>
                                            <th>Priority</th>
                                            <th>Status</th>
                                            <th class="text-center">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($tickets as $key => $ticket)
                                            <tr>
                                                <td class="text-center">{{ $key + 1 }}</td>
                                                <td>{{ $ticket->subject }}</td>
                                                <td>
                                                    <span class="badge {{ $ticket->priority == 'low' ? 'badge-info' : ($ticket->priority == 'medium' ? 'badge-warning' : 'badge-danger') }}">
                                                        {{ ucfirst($ticket->priority) }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge status-badge {{ $ticket->status == 'open' ? 'badge-warning' : ($ticket->status == 'resolved' ? 'badge-success' : 'badge-primary') }}"
                                                          data-id="{{ $ticket->id }}"
                                                          data-current-status="{{ $ticket->status }}"
                                                          style="cursor: pointer;">
                                                        {{ ucfirst($ticket->status) }}
                                                    </span>
                                                </td>
                                                <td class="text-center">
                                                    <a href="{{ route('support_tickets.edit', $ticket->id) }}" 
                                                       class="btn btn-sm btn-primary mr-1" 
                                                       title="Edit">
                                                        <i class="fa fa-edit"></i>
                                                    </a>
                                                    <form action="{{ route('support_tickets.destroy', $ticket->id) }}" 
                                                          method="POST" 
                                                          class="d-inline delete-form">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" 
                                                                class="btn btn-sm btn-danger delete-btn" 
                                                                title="Delete" 
                                                                data-name="{{ $ticket->subject }}">
                                                            <i class="fa fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="alert alert-info text-center">
                                <i class="fa fa-info-circle mr-2"></i>
                                No support tickets found.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('style')
    <style>
        .btn-sm { 
            padding: 0.25rem 0.5rem; 
        }
        .badge {
            font-size: 0.8rem;
            padding: 0.3em 0.6em;
        }
        .table th, .table td {
            vertical-align: middle;
        }
        .alert {
            background-color: #e7f1ff;
            border: 1px solid #b8daff;
            color: #004085;
            padding: 0.75rem 1.25rem;
            border-radius: 0.25rem;
            margin-bottom: 1rem;
        }
        .status-badge:hover {
            opacity: 0.8;
        }
    </style>
@endpush

@push('script')
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Success Message from Session
            @if(session('success'))
                Swal.fire({
                    title: 'Success!',
                    text: '{{ session('success') }}',
                    icon: 'success',
                    showConfirmButton: false,
                    timer: 4000,
                    timerProgressBar: true,
                    position: 'top-end',
                    toast: true,
                    customClass: {
                        popup: 'swal2-popup-custom',
                        title: 'swal2-title-custom',
                        content: 'swal2-content-custom'
                    }
                });
            @endif

            // Delete Confirmation
            document.querySelectorAll('.delete-btn').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const form = this.closest('form');
                    const ticketSubject = this.getAttribute('data-name');

                    Swal.fire({
                        title: 'Are you sure?',
                        text: `You are about to delete the support ticket "${ticketSubject}". This action cannot be undone!`,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#3085d6',
                        confirmButtonText: 'Yes, delete it!',
                        cancelButtonText: 'Cancel'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            form.submit();
                        }
                    });
                });
            });

            // Status Change Handler
            document.querySelectorAll('.status-badge').forEach(badge => {
                badge.addEventListener('click', function() {
                    const ticketId = this.getAttribute('data-id');
                    const currentStatus = this.getAttribute('data-current-status');

                    Swal.fire({
                        title: 'Change Status',
                        input: 'select',
                        inputOptions: {
                            'open': 'Open',
                            'in_progress': 'In Progress',
                            'resolved': 'Resolved'
                        },
                        inputValue: currentStatus,
                        showCancelButton: true,
                        confirmButtonText: 'Update',
                        cancelButtonText: 'Cancel',
                        inputValidator: (value) => {
                            if (!value) {
                                return 'You need to select a status!';
                            }
                        }
                    }).then((result) => {
                        if (result.isConfirmed) {
                            fetch('{{ url('/support-tickets') }}/' + ticketId + '/status', {
                                method: 'PATCH',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                                },
                                body: JSON.stringify({ status: result.value })
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    // Update badge text and class
                                    badge.textContent = result.value.replace('_', ' ').toUpperCase();
                                    badge.setAttribute('data-current-status', result.value);
                                    badge.className = 'badge status-badge ' + (
                                        result.value === 'open' ? 'badge-warning' :
                                        result.value === 'resolved' ? 'badge-success' : 'badge-primary'
                                    );

                                    Swal.fire({
                                        title: 'Success!',
                                        text: data.message,
                                        icon: 'success',
                                        toast: true,
                                        position: 'top-end',
                                        showConfirmButton: false,
                                        timer: 3000,
                                        timerProgressBar: true
                                    });
                                } else {
                                    Swal.fire('Error!', 'Failed to update status.', 'error');
                                }
                            })
                            .catch(error => {
                                Swal.fire('Error!', 'Something went wrong.', 'error');
                            });
                        }
                    });
                });
            });
        });
    </script>

    <style>
        .swal2-popup-custom {
            border-radius: 8px;
            padding: 15px;
            background: #ffffff;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
        }
        .swal2-title-custom {
            color: #28a745;
            font-weight: bold;
            font-size: 18px;
        }
        .swal2-content-custom {
            font-size: 14px;
            color: #555;
        }
    </style>
@endpush