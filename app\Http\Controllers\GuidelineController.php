<?php

namespace App\Http\Controllers;

use App\Models\Guideline;
use Illuminate\Http\Request;

class GuidelineController extends Controller
{
    public function index(Request $request)
    {
        $type = $request->query('type');
        $guidelines = $type
            ? Guideline::where('type', $type)->get()
            : Guideline::all();
        
        return view('modules.guidelines.index', compact('guidelines', 'type'));
    }

    public function create()
    {
        return view('modules.guidelines.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'type' => 'required|in:Fire Safety,Structural,Zoning Laws',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'date_time' => 'required|date',
        ]);

        Guideline::create($request->all());
        return redirect()->route('guidelines.index')->with('success', 'Guideline created successfully.');
    }

    public function show(Guideline $guideline)
    {
        return view('modules.guidelines.show', compact('guideline'));
    }

    public function edit(Guideline $guideline)
    {
        return view('modules.guidelines.edit', compact('guideline'));
    }

    public function update(Request $request, Guideline $guideline)
    {
        $request->validate([
            'type' => 'required|in:Fire Safety,Structural,Zoning Laws',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'date_time' => 'required|date',
        ]);

        $guideline->update($request->all());
        return redirect()->route('guidelines.index')->with('success', 'Guideline updated successfully.');
    }

    public function destroy(Guideline $guideline)
    {
        $guideline->delete();
        return redirect()->route('guidelines.index')->with('success', 'Guideline deleted successfully.');
    }
}