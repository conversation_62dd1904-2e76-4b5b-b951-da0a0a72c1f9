@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4>My Muncipal > List of  Assign Municipals to Projects </h4>
                        <a href="{{ route('project_municipals.create') }}" class="btn btn-primary btn-sm">
                            <i class="fa fa-plus"></i> Assign Muncipal                        </a>
                    </div>
                    <div class="card-body">
                        {{-- @if (session('success'))
                            <div class="alert alert-info text-center">
                                <i class="fa fa-info-circle mr-2"></i>
                                {{ session('success') }}
                            </div>
                        @endif --}}

                        @if ($projectMunicipals->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="table-1">
                                    <thead>
                                        <tr>
                                            <th>Sr No</th>
                                            <th>Project</th>
                                            <th>Municipal</th>
                                            <th>Status</th>
                                            {{-- <th>Description</th> --}}
                                            <th class="text-center">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($projectMunicipals as $key => $projectMunicipal)
                                            <tr>
                                                <td class="text-center">{{ $key + 1 }}</td>
                                                <td>{{ $projectMunicipal->project->project_name }}</td>
                                                <td>{{ $projectMunicipal->municipal->title }}</td>
                                                <td>
                                                    <span class="badge {{ $projectMunicipal->status == 'Pending' ? 'badge-warning' : ($projectMunicipal->status == 'Approved' ? 'badge-success' : ($projectMunicipal->status == 'Objection' ? 'badge-info' : ($projectMunicipal->status == 'Rejected' ? 'badge-danger' : 'badge-primary'))) }}">
                                                        {{ $projectMunicipal->status ?? 'N/A' }}
                                                    </span>
                                                </td>
                                                {{-- <td>{{ $projectMunicipal->description ?? 'N/A' }}</td> --}}
                                                <td class="text-center">
                                                    <a href="{{ route('project_municipals.edit', $projectMunicipal) }}" 
                                                       class="btn btn-sm btn-primary mr-1" 
                                                       title="Edit">
                                                        <i class="fa fa-edit"></i>
                                                    </a>
                                                    <form action="{{ route('project_municipals.destroy', $projectMunicipal) }}" 
                                                          method="POST" 
                                                          class="d-inline delete-form">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" 
                                                                class="btn btn-sm btn-danger delete-btn" 
                                                                title="Delete" 
                                                                data-name="{{ $projectMunicipal->project->project_name }}">
                                                            <i class="fa fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="alert alert-info text-center">
                                <i class="fa fa-info-circle mr-2"></i>
                                No project municipals found.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('style')
    <style>
        .btn-sm { 
            padding: 0.25rem 0.5rem; 
        }
        .badge {
            font-size: 0.8rem;
            padding: 0.3em 0.6em;
        }
        .table th, .table td {
            vertical-align: middle;
        }
        .alert {
            background-color: #e7f1ff;
            border: 1px solid #b8daff;
            color: #004085;
            padding: 0.75rem 1.25rem;
            border-radius: 0.25rem;
            margin-bottom: 1rem;
        }
    </style>
@endpush

@push('script')
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Success Message from Session
            @if(session('success'))
                Swal.fire({
                    title: 'Success!',
                    text: '{{ session('success') }}',
                    icon: 'success',
                    showConfirmButton: false,
                    timer: 4000,
                    timerProgressBar: true,
                    position: 'top-end',
                    toast: true,
                    customClass: {
                        popup: 'swal2-popup-custom',
                        title: 'swal2-title-custom',
                        content: 'swal2-content-custom'
                    }
                });
            @endif

            // Delete Confirmation
            document.querySelectorAll('.delete-btn').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const form = this.closest('form');
                    const projectName = this.getAttribute('data-name');

                    Swal.fire({
                        title: 'Are you sure?',
                        text: `You are about to delete the project municipal for "${projectName}". This action cannot be undone!`,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#3085d6',
                        confirmButtonText: 'Yes, delete it!',
                        cancelButtonText: 'Cancel'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            form.submit();
                        }
                    });
                });
            });
        });
    </script>

    <style>
        .swal2-popup-custom {
            border-radius: 8px;
            padding: 15px;
            background: #ffffff;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
        }
        .swal2-title-custom {
            color: #28a745;
            font-weight: bold;
            font-size: 18px;
        }
        .swal2-content-custom {
            font-size: 14px;
            color: #555;
        }
    </style>
@endpush