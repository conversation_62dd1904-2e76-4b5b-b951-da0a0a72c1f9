<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no" name="viewport">
    <title>Construction-Mcdpanel Dashboard Template</title>

    <!-- General CSS Files -->
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link rel="stylesheet" href="{{ asset('assets/css/app.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/bundles/datatables/datatables.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/bundles/datatables/DataTables-1.10.16/css/dataTables.bootstrap4.min.css') }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">


    <!-- Template CSS -->
    <link rel="stylesheet" href="{{ asset('assets/css/style.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/components.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/custom.css') }}">
    <link rel="shortcut icon" type="image/x-icon" href="{{ asset('assets/img/logo.jpg') }}">

    @stack('style')
</head>

<body>
    <div class="loader"></div>
    <div id="app">
        <div class="main-wrapper main-wrapper-1">
            <div class="navbar-bg"></div>

            @include('mcdpanel.layouts.header')
            @include('mcdpanel.layouts.sidebar')

            @yield('content')
            @include('mcdpanel.layouts.footer')
        </div>
    </div>

    <!-- JS Files -->
    <script src="{{ asset('assets/bundles/jquery-ui/jquery-ui.min.js') }}"></script>
    <script src="{{ asset('assets/js/app.min.js') }}"></script>
    <script src="{{ asset('assets/bundles/datatables/datatables.min.js') }}"></script>
    <script src="{{ asset('assets/bundles/datatables/DataTables-1.10.16/js/dataTables.bootstrap4.min.js') }}"></script>
    <!-- Bootstrap Bundle JS (includes Popper) -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>


    <!-- Feather Icons -->
    <script src="https://unpkg.com/feather-icons"></script>

    <!-- Chart.js Library -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Page Specific JS File -->
    <script src="{{ asset('assets/js/page/datatables.js') }}"></script>
    <script src="{{ asset('assets/js/page/index.js') }}"></script>
    <script src="{{ asset('assets/js/scripts.js') }}"></script>
    <script src="{{ asset('assets/js/custom.js') }}"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Page Specific JS File -->
    <script src="{{ asset('assets/js/page/chart-chartjs.js') }}"></script>

    <!-- Initialize Feather Icons and Sidebar -->
    <script>
        (function($) {
            $(document).ready(function() {
                // Initialize Feather Icons
                feather.replace();

                // Initialize DataTables
                $('#table-1').DataTable();

                // Sidebar dropdown toggle
                $('.menu-toggle').off('click').on('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    const $parent = $(this).closest('.dropdown');
                    const $dropdownMenu = $parent.find('.dropdown-menu').first();

                    // Toggle current dropdown
                    if ($parent.hasClass('active')) {
                        $dropdownMenu.slideUp();
                        $parent.removeClass('active');
                    } else {
                        // Close other dropdowns
                        $('.dropdown').not($parent).removeClass('active').find('.dropdown-menu').slideUp();
                        // Open current dropdown
                        $dropdownMenu.slideDown();
                        $parent.addClass('active');
                    }
                });

                // Prevent dropdown menu clicks from closing the dropdown
                $('.dropdown-menu').on('click', function(e) {
                    e.stopPropagation();
                });

                // Keep dropdown open for active route
                $('.dropdown').each(function() {
                    const $dropdown = $(this);
                    if ($dropdown.find('.nav-link.active').length > 0) {
                        $dropdown.addClass('active');
                        $dropdown.find('.dropdown-menu').show();
                    }
                });
            });
        })(jQuery);
    </script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>


<style>
  .pdf-annotate-container canvas {
    border: 1px solid #ccc;
  }
</style>


    @stack('script')
</body>
</html>