<?php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\WebsiteBuilderProject;
use App\Models\McdStaffRole;
use App\Models\ProjectReviewDocument;
use App\Models\AnnotatedPdf;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AssignedProjectApiController extends Controller
{
    // public function index(Request $request)
    // {
    //     $inspector = Auth::guard('api')->user();

    //     if (!$inspector) {
    //         return response()->json(['message' => 'Unauthorized'], 401);
    //     }

    //     $inspectorDepartmentId = $inspector->department;
    //     $inspectorId = (string) $inspector->id;

    //     $projects = WebsiteBuilderProject::with([
    //         'inspector' => function ($query) {
    //             $query->select('id', 'first_name', 'last_name', 'email', 'phone', 'designation', 'department', 'address', 'status', 'inspector_id', 'image');
    //         }, 
    //         'inspector.role' => function ($query) {
    //             $query->select('id', 'name', 'description', 'is_active');
    //         },
    //         'architectCategory' => function ($query) {
    //             $query->select('id', 'name');
    //         },
    //         'builderSubCategory' => function ($query) {
    //             $query->select('id', 'sub_category_name');
    //         },
    //         'permitNumbers' => function ($query) {
    //             $query->select('id', 'website_builder_project_id', 'permit_number');
    //         },
    //         'departmentPermitNumbers' => function ($query) use ($inspectorDepartmentId) {
    //             $query->where('mcd_staff_role_id', $inspectorDepartmentId)
    //                   ->select('id', 'website_builder_project_id', 'permit_number');
    //         },
    //         'physicalInspectionReports' => function ($query) use ($inspectorId) {
    //             $query->whereJsonContains('inspectors', $inspectorId)
    //                   ->select('id', 'project_id', 'report_status', 'inspectors', 'inspection_done_at')
    //                   ->latest('inspection_done_at');
    //         }
    //     ])
    //     ->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode($inspectorDepartmentId)])
    //     ->get();

    //     // Map report_status values to labels
    //     $statusMap = [
    //         0 => 'Approved',
    //         1 => 'Violation',
    //         2 => 'Pending'
    //     ];

    //     $projects->each(function ($project) use ($statusMap, $inspectorDepartmentId) {
    //         $departmentIds = is_array($project->department_trade)
    //             ? $project->department_trade
    //             : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);

    //         $departmentIds = array_filter($departmentIds, 'is_numeric');
    //         $project->department_names = McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();

    //         $project->architect_category_name = $project->architectCategory->name ?? null;
    //         $project->builder_sub_category_name = $project->builderSubCategory->sub_category_name ?? null;

    //         $project->PUID = $project->permitNumbers->pluck('permit_number')->toArray();
    //         $project->department_PermitNumber = $project->departmentPermitNumbers->pluck('permit_number')->toArray();

    //         if ($project->physicalInspectionReports->isNotEmpty()) {
    //             $latestReport = $project->physicalInspectionReports->first();
    //             $project->status = $statusMap[$latestReport->report_status] ?? $project->status;
    //         } else {
    //             $project->status = 'Pending';
    //         }

    //         // Fetch PDFs and images from project_attachment table
    //         $departmentPdfs = DB::table('project_attachment')
    //             ->where('project_id', $project->id)
    //             ->where('trade_id', $inspectorDepartmentId)
    //             ->whereIn('file_type', [
    //                 'full_plan_set',
    //                 'site_plan',
    //                 'structural_calculations',
    //                 'engineering_reports',
    //                 'energy_calculations',
    //                 'special_certifications'
    //             ])
    //             ->select('file_path', 'file_type', 'created_at', 'updated_at')
    //             ->orderBy('created_at', 'desc')
    //             ->get();

    //         // Fetch project images (trade_id is NULL)
    //         $projectImages = DB::table('project_attachment')
    //             ->where('project_id', $project->id)
    //             ->whereNull('trade_id')
    //             ->where('file_type', 'project_image')
    //             ->select('file_path', 'file_type', 'created_at', 'updated_at')
    //             ->orderBy('created_at', 'desc')
    //             ->get();

    //         // Combine department PDFs and project images
    //         $allDocuments = $departmentPdfs->merge($projectImages);

    //         // Add display name for file types
    //         foreach ($allDocuments as $doc) {
    //             $doc->file_type_display = ucwords(str_replace('_', ' ', $doc->file_type));
    //         }

    //         // Attach documents to the project
    //         $project->documents = $allDocuments;

    //         // Clean up unused fields
    //         unset($project->architect_category_id);
    //         unset($project->builder_sub_category_id);
    //         unset($project->architectCategory);
    //         unset($project->builderSubCategory);
    //         unset($project->permitNumbers);
    //         unset($project->departmentPermitNumbers);
    //         unset($project->physicalInspectionReports);
    //     });

    //     $inspectorDetails = [
    //         'id' => $inspector->id,
    //         'first_name' => $inspector->first_name,
    //         'last_name' => $inspector->last_name,
    //         'email' => $inspector->email,
    //         'phone' => $inspector->phone,
    //         'designation' => $inspector->designation,
    //         'department' => $inspector->department,
    //         'address' => $inspector->address,
    //         'status' => $inspector->status,
    //         'inspector_id' => $inspector->inspector_id,
    //         'image' => $inspector->image,
    //         'role' => $inspector->role ? [
    //             'id' => $inspector->role->id,
    //             'name' => $inspector->role->name,
    //             'description' => $inspector->role->description,
    //             'is_active' => $inspector->role->is_active,
    //         ] : null,
    //     ];

    //     if ($projects->isEmpty()) {
    //         return response()->json([
    //             'message' => 'No projects found for your department',
    //             'inspector' => $inspectorDetails,
    //             'data' => []
    //         ], 200);
    //     }

    //     return response()->json([
    //         'message' => 'Assigned projects retrieved successfully',
    //         'inspector' => $inspectorDetails,
    //         'data' => $projects
    //     ], 200);
    // }
public function index(Request $request)
{
    $inspector = Auth::guard('api')->user();

    if (!$inspector) {
        return response()->json(['message' => 'Unauthorized'], 401);
    }

    $inspectorDepartmentId = $inspector->department;
    $inspectorId = (string) $inspector->id;

    // Fetch projects with relationships
    $projects = WebsiteBuilderProject::with([
        'inspector' => function ($query) {
            $query->select('id', 'first_name', 'last_name', 'email', 'phone', 'designation', 'department', 'address', 'status', 'inspector_id', 'image');
        }, 
        'inspector.role' => function ($query) {
            $query->select('id', 'name', 'description', 'is_active');
        },
        'architectCategory' => function ($query) {
            $query->select('id', 'name');
        },
        'builderSubCategory' => function ($query) {
            $query->select('id', 'sub_category_name');
        },
        'permitNumbers' => function ($query) {
            $query->select('id', 'website_builder_project_id', 'permit_number');
        },
        'departmentPermitNumbers' => function ($query) use ($inspectorDepartmentId) {
            $query->where('mcd_staff_role_id', $inspectorDepartmentId)
                  ->select('id', 'website_builder_project_id', 'permit_number');
        },
        'physicalInspectionReports' => function ($query) use ($inspectorId) {
            $query->whereJsonContains('inspectors', $inspectorId)
                  ->select('id', 'project_id', 'report_status', 'inspectors', 'inspection_done_at')
                  ->latest('inspection_done_at');
        }
    ])
    ->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode($inspectorDepartmentId)])
    ->get();

    // Map report_status values to labels
    $statusMap = [
        0 => 'Approved',
        1 => 'Violation',
        2 => 'Pending'
    ];

    // Filter approved projects
    $approvedProjects = [];

    foreach ($projects as $project) {
        // Process department_trade
        $departmentIds = is_array($project->department_trade)
            ? $project->department_trade
            : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
        
        $departmentIds = array_filter($departmentIds, 'is_numeric');
        $project->department_names = \App\Models\McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();

        $project->architect_category_name = $project->architectCategory->name ?? null;
        $project->builder_sub_category_name = $project->builderSubCategory->sub_category_name ?? null;
        $project->PUID = $project->permitNumbers->pluck('permit_number')->toArray();
        $project->department_PermitNumber = $project->departmentPermitNumbers->pluck('permit_number')->toArray();

        if ($project->physicalInspectionReports->isNotEmpty()) {
            $latestReport = $project->physicalInspectionReports->first();
            $project->status = $statusMap[$latestReport->report_status] ?? $project->status;
        } else {
            $project->status = 'Pending';
        }

        // Fetch PDFs and images from project_attachment table
        $departmentPdfs = DB::table('project_attachment')
            ->where('project_id', $project->id)
            ->where('trade_id', $inspectorDepartmentId)
            ->whereIn('file_type', [
                'full_plan_set',
                'site_plan',
                'structural_calculations',
                'engineering_reports',
                'energy_calculations',
                'special_certifications'
            ])
            ->select('file_path', 'file_type', 'created_at', 'updated_at')
            ->orderBy('created_at', 'desc')
            ->get();

        $projectImages = DB::table('project_attachment')
            ->where('project_id', $project->id)
            ->whereNull('trade_id')
            ->where('file_type', 'project_image')
            ->select('file_path', 'file_type', 'created_at', 'updated_at')
            ->orderBy('created_at', 'desc')
            ->get();

        $allDocuments = $departmentPdfs->merge($projectImages);

        foreach ($allDocuments as $doc) {
            $doc->file_type_display = ucwords(str_replace('_', ' ', $doc->file_type));
        }

        $project->documents = $allDocuments;

        // Clean up unused fields
        unset($project->architect_category_id);
        unset($project->builder_sub_category_id);
        unset($project->architectCategory);
        unset($project->builderSubCategory);
        unset($project->permitNumbers);
        unset($project->departmentPermitNumbers);
        unset($project->physicalInspectionReports);

        // Check if all department review steps are approved
        $allDepartmentStepsApproved = true;
        foreach ($departmentIds as $deptId) {
            $departmentModel = \App\Models\McdStaffRole::find($deptId);
            if (!$departmentModel) {
                \Log::warning("Department ID {$deptId} not found for project ID {$project->id}");
                continue;
            }

            $reviewProcess = \App\Models\ReviewProcess::where('review_level', 'Department')
                ->whereJsonContains('department', $departmentModel->name)
                ->first();

            if (!$reviewProcess || empty($reviewProcess->review_steps)) {
                \Log::warning("No Department review process or steps for department {$departmentModel->name}, project ID {$project->id}");
                $allDepartmentStepsApproved = false;
                break;
            }

            // Fetch all review documents for this department in one query
            $docs = \App\Models\ProjectReviewDocument::where('project_id', $project->id)
                ->where('department_id', $deptId)
                ->whereIn('review_step', $reviewProcess->review_steps)
                ->get();

            if (count($reviewProcess->review_steps) !== $docs->count() || !$docs->every(fn($doc) => $doc->status === 2)) {
                \Log::info("Not all Department steps approved for project ID {$project->id}, department ID {$deptId}");
                $allDepartmentStepsApproved = false;
                break;
            }
        }

        // Check if all Planning Commission review steps are approved
        $allPlanningCommissionStepsApproved = true;
        $pcReviewProcess = \App\Models\ReviewProcess::where('review_level', 'PlanningCommission')
            ->first();

        if ($pcReviewProcess && !empty($pcReviewProcess->review_steps)) {
            $pcDepartments = is_array($pcReviewProcess->department)
                ? $pcReviewProcess->department
                : json_decode($pcReviewProcess->department, true) ?? [];

            foreach ($pcDepartments as $pcDeptName) {
                $pcDeptModel = \App\Models\McdStaffRole::where('name', $pcDeptName)->first();
                if (!$pcDeptModel) {
                    \Log::warning("Planning Commission department {$pcDeptName} not found for project ID {$project->id}");
                    continue;
                }

                $pcDocs = \App\Models\ProjectReviewDocumentPc::where('project_id', $project->id)
                    ->where('department_id', $pcDeptModel->id)
                    ->whereIn('review_step', $pcReviewProcess->review_steps)
                    ->get();

                if (count($pcReviewProcess->review_steps) !== $pcDocs->count() || !$pcDocs->every(fn($doc) => $doc->status === 2)) {
                    \Log::info("Not all Planning Commission steps approved for project ID {$project->id}, department {$pcDeptName}");
                    $allPlanningCommissionStepsApproved = false;
                    break;
                }
            }
        }

        // Include project if all steps from both reviews are approved
        if ($allDepartmentStepsApproved && $allPlanningCommissionStepsApproved) {
            $approvedProjects[] = $project;
        }
    }

    $inspectorDetails = [
        'id' => $inspector->id,
        'first_name' => $inspector->first_name,
        'last_name' => $inspector->last_name,
        'email' => $inspector->email,
        'phone' => $inspector->phone,
        'designation' => $inspector->designation,
        'department' => $inspector->department,
        'address' => $inspector->address,
        'status' => $inspector->status,
        'inspector_id' => $inspector->inspector_id,
        'image' => $inspector->image,
        'role' => $inspector->role ? [
            'id' => $inspector->role->id,
            'name' => $inspector->role->name,
            'description' => $inspector->role->description,
            'is_active' => $inspector->role->is_active,
        ] : null,
    ];

    if (empty($approvedProjects)) {
        return response()->json([
            'message' => 'No approved projects found for your department',
            'inspector' => $inspectorDetails,
            'data' => []
        ], 200);
    }

    return response()->json([
        'message' => 'Approved projects retrieved successfully',
        'inspector' => $inspectorDetails,
        'data' => $approvedProjects
    ], 200);
}
    // public function show(Request $request, $id)
    // {
    //     $inspector = Auth::guard('api')->user();

    //     if (!$inspector) {
    //         return response()->json(['message' => 'Unauthorized'], 401);
    //     }

    //     $inspectorDepartmentId = $inspector->department;
    //     $inspectorId = (string) $inspector->id;

    //     // Map report_status values to labels
    //     $statusMap = [
    //         0 => 'Approved',
    //         1 => 'Violation',
    //         2 => 'Pending'
    //     ];

    //     $project = WebsiteBuilderProject::with([
    //         'inspector' => function ($query) {
    //             $query->select('id', 'first_name', 'last_name', 'email', 'phone', 'designation', 'department', 'address', 'status', 'inspector_id', 'image');
    //         },
    //         'inspector.role' => function ($query) {
    //             $query->select('id', 'name', 'description', 'is_active');
    //         },
    //         'architectCategory' => function ($query) {
    //             $query->select('id', 'name');
    //         },
    //         'builderSubCategory' => function ($query) {
    //             $query->select('id', 'sub_category_name');
    //         },
    //         'permitNumbers' => function ($query) {
    //             $query->select('id', 'website_builder_project_id', 'permit_number');
    //         },
    //         'departmentPermitNumbers' => function ($query) use ($inspectorDepartmentId) {
    //             $query->where('mcd_staff_role_id', $inspectorDepartmentId)
    //                   ->select('id', 'website_builder_project_id', 'permit_number');
    //         },
    //         'physicalInspectionReports' => function ($query) use ($inspectorId) {
    //             $query->whereJsonContains('inspectors', $inspectorId)
    //                   ->select('id', 'project_id', 'report_status', 'inspection_done_at', 'inspectors')
    //                   ->latest('inspection_done_at');
    //         }
    //     ])
    //     ->where('id', $id)
    //     ->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$inspectorDepartmentId)])
    //     ->first();

    //     if (!$project) {
    //         return response()->json([
    //             'message' => 'Project not found or not assigned to your department',
    //             'inspector' => [
    //                 'id' => $inspector->id,
    //                 'first_name' => $inspector->first_name,
    //                 'last_name' => $inspector->last_name,
    //                 'email' => $inspector->email,
    //                 'phone' => $inspector->phone,
    //                 'designation' => $inspector->designation,
    //                 'department' => $inspector->department,
    //                 'address' => $inspector->address,
    //                 'status' => $inspector->status,
    //                 'inspector_id' => $inspector->inspector_id,
    //                 'image' => $inspector->image,
    //                 'role' => $inspector->role ? [
    //                     'id' => $inspector->role->id,
    //                     'name' => $inspector->role->name,
    //                     'description' => $inspector->role->description,
    //                     'is_active' => $inspector->role->is_active,
    //                 ] : null,
    //             ],
    //             'data' => null
    //         ], 404);
    //     }

    //     // Process department names
    //     $departmentIds = is_array($project->department_trade)
    //         ? $project->department_trade
    //         : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
    //     $departmentIds = array_filter($departmentIds, 'is_numeric');
    //     $project->department_names = McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();

    //     // Transform categories
    //     $project->architect_category_name = $project->architectCategory->name ?? null;
    //     $project->builder_sub_category_name = $project->builderSubCategory->sub_category_name ?? null;

    //     // Permit Numbers
    //     $project->PUID = $project->permitNumbers->pluck('permit_number')->toArray();
    //     $project->department_PermitNumber = $project->departmentPermitNumbers->pluck('permit_number')->toArray();

    //     // Assign status based on the logged-in inspector's report
    //     if ($project->physicalInspectionReports->isNotEmpty()) {
    //         $latestReport = $project->physicalInspectionReports->first();
    //         $project->status = $statusMap[$latestReport->report_status] ?? $project->status;
    //     } else {
    //         $project->status = $statusMap[$project->status] ?? $project->status;
    //     }

    //     // Fetch department name for the inspector
    //     $departmentModel = McdStaffRole::where('id', $inspectorDepartmentId)->first();
    //     $departmentName = $departmentModel->name ?? null;

    //     // Fetch ReviewProcess
    //     $reviewProcess = \App\Models\ReviewProcess::where('review_level', 'Department')
    //         ->whereJsonContains('department', $departmentName)
    //         ->first();

    //     // Safety check for review process
    //     if (!$reviewProcess || empty($reviewProcess->review_steps)) {
    //         return response()->json([
    //             'message' => 'Review steps not found',
    //             'inspector' => [
    //                 'id' => $inspector->id,
    //                 'first_name' => $inspector->first_name,
    //                 'last_name' => $inspector->last_name,
    //                 'email' => $inspector->email,
    //                 'phone' => $inspector->phone,
    //                 'designation' => $inspector->designation,
    //                 'department' => $inspector->department,
    //                 'address' => $inspector->address,
    //                 'status' => $inspector->status,
    //                 'inspector_id' => $inspector->inspector_id,
    //                 'image' => $inspector->image,
    //                 'role' => $inspector->role ? [
    //                     'id' => $inspector->role->id,
    //                     'name' => $inspector->role->name,
    //                     'description' => $inspector->role->description,
    //                     'is_active' => $inspector->role->is_active,
    //                 ] : null,
    //             ],
    //             'data' => null
    //         ], 404);
    //     }

    //     // Loop all steps and get remarks
    //     $stepRemarks = [];
    //     foreach ($reviewProcess->review_steps as $reviewStep) {
    //         $doc = ProjectReviewDocument::where('project_id', $project->id)
    //             ->where('department_id', $inspectorDepartmentId)
    //             ->whereRaw('TRIM(LOWER(review_step)) = ?', [strtolower(trim($reviewStep))])
    //             ->latest()
    //             ->first();

    //         $stepRemarks[] = [
    //             'step' => $reviewStep,
    //             'remarks' => $doc->remarks ?? '',
    //             'status' => match ($doc->status ?? null) {
    //                 0 => 'Pending',
    //                 1 => 'Violation',
    //                 2 => 'Approved',
    //                 3 => 'Rejected',
    //                 default => 'Not Started',
    //             },
    //             'attachment' => $doc->file_path ?? null,
    //         ];
    //     }

    //     // Fetch annotated PDFs for this project and department
    //     $annotatedPdfs = AnnotatedPdf::where('project_id', $project->id)
    //         ->where('department_id', $inspectorDepartmentId)
    //         ->orderBy('created_at', 'desc')
    //         ->get();

    //     // Transform annotated PDFs to include only desired fields
    //     $transformedPdfs = $annotatedPdfs->map(function ($pdf) {
    //         $uploaderName = 'Unknown User';
    //         $uploaderRoleDisplay = $pdf->uploaded_by_role ?? 'Unknown Role';

    //         if ($pdf->uploaded_by && $pdf->uploaded_by_role) {
    //             if (str_contains(strtolower($pdf->uploaded_by_role), 'mcd') ||
    //                 str_contains(strtolower($pdf->uploaded_by_role), 'staff') ||
    //                 str_contains(strtolower($pdf->uploaded_by_role), 'manager') ||
    //                 str_contains(strtolower($pdf->uploaded_by_role), 'admin')) {
    //                 $mcdUser = \App\Models\McdStaff::find($pdf->uploaded_by);
    //                 if ($mcdUser) {
    //                     $uploaderName = $mcdUser->staff_name ?? 'MCD Staff';
    //                 }
    //             } elseif (str_contains(strtolower($pdf->uploaded_by_role), 'builder')) {
    //                 $builderUser = \App\Models\BuilderStaff::find($pdf->uploaded_by);
    //                 if ($builderUser) {
    //                     $uploaderName = $builderUser->staff_name ?? 'Builder Staff';
    //                 }
    //             } else {
    //                 $webUser = \App\Models\User::find($pdf->uploaded_by);
    //                 if ($webUser) {
    //                     $uploaderName = $webUser->name ?? 'Admin User';
    //                 }
    //             }
    //         }

    //         return [
    //             'id' => $pdf->id,
    //             'project_id' => $pdf->project_id,
    //             'department_id' => $pdf->department_id,
    //             'review_step' => $pdf->review_step,
    //             'file_path' => $pdf->file_path,
    //             'created_at' => $pdf->created_at,
    //             'updated_at' => $pdf->updated_at,
    //             'uploader_name' => $uploaderName,
    //             'uploader_role_display' => $uploaderRoleDisplay,
    //         ];
    //     });

    //     // Fetch PDFs and images from project_attachment table
    //     $departmentPdfs = DB::table('project_attachment')
    //         ->where('project_id', $project->id)
    //         ->where('trade_id', $inspectorDepartmentId)
    //         ->whereIn('file_type', [
    //             'full_plan_set',
    //             'site_plan',
    //             'structural_calculations',
    //             'engineering_reports',
    //             'energy_calculations',
    //             'special_certifications'
    //         ])
    //         ->select('file_path', 'file_type', 'created_at', 'updated_at')
    //         ->orderBy('created_at', 'desc')
    //         ->get();

    //     // Fetch project images (trade_id is NULL)
    //     $projectImages = DB::table('project_attachment')
    //         ->where('project_id', $project->id)
    //         ->whereNull('trade_id')
    //         ->where('file_type', 'project_image')
    //         ->select('file_path', 'file_type', 'created_at', 'updated_at')
    //         ->orderBy('created_at', 'desc')
    //         ->get();

    //     // Combine department PDFs and project images
    //     $allDocuments = $departmentPdfs->merge($projectImages);

    //     // Add display name for file types
    //     foreach ($allDocuments as $doc) {
    //         $doc->file_type_display = ucwords(str_replace('_', ' ', $doc->file_type));
    //     }

    //     // Clean up unused fields
    //     unset($project->architect_category_id);
    //     unset($project->builder_sub_category_id);
    //     unset($project->architectCategory);
    //     unset($project->builderSubCategory);
    //     unset($project->permitNumbers);
    //     unset($project->departmentPermitNumbers);
    //     unset($project->physicalInspectionReports);

    //     // Inspector details
    //     $inspectorDetails = [
    //         'id' => $inspector->id,
    //         'first_name' => $inspector->first_name,
    //         'last_name' => $inspector->last_name,
    //         'email' => $inspector->email,
    //         'phone' => $inspector->phone,
    //         'designation' => $inspector->designation,
    //         'department' => $inspector->department,
    //         'address' => $inspector->address,
    //         'status' => $inspector->status,
    //         'inspector_id' => $inspector->inspector_id,
    //         'image' => $inspector->image,
    //         'role' => $inspector->role ? [
    //             'id' => $inspector->role->id,
    //             'name' => $inspector->role->name,
    //             'description' => $inspector->role->description,
    //             'is_active' => $inspector->role->is_active,
    //         ] : null,
    //     ];

    //     return response()->json([
    //         'message' => 'Project details retrieved successfully',
    //         'inspector' => $inspectorDetails,
    //         'data' => [
    //             'project' => $project,
    //             'step_remarks' => $stepRemarks,
    //             'annotated_pdfs' => $transformedPdfs,
    //             'documents' => $allDocuments
    //         ]
    //     ], 200);
    // }
    public function show(Request $request, $id)
{
    $inspector = Auth::guard('api')->user();

    if (!$inspector) {
        return response()->json(['message' => 'Unauthorized'], 401);
    }

    $inspectorDepartmentId = $inspector->department;
    $inspectorId = (string) $inspector->id;

    // Map report_status values to labels
    $statusMap = [
        0 => 'Approved',
        1 => 'Violation',
        2 => 'Pending'
    ];

    $project = WebsiteBuilderProject::with([
        'inspector' => function ($query) {
            $query->select('id', 'first_name', 'last_name', 'email', 'phone', 'designation', 'department', 'address', 'status', 'inspector_id', 'image');
        },
        'inspector.role' => function ($query) {
            $query->select('id', 'name', 'description', 'is_active');
        },
        'architectCategory' => function ($query) {
            $query->select('id', 'name');
        },
        'builderSubCategory' => function ($query) {
            $query->select('id', 'sub_category_name');
        },
        'permitNumbers' => function ($query) {
            $query->select('id', 'website_builder_project_id', 'permit_number');
        },
        'departmentPermitNumbers' => function ($query) use ($inspectorDepartmentId) {
            $query->where('mcd_staff_role_id', $inspectorDepartmentId)
                  ->select('id', 'website_builder_project_id', 'permit_number');
        },
        'physicalInspectionReports' => function ($query) use ($inspectorId) {
            $query->whereJsonContains('inspectors', $inspectorId)
                  ->select('id', 'project_id', 'report_status', 'inspection_done_at', 'inspectors')
                  ->latest('inspection_done_at');
        }
    ])
    ->where('id', $id)
    ->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$inspectorDepartmentId)])
    ->first();

    if (!$project) {
        return response()->json([
            'message' => 'Project not found or not assigned to your department',
            'inspector' => [
                'id' => $inspector->id,
                'first_name' => $inspector->first_name,
                'last_name' => $inspector->last_name,
                'email' => $inspector->email,
                'phone' => $inspector->phone,
                'designation' => $inspector->designation,
                'department' => $inspector->department,
                'address' => $inspector->address,
                'status' => $inspector->status,
                'inspector_id' => $inspector->inspector_id,
                'image' => $inspector->image,
                'role' => $inspector->role ? [
                    'id' => $inspector->role->id,
                    'name' => $inspector->role->name,
                    'description' => $inspector->role->description,
                    'is_active' => $inspector->role->is_active,
                ] : null,
            ],
            'data' => null
        ], 404);
    }

    // Process department names
    $departmentIds = is_array($project->department_trade)
        ? $project->department_trade
        : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
    $departmentIds = array_filter($departmentIds, 'is_numeric');
    $project->department_names = McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();

    // Transform categories
    $project->architect_category_name = $project->architectCategory->name ?? null;
    $project->builder_sub_category_name = $project->builderSubCategory->sub_category_name ?? null;

    // Permit Numbers
    $project->PUID = $project->permitNumbers->pluck('permit_number')->toArray();
    $project->department_PermitNumber = $project->departmentPermitNumbers->pluck('permit_number')->toArray();

    // Assign status based on the logged-in inspector's report
    if ($project->physicalInspectionReports->isNotEmpty()) {
        $latestReport = $project->physicalInspectionReports->first();
        $project->status = $statusMap[$latestReport->report_status] ?? $project->status;
    } else {
        $project->status = $statusMap[$project->status] ?? $project->status;
    }

    // Fetch department name for the inspector
    $departmentModel = McdStaffRole::where('id', $inspectorDepartmentId)->first();
    $departmentName = $departmentModel->name ?? null;

    // Fetch ReviewProcess
    $reviewProcess = \App\Models\ReviewProcess::where('review_level', 'Department')
        ->whereJsonContains('department', $departmentName)
        ->first();

    // Safety check for review process
    if (!$reviewProcess || empty($reviewProcess->review_steps)) {
        return response()->json([
            'message' => 'Review steps not found',
            'inspector' => [
                'id' => $inspector->id,
                'first_name' => $inspector->first_name,
                'last_name' => $inspector->last_name,
                'email' => $inspector->email,
                'phone' => $inspector->phone,
                'designation' => $inspector->designation,
                'department' => $inspector->department,
                'address' => $inspector->address,
                'status' => $inspector->status,
                'inspector_id' => $inspector->inspector_id,
                'image' => $inspector->image,
                'role' => $inspector->role ? [
                    'id' => $inspector->role->id,
                    'name' => $inspector->role->name,
                    'description' => $inspector->role->description,
                    'is_active' => $inspector->role->is_active,
                ] : null,
            ],
            'data' => null
        ], 404);
    }

    // Loop all steps and get remarks
    $stepRemarks = [];
    foreach ($reviewProcess->review_steps as $reviewStep) {
        $doc = ProjectReviewDocument::where('project_id', $project->id)
            ->where('department_id', $inspectorDepartmentId)
            ->whereRaw('TRIM(LOWER(review_step)) = ?', [strtolower(trim($reviewStep))])
            ->latest()
            ->first();

        $stepRemarks[] = [
            'step' => $reviewStep,
            'remarks' => $doc->remarks ?? '',
            'status' => match ($doc->status ?? null) {
                0 => 'Pending',
                1 => 'Violation',
                2 => 'Approved',
                3 => 'Rejected',
                default => 'Not Started',
            },
            'attachment' => $doc->file_path ?? null,
        ];
    }

    // Fetch annotated PDFs for this project and department
    $annotatedPdfs = AnnotatedPdf::where('project_id', $project->id)
        ->where('department_id', $inspectorDepartmentId)
        ->orderBy('created_at', 'desc')
        ->get();

    // Transform annotated PDFs to include only desired fields
    $transformedPdfs = $annotatedPdfs->map(function ($pdf) {
        $uploaderName = 'Unknown User';
        $uploaderRoleDisplay = $pdf->uploaded_by_role ?? 'Unknown Role';

        if ($pdf->uploaded_by && $pdf->uploaded_by_role) {
            if (str_contains(strtolower($pdf->uploaded_by_role), 'mcd') ||
                str_contains(strtolower($pdf->uploaded_by_role), 'staff') ||
                str_contains(strtolower($pdf->uploaded_by_role), 'manager') ||
                str_contains(strtolower($pdf->uploaded_by_role), 'admin')) {
                $mcdUser = \App\Models\McdStaff::find($pdf->uploaded_by);
                if ($mcdUser) {
                    $uploaderName = $mcdUser->staff_name ?? 'MCD Staff';
                }
            } elseif (str_contains(strtolower($pdf->uploaded_by_role), 'builder')) {
                $builderUser = \App\Models\BuilderStaff::find($pdf->uploaded_by);
                if ($builderUser) {
                    $uploaderName = $builderUser->staff_name ?? 'Builder Staff';
                }
            } else {
                $webUser = \App\Models\User::find($pdf->uploaded_by);
                if ($webUser) {
                    $uploaderName = $webUser->name ?? 'Admin User';
                }
            }
        }

        return [
            'id' => $pdf->id,
            'project_id' => $pdf->project_id,
            'department_id' => $pdf->department_id,
            'review_step' => $pdf->review_step,
            'file_path' => $pdf->annotated_pdf_path, // Use annotated_pdf_path instead of file_path
            'created_at' => $pdf->created_at,
            'updated_at' => $pdf->updated_at,
            'uploader_name' => $uploaderName,
            'uploader_role_display' => $uploaderRoleDisplay,
        ];
    });

    // Fetch PDFs and images from project_attachment table
    $departmentPdfs = DB::table('project_attachment')
        ->where('project_id', $project->id)
        ->where('trade_id', $inspectorDepartmentId)
        ->whereIn('file_type', [
            'full_plan_set',
            'site_plan',
            'structural_calculations',
            'engineering_reports',
            'energy_calculations',
            'special_certifications'
        ])
        ->select('file_path', 'file_type', 'created_at', 'updated_at')
        ->orderBy('created_at', 'desc')
        ->get();

    // Fetch project images (trade_id is NULL)
    $projectImages = DB::table('project_attachment')
        ->where('project_id', $project->id)
        ->whereNull('trade_id')
        ->where('file_type', 'project_image')
        ->select('file_path', 'file_type', 'created_at', 'updated_at')
        ->orderBy('created_at', 'desc')
        ->get();

    // Combine department PDFs and project images
    $allDocuments = $departmentPdfs->merge($projectImages);

    // Add display name for file types
    foreach ($allDocuments as $doc) {
        $doc->file_type_display = ucwords(str_replace('_', ' ', $doc->file_type));
    }

    // Clean up unused fields
    unset($project->architect_category_id);
    unset($project->builder_sub_category_id);
    unset($project->architectCategory);
    unset($project->builderSubCategory);
    unset($project->permitNumbers);
    unset($project->departmentPermitNumbers);
    unset($project->physicalInspectionReports);

    // Inspector details
    $inspectorDetails = [
        'id' => $inspector->id,
        'first_name' => $inspector->first_name,
        'last_name' => $inspector->last_name,
        'email' => $inspector->email,
        'phone' => $inspector->phone,
        'designation' => $inspector->designation,
        'department' => $inspector->department,
        'address' => $inspector->address,
        'status' => $inspector->status,
        'inspector_id' => $inspector->inspector_id,
        'image' => $inspector->image,
        'role' => $inspector->role ? [
            'id' => $inspector->role->id,
            'name' => $inspector->role->name,
            'description' => $inspector->role->description,
            'is_active' => $inspector->role->is_active,
        ] : null,
    ];

    return response()->json([
        'message' => 'Project details retrieved successfully',
        'inspector' => $inspectorDetails,
        'data' => [
            'project' => $project,
            'step_remarks' => $stepRemarks,
            'annotated_pdfs' => $transformedPdfs,
            'documents' => $allDocuments
        ]
    ], 200);
}

    public function count(Request $request)
    {
        $inspector = Auth::guard('api')->user();

        if (!$inspector) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }

        $inspectorDepartmentId = $inspector->department;

        $projectCount = WebsiteBuilderProject::whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$inspectorDepartmentId)])
            ->count();

        // Prepare inspector details
        $inspectorDetails = [
            'id' => $inspector->id,
            'first_name' => $inspector->first_name,
            'last_name' => $inspector->last_name,
            'email' => $inspector->email,
            'phone' => $inspector->phone,
            'designation' => $inspector->designation,
            'department' => $inspector->department,
            'address' => $inspector->address,
            'status' => $inspector->status,
            'inspector_id' => $inspector->inspector_id,
            'image' => $inspector->image,
            'role' => $inspector->role ? [
                'id' => $inspector->role->id,
                'name' => $inspector->role->name,
                'description' => $inspector->role->description,
                'is_active' => $inspector->role->is_active,
            ] : null,
        ];

        return response()->json([
            'message' => 'Project count retrieved successfully',
            'inspector' => $inspectorDetails,
            'count' => (int)$projectCount
        ], 200);
    }
}