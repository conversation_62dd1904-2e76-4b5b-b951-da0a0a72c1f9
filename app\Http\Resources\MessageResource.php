<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\User;
use App\Models\Inspector;

class MessageResource extends JsonResource
{
    public function toArray($request)
    {
        // Determine sender details
        $sender = $this->sender_id
            ? User::find($this->sender_id) // Sender is a User
            : ($this->sender_id_inspector ? Inspector::find($this->sender_id_inspector) : null); // Sender is an Inspector

        // Determine receiver details
        $receiver = $this->receiver_id
            ? User::find($this->receiver_id) // Receiver is a User
            : ($this->receiver_id_inspector ? Inspector::find($this->receiver_id_inspector) : null); // Receiver is an Inspector

        return [
            'id' => $this->id,
            'subject' => $this->getSubjectAttribute(),
            'body' => $this->getContentAttribute(),
            'sender' => $this->formatSenderReceiver($sender),
            'receiver' => $this->formatSenderReceiver($receiver),
            'attachments' => $this->whenLoaded('attachments', function () {
                return $this->getAttachmentsAttribute();
            }, []),
            'status' => $this->status,
            'is_starred' => $this->is_starred,
            'created_at' => $this->created_at->toDateTimeString(),
            'updated_at' => $this->updated_at->toDateTimeString(),
            'deleted_at' => $this->deleted_at ? $this->deleted_at->toDateTimeString() : null,
        ];
    }

    /**
     * Format sender or receiver details based on whether they are a User or Inspector
     *
     * @param mixed $entity
     * @return array|null
     */
    private function formatSenderReceiver($entity)
    {
        if (!$entity) {
            return null;
        }

        if ($entity instanceof User) {
            return [
                'type' => 'user',
                'id' => $entity->id,
                'name' => $entity->name,
                'email' => $entity->email,
                'profile_image' => $entity->profile_image,
                'email_verified_at' => $entity->email_verified_at ? $entity->email_verified_at->toDateTimeString() : null,
                'role_id' => $entity->role_id,
                'created_at' => $entity->created_at->toDateTimeString(),
                'updated_at' => $entity->updated_at->toDateTimeString(),
            ];
        }

       if ($entity instanceof Inspector) {
    return [
        'type' => 'inspector',
        'id' => $entity->id,
        'name' => trim($entity->first_name . ' ' . $entity->last_name),
        'email' => $entity->email,
        'phone' => $entity->phone,
        'image' => $entity->image,
        'inspector_id' => $entity->inspector_id,
        'designation' => $entity->designation,
        'department' => $entity->department,
        'address' => $entity->address,
        'status' => $entity->status,
        'created_at' => $entity->created_at->toDateTimeString(),
        'updated_at' => $entity->updated_at->toDateTimeString(),
    ];
}

        return null;
    }
}