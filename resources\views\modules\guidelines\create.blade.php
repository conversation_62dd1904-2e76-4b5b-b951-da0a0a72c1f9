@extends('mcdpanel.layouts.master')

@section('content')
<div class="main-content">
    <section class="section">
        <div class="section-body">
            <div class="row">
                <div class="col-12 col-md-12 col-lg-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4>Guidelines > Create</h4>
                        </div>

                        <form action="{{ route('guidelines.store') }}" method="POST">
                            @csrf
                            <div class="card-body">
                                <!-- Basic Details -->
                                <h5 class="section-title">📋 Guideline Details</h5>
                                <hr>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Guideline Type<span class="text-danger">*</span></label>
                                            <select name="type" id="type" class="form-control @error('type') is-invalid @enderror" required>
                                                <option value="">Select Type</option>
                                                <option value="Fire Safety" {{ old('type') == 'Fire Safety' ? 'selected' : '' }}>Fire Safety</option>
                                                <option value="Structural" {{ old('type') == 'Structural' ? 'selected' : '' }}>Structural</option>
                                                <option value="Zoning Laws" {{ old('type') == 'Zoning Laws' ? 'selected' : '' }}>Zoning Laws</option>
                                            </select>
                                            @error('type')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Title<span class="text-danger">*</span></label>
                                            <input type="text" name="title" id="title" class="form-control @error('title') is-invalid @enderror" value="{{ old('title') }}" required>
                                            @error('title')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label>Description<span class="text-danger">*</span></label>
                                            <textarea name="description" id="description" class="form-control @error('description') is-invalid @enderror" required>{{ old('description') }}</textarea>
                                            @error('description')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Date & Time -->
                                <h5 class="section-title">📅 Date & Time</h5>
                                <hr>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Date & Time<span class="text-danger">*</span></label>
                                            <input type="datetime-local" name="date_time" id="date_time" class="form-control @error('date_time') is-invalid @enderror" value="{{ old('date_time') }}" required>
                                            @error('date_time')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="card-footer text-right">
                                <button type="submit" class="btn btn-success mr-1">Save</button>
                                <a href="{{ route('guidelines.index') }}" class="btn btn-secondary">Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Custom Styling -->
    <style>
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
    </style>
@endsection