@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <h2 class="page-title">Physical Inspection Report</h2>

                    <!-- Top Buttons -->
                    <div class="mt-2 mb-2 d-flex justify-content-end">
                        <a href="{{ route('physicalinspection.download', $project->id) }}" class="btn btn-success me-2">
                            <i class="fas fa-download"></i> Download Report
                        </a>
                        <a href="{{ route('physicalinspection.report') }}" class="btn btn-primary">Back to Reports</a>
                    </div>

                    <!-- Project Details -->
                    <div class="card mb-2">
                        <div class="card-header">
                            <h2 class="card-title">Project Details</h2>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Project Name:</strong> {{ $project->project_name }}</p>
                                    <p><strong>Address:</strong> {{ $project->full_address }}</p>
                                    <p><strong>Project Status:</strong> {{ $project->status }}</p>
                                    <p><strong>Departments:</strong> {{ implode(', ', $project->department_names) }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Scope of Work:</strong> {{ $project->scope_of_work ?? 'N/A' }}</p>
                                    <p><strong>Expected Start Date:</strong> {{ $project->expected_start_date ?? 'N/A' }}
                                    </p>
                                    <p><strong>Expected End Date:</strong> {{ $project->expected_end_date ?? 'N/A' }}</p>
                                    <p><strong>Property Owner:</strong> {{ $project->property_owner_name ?? 'N/A' }}</p>
                                </div>
                            </div>
                            @if ($project->project_image)
                                <div class="mt-3">
                                    <p><strong>Project Image:</strong></p>
                                    <img src="{{ url('public' . $project->project_image) }}" alt="Project Image"
                                        class="img-fluid" style="max-width: 200px;">
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Inspection Reports -->
                    @if ($inspectionReports->isNotEmpty())
                        <div class="accordion" id="inspectionReportsAccordion">
                            @foreach ($inspectionReports as $index => $report)
                                <div class="card mb-2">
                                    <div class="card-header" id="heading{{ $index }}">
                                        <h2 class="card-title mb-0">
                                            <button class="btn btn-link text-decoration-none" type="button"
                                                data-bs-toggle="collapse" data-bs-target="#collapse{{ $index }}"
                                                aria-expanded="{{ $index === 0 ? 'true' : 'false' }}"
                                                aria-controls="collapse{{ $index }}">
                                                Physical Inspection Report #{{ $index + 1 }} (
                                                @foreach ($report->inspector_data as $inspector)
                                                    Inspector:{{ $inspector['name'] }} |
                                                    Department:{{ $inspector['department'] }}@if (!$loop->last)
                                                        ,
                                                    @endif
                                                @endforeach
                                                )
                                            </button>

                                        </h2>
                                    </div>
                                    <div id="collapse{{ $index }}"
                                        class="collapse {{ $index === 0 ? 'show' : '' }}"
                                        aria-labelledby="heading{{ $index }}">
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <p><strong>Departments:</strong>
                                                        @foreach ($report->inspector_data as $inspector)
                                                            {{ $inspector['department'] }}@if (!$loop->last)
                                                                ,
                                                            @endif
                                                        @endforeach
                                                    </p>
                                                    <p><strong>Inspectors:</strong>
                                                        @foreach ($report->inspector_data as $inspector)
                                                            {{ $inspector['name'] }} @if (!$loop->last)
                                                                ,
                                                            @endif
                                                        @endforeach
                                                    </p>
                                                    <p><strong>Inspection Date:</strong>
                                                        {{ $report->inspection_done_at ? $report->inspection_done_at->format('Y-m-d H:i:s') : 'N/A' }}
                                                    </p>
                                                    <p><strong>Note:</strong> {{ $report->note ?? 'N/A' }}</p>
                                                    <p><strong>Violation Note:</strong>
                                                        {{ $report->violation_note ?? 'N/A' }}</p>
                                                </div>
                                                <div class="col-md-6">
                                                    @php
                                                        $reportStatus = $report->report_status ?? null;
                                                        switch ($reportStatus) {
                                                            case 0:
                                                                $statusText = 'Approval';
                                                                $badgeClass = 'badge-success';
                                                                break;
                                                            case 1:
                                                                $statusText = 'Violation';
                                                                $badgeClass = 'badge-danger';
                                                                break;
                                                            case 2:
                                                                $statusText = 'Pending';
                                                                $badgeClass = 'badge-warning';
                                                                break;
                                                            default:
                                                                $statusText = 'N/A';
                                                                $badgeClass = 'badge-light';
                                                                break;
                                                        }
                                                    @endphp
                                                    <p>
                                                        <strong>Inspection Report Status:</strong>
                                                        <span class="badge {{ $badgeClass }}">{{ $statusText }}</span>
                                                    </p>
                                                    <p><strong>Potential Violations:</strong>
                                                        {{ implode(', ', $report->potential_violations ?? []) ?: 'None' }}
                                                    </p>
                                                </div>
                                            </div>
                                            @if (!empty($report->report_images))
                                                <div class="mt-3">
                                                    <p><strong>Images:</strong></p>
                                                    <div class="row">
                                                        @foreach ($report->report_images as $image)
                                                            <div class="col-md-3 mb-3">
                                                                <a href="{{ url('/' . $image) }}" target="_blank">
                                                                    <img src="{{ url('/' . $image) }}" alt="Report Image"
                                                                        class="img-fluid rounded">
                                                                </a>
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="card">
                            <div class="card-body">
                                <p class="text-danger">No physical inspection reports available for this project.</p>
                            </div>
                        </div>
                    @endif

                    <!-- Bottom Buttons -->
                    <div class="mt-4 mb-3 d-flex justify-content-end">
                        <a href="{{ route('physicalinspection.download', $project->id) }}" class="btn btn-success me-2">
                            <i class="fas fa-download"></i> Download Report
                        </a>
                        <a href="{{ route('physicalinspection.report') }}" class="btn btn-primary">Back to Reports</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include Bootstrap JS for collapse functionality -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize collapse functionality for each accordion item
            const collapseButtons = document.querySelectorAll('[data-bs-toggle="collapse"]');
            collapseButtons.forEach(button => {
                const targetSelector = button.getAttribute('data-bs-target');
                const target = document.querySelector(targetSelector);
                if (target) {
                    // Initialize Bootstrap Collapse
                    const bsCollapse = new bootstrap.Collapse(target, {
                        toggle: false // Prevent automatic toggling on initialization
                    });
                    // Handle click to toggle collapse
                    button.addEventListener('click', function(event) {
                        event.preventDefault(); // Prevent default behavior
                        if (target.classList.contains('show')) {
                            bsCollapse.hide(); // Close if open
                        } else {
                            bsCollapse.show(); // Open if closed
                        }
                    });
                }
            });
        });
    </script>
@endsection
