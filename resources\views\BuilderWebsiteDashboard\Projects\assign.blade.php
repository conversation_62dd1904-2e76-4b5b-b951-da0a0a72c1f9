@extends('mcdpanel.layouts.master')

@section('content')
<style>
    /* FORCE override dropdown and option styles */
    select.form-control,
    select.form-control option {
        color: #000 !important;
        background-color: #fff !important;
    }

    /* Ensure hover is also visible */
    select.form-control option:hover {
        background-color: #f1f1f1 !important;
        color: #000 !important;
    }

    /* For dark themes that may override borders or appearance */
    select.form-control {
        border: 1px solid #ced4da !important;
        appearance: auto !important;
    }
</style>

<div class="main-content">
<div class="container">
    <h2>Assign Inspector to {{ $project->project_name }}</h2>

    <form action="{{ route('myprojects.assign', $project->id) }}" method="POST">
        @csrf
        <div class="form-group">
            <label for="inspector_id" class="text-dark">Select Inspector</label>
            <select name="inspector_id" id="inspector_id" class="form-control" required>
                <option value="">Select an Inspector</option>
                @foreach($inspectors as $inspector)
                <option value="{{ $inspector->id }}">{{ $inspector->first_name }} {{ $inspector->last_name }}</option>
                @endforeach
            </select>
        </div>

        <div class="form-group mt-3">
            <button type="submit" class="btn btn-primary">Assign Inspector</button>
            <a href="{{ route('myprojects.index') }}" class="btn btn-secondary">Cancel</a>
        </div>
    </form>
</div>
</div>
@endsection
