<!DOCTYPE html>
<html>
<head>
    <title>Inspectors List</title>
    {{-- <link href="{{ asset('css/app.css') }}" rel="stylesheet"> --}}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    @extends('mcdpanel.layouts.master')
@php
    $user = Auth::guard('staff_mcd')->user();
    $permissions = $user && $user->role ? $user->role->permissions : [];
    $isAdmin = Auth::guard('web')->check();
    $hasWritePermission = $isAdmin || (isset($permissions['inspectors']['write']) && $permissions['inspectors']['write'] == 1);
@endphp

    @section('content')
        <div class="main-content">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4>Inspectors Management > Inspectors List</h4>
                            @if ($hasWritePermission)
                                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#inviteModal">
                                    <i class="fa fa-envelope"></i> Invite Inspector
                                </button>
                            @else
                                <button type="button" class="btn btn-primary" onclick="showAccessDenied()">
                                    <i class="fa fa-envelope"></i> Invite Inspector
                                </button>
                            @endif
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="table-1">
                                    <thead>
                                        <tr>
                                            <th class="text-center">Sr No</th>
                                            <th>Name</th>
                                            <th>Email</th>
                                            <th>Phone</th>
                                            <th>Designation</th>
                                            <th>Department</th>
                                            <th>Status</th>
                                            <th class="text-center">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse ($inspectors as $key => $inspector)
                                            <tr>
                                                <td class="text-center">{{ $key + 1 }}</td>
                                                <td>{{ $inspector->first_name }} {{ $inspector->last_name }}</td>
                                                <td>{{ $inspector->email }}</td>
                                                <td>{{ $inspector->phone }}</td>
                                                <td>{{ $inspector->designation }}</td>
                                                <td>{{ $inspector->role->name ?? "N/A" }}</td>
                                                <td>
                                                    <label class="switch">
                                                        <input type="checkbox" class="status-toggle" 
                                                               data-id="{{ $inspector->id }}" 
                                                               {{ $inspector->status == 'active' ? 'checked' : '' }}>
                                                        <span class="slider round"></span>
                                                    </label>
                                                </td>
                                                <td class="text-center">
                                                    @if ($hasWritePermission)
                                                        <a href="{{ route('inspectors.edit', $inspector) }}" 
                                                        class="btn btn-sm btn-primary mr-1" title="Edit">
                                                            <i class="fa fa-edit"></i>
                                                        </a>

                                                        <form action="{{ route('inspectors.destroy', $inspector) }}" 
                                                            method="POST" class="d-inline delete-form">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="btn btn-sm btn-danger delete-btn" 
                                                                    title="Delete" data-name="{{ $inspector->first_name }} {{ $inspector->last_name }}">
                                                                <i class="fa fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    @else
                                                        <button type="button" class="btn btn-sm btn-primary mr-1" title="Edit" onclick="showAccessDenied()">
                                                            <i class="fa fa-edit"></i>
                                                        </button>

                                                         <button type="button" class="btn btn-sm btn-danger" title="Delete" onclick="showAccessDenied()">
                                                            <i class="fa fa-trash"></i>
                                                        </button>
                                                        
                                                        @endif
                                                    {{-- <form action="{{ route('inspectors.destroy', $inspector) }}" 
                                                          method="POST" class="d-inline delete-form">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-danger delete-btn" 
                                                                title="Delete" data-name="{{ $inspector->first_name }} {{ $inspector->last_name }}">
                                                            <i class="fa fa-trash"></i>
                                                        </button>
                                                    </form> --}}
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="8" class="text-center">No inspectors found.</td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Invite Inspector Modal -->
        <div class="modal fade" id="inviteModal" tabindex="-1" role="dialog" aria-labelledby="inviteModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="inviteModalLabel">Invite Inspector</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <form action="{{ route('inspectors.invite') }}" method="POST">
                        @csrf
                        <div class="modal-body">
                            <div class="form-group">
                                <label for="email">Email Address <span class="text-danger">*</span></label>
                                <input type="email" name="email" id="email" class="form-control @error('email') is-invalid @enderror" required>
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="form-group">
                                <label for="message">Optional Message</label>
                                <textarea name="message" id="message" class="form-control @error('message') is-invalid @enderror" rows="4"></textarea>
                                @error('message')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary">Send Invite</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endsection

    @push('style')
        <style>
            /* Toggle Switch Styling */
            .switch {
                position: relative;
                display: inline-block;
                width: 25px;
                height: 14px;
            }
            .switch input {
                opacity: 0;
                width: 0;
                height: 0;
            }
            .slider {
                position: absolute;
                cursor: pointer;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: #dc3545;
                transition: .4s;
                border-radius: 14px;
            }
            .slider:before {
                position: absolute;
                content: "";
                height: 10px;
                width: 10px;
                left: 2px;
                bottom: 2px;
                background-color: white;
                transition: .4s;
                border-radius: 50%;
            }
            input:checked + .slider {
                background-color: #28a745;
            }
            input:checked + .slider:before {
                transform: translateX(11px);
            }
        </style>
    @endpush

    @push('scripts')
    
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Success Message from Session
                @if(session('success'))
                    Swal.fire({
                        title: 'Success!',
                        text: '{{ session('success') }}',
                        icon: 'success',
                        showConfirmButton: false,
                        timer: 4000,
                        timerProgressBar: true,
                        position: 'top-end',
                        toast: true,
                        customClass: {
                            popup: 'swal2-popup-custom',
                            title: 'swal2-title-custom',
                            content: 'swal2-content-custom'
                        }
                    });
                @endif

                // Delete Confirmation
                document.querySelectorAll('.delete-btn').forEach(button => {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        const form = this.closest('form');
                        const inspectorName = this.getAttribute('data-name');

                        Swal.fire({
                            title: 'Are you sure?',
                            text: `You are about to delete "${inspectorName}". This action cannot be undone!`,
                            icon: 'warning',
                            showCancelButton: true,
                            confirmButtonColor: '#d33',
                            cancelButtonColor: '#3085d6',
                            confirmButtonText: 'Yes, delete it!'
                        }).then((result) => {
                            if (result.isConfirmed) {
                                form.submit();
                            }
                        });
                    });
                });

                // Status Toggle Functionality (unchanged)
            });

             function showAccessDenied() {
        Swal.fire({
            icon: 'error',
            title: 'Access Denied',
            text: 'You do not have permission to modify the  Inspectors.'
        });
    }
        </script>
    @endpush
</body>
</html>