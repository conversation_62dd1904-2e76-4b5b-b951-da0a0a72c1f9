<?php

namespace App\Http\Controllers;

use App\Models\ProjectDepartmentPermit;
use App\Models\WebsiteBuilderProject;
use App\Models\McdStaffRole;
use Illuminate\Http\Request;

class AssignPermitController extends Controller
{
    public function index()
    {
        $permits = ProjectDepartmentPermit::with(['project', 'department'])->get();
        return view('AssignPermit.index', compact('permits'));
    }

    public function create()
    {
        $projects = WebsiteBuilderProject::all();
        // dd($projects);
        return view('AssignPermit.create', compact('projects'));
    }

    // public function getProjectDepartments(Request $request, $projectId)
    // {
    //     $project = WebsiteBuilderProject::findOrFail($projectId);
    //     $departmentIds = json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
        
    //     // Get departments that are in department_trade and not already assigned
    //     $assignedDepartmentIds = ProjectDepartmentPermit::where('website_builder_project_id', $projectId)
    //         ->pluck('mcd_staff_role_id')
    //         ->toArray();
        
    //     $availableDepartments = McdStaffRole::whereIn('id', $departmentIds)
    //         ->whereNotIn('id', $assignedDepartmentIds)
    //         ->where('is_active', true)
    //         ->get(['id', 'name']);
        
    //     return response()->json($availableDepartments);
    // }
 public function getProjectDepartments(Request $request, $projectId)
{
    try {
        \Log::info('getProjectDepartments called for project ID: ' . $projectId);
        $project = WebsiteBuilderProject::find($projectId);
        if (!$project) {
            \Log::warning('Project not found for ID: ' . $projectId);
            return response()->json(['error' => 'Project not found'], 404);
        }

        \Log::info('department_trade: ' . ($project->department_trade ?? 'null'));
        $departmentIds = [];
        if (!empty($project->department_trade)) {
            $decoded = json_decode($project->department_trade, true);
            if (is_array($decoded)) {
                $departmentIds = $decoded;
            } else {
                $departmentIds = array_filter(explode(',', $project->department_trade));
            }
        }
        $departmentIds = array_map('trim', $departmentIds);
        $departmentIds = array_filter($departmentIds, 'is_numeric');
        \Log::info('Parsed department IDs: ' . json_encode($departmentIds));

        if (empty($departmentIds)) {
            \Log::info('No valid department IDs found');
            return response()->json([], 200);
        }

        $assignedDepartmentIds = ProjectDepartmentPermit::where('website_builder_project_id', $projectId)
            ->pluck('mcd_staff_role_id')
            ->toArray();
        \Log::info('Assigned department IDs: ' . json_encode($assignedDepartmentIds));

        $availableDepartments = McdStaffRole::whereIn('id', $departmentIds)
            ->whereNotIn('id', $assignedDepartmentIds)
            ->where('is_active', true)
            ->select('id', 'name')
            ->get();
        \Log::info('Available departments: ' . $availableDepartments->toJson());

        return response()->json($availableDepartments);
    } catch (\Exception $e) {
        \Log::error('Error in getProjectDepartments: ' . $e->getMessage() . ' at ' . $e->getFile() . ':' . $e->getLine());
        return response()->json(['error' => 'Unable to load departments'], 500);
    }
}

    public function store(Request $request)
    {
        $request->validate([
            'website_builder_project_id' => 'required|exists:website_builder_projects,id',
            'mcd_staff_role_id' => 'required|exists:mcd_staff_roles,id',
            'permit_number' => 'required|string|max:255',
        ]);

        ProjectDepartmentPermit::create($request->all());
        return redirect()->route('project-department-permits.index')
            ->with('success', 'Permit number assigned successfully.');
    }

    public function edit($id)
    {
        $permit = ProjectDepartmentPermit::findOrFail($id);
        $projects = WebsiteBuilderProject::all();
        $departments = McdStaffRole::where('is_active', true)->get();
        return view('AssignPermit.edit', compact('permit', 'projects', 'departments'));
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'website_builder_project_id' => 'required|exists:website_builder_projects,id',
            'mcd_staff_role_id' => 'required|exists:mcd_staff_roles,id',
            'permit_number' => 'required|string|max:255',
        ]);

        $permit = ProjectDepartmentPermit::findOrFail($id);
        $permit->update($request->only('website_builder_project_id', 'mcd_staff_role_id', 'permit_number'));

        return redirect()->route('project-department-permits.index')
            ->with('success', 'Permit number updated successfully.');
    }

    public function destroy($id)
    {
        $permit = ProjectDepartmentPermit::findOrFail($id);
        $permit->delete();

        return redirect()->back()->with('success', 'Permit number deleted successfully.');
    }

    public function assignIndex()
    {
        $projects = WebsiteBuilderProject::with('projectDepartmentPermits.department')->get();

        foreach ($projects as $project) {
            $departmentIds = json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
            $departmentNames = McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();
            $project->department_names = $departmentNames;
        }

        return view('AssignPermit.assign', compact('projects'));
    }

    public function assignStore(Request $request, WebsiteBuilderProject $project)
    {
        $request->validate([
            'mcd_staff_role_id' => 'required|exists:mcd_staff_roles,id',
            'permit_number' => 'required|string|max:255',
        ]);

        ProjectDepartmentPermit::create([
            'website_builder_project_id' => $project->id,
            'mcd_staff_role_id' => $request->mcd_staff_role_id,
            'permit_number' => $request->permit_number,
        ]);

        return back()->with('success', 'Permit number assigned successfully.');
    }
}