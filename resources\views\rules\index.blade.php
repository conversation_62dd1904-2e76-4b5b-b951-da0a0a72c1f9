@extends('mcdpanel.layouts.master')
@php
    $user = Auth::guard('staff_mcd')->user();
    $permissions = $user && $user->role ? $user->role->permissions : [];
    $isAdmin = Auth::guard('web')->check();
    $hasWritePermission = $isAdmin || (isset($permissions['rules']['write']) && $permissions['rules']['write'] == 1);
@endphp

@section('content')
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4>Rules & Regulations > Rules List</h4>
                         @if ($hasWritePermission)
                            <a href="{{ route('rules.create') }}" class="btn btn-primary">
                                <i class="fa fa-plus"></i> Add New Rule
                            </a>
                        @else
                            <button type="button" class="btn btn-light btn-sm" onclick="showAccessDenied()">
                                    <i class="fas fa-plus-circle"></i> Add New Rule
                            </button>
                        @endif    
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="table-1">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Title</th>
                                        <th>Description</th>
                                        <th>Status</th>
                                        <th>Document</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($rules as $index => $rule)
                                        <tr>
                                            <td>{{ $index + 1 }}</td>
                                            <td>{{ $rule->title }}</td>
                                            <td>{!! $rule->description !!}</td>
                                            <td>{{ ucfirst($rule->status) }}</td>
                                            <td>
                                                @if ($rule->document)
                                                    @if (Storage::disk('public')->exists($rule->document))
                                                        <a href="{{ url('/documents/' . basename($rule->document)) }}" 
                                                           target="_blank" 
                                                           title="Document kholo">
                                                           <i class="fas fa-file-alt" style="font-size: 24px;"></i>
                                                        </a>
                                                    @else
                                                        Document not found
                                                    @endif
                                                @else
                                                    No documents available
                                                @endif
                                            </td>
                                            <td>
                                                @if ($hasWritePermission)
                                                <a href="{{ route('rules.edit', $rule->id) }}"
                                                    class="btn btn-primary btn-sm">
                                                    <i class="fa fa-edit"></i>
                                                </a>
                                                @else
                                                        <button type="button" class="btn btn-light btn-sm" onclick="showAccessDenied()">
                                                           <i class="fa fa-edit"></i>
                                                        </button>
                                                    @endif  
                                                @if ($hasWritePermission)  
                                                    <button type="button" class="btn btn-danger btn-sm delete-btn"
                                                        data-bs-toggle="modal" data-bs-target="#deleteModal"
                                                        data-url="{{ route('rules.destroy', $rule->id) }}">
                                                        <i class="fa fa-trash"></i>
                                                    </button>
                                                @else
                                                        <button type="button" class="btn btn-danger btn-sm" onclick="showAccessDenied()" title="Delete Notification">
                                                                <i class="fas fa-trash"></i>
                                                        </button>
                                                @endif  
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    Are you sure you want to delete this rule?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form id="deleteForm" method="POST">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">Delete</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        $(document).ready(function() {
            $('.delete-btn').click(function() {
                let deleteUrl = $(this).data('url');
                $('#deleteForm').attr('action', deleteUrl);
            });
        });

        function showAccessDenied() {
        Swal.fire({
            icon: 'error',
            title: 'Access Denied',
            text: 'You do not have permission to modify the Rules & Regulations.'
        });
    }
    </script>
@endpush