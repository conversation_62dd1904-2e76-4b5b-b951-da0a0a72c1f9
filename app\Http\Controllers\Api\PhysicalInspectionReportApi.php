<?php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PhysicalInspectionReport;
use App\Models\WebsiteBuilderProject;
use App\Models\McdStaffRole;
use App\Models\Inspector;
use Barryvdh\DomPDF\Facade\Pdf;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class PhysicalInspectionReportApi extends Controller
{
   

//   public function index(Request $request)
//     {
//         $inspector = Auth::guard('api')->user();

//         if (!$inspector) {
//             return response()->json(['message' => 'Unauthorized'], 401);
//         }

//         $inspectorDepartmentId = $inspector->department;

//         // Get all projects assigned to inspector's department
//         $projects = WebsiteBuilderProject::with([
//             'inspector' => function ($query) {
//                 $query->select('id', 'first_name', 'last_name', 'email', 'phone', 'designation', 'department', 'address', 'status', 'inspector_id', 'image');
//             },
//             'inspector.role' => function ($query) {
//                 $query->select('id', 'name', 'description', 'is_active');
//             },
//             'architectCategory' => function ($query) {
//                 $query->select('id', 'name');
//             },
//             'builderSubCategory' => function ($query) {
//                 $query->select('id', 'sub_category_name');
//             },
//             'permitNumbers' => function ($query) {
//                 $query->select('id', 'website_builder_project_id', 'permit_number');
//             },
//             'departmentPermitNumbers' => function ($query) use ($inspectorDepartmentId) {
//                 $query->where('mcd_staff_role_id', $inspectorDepartmentId)
//                       ->select('id', 'website_builder_project_id', 'permit_number');
//             }
//         ])
//         ->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$inspectorDepartmentId)])
//         ->get();

//         if ($projects->isEmpty()) {
//             return response()->json([
//                 'message' => 'No projects found for your department',
//                 'inspector' => [
//                     'id' => $inspector->id,
//                     'first_name' => $inspector->first_name,
//                     'last_name' => $inspector->last_name,
//                     'email' => $inspector->email,
//                     'phone' => $inspector->phone,
//                     'designation' => $inspector->designation,
//                     'department' => $inspector->department,
//                     'address' => $inspector->address,
//                     'status' => $inspector->status,
//                     'inspector_id' => $inspector->inspector_id,
//                     'image' => $inspector->image,
//                     'role' => $inspector->role ? [
//                         'id' => $inspector->role->id,
//                         'name' => $inspector->role->name,
//                         'description' => $inspector->role->description,
//                         'is_active' => $inspector->role->is_active,
//                     ] : null,
//                 ],
//                 'data' => []
//             ], 200);
//         }

//         // Get project IDs
//         $projectIds = $projects->pluck('id')->toArray();

//         // Debug: Log project IDs and inspector ID
//         \Log::info('Project IDs: ' . json_encode($projectIds));
//         \Log::info('Inspector ID: ' . $inspector->id);

//         // Get all reports for these projects where the inspector's ID is in the inspectors array
//         $reports = PhysicalInspectionReport::with('project')
//             ->whereIn('project_id', $projectIds)
//             ->whereRaw('JSON_CONTAINS(inspectors, ?)', ['"' . $inspector->id . '"']) // Ensure ID is a string
//             ->get()
//             ->map(function ($report) use ($projects) {
//                 $report->report_images = array_map(function ($path) {
//                     return url($path);
//                 }, $report->report_images ?? []);

//                 // Find the corresponding project
//                 $project = $projects->firstWhere('id', $report->project_id);
                
//                 if ($project) {
//                     // Add project details to each report
//                     $departmentIds = is_array($project->department_trade)
//                         ? $project->department_trade
//                         : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
//                     $departmentIds = array_filter($departmentIds, 'is_numeric');
//                     $report->project->department_names = McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();
                    
//                     $report->project->architect_category_name = $project->architectCategory->name ?? null;
//                     $report->project->builder_sub_category_name = $project->builderSubCategory->sub_category_name ?? null;
//                     $report->project->PUID = $project->permitNumbers->pluck('permit_number')->toArray();
//                     $report->project->department_PermitNumber = $project->departmentPermitNumbers->pluck('permit_number')->toArray();

//                     // Unset the original ID fields and relationships
//                     unset($report->project->architect_category_id);
//                     unset($report->project->builder_sub_category_id);
//                     unset($report->project->architectCategory);
//                     unset($report->project->builderSubCategory);
//                     unset($report->project->permitNumbers);
//                     unset($report->project->departmentPermitNumbers);
//                 }

//                 return $report;
//             });

//         // Debug: Log the number of reports retrieved
//         \Log::info('Reports retrieved: ' . $reports->count());

//         // Prepare inspector details
//         $inspectorDetails = [
//             'id' => $inspector->id,
//             'first_name' => $inspector->first_name,
//             'last_name' => $inspector->last_name,
//             'email' => $inspector->email,
//             'phone' => $inspector->phone,
//             'designation' => $inspector->designation,
//             'department' => $inspector->department,
//             'address' => $inspector->address,
//             'status' => $inspector->status,
//             'inspector_id' => $inspector->inspector_id,
//             'image' => $inspector->image,
//             'role' => $inspector->role ? [
//                 'id' => $inspector->role->id,
//                 'name' => $inspector->role->name,
//                 'description' => $inspector->role->description,
//                 'is_active' => $inspector->role->is_active,
//             ] : null,
//         ];

//         return response()->json([
//             'message' => $reports->isEmpty() ? 'No inspection reports found for this inspector' : 'All inspection reports retrieved successfully',
//             'inspector' => $inspectorDetails,
//             'data' => $reports,
//             'total_reports' => $reports->count(),
//             'total_projects' => $projects->count()
//         ], 200);
//     }
//     public function store(Request $request)
// {
//     $inspector = Auth::guard('api')->user();

//     if (!$inspector) {
//         return response()->json(['message' => 'Unauthorized'], 401);
//     }

//     $validator = Validator::make($request->all(), [
//         'project_id' => 'required|integer|exists:website_builder_projects,id',
//         'inspectors' => 'required|array|min:1',
//         'inspectors.*' => 'integer|exists:inspectors,id',
//         'report_images' => 'nullable|array',
//         'report_images.*' => 'file|mimes:jpeg,png,jpg,gif|max:2048',
//         'note' => 'nullable|string|max:1000',
//         'violation_note' => 'nullable|string|max:1000',
//         'potential_violations' => 'nullable|array',
//         'potential_violations.*' => 'string|max:255',
//         'inspection_done_at' => 'nullable|date',
//         'report_status' => 'required|in:0,1,2',
//     ]);

//     if ($validator->fails()) {
//         return response()->json([
//             'message' => 'Validation failed',
//             'errors' => $validator->errors(),
//         ], 422);
//     }

//     // Check if the project belongs to the inspector's department
//     $project = WebsiteBuilderProject::where('id', $request->project_id)
//         ->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$inspector->department)])
//         ->first();

//     if (!$project) {
//         return response()->json([
//             'message' => 'Project not found or not assigned to your department',
//         ], 403);
//     }

//     // Normalize inspectors array for comparison
//     $submittedInspectors = $request->inspectors ?? [$inspector->id];
//     sort($submittedInspectors); // Sort to ensure consistent comparison
//     $submittedInspectorsJson = json_encode($submittedInspectors);

//     // Check for existing report with the same project_id and exact inspectors array
//     $existingReport = PhysicalInspectionReport::where('project_id', $request->project_id)
//         ->where('inspectors', $submittedInspectorsJson)
//         ->first();

//     // Prevent submission or update if an existing report has report_status = 0 (Approval)
//     if ($existingReport && $existingReport->report_status == 0) {
//         return response()->json([
//             'message' => 'Cannot update or submit report: The report for this project is already approved (status = 0).',
//         ], 403);
//     }

//     // Prevent new submission with report_status = 0 if an approved report exists for the project
//     if ($request->report_status == 0 && PhysicalInspectionReport::where('project_id', $request->project_id)
//         ->where('report_status', 0)
//         ->exists()) {
//         return response()->json([
//             'message' => 'Cannot submit report: An approved report (status = 0) already exists for this project.',
//         ], 403);
//     }

//     $imagePaths = [];
//     $imageUrls = [];
//     if ($request->hasFile('report_images')) {
//         $directory = public_path('inspectionreport');
//         if (!file_exists($directory)) {
//             mkdir($directory, 0755, true);
//         }

//         foreach ($request->file('report_images') as $image) {
//             $filename = time() . '_' . $image->getClientOriginalName();
//             $image->move($directory, $filename);
//             $imagePaths[] = 'public/inspectionreport/' . $filename;
//             $imageUrls[] = url('public/inspectionreport/' . $filename);
//         }
//     }

//     $data = [
//         'project_id' => $request->project_id,
//         'inspectors' => $submittedInspectors,
//         'report_images' => $imagePaths,
//         'note' => $request->note,
//         'violation_note' => $request->violation_note,
//         'potential_violations' => $request->potential_violations ?? [],
//         'inspection_done_at' => $request->inspection_done_at ?? now(),
//         'report_status' => $request->report_status,
//     ];

//     if ($existingReport) {
//         $existingReport->update($data);
//         $report = $existingReport;
//         $message = 'Inspection report updated successfully';
//         $statusCode = 200;
//     } else {
//         $report = PhysicalInspectionReport::create($data);
//         $message = 'Inspection report submitted successfully';
//         $statusCode = 201;
//     }

//     $report->report_images = $imageUrls;

//     return response()->json([
//         'message' => $message,
//         'data' => $report,
//     ], $statusCode);
// }
public function index(Request $request)
{
    $inspector = Auth::guard('api')->user();

    if (!$inspector) {
        return response()->json(['message' => 'Unauthorized'], 401);
    }

    $inspectorDepartmentId = $inspector->department;

    // Get all projects assigned to inspector's department
    $projects = WebsiteBuilderProject::with([
        'inspector' => function ($query) {
            $query->select('id', 'first_name', 'last_name', 'email', 'phone', 'designation', 'department', 'address', 'status', 'inspector_id', 'image');
        },
        'inspector.role' => function ($query) {
            $query->select('id', 'name', 'description', 'is_active');
        },
        'architectCategory' => function ($query) {
            $query->select('id', 'name');
        },
        'builderSubCategory' => function ($query) {
            $query->select('id', 'sub_category_name');
        },
        'permitNumbers' => function ($query) {
            $query->select('id', 'website_builder_project_id', 'permit_number');
        },
        'departmentPermitNumbers' => function ($query) use ($inspectorDepartmentId) {
            $query->where('mcd_staff_role_id', $inspectorDepartmentId)
                  ->select('id', 'website_builder_project_id', 'permit_number');
        }
    ])
    ->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$inspectorDepartmentId)])
    ->get();

    if ($projects->isEmpty()) {
        return response()->json([
            'message' => 'No projects found for your department',
            'inspector' => [
                'id' => $inspector->id,
                'first_name' => $inspector->first_name,
                'last_name' => $inspector->last_name,
                'email' => $inspector->email,
                'phone' => $inspector->phone,
                'designation' => $inspector->designation,
                'department' => $inspector->department,
                'address' => $inspector->address,
                'status' => $inspector->status,
                'inspector_id' => $inspector->inspector_id,
                'image' => $inspector->image,
                'role' => $inspector->role ? [
                    'id' => $inspector->role->id,
                    'name' => $inspector->role->name,
                    'description' => $inspector->role->description,
                    'is_active' => $inspector->role->is_active,
                ] : null,
            ],
            'data' => []
        ], 200);
    }

    // Get project IDs
    $projectIds = $projects->pluck('id')->toArray();

    // Debug: Log project IDs and inspector ID
    \Log::info('Project IDs: ' . json_encode($projectIds));
    \Log::info('Inspector ID: ' . $inspector->id);

    // Get the latest report for each project where the inspector's ID is in the inspectors array
    $reports = PhysicalInspectionReport::with('project')
        ->whereIn('project_id', $projectIds)
        ->whereRaw('JSON_CONTAINS(inspectors, ?)', ['"' . $inspector->id . '"'])
        ->selectRaw('*, ROW_NUMBER() OVER (PARTITION BY project_id ORDER BY created_at DESC) as rn')
        ->get()
        ->filter(function ($report) {
            return $report->rn == 1; // Keep only the latest report per project
        })
        ->map(function ($report) use ($projects) {
            $report->report_images = array_map(function ($path) {
                return url($path);
            }, $report->report_images ?? []);

            // Find the corresponding project
            $project = $projects->firstWhere('id', $report->project_id);
            
            if ($project) {
                // Add project details to each report
                $departmentIds = is_array($project->department_trade)
                    ? $project->department_trade
                    : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
                $departmentIds = array_filter($departmentIds, 'is_numeric');
                $report->project->department_names = McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();
                
                $report->project->architect_category_name = $project->architectCategory->name ?? null;
                $report->project->builder_sub_category_name = $project->builderSubCategory->sub_category_name ?? null;
                $report->project->PUID = $project->permitNumbers->pluck('permit_number')->toArray();
                $report->project->department_PermitNumber = $project->departmentPermitNumbers->pluck('permit_number')->toArray();

                // Unset the original ID fields and relationships
                unset($report->project->architect_category_id);
                unset($report->project->builder_sub_category_id);
                unset($report->project->architectCategory);
                unset($report->project->builderSubCategory);
                unset($report->project->permitNumbers);
                unset($report->project->departmentPermitNumbers);
            }

            // Remove the rn field from the response
            unset($report->rn);

            return $report;
        });

    // Debug: Log the number of reports retrieved
    \Log::info('Reports retrieved: ' . $reports->count());

    // Prepare inspector details
    $inspectorDetails = [
        'id' => $inspector->id,
        'first_name' => $inspector->first_name,
        'last_name' => $inspector->last_name,
        'email' => $inspector->email,
        'phone' => $inspector->phone,
        'designation' => $inspector->designation,
        'department' => $inspector->department,
        'address' => $inspector->address,
        'status' => $inspector->status,
        'inspector_id' => $inspector->inspector_id,
        'image' => $inspector->image,
        'role' => $inspector->role ? [
            'id' => $inspector->role->id,
            'name' => $inspector->role->name,
            'description' => $inspector->role->description,
            'is_active' => $inspector->role->is_active,
        ] : null,
    ];

    return response()->json([
        'message' => $reports->isEmpty() ? 'No inspection reports found for this inspector' : 'All inspection reports retrieved successfully',
        'inspector' => $inspectorDetails,
        'data' => $reports,
        'total_reports' => $reports->count(),
        'total_projects' => $projects->count()
    ], 200);
}

public function store(Request $request)
{
    $inspector = Auth::guard('api')->user();

    if (!$inspector) {
        return response()->json(['message' => 'Unauthorized'], 401);
    }

    $validator = Validator::make($request->all(), [
        'project_id' => 'required|integer|exists:website_builder_projects,id',
        'inspectors' => 'required|array|min:1',
        'inspectors.*' => 'integer|exists:inspectors,id',
        'report_images' => 'nullable|array',
        'report_images.*' => 'file|mimes:jpeg,png,jpg,gif|max:2048',
        'note' => 'nullable|string|max:1000',
        'violation_note' => 'nullable|string|max:1000',
        'potential_violations' => 'nullable|array',
        'potential_violations.*' => 'string|max:255',
        'inspection_done_at' => 'nullable|date',
        'report_status' => 'required|in:0,1,2',
    ]);

    if ($validator->fails()) {
        return response()->json([
            'message' => 'Validation failed',
            'errors' => $validator->errors(),
        ], 422);
    }

    // Check if the project belongs to the inspector's department
    $project = WebsiteBuilderProject::where('id', $request->project_id)
        ->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$inspector->department)])
        ->first();

    if (!$project) {
        return response()->json([
            'message' => 'Project not found or not assigned to your department',
        ], 403);
    }

    // Prevent submission with report_status = 0 if an approved report exists for the project
    // if ($request->report_status == 0 && PhysicalInspectionReport::where('project_id', $request->project_id)
    //     ->where('report_status', 0)
    //     ->exists()) {
    //     return response()->json([
    //         'message' => 'Cannot submit report: An approved report (status = 0) already exists for this project.',
    //     ], 403);
    // }

    $imagePaths = [];
    $imageUrls = [];
    if ($request->hasFile('report_images')) {
        $directory = public_path('inspectionreport');
        if (!file_exists($directory)) {
            mkdir($directory, 0755, true);
        }

        foreach ($request->file('report_images') as $image) {
            $filename = time() . '_' . $image->getClientOriginalName();
            $image->move($directory, $filename);
            $imagePaths[] = 'public/inspectionreport/' . $filename;
            $imageUrls[] = url('public/inspectionreport/' . $filename);
        }
    }

    $data = [
        'project_id' => $request->project_id,
        'inspectors' => $request->inspectors ?? [$inspector->id],
        'report_images' => $imagePaths,
        'note' => $request->note,
        'violation_note' => $request->violation_note,
        'potential_violations' => $request->potential_violations ?? [],
        'inspection_done_at' => $request->inspection_done_at ?? now(),
        'report_status' => $request->report_status,
        'created_at' => now(), // Ensure timestamp is set
        'updated_at' => now(),
    ];

    // Create a new report instead of updating an existing one
    $report = PhysicalInspectionReport::create($data);
    $report->report_images = $imageUrls;

    return response()->json([
        'message' => 'Inspection report submitted successfully',
        'data' => $report,
    ], 201);
}

//    public function activeInspection(Request $request)
// {
//     $inspector = Auth::guard('api')->user();

//     if (!$inspector) {
//         return response()->json(['message' => 'Unauthorized'], 401);
//     }

//     $inspectorDepartmentId = $inspector->department;
//     $inspectorId = (string) $inspector->id;

//     $query = WebsiteBuilderProject::with([
//         'inspector',
//         'inspector.role',
//         'architectCategory' => function ($query) {
//             $query->select('id', 'name');
//         },
//         'builderSubCategory' => function ($query) {
//             $query->select('id', 'sub_category_name');
//         },
//         'permitNumbers' => function ($query) {
//             $query->select('id', 'website_builder_project_id', 'permit_number');
//         },
//         'departmentPermitNumbers' => function ($query) use ($inspectorDepartmentId) {
//             $query->where('mcd_staff_role_id', $inspectorDepartmentId)
//                   ->select('id', 'website_builder_project_id', 'permit_number');
//         }
//     ])
//     ->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$inspectorDepartmentId)])
//     ->whereDoesntHave('physicalInspectionReportnew', function ($query) use ($inspectorId) {
//         $query->whereJsonContains('inspectors', $inspectorId);
//     });

//     $projects = $query->get();

//     $projects->each(function ($project) {
//         $departmentIds = is_array($project->department_trade)
//             ? $project->department_trade
//             : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
//         $departmentIds = array_filter($departmentIds, 'is_numeric');
//         $project->department_names = McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();

//         // Transform the response to include names instead of IDs
//         $project->architect_category_name = $project->architectCategory->name ?? null;
//         $project->builder_sub_category_name = $project->builderSubCategory->sub_category_name ?? null;

//         // Include all permit numbers
//         $project->PUID = $project->permitNumbers->pluck('permit_number')->toArray();

//         // Include department-specific permit numbers
//         $project->department_PermitNumber = $project->departmentPermitNumbers->pluck('permit_number')->toArray();

//         // Unset the original ID fields and relationships
//         unset($project->architect_category_id);
//         unset($project->builder_sub_category_id);
//         unset($project->architectCategory);
//         unset($project->builderSubCategory);
//         unset($project->permitNumbers);
//         unset($project->departmentPermitNumbers);
//     });

//     if ($projects->isEmpty()) {
//         return response()->json([
//             'message' => 'No pending projects found for your department',
//             'data' => []
//         ], 200);
//     }

//     return response()->json([
//         'message' => 'Pending projects retrieved successfully',
//         'data' => $projects
//     ], 200);
// }
public function activeInspection(Request $request)
{
    $inspector = Auth::guard('api')->user();

    if (!$inspector) {
        return response()->json(['message' => 'Unauthorized'], 401);
    }

    $inspectorDepartmentId = $inspector->department;
    $inspectorId = (string) $inspector->id;

    $query = WebsiteBuilderProject::with([
        'inspector',
        'inspector.role',
        'architectCategory' => function ($query) {
            $query->select('id', 'name');
        },
        'builderSubCategory' => function ($query) {
            $query->select('id', 'sub_category_name');
        },
        'permitNumbers' => function ($query) {
            $query->select('id', 'website_builder_project_id', 'permit_number');
        },
        'departmentPermitNumbers' => function ($query) use ($inspectorDepartmentId) {
            $query->where('mcd_staff_role_id', $inspectorDepartmentId)
                  ->select('id', 'website_builder_project_id', 'permit_number');
        }
    ])
    ->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$inspectorDepartmentId)])
    ->whereDoesntHave('physicalInspectionReportnew', function ($query) use ($inspectorId) {
        $query->whereJsonContains('inspectors', $inspectorId);
    });

    $projects = $query->get();

    // Filter approved projects
    $approvedProjects = [];

    foreach ($projects as $project) {
        // Process department_trade
        $departmentIds = is_array($project->department_trade)
            ? $project->department_trade
            : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
        $departmentIds = array_filter($departmentIds, 'is_numeric');
        $project->department_names = \App\Models\McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();

        // Transform the response to include names instead of IDs
        $project->architect_category_name = $project->architectCategory->name ?? null;
        $project->builder_sub_category_name = $project->builderSubCategory->sub_category_name ?? null;

        // Include all permit numbers
        $project->PUID = $project->permitNumbers->pluck('permit_number')->toArray();

        // Include department-specific permit numbers
        $project->department_PermitNumber = $project->departmentPermitNumbers->pluck('permit_number')->toArray();

        // Unset the original ID fields and relationships
        unset($project->architect_category_id);
        unset($project->builder_sub_category_id);
        unset($project->architectCategory);
        unset($project->builderSubCategory);
        unset($project->permitNumbers);
        unset($project->departmentPermitNumbers);

        // Check if all department review steps are approved
        $allDepartmentStepsApproved = true;
        foreach ($departmentIds as $deptId) {
            $departmentModel = \App\Models\McdStaffRole::find($deptId);
            if (!$departmentModel) {
                \Log::warning("Department ID {$deptId} not found for project ID {$project->id}");
                continue;
            }

            $reviewProcess = \App\Models\ReviewProcess::where('review_level', 'Department')
                ->whereJsonContains('department', $departmentModel->name)
                ->first();

            if (!$reviewProcess || empty($reviewProcess->review_steps)) {
                \Log::warning("No Department review process or steps for department {$departmentModel->name}, project ID {$project->id}");
                $allDepartmentStepsApproved = false;
                break;
            }

            // Fetch all review documents for this department in one query
            $docs = \App\Models\ProjectReviewDocument::where('project_id', $project->id)
                ->where('department_id', $deptId)
                ->whereIn('review_step', $reviewProcess->review_steps)
                ->get();

            if (count($reviewProcess->review_steps) !== $docs->count() || !$docs->every(fn($doc) => $doc->status === 2)) {
                \Log::info("Not all Department steps approved for project ID {$project->id}, department ID {$deptId}");
                $allDepartmentStepsApproved = false;
                break;
            }
        }

        // Check if all Planning Commission review steps are approved
        $allPlanningCommissionStepsApproved = true;
        $pcReviewProcess = \App\Models\ReviewProcess::where('review_level', 'PlanningCommission')
            ->first();

        if ($pcReviewProcess && !empty($pcReviewProcess->review_steps)) {
            $pcDepartments = is_array($pcReviewProcess->department)
                ? $pcReviewProcess->department
                : json_decode($pcReviewProcess->department, true) ?? [];

            foreach ($pcDepartments as $pcDeptName) {
                $pcDeptModel = \App\Models\McdStaffRole::where('name', $pcDeptName)->first();
                if (!$pcDeptModel) {
                    \Log::warning("Planning Commission department {$pcDeptName} not found for project ID {$project->id}");
                    continue;
                }

                $pcDocs = \App\Models\ProjectReviewDocumentPc::where('project_id', $project->id)
                    ->where('department_id', $pcDeptModel->id)
                    ->whereIn('review_step', $pcReviewProcess->review_steps)
                    ->get();

                if (count($pcReviewProcess->review_steps) !== $pcDocs->count() || !$pcDocs->every(fn($doc) => $doc->status === 2)) {
                    \Log::info("Not all Planning Commission steps approved for project ID {$project->id}, department {$pcDeptName}");
                    $allPlanningCommissionStepsApproved = false;
                    break;
                }
            }
        }

        // Include project if all steps from both reviews are approved
        if ($allDepartmentStepsApproved && $allPlanningCommissionStepsApproved) {
            $approvedProjects[] = $project;
        }
    }

    if (empty($approvedProjects)) {
        return response()->json([
            'message' => 'No pending approved projects found for your department',
            'data' => []
        ], 200);
    }

    return response()->json([
        'message' => 'Pending approved projects retrieved successfully',
        'data' => $approvedProjects
    ], 200);
}

    // public function activeInspection(Request $request)
    // {
    //     $inspector = Auth::guard('api')->user();

    //     if (!$inspector) {
    //         return response()->json(['message' => 'Unauthorized'], 401);
    //     }

    //     $inspectorDepartmentId = $inspector->department;

    //     $query = WebsiteBuilderProject::with([
    //         'inspector',
    //         'inspector.role',
    //         'architectCategory' => function ($query) {
    //             $query->select('id', 'name');
    //         },
    //         'builderSubCategory' => function ($query) {
    //             $query->select('id', 'sub_category_name');
    //         },
    //         'permitNumbers' => function ($query) {
    //             $query->select('id', 'website_builder_project_id', 'permit_number');
    //         },
    //         'departmentPermitNumbers' => function ($query) use ($inspectorDepartmentId) {
    //             $query->where('mcd_staff_role_id', $inspectorDepartmentId)
    //                   ->select('id', 'website_builder_project_id', 'permit_number');
    //         },
    //         'physicalInspectionReport' => function ($query) {
    //             $query->latest('inspection_done_at')->first();
    //         }
    //     ])
    //         ->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$inspectorDepartmentId)])
    //         ->where(function ($q) {
    //             $q->whereDoesntHave('physicalInspectionReport')
    //               ->orWhereHas('physicalInspectionReport', function ($subQuery) {
    //                   $subQuery->where('report_status', '2'); // Only Pending reports
    //               });
    //         });

    //     $projects = $query->get();

    //     $projects->each(function ($project) {
    //         $departmentIds = is_array($project->department_trade)
    //             ? $project->department_trade
    //             : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
    //         $departmentIds = array_filter($departmentIds, 'is_numeric');
    //         $project->department_names = McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();

    //         // Transform the response to include names instead of IDs
    //         $project->architect_category_name = $project->architectCategory->name ?? null;
    //         $project->builder_sub_category_name = $project->builderSubCategory->sub_category_name ?? null;

    //         // Include all permit numbers
    //         $project->PUID = $project->permitNumbers->pluck('permit_number')->toArray();

    //         // Include department-specific permit numbers
    //         $project->department_PermitNumber = $project->departmentPermitNumbers->pluck('permit_number')->toArray();

    //         // Unset the original ID fields and relationships
    //         unset($project->architect_category_id);
    //         unset($project->builder_sub_category_id);
    //         unset($project->architectCategory);
    //         unset($project->builderSubCategory);
    //         unset($project->permitNumbers);
    //         unset($project->departmentPermitNumbers);
    //         unset($project->physicalInspectionReport);
    //     });

    //     if ($projects->isEmpty()) {
    //         return response()->json([
    //             'message' => 'No pending projects found for your department',
    //             'data' => []
    //         ], 200);
    //     }

    //     return response()->json([
    //         'message' => 'Pending projects retrieved successfully',
    //         'data' => $projects
    //     ], 200);
    // }
    public function countPending(Request $request)
    {
        $inspector = Auth::guard('api')->user();

        if (!$inspector) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }

        $inspectorDepartmentId = $inspector->department;

        $projectCount = WebsiteBuilderProject::whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$inspectorDepartmentId)])
            ->where(function ($q) {
                $q->whereDoesntHave('physicalInspectionReport')
                  ->orWhereHas('physicalInspectionReport', function ($subQuery) {
                      $subQuery->where('report_status', '2');
                  });
            })
            ->count();

        return response()->json([
            'message' => 'Pending project count retrieved successfully',
            'count' => (int)$projectCount
        ], 200);
    }
   
    public function countViolations(Request $request)
    {
        $inspector = Auth::guard('api')->user();

        if (!$inspector) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }

        $inspectorDepartmentId = $inspector->department;

        $projectCount = WebsiteBuilderProject::whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$inspectorDepartmentId)])
            ->whereHas('physicalInspectionReport', function ($subQuery) {
                $subQuery->where('report_status', '1');
            })
            ->count();

        return response()->json([
            'message' => 'Violation project count retrieved successfully',
            'count' => (int)$projectCount
        ], 200);
    }

 public function getProjectDetails(Request $request, $project_id)
    {
        $inspector = Auth::guard('api')->user();

        if (!$inspector) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }

        $validator = Validator::make(['project_id' => $project_id], [
            'project_id' => 'required|integer|exists:website_builder_projects,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $inspectorDepartmentId = $inspector->department;

        $project = WebsiteBuilderProject::with([
            'inspector' => function ($query) {
                $query->select('id', 'first_name', 'last_name', 'email', 'phone', 'designation', 'department', 'address', 'status', 'inspector_id', 'image');
            },
            'inspector.role' => function ($query) {
                $query->select('id', 'name', 'description', 'is_active');
            },
            'architectCategory' => function ($query) {
                $query->select('id', 'name');
            },
            'builderSubCategory' => function ($query) {
                $query->select('id', 'sub_category_name');
            },
            'permitNumbers' => function ($query) {
                $query->select('id', 'website_builder_project_id', 'permit_number');
            },
            'departmentPermitNumbers' => function ($query) use ($inspectorDepartmentId) {
                $query->where('mcd_staff_role_id', $inspectorDepartmentId)
                      ->select('id', 'website_builder_project_id', 'permit_number');
            },
            'physicalInspectionReport' => function ($query) use ($inspector) {
                $query->whereRaw('JSON_CONTAINS(inspectors, ?)', ['"' . $inspector->id . '"']);
            }
        ])
            ->where('id', $project_id)
            ->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$inspectorDepartmentId)])
            ->first();

        if (!$project) {
            return response()->json([
                'message' => 'Project not found or not assigned to your department',
                'inspector' => [
                    'id' => $inspector->id,
                    'first_name' => $inspector->first_name,
                    'last_name' => $inspector->last_name,
                    'email' => $inspector->email,
                    'phone' => $inspector->phone,
                    'designation' => $inspector->designation,
                    'department' => $inspector->department,
                    'address' => $inspector->address,
                    'status' => $inspector->status,
                    'inspector_id' => $inspector->inspector_id,
                    'image' => $inspector->image,
                    'role' => $inspector->role ? [
                        'id' => $inspector->role->id,
                        'name' => $inspector->role->name,
                        'description' => $inspector->role->description,
                        'is_active' => $inspector->role->is_active,
                    ] : null,
                ],
                'data' => []
            ], 403);
        }

        // Process department trades
        $departmentIds = is_array($project->department_trade)
            ? $project->department_trade
            : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
        $departmentIds = array_filter($departmentIds, 'is_numeric');
        $project->department_names = McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();

        // Transform inspection reports, handling null case
        $reports = collect($project->physicalInspectionReport ?? [])->map(function ($report) {
            $report->report_images = array_map(function ($path) {
                return url($path);
            }, $report->report_images ?? []);

            // Get inspector names for the report
            $reportInspectorNames = Inspector::whereIn('id', $report->inspectors ?? [])
                ->get()
                ->map(function ($inspector) {
                    return [
                        'id' => $inspector->id,
                        'name' => $inspector->first_name . ' ' . $inspector->last_name,
                        'email' => $inspector->email,
                        'phone' => $inspector->phone,
                        'designation' => $inspector->designation,
                        'department' => $inspector->department
                    ];
                })->toArray();

            $report->inspectors_details = $reportInspectorNames;

            // Add status text
            switch ($report->report_status) {
                case 0:
                    $report->status_text = 'Approval';
                    break;
                case 1:
                    $report->status_text = 'Violation';
                    break;
                case 2:
                    $report->status_text = 'Pending';
                    break;
                default:
                    $report->status_text = 'N/A';
                    break;
            }

            return $report;
        })->values()->toArray();

        // Prepare project details
        $projectDetails = [
            'id' => $project->id,
            'project_name' => $project->project_name,
            'project_address' => $project->project_address,
            'city' => $project->city,
            'state' => $project->state,
            'zip' => $project->zip,
            'full_address' => implode(', ', array_filter([
                $project->project_address,
                $project->city,
                $project->state,
                $project->zip
            ])),
            'architect_category_name' => $project->architectCategory->name ?? null,
            'builder_sub_category_name' => $project->builderSubCategory->sub_category_name ?? null,
            'PUID' => $project->permitNumbers->pluck('permit_number')->toArray(),
            'department_PermitNumber' => $project->departmentPermitNumbers->pluck('permit_number')->toArray(),
            'department_names' => $project->department_names,
            'created_at' => $project->created_at,
            'updated_at' => $project->updated_at
        ];

        // Prepare inspector details
        $inspectorDetails = [
            'id' => $inspector->id,
            'first_name' => $inspector->first_name,
            'last_name' => $inspector->last_name,
            'email' => $inspector->email,
            'phone' => $inspector->phone,
            'designation' => $inspector->designation,
            'department' => $inspector->department,
            'address' => $inspector->address,
            'status' => $inspector->status,
            'inspector_id' => $inspector->inspector_id,
            'image' => $inspector->image,
            'role' => $inspector->role ? [
                'id' => $inspector->role->id,
                'name' => $inspector->role->name,
                'description' => $inspector->role->description,
                'is_active' => $inspector->role->is_active,
            ] : null,
        ];

        // Clean up project response
        unset($project->architect_category_id);
        unset($project->builder_sub_category_id);
        unset($project->architectCategory);
        unset($project->builderSubCategory);
        unset($project->permitNumbers);
        unset($project->departmentPermitNumbers);
        unset($project->physicalInspectionReport);
        unset($project->department_trade);
        unset($project->inspector);

        return response()->json([
            'message' => $reports ? 'Project details retrieved successfully' : 'No inspection reports found for this inspector',
            'data' => [
                'project' => $projectDetails,
                'current_inspector' => $inspectorDetails,
                'inspection_reports' => $reports
            ]
        ], 200);
    }
// public function getProjectDetails(Request $request, $project_id)
// {
//     $inspector = Auth::guard('api')->user();

//     if (!$inspector) {
//         return response()->json(['message' => 'Unauthorized'], 401);
//     }

//     $validator = Validator::make(['project_id' => $project_id], [
//         'project_id' => 'required|integer|exists:website_builder_projects,id',
//     ]);

//     if ($validator->fails()) {
//         return response()->json([
//             'message' => 'Validation failed',
//             'errors' => $validator->errors(),
//         ], 422);
//     }

//     $project = WebsiteBuilderProject::with([
//         'inspector' => function ($query) {
//             $query->select('id', 'first_name', 'last_name', 'email', 'phone', 'designation', 'department', 'address', 'status', 'inspector_id', 'image');
//         },
//         'inspector.role' => function ($query) {
//             $query->select('id', 'name', 'description', 'is_active');
//         },
//         'architectCategory' => function ($query) {
//             $query->select('id', 'name');
//         },
//         'builderSubCategory' => function ($query) {
//             $query->select('id', 'sub_category_name');
//         },
//         'permitNumbers' => function ($query) {
//             $query->select('id', 'website_builder_project_id', 'permit_number');
//         },
//         'physicalInspectionReport' => function ($query) use ($inspector) {
//             $query->whereRaw('JSON_CONTAINS(inspectors, ?)', ['"' . $inspector->id . '"']);
//         }
//     ])
//         ->where('id', $project_id)
//         ->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$inspector->department)])
//         ->first();

//     if (!$project) {
//         return response()->json([
//             'message' => 'Project not found or not assigned to your department',
//             'inspector' => [
//                 'id' => $inspector->id,
//                 'first_name' => $inspector->first_name,
//                 'last_name' => $inspector->last_name,
//                 'email' => $inspector->email,
//                 'phone' => $inspector->phone,
//                 'designation' => $inspector->designation,
//                 'department' => $inspector->department,
//                 'address' => $inspector->address,
//                 'status' => $inspector->status,
//                 'inspector_id' => $inspector->inspector_id,
//                 'image' => $inspector->image,
//                 'role' => $inspector->role ? [
//                     'id' => $inspector->role->id,
//                     'name' => $inspector->role->name,
//                     'description' => $inspector->role->description,
//                     'is_active' => $inspector->role->is_active,
//                 ] : null,
//             ],
//             'data' => []
//         ], 403);
//     }

//     // Process department trades
//     $departmentIds = is_array($project->department_trade)
//         ? $project->department_trade
//         : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
//     $departmentIds = array_filter($departmentIds, 'is_numeric');
//     $project->department_names = McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();

//     // Transform inspection reports, handling null case
//     $reports = collect($project->physicalInspectionReport ?? [])->map(function ($report) {
//         $report->report_images = array_map(function ($path) {
//             return url($path);
//         }, $report->report_images ?? []);

//         // Get inspector names for the report
//         $reportInspectorNames = Inspector::whereIn('id', $report->inspectors ?? [])
//             ->get()
//             ->map(function ($inspector) {
//                 return [
//                     'id' => $inspector->id,
//                     'name' => $inspector->first_name . ' ' . $inspector->last_name,
//                     'email' => $inspector->email,
//                     'phone' => $inspector->phone,
//                     'designation' => $inspector->designation,
//                     'department' => $inspector->department
//                 ];
//             })->toArray();

//         $report->inspectors_details = $reportInspectorNames;

//         // Add status text
//         switch ($report->report_status) {
//             case 0:
//                 $report->status_text = 'Approval';
//                 break;
//             case 1:
//                 $report->status_text = 'Violation';
//                 break;
//             case 2:
//                 $report->status_text = 'Pending';
//                 break;
//             default:
//                 $report->status_text = 'N/A';
//                 break;
//         }

//         return $report;
//     })->values()->toArray();

//     // Prepare project details
//     $projectDetails = [
//         'id' => $project->id,
//         'project_name' => $project->project_name,
//         'project_address' => $project->project_address,
//         'city' => $project->city,
//         'state' => $project->state,
//         'zip' => $project->zip,
//         'full_address' => implode(', ', array_filter([
//             $project->project_address,
//             $project->city,
//             $project->state,
//             $project->zip
//         ])),
//         'architect_category_name' => $project->architectCategory->name ?? null,
//         'builder_sub_category_name' => $project->builderSubCategory->sub_category_name ?? null,
//         'permit_numbers' => $project->permitNumbers->pluck('permit_number')->toArray(),
//         'department_names' => $project->department_names,
//         'created_at' => $project->created_at,
//         'updated_at' => $project->updated_at
//     ];

//     // Prepare inspector details
//     $inspectorDetails = [
//         'id' => $inspector->id,
//         'first_name' => $inspector->first_name,
//         'last_name' => $inspector->last_name,
//         'email' => $inspector->email,
//         'phone' => $inspector->phone,
//         'designation' => $inspector->designation,
//         'department' => $inspector->department,
//         'address' => $inspector->address,
//         'status' => $inspector->status,
//         'inspector_id' => $inspector->inspector_id,
//         'image' => $inspector->image,
//         'role' => $inspector->role ? [
//             'id' => $inspector->role->id,
//             'name' => $inspector->role->name,
//             'description' => $inspector->role->description,
//             'is_active' => $inspector->role->is_active,
//         ] : null,
//     ];

//     // Clean up project response
//     unset($project->architect_category_id);
//     unset($project->builder_sub_category_id);
//     unset($project->architectCategory);
//     unset($project->builderSubCategory);
//     unset($project->permitNumbers);
//     unset($project->physicalInspectionReport);
//     unset($project->department_trade);
//     unset($project->inspector);

//     return response()->json([
//         'message' => $reports ? 'Project details retrieved successfully' : 'No inspection reports found for this inspector',
//         'data' => [
//             'project' => $projectDetails,
//             'current_inspector' => $inspectorDetails,
//             'inspection_reports' => $reports
//         ]
//     ], 200);
// }
// public function downloadReport(Request $request)
// {
//     try {
//         $inspector = Auth::guard('api')->user();

//         if (!$inspector) {
//             return response()->json(['message' => 'Unauthorized'], 401);
//         }

//         $validator = Validator::make($request->all(), [
//             'project_id' => 'required|integer|exists:website_builder_projects,id',
//         ]);

//         if ($validator->fails()) {
//             return response()->json([
//                 'message' => 'Validation failed',
//                 'errors' => $validator->errors(),
//             ], 422);
//         }

//         $project_id = $request->input('project_id');

//         $project = WebsiteBuilderProject::with([
//             'physicalInspectionReportnew' => function ($query) {
//                 $query->latest('inspection_done_at');
//             },
//             'inspector'
//         ])
//             ->where('id', $project_id)
//             ->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$inspector->department)])
//             ->first();

//         if (!$project) {
//             return response()->json([
//                 'message' => 'Project not found or not assigned to your department',
//             ], 404);
//         }

//         if (!$project->physicalInspectionReportnew) {
//             return response()->json([
//                 'message' => 'No inspection report found for this project',
//             ], 404);
//         }

//         $departmentIds = is_array($project->department_trade)
//             ? $project->department_trade
//             : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
//         $departmentIds = array_filter($departmentIds, 'is_numeric');
//         $project->department_names = McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();

//         $project->full_address = implode(', ', array_filter([
//             $project->project_address,
//             $project->city,
//             $project->state,
//             $project->zip
//         ]));

//         $inspectorNames = [];
//         if ($project->physicalInspectionReportnew && is_array($project->physicalInspectionReportnew->inspectors)) {
//             $inspectorNames = Inspector::whereIn('id', $project->physicalInspectionReportnew->inspectors)
//                 ->get()
//                 ->map(function ($inspector) {
//                     return $inspector->first_name . ' ' . $inspector->last_name;
//                 })
//                 ->toArray();
//         }

//         $reportStatus = $project->physicalInspectionReportnew->report_status ?? null;
//         switch ($reportStatus) {
//             case 0:
//                 $statusText = 'Approval';
//                 break;
//             case 1:
//                 $statusText = 'Violation';
//                 break;
//             case 2:
//                 $statusText = 'Pending';
//                 break;
//             default:
//                 $statusText = 'N/A';
//                 break;
//         }

//         $viewPath = 'BuilderWebsiteDashboard.physicalinspection.pdfreport';
//         if (!view()->exists($viewPath)) {
//             return response()->json([
//                 'message' => 'PDF template not found. Please contact administrator.',
//             ], 500);
//         }

//         $pdf = Pdf::loadView($viewPath, compact('project', 'inspectorNames', 'statusText'));
//         $pdf->setPaper('A4', 'portrait');

//         $filename = 'inspection_report_' . $project->id . '_' . date('Y-m-d') . '.pdf';

//         // Return PDF as downloadable response
//         return $pdf->download($filename);

//     } catch (\Exception $e) {
//         \Log::error('PDF Download Error: ' . $e->getMessage(), [
//             'project_id' => $request->input('project_id'),
//             'inspector_id' => Auth::guard('api')->id(),
//             'trace' => $e->getTraceAsString()
//         ]);

//         return response()->json([
//             'success' => false,
//             'message' => 'Failed to generate PDF report',
//             'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
//         ], 500);
//     }
// }
public function downloadReport(Request $request)
    {
        try {
            $inspector = Auth::guard('api')->user();

            if (!$inspector) {
                return response()->json(['message' => 'Unauthorized'], 401);
            }

            $validator = Validator::make($request->all(), [
                'project_id' => 'required|integer|exists:website_builder_projects,id',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $project_id = $request->input('project_id');

            $project = WebsiteBuilderProject::with([
                'physicalInspectionReport' => function ($query) use ($inspector) {
                    $query->whereRaw('JSON_CONTAINS(inspectors, ?)', ['"' . $inspector->id . '"'])
                          ->latest('inspection_done_at')
                          ->take(1); // Use take(1) to limit to one record
                },
                'inspector'
            ])
                ->where('id', $project_id)
                ->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$inspector->department)])
                ->first();

            if (!$project) {
                return response()->json([
                    'message' => 'Project not found or not assigned to your department',
                ], 404);
            }

            // Access the first report from the collection
            $report = $project->physicalInspectionReport->first();

            if (!$report) {
                return response()->json([
                    'message' => 'No inspection report found for this project',
                ], 404);
            }

            $departmentIds = is_array($project->department_trade)
                ? $project->department_trade
                : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
            $departmentIds = array_filter($departmentIds, 'is_numeric');
            $project->department_names = McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();

            $project->full_address = implode(', ', array_filter([
                $project->project_address,
                $project->city,
                $project->state,
                $project->zip
            ]));

            $inspectorData = [];
            // Decode inspectors JSON if it's a string
            $reportInspectors = is_string($report->inspectors) ? json_decode($report->inspectors, true) : $report->inspectors;
            if (is_array($reportInspectors) && !empty($reportInspectors)) {
                $inspectorData = Inspector::whereIn('id', $reportInspectors)
                    ->get()
                    ->map(function ($inspector) {
                        return [
                            'name' => $inspector->first_name . ' ' . $inspector->last_name,
                            'department' => $inspector->department
                        ];
                    })
                    ->toArray();
            }

            $reportStatus = $report->report_status ?? null;
            switch ($reportStatus) {
                case 0:
                    $statusText = 'Approval';
                    break;
                case 1:
                    $statusText = 'Violation';
                    break;
                case 2:
                    $statusText = 'Pending';
                    break;
                default:
                    $statusText = 'N/A';
                    break;
            }

            $viewPath = 'BuilderWebsiteDashboard.physicalinspection.pdfreport';
            if (!view()->exists($viewPath)) {
                return response()->json([
                    'message' => 'PDF template not found. Please contact administrator.',
                ], 500);
            }

            $pdf = Pdf::loadView($viewPath, compact('project', 'report', 'inspectorData', 'statusText'));
            $pdf->setPaper('A4', 'portrait');

            $filename = 'inspection_report_' . $project->id . '_' . date('Y-m-d') . '.pdf';

            return $pdf->download($filename);

        } catch (\Exception $e) {
            \Log::error('PDF Download Error: ' . $e->getMessage(), [
                'project_id' => $request->input('project_id'),
                'inspector_id' => Auth::guard('api')->id(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to generate PDF report',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }
// public function countAll(Request $request)
// {
//     $inspector = Auth::guard('api')->user();

//     if (!$inspector) {
//         return response()->json(['message' => 'Unauthorized'], 401);
//     }

//     $inspectorId = $inspector->id;

//     // Total project count for the logged-in inspector (via physicalInspectionReport)
//     $totalProjectCount = WebsiteBuilderProject::whereHas('physicalInspectionReport', function ($subQuery) use ($inspectorId) {
//         $subQuery->whereRaw('JSON_CONTAINS(inspectors, ?)', ['"' . $inspectorId . '"']);
//     })->count();

//     // Pending project count for the logged-in inspector
//     $pendingProjectCount = WebsiteBuilderProject::where(function ($q) use ($inspectorId) {
//         $q->whereDoesntHave('physicalInspectionReport')
//           ->orWhereHas('physicalInspectionReport', function ($subQuery) use ($inspectorId) {
//               $subQuery->where('report_status', '2')
//                        ->whereRaw('JSON_CONTAINS(inspectors, ?)', ['"' . $inspectorId . '"']);
//           });
//     })->count();

//     // Violation project count for the logged-in inspector
//     $violationProjectCount = WebsiteBuilderProject::whereHas('physicalInspectionReport', function ($subQuery) use ($inspectorId) {
//         $subQuery->where('report_status', '1')
//                  ->whereRaw('JSON_CONTAINS(inspectors, ?)', ['"' . $inspectorId . '"']);
//     })->count();

//     // Approved project count for the logged-in inspector
//     $approvedProjectCount = WebsiteBuilderProject::whereHas('physicalInspectionReport', function ($subQuery) use ($inspectorId) {
//         $subQuery->where('report_status', '0')
//                  ->whereRaw('JSON_CONTAINS(inspectors, ?)', ['"' . $inspectorId . '"']);
//     })->count();

//     // Prepare inspector details
//     $inspectorDetails = [
//         'id' => $inspector->id,
//         'first_name' => $inspector->first_name,
//         'last_name' => $inspector->last_name,
//         'email' => $inspector->email,
//         'phone' => $inspector->phone,
//         'designation' => $inspector->designation,
//         'department' => $inspector->department,
//         'address' => $inspector->address,
//         'status' => $inspector->status,
//         'inspector_id' => $inspector->inspector_id,
//         'image' => $inspector->image,
//         'role' => $inspector->role ? [
//             'id' => $inspector->role->id,
//             'name' => $inspector->role->name,
//             'description' => $inspector->role->description,
//             'is_active' => $inspector->role->is_active,
//         ] : null,
//     ];

//     return response()->json([
//         'message' => 'Project counts retrieved successfully',
//         'inspector' => $inspectorDetails,
//         'data' => [
//             'total_projects' => (int)$totalProjectCount,
//             'pending_projects' => (int)$pendingProjectCount,
//             'violation_projects' => (int)$violationProjectCount,
//             'approved_projects' => (int)$approvedProjectCount,
//         ]
//     ], 200);
// }
public function countAll(Request $request)
{
    $inspector = Auth::guard('api')->user();

    if (!$inspector) {
        return response()->json(['message' => 'Unauthorized'], 401);
    }

    $inspectorId = $inspector->id;
    $inspectorDepartmentId = $inspector->department;

    // Base query to filter projects with all Department and Planning Commission steps approved
    $approvedProjectsQuery = function ($query) use ($inspectorDepartmentId) {
        $query->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$inspectorDepartmentId)]);

        $query->where(function ($subQuery) use ($inspectorDepartmentId) {
            $subQuery->where(function ($q) use ($inspectorDepartmentId) {
                // Fetch department IDs dynamically
                $departmentIds = \DB::table('website_builder_projects')
                    ->select('department_trade')
                    ->get()
                    ->flatMap(function ($project) {
                        return is_array($project->department_trade)
                            ? $project->department_trade
                            : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
                    })->filter(function ($id) {
                        return is_numeric($id);
                    })->unique()->toArray();

                foreach ($departmentIds as $deptId) {
                    $departmentModel = \App\Models\McdStaffRole::find($deptId);
                    if (!$departmentModel) continue;

                    $reviewProcess = \App\Models\ReviewProcess::where('review_level', 'Department')
                        ->whereJsonContains('department', $departmentModel->name)
                        ->first();

                    if ($reviewProcess && !empty($reviewProcess->review_steps)) {
                        $q->whereExists(function ($q2) use ($deptId, $reviewProcess) {
                            $q2->select(\DB::raw(1))
                               ->from('project_review_documents')
                               ->whereColumn('project_review_documents.project_id', 'website_builder_projects.id')
                               ->where('project_review_documents.department_id', $deptId)
                               ->whereIn('project_review_documents.review_step', $reviewProcess->review_steps)
                               ->groupBy('project_review_documents.project_id')
                               ->havingRaw('COUNT(*) = ?', [count($reviewProcess->review_steps)])
                               ->havingRaw('COUNT(*) = SUM(CASE WHEN project_review_documents.status = 2 THEN 1 ELSE 0 END)');
                        });
                    }
                }
            });
        });

        $query->where(function ($subQuery) {
            $pcReviewProcess = \App\Models\ReviewProcess::where('review_level', 'PlanningCommission')->first();
            if ($pcReviewProcess && !empty($pcReviewProcess->review_steps)) {
                $pcDepartments = is_array($pcReviewProcess->department)
                    ? $pcReviewProcess->department
                    : json_decode($pcReviewProcess->department, true) ?? [];

                foreach ($pcDepartments as $pcDeptName) {
                    $pcDeptModel = \App\Models\McdStaffRole::where('name', $pcDeptName)->first();
                    if (!$pcDeptModel) continue;

                    $subQuery->whereExists(function ($q2) use ($pcDeptModel, $pcReviewProcess) {
                        $q2->select(\DB::raw(1))
                           ->from('project_review_document_pcs')
                           ->whereColumn('project_review_document_pcs.project_id', 'website_builder_projects.id')
                           ->where('project_review_document_pcs.department_id', $pcDeptModel->id)
                           ->whereIn('project_review_document_pcs.review_step', $pcReviewProcess->review_steps)
                           ->groupBy('project_review_document_pcs.project_id')
                           ->havingRaw('COUNT(*) = ?', [count($pcReviewProcess->review_steps)])
                           ->havingRaw('COUNT(*) = SUM(CASE WHEN project_review_document_pcs.status = 2 THEN 1 ELSE 0 END)');
                    });
                }
            }
        });
    };

    // Total project count for the logged-in inspector (via physicalInspectionReport)
    $totalProjectCount = WebsiteBuilderProject::whereHas('physicalInspectionReport', function ($subQuery) use ($inspectorId) {
        $subQuery->whereRaw('JSON_CONTAINS(inspectors, ?)', ['"' . $inspectorId . '"']);
    })->where($approvedProjectsQuery)->count();

    // Pending project count for the logged-in inspector
    $pendingProjectCount = WebsiteBuilderProject::where(function ($q) use ($inspectorId) {
        $q->whereDoesntHave('physicalInspectionReport')
          ->orWhereHas('physicalInspectionReport', function ($subQuery) use ($inspectorId) {
              $subQuery->where('report_status', '2')
                       ->whereRaw('JSON_CONTAINS(inspectors, ?)', ['"' . $inspectorId . '"']);
          });
    })->where($approvedProjectsQuery)->count();

    // Violation project count for the logged-in inspector
    $violationProjectCount = WebsiteBuilderProject::whereHas('physicalInspectionReport', function ($subQuery) use ($inspectorId) {
        $subQuery->where('report_status', '1')
                 ->whereRaw('JSON_CONTAINS(inspectors, ?)', ['"' . $inspectorId . '"']);
    })->where($approvedProjectsQuery)->count();

    // Approved project count for the logged-in inspector
    $approvedProjectCount = WebsiteBuilderProject::whereHas('physicalInspectionReport', function ($subQuery) use ($inspectorId) {
        $subQuery->where('report_status', '0')
                 ->whereRaw('JSON_CONTAINS(inspectors, ?)', ['"' . $inspectorId . '"']);
    })->where($approvedProjectsQuery)->count();

    // Prepare inspector details
    $inspectorDetails = [
        'id' => $inspector->id,
        'first_name' => $inspector->first_name,
        'last_name' => $inspector->last_name,
        'email' => $inspector->email,
        'phone' => $inspector->phone,
        'designation' => $inspector->designation,
        'department' => $inspector->department,
        'address' => $inspector->address,
        'status' => $inspector->status,
        'inspector_id' => $inspector->inspector_id,
        'image' => $inspector->image,
        'role' => $inspector->role ? [
            'id' => $inspector->role->id,
            'name' => $inspector->role->name,
            'description' => $inspector->role->description,
            'is_active' => $inspector->role->is_active,
        ] : null,
    ];

    return response()->json([
        'message' => 'Project counts retrieved successfully',
        'inspector' => $inspectorDetails,
        'data' => [
            'total_projects' => (int)$totalProjectCount,
            'pending_projects' => (int)$pendingProjectCount,
            'violation_projects' => (int)$violationProjectCount,
            'approved_projects' => (int)$approvedProjectCount,
        ]
    ], 200);
}
// public function countAll(Request $request)
// {
//     $inspector = Auth::guard('api')->user();

//     if (!$inspector) {
//         return response()->json(['message' => 'Unauthorized'], 401);
//     }

//     $inspectorDepartmentId = $inspector->department;
//     $inspectorId = (string) $inspector->id;

//     // Total project count
//     $totalProjectCount = WebsiteBuilderProject::whereRaw(
//         'JSON_CONTAINS(department_trade, ?)',
//         [json_encode($inspectorDepartmentId)]
//     )->count();

//     // ✅ Pending = where inspector has NOT submitted a report with report_status = 0 (Approved)
//     $pendingProjectCount = WebsiteBuilderProject::whereRaw(
//         'JSON_CONTAINS(department_trade, ?)',
//         [json_encode($inspectorDepartmentId)]
//     )->where(function ($query) use ($inspectorId) {
//         $query->whereDoesntHave('physicalInspectionReportnew', function ($subQuery) use ($inspectorId) {
//             $subQuery->where('report_status', 0)
//                      ->whereJsonContains('inspectors', $inspectorId);
//         });
//     })->count();

//     // Violation count where inspector submitted with status = 1
//     $violationProjectCount = WebsiteBuilderProject::whereRaw(
//         'JSON_CONTAINS(department_trade, ?)',
//         [json_encode($inspectorDepartmentId)]
//     )->whereHas('physicalInspectionReportnew', function ($query) use ($inspectorId) {
//         $query->where('report_status', 1)
//               ->whereJsonContains('inspectors', $inspectorId);
//     })->count();

//     // Approved count where inspector submitted with status = 0
//     $approvedProjectCount = WebsiteBuilderProject::whereRaw(
//         'JSON_CONTAINS(department_trade, ?)',
//         [json_encode($inspectorDepartmentId)]
//     )->whereHas('physicalInspectionReportnew', function ($query) use ($inspectorId) {
//         $query->where('report_status', 0)
//               ->whereJsonContains('inspectors', $inspectorId);
//     })->count();

//     // Inspector details
//     $inspectorDetails = [
//         'id' => $inspector->id,
//         'first_name' => $inspector->first_name,
//         'last_name' => $inspector->last_name,
//         'email' => $inspector->email,
//         'phone' => $inspector->phone,
//         'designation' => $inspector->designation,
//         'department' => $inspector->department,
//         'address' => $inspector->address,
//         'status' => $inspector->status,
//         'inspector_id' => $inspector->inspector_id,
//         'image' => $inspector->image,
//         'role' => $inspector->role ? [
//             'id' => $inspector->role->id,
//             'name' => $inspector->role->name,
//             'description' => $inspector->role->description,
//             'is_active' => $inspector->role->is_active,
//         ] : null,
//     ];

//     return response()->json([
//         'message' => 'Project counts retrieved successfully',
//         'inspector' => $inspectorDetails,
//         'data' => [
//             'total_projects' => (int)$totalProjectCount,
//             'pending_projects' => (int)$pendingProjectCount,
//             'violation_projects' => (int)$violationProjectCount,
//             'approved_projects' => (int)$approvedProjectCount,
//         ]
//     ], 200);
// }


// public function countAll(Request $request)
// {
//     $inspector = Auth::guard('api')->user();

//     if (!$inspector) {
//         return response()->json(['message' => 'Unauthorized'], 401);
//     }

//     $inspectorDepartmentId = $inspector->department;

//     // Total project count
//     $totalProjectCount = WebsiteBuilderProject::whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$inspectorDepartmentId)])
//         ->count();

//     // Pending project count
//     $pendingProjectCount = WebsiteBuilderProject::whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$inspectorDepartmentId)])
//         ->where(function ($q) {
//             $q->whereDoesntHave('physicalInspectionReport')
//               ->orWhereHas('physicalInspectionReport', function ($subQuery) {
//                   $subQuery->where('report_status', '2');
//               });
//         })
//         ->count();

//     // Violation project count
//     $violationProjectCount = WebsiteBuilderProject::whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$inspectorDepartmentId)])
//         ->whereHas('physicalInspectionReport', function ($subQuery) {
//             $subQuery->where('report_status', '1');
//         })
//         ->count();

//     // Approved project count
//     $approvedProjectCount = WebsiteBuilderProject::whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$inspectorDepartmentId)])
//         ->whereHas('physicalInspectionReport', function ($subQuery) use ($inspector) {
//             $subQuery->where('report_status', '0')
//                      ->whereRaw('JSON_CONTAINS(inspectors, ?)', ['"' . $inspector->id . '"']);
//         })
//         ->count();

//     // Prepare inspector details
//     $inspectorDetails = [
//         'id' => $inspector->id,
//         'first_name' => $inspector->first_name,
//         'last_name' => $inspector->last_name,
//         'email' => $inspector->email,
//         'phone' => $inspector->phone,
//         'designation' => $inspector->designation,
//         'department' => $inspector->department,
//         'address' => $inspector->address,
//         'status' => $inspector->status,
//         'inspector_id' => $inspector->inspector_id,
//         'image' => $inspector->image,
//         'role' => $inspector->role ? [
//             'id' => $inspector->role->id,
//             'name' => $inspector->role->name,
//             'description' => $inspector->role->description,
//             'is_active' => $inspector->role->is_active,
//         ] : null,
//     ];

//     return response()->json([
//         'message' => 'Project counts retrieved successfully',
//         'inspector' => $inspectorDetails,
//         'data' => [
//             'total_projects' => (int)$totalProjectCount,
//             'pending_projects' => (int)$pendingProjectCount,
//             'violation_projects' => (int)$violationProjectCount,
//             'approved_projects' => (int)$approvedProjectCount,
//         ]
//     ], 200);
// }
}