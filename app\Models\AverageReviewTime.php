<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AverageReviewTime extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_name',
        'days', // Changed from 'time' to 'date'
        'builder_project_id', // Foreign key to link with BuilderProject
        'end_date'
    ];

   

    // Relationship with BuilderProject
    public function builderProject()
    {
        return $this->belongsTo(BuilderProject::class);
    }
}