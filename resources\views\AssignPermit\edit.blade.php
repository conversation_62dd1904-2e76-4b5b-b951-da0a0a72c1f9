@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4>Edit Permit Number</h4>
                        <a href="{{ route('project-department-permits.index') }}" class="btn btn-secondary">Back</a>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('project-department-permits.update', $permit->id) }}" method="POST">
                            @csrf
                            @method('PUT')
                            <div class="form-group">
                                <label for="website_builder_project_id">Project <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" value="{{ $permit->project->project_name }}" readonly>
                                <input type="hidden" name="website_builder_project_id" value="{{ $permit->website_builder_project_id }}" required>
                                @error('website_builder_project_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="form-group">
                                <label for="mcd_staff_role_id">Department <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" value="{{ $permit->department->name }}" readonly>
                                <input type="hidden" name="mcd_staff_role_id" value="{{ $permit->mcd_staff_role_id }}" required>
                                @error('mcd_staff_role_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="form-group">
                                <label for="permit_number">Permit Number <span class="text-danger">*</span></label>
                                <input type="text" name="permit_number" id="permit_number" class="form-control @error('permit_number') is-invalid @enderror" value="{{ old('permit_number', $permit->permit_number) }}" required>
                                @error('permit_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary mr-2">Update Permit</button>
                                <a href="{{ route('project-department-permits.index') }}" class="btn btn-secondary">Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection