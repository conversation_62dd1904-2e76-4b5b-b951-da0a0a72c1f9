<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;

class Message extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'sender_id',
        'sender_id_inspector',
        'sender_id_mcd_staff',
        'sender_id_builder_staff',
        'receiver_id',
        'receiver_id_inspector',
        'receiver_id_mcd_staff',
        'receiver_id_builder_staff',
        'subject',
        'body',
        'attachment_count',
        'parent_id',
        'sender_id_architect',
        'receiver_id_architect'
    ];

    public function senderInspectorRelation()
    {
        return $this->belongsTo(Inspector::class, 'sender_id_inspector');
    }

    public function senderUserRelation()
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    public function senderMcdStaffRelation()
    {
        return $this->belongsTo(McdStaff::class, 'sender_id_mcd_staff');
    }

    public function senderBuilderStaffRelation()
    {
        return $this->belongsTo(BuilderStaff::class, 'sender_id_builder_staff');
    }
 public function senderArchitectRelation()
    {
        return $this->belongsTo(Architect::class, 'sender_id_architect');
    }
    public function receiverInspectorRelation()
    {
        return $this->belongsTo(Inspector::class, 'receiver_id_inspector');
    }

    public function receiverUserRelation()
    {
        return $this->belongsTo(User::class, 'receiver_id');
    }

    public function receiverMcdStaffRelation()
    {
        return $this->belongsTo(McdStaff::class, 'receiver_id_mcd_staff');
    }

    public function receiverBuilderStaffRelation()
    {
        return $this->belongsTo(BuilderStaff::class, 'receiver_id_builder_staff');
    }
public function receiverArchitectRelation()
    {
        return $this->belongsTo(Architect::class, 'receiver_id_architect');
    }
    public function attachments()
    {
        return $this->hasMany(Attachment::class, 'message_id');
    }

    public function userStars()
    {
        return $this->hasMany(MessageUserStar::class, 'message_id');
    }

    // public function getSenderNameAttribute()
    // {
    //     if ($this->sender_id_inspector && $inspector = $this->senderInspectorRelation()->first()) {
    //         return $inspector->first_name . ' ' . $inspector->last_name;
    //     } elseif ($this->sender_id_mcd_staff && $mcdStaff = $this->senderMcdStaffRelation()->first()) {
    //         return $mcdStaff->staff_name;
    //     } elseif ($this->sender_id_builder_staff && $builderStaff = $this->senderBuilderStaffRelation()->first()) {
    //         return $builderStaff->staff_name;
    //     } elseif ($this->sender_id && $user = $this->senderUserRelation()->first()) {
    //         return $user->name;
    //     }
    //     return 'Unknown';
    // }

    // public function getReceiverNameAttribute()
    // {
    //     if ($this->receiver_id_inspector && $inspector = $this->receiverInspectorRelation()->first()) {
    //         return $inspector->first_name . ' ' . $inspector->last_name;
    //     } elseif ($this->receiver_id_mcd_staff && $mcdStaff = $this->receiverMcdStaffRelation()->first()) {
    //         return $mcdStaff->staff_name;
    //     } elseif ($this->receiver_id_builder_staff && $builderStaff = $this->receiverBuilderStaffRelation()->first()) {
    //         return $builderStaff->staff_name;
    //     } elseif ($this->receiver_id && $user = $this->receiverUserRelation()->first()) {
    //         return $user->name;
    //     }
    //     return 'Unknown';
    // }

    public function getSenderNameAttribute()
{
    if ($this->sender_id_inspector && $inspector = $this->senderInspectorRelation()->first()) {
        return $inspector->first_name . ' ' . $inspector->last_name;
    } elseif ($this->sender_id_mcd_staff && $mcdStaff = $this->senderMcdStaffRelation()->first()) {
        return $mcdStaff->staff_name;
    } elseif ($this->sender_id_builder_staff && $builderStaff = $this->senderBuilderStaffRelation()->first()) {
        return $builderStaff->staff_name;
    } elseif ($this->sender_id_architect && $architect = $this->senderArchitectRelation()->first()) {
        return $architect->name;
    } elseif ($this->sender_id && $user = $this->senderUserRelation()->first()) {
        return $user->name;
    }
    return 'Unknown';
}

public function getReceiverNameAttribute()
{
    if ($this->receiver_id_inspector && $inspector = $this->receiverInspectorRelation()->first()) {
        return $inspector->first_name . ' ' . $inspector->last_name;
    } elseif ($this->receiver_id_mcd_staff && $mcdStaff = $this->receiverMcdStaffRelation()->first()) {
        return $mcdStaff->staff_name;
    } elseif ($this->receiver_id_builder_staff && $builderStaff = $this->receiverBuilderStaffRelation()->first()) {
        return $builderStaff->staff_name;
    } elseif ($this->receiver_id_architect && $architect = $this->receiverArchitectRelation()->first()) {
        return $architect->name;
    } elseif ($this->receiver_id && $user = $this->receiverUserRelation()->first()) {
        return $user->name;
    }
    return 'Unknown';
}

    public function getContentAttribute()
    {
        return $this->body;
    }

    public function getSubjectAttribute()
    {
        return $value ?? 'No Subject';
    }

    public function getIsReplyAttribute()
    {
        return !is_null($this->parent_id);
    }

    public function getIsStarredAttribute()
    {
        $guard = auth()->guard('staff_mcd')->check() ? 'staff_mcd' : (auth()->guard('staff_builder')->check() ? 'staff_builder' : 'web');
        $userId = auth()->guard('staff_mcd')->check()
            ? auth()->guard('staff_mcd')->id()
            : (auth()->guard('staff_builder')->check()
                ? auth()->guard('staff_builder')->id()
                : Auth::id());

        $star = $this->userStars()->where(function ($query) use ($guard, $userId) {
            if ($guard === 'staff_mcd') {
                $query->where('mcd_staff_id', $userId);
            } elseif ($guard === 'staff_builder') {
                $query->where('builder_staff_id', $userId);
            } else {
                $query->where('user_id', $userId);
            }
        })->first();

        return $star ? $star->is_starred : false;
    }

   public function getAttachmentsAttribute()
{
    $panels = [
        'builderwebsite' => 'http://localhost/builderwebsite/public',
        'mcdconstructions' => 'http://localhost/mcdconstructions/public',
        'engineer' => 'http://localhost/engineer/public',
        'realstate_superadmin' => 'http://localhost/realstate_superadmin/public',
        'inspector' => 'http://localhost/mcdcconstructions/inspector/public',
    ];

    $attachments = $this->relationLoaded('attachments') ? $this->getRelation('attachments') : $this->attachments()->get();

    return $attachments->map(function ($attachment) use ($panels) {
        $fileName = $attachment->file_name ?? basename(parse_url($attachment->file_path, PHP_URL_PATH)) ?? 'unknown-file';
        $relativePath = ltrim(parse_url($attachment->file_path, PHP_URL_PATH), '/');
        $mimeType = 'application/octet-stream';
        $url = null;

        foreach ($panels as $folder => $baseUrl) {
            $fullPath = base_path("../$folder/public/" . $relativePath);
            if (file_exists($fullPath)) {
                $mimeType = mime_content_type($fullPath);
                $url = "$baseUrl/$relativePath";
                break;
            }
        }

        $url = $url ?? asset($attachment->file_path);

        return [
            'url' => $url,
            'name' => $fileName,
            'type' => $mimeType,
        ];
    })->toArray();
}

}