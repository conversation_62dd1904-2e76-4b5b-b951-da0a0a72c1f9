# PDF Annotation Tool - Server Setup Commands

## Overview
This document contains all the commands needed to run the PDF annotation system with <PERSON><PERSON> backend and Vue.js frontend.

## Current Server Configuration

### 1. Laravel Backend Server
**Purpose:** Serves the main Laravel application and API endpoints
**Port:** 8000 (to avoid conflict with XAMPP)
**Status:** Currently running

```bash
# Navigate to project directory
cd C:\xampp\htdocs\mcdconstructions

# Start Laravel development server
php artisan serve --host=localhost --port=8000
```

**Access URLs:**
- Main Laravel App: `http://localhost:8000`
- API Endpoints: `http://localhost:8000/api/annotated-pdfs`
- Test API: `http://localhost:8000/api/guidelines`

### 2. Vue.js Annotation Frontend
**Purpose:** PDF annotation tool interface
**Port:** 3002 (auto-selected when 3001 was in use)
**Status:** Currently running

```bash
# Navigate to project directory (if not already there)
cd C:\xampp\htdocs\mcdconstructions

# Start Vue development server
npm run dev
```

**Access URLs:**
- Annotation Tool: `http://localhost:3002/`
- With PDF: `http://localhost:3002/?file=http://localhost:8000/attachments/6853f39248aa3.pdf&projectId=1&departmentId=2&reviewStep=3`

### 3. XAMPP Services
**Purpose:** Database (MySQL) and phpMyAdmin access
**Port:** 80 (default Apache port)
**Status:** Should be running via XAMPP Control Panel

```bash
# Start XAMPP services via XAMPP Control Panel:
# 1. Open XAMPP Control Panel
# 2. Start Apache service
# 3. Start MySQL service
```

**Access URLs:**
- phpMyAdmin: `http://localhost/phpmyadmin/`
- XAMPP Dashboard: `http://localhost/`

## Complete Startup Sequence

### Step 1: Start XAMPP Services
1. Open **XAMPP Control Panel**
2. Click **Start** for **Apache**
3. Click **Start** for **MySQL**
4. Verify both show "Running" status

### Step 2: Start Laravel Backend
```bash
# Open PowerShell/Command Prompt
cd C:\xampp\htdocs\mcdconstructions
php artisan serve --host=localhost --port=8000
```

### Step 3: Start Vue Frontend
```bash
# Open another PowerShell/Command Prompt window
cd C:\xampp\htdocs\mcdconstructions
npm run dev
```

## Verification Commands

### Test Laravel API
```bash
# Test CORS and API availability
Invoke-WebRequest -Uri "http://localhost:8000/api/guidelines" -Method GET

# Test annotation endpoint (OPTIONS preflight)
Invoke-WebRequest -Uri "http://localhost:8000/api/annotated-pdfs" -Method OPTIONS -Headers @{"Origin"="http://localhost:3002"}
```

### Test Database Connection
- Access: `http://localhost/phpmyadmin/`
- Check tables: `annotations`, `annotated_pdfs`

### Test Annotation Tool
- Access: `http://localhost:3002/?file=http://localhost:8000/attachments/6853f39248aa3.pdf&projectId=1&departmentId=2&reviewStep=3`
- Test annotation saving functionality

## Port Configuration Summary

| Service | Port | URL | Purpose |
|---------|------|-----|---------|
| XAMPP Apache | 80 | `http://localhost/` | phpMyAdmin, static files |
| Laravel Backend | 8000 | `http://localhost:8000/` | API endpoints, main app |
| Vue Frontend | 3002 | `http://localhost:3002/` | Annotation interface |
| MySQL Database | 3306 | Internal | Database connection |

## Troubleshooting

### If Laravel server fails to start:
```bash
# Check if port 8000 is in use
netstat -an | findstr :8000

# Try different port if needed
php artisan serve --host=localhost --port=8001
```

### If Vue server fails to start:
```bash
# Clear npm cache
npm cache clean --force

# Reinstall dependencies
npm install

# Start development server
npm run dev
```

### If phpMyAdmin not accessible:
1. Ensure XAMPP Apache is running
2. Check XAMPP Control Panel for port conflicts
3. Try `http://127.0.0.1/phpmyadmin/`

## Current Working Configuration
- ✅ Laravel Backend: `http://localhost:8000`
- ✅ Vue Frontend: `http://localhost:3002`
- ✅ phpMyAdmin: `http://localhost/phpmyadmin/`
- ✅ CORS: Properly configured
- ✅ Database: Connected and working
- ✅ File Storage: `public/annotated_pdfs/project_{id}/`

## Notes
- Laravel backend URL in Vue app: `http://localhost:8000`
- All three services can run simultaneously without conflicts
- Annotation saving works end-to-end with database integration
- Files are saved to public folder as requested
