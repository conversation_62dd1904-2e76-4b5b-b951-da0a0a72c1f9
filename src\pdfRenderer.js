import * as pdfjsLib from 'pdfjs-dist'

// Initialize PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js'

export class PDFRenderer {
  constructor() {
    this.pdfDoc = null
    this.currentScale = 1.5
    this.pageCanvases = new Map()
    this.baseScale = 1.5
  }

  async loadDocument(arrayBuffer) {
    this.pdfDoc = await pdfjsLib.getDocument({ data: arrayBuffer }).promise
    return this.pdfDoc.numPages
  }

  setScale(containerWidth) {
    if (!this.pdfDoc) return

    // Get the first page to calculate scale
    this.pdfDoc.getPage(1).then(page => {
      const viewport = page.getViewport({ scale: 1 })
      const pageWidth = viewport.width
      
      // Calculate scale to fit container width with margins
      this.currentScale = (containerWidth / pageWidth)
      
      // Limit minimum and maximum scale
      this.currentScale = Math.max(0.5, Math.min(2, this.currentScale))
    })
  }

  async renderPage(pageNumber, container) {
    if (!this.pdfDoc || !container) {
      console.error('PDF document or container not available')
      return null
    }

    try {
      const page = await this.pdfDoc.getPage(pageNumber)
      const viewport = page.getViewport({ scale: this.currentScale })

      // Create page container
      const pageContainer = document.createElement('div')
      pageContainer.className = 'pdf-page'
      pageContainer.style.position = 'relative'
      pageContainer.style.width = `${viewport.width}px`
      pageContainer.style.height = `${viewport.height}px`
      pageContainer.style.margin = '0 auto'

      // Create PDF canvas
      const canvas = document.createElement('canvas')
      canvas.className = 'pdf-canvas'
      const context = canvas.getContext('2d')
      canvas.width = viewport.width
      canvas.height = viewport.height

      // Store canvas reference
      this.pageCanvases.set(pageNumber, canvas)

      // Render PDF page
      await page.render({
        canvasContext: context,
        viewport: viewport
      }).promise

      pageContainer.appendChild(canvas)
      container.appendChild(pageContainer)

      return {
        container: pageContainer,
        viewport: viewport,
        canvas: canvas
      }
    } catch (error) {
      console.error('Error rendering PDF page:', error)
      return null
    }
  }

  getPageCanvas(pageNumber) {
    return this.pageCanvases.get(pageNumber)
  }

  clearPages() {
    this.pageCanvases.clear()
  }
} 