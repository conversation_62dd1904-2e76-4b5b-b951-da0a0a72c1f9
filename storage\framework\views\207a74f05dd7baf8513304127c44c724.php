<div class="main-sidebar sidebar-style-2">
    <?php   
    $user = Auth::guard('staff_mcd')->user();
    $isAdmin = Auth::guard('web')->check();
    $permissions = $user && $user->role ? $user->role->permissions : [];

    function hasPermission($permissions, $key, $type = 'read') {
        // Access the global $isAdmin inside the function
        return (Auth::guard('web')->check()) || 
               (isset($permissions[$key]) && $permissions[$key][$type] == 1);
    }
?>

    <aside id="sidebar-wrapper">
        <div class="sidebar-brand">
            <?php if($isAdmin || hasPermission($permissions, 'dashboard')): ?>
                <a href="<?php echo e(route('dashboard')); ?>"> 
                    <img alt="image" src="<?php echo e(asset('assets/img/cos.png')); ?>" style="height: 70px;" class="header-logo" /> 
                    <span class="logo-name" style="font-size: 12px;"></span>
                </a>
            <?php endif; ?>
        </div>

        <ul class="sidebar-menu">
            <?php if($isAdmin || hasPermission($permissions, 'dashboard')): ?>
                <li class="dropdown <?php echo e(request()->routeIs('dashboard') ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('dashboard')); ?>" class="nav-link"><i class="fas fa-th"></i><span>Dashboard</span></a>
                </li>
            <?php endif; ?>

            
            <?php if($isAdmin): ?>
            <li class="dropdown <?php echo e(request()->routeIs('mcd-staff.create') || 
                request()->routeIs('mcd-staff.edit') || 
                request()->routeIs('roles.index') || 
                request()->routeIs('mcd-staff.index') || 
                request()->routeIs('permissions.index') ? 'active' : ''); ?>">
                <a href="javascript:void(0)" class="menu-toggle nav-link has-dropdown"><i class="fas fa-clipboard-list"></i><span>Roles Management</span></a>
                <ul class="dropdown-menu">
                    <li><a class="nav-link <?php echo e(request()->routeIs('roles.index') ? 'active' : ''); ?>" href="<?php echo e(route('roles.index')); ?>">Roles List</a></li>
                    <li><a class="nav-link <?php echo e(request()->routeIs('mcd-staff.index') ? 'active' : ''); ?>" href="<?php echo e(route('mcd-staff.index')); ?>">Staff List</a></li>
                    <li><a class="nav-link <?php echo e(request()->routeIs('permissions.index') ? 'active' : ''); ?>" href="<?php echo e(route('permissions.index')); ?>">Permissions</a></li>
                </ul>
            </li>
            <?php endif; ?>

            
            <?php if(hasPermission($permissions, 'department_trade') || hasPermission($permissions, 'inspectors')): ?>
            <li class="dropdown <?php echo e(request()->routeIs('mcd-staff-roles.index') || 
                request()->routeIs('inspectors.index') ? 'active' : ''); ?>">
                <a href="javascript:void(0)" class="menu-toggle nav-link has-dropdown"><i class="fas fa-clipboard-list"></i><span>Department Management</span></a>
                <ul class="dropdown-menu">
                    <?php if(hasPermission($permissions, 'department_trade')): ?>
                    <li><a class="nav-link <?php echo e(request()->routeIs('mcd-staff-roles.index') ? 'active' : ''); ?>" href="<?php echo e(route('mcd-staff-roles.index')); ?>">Department (Trade)</a></li>
                    <?php endif; ?>
                    <?php if(hasPermission($permissions, 'inspectors')): ?>
                    <li><a class="nav-link <?php echo e(request()->routeIs('inspectors.index') ? 'active' : ''); ?>" href="<?php echo e(route('inspectors.index')); ?>">Inspectors</a></li>
                    <?php endif; ?>
                </ul>
            </li>
            <?php endif; ?>

            
            <?php if(
                hasPermission($permissions, 'review_processes') || hasPermission($permissions, 'departmental_review') ||
                hasPermission($permissions, 'plannining_commission_review') || hasPermission($permissions, 'total_review_process') ||
                hasPermission($permissions, 'assign_puid') || hasPermission($permissions, 'assign_permit_number') ||
                hasPermission($permissions, 'avg_review_time')
            ): ?>
            <li class="dropdown <?php echo e(request()->routeIs('myprojects.index') || 
                request()->routeIs('myprojects.non_pending') || 
                request()->routeIs('project_municipals.index') || 
                request()->routeIs('average-review-times.index') || 
                request()->routeIs('review_processes.index') || 
                request()->routeIs('deptreview.index') || 
                request()->routeIs('assign-permit.index') ||
                request()->routeIs('commissionreview') || 
                request()->routeIs('permit-numbers.index') ? 'active' : ''); ?>">
                <a href="javascript:void(0)" class="menu-toggle nav-link has-dropdown"><i class="far fa-lightbulb"></i><span>Project Management</span></a>
                <ul class="dropdown-menu">
                    <?php if(hasPermission($permissions, 'review_processes')): ?>
                    <li><a class="nav-link <?php echo e(request()->routeIs('review_processes.index') ? 'active' : ''); ?>" href="<?php echo e(route('review_processes.index')); ?>">Create Review Process</a></li>
                    <?php endif; ?>
                    <?php if(hasPermission($permissions, 'departmental_review')): ?>
                    <li><a class="nav-link <?php echo e(request()->routeIs('deptreview.index') ? 'active' : ''); ?>" href="<?php echo e(route('deptreview.index')); ?>">Departmental Review</a></li>
                    <?php endif; ?>
                    <?php if(hasPermission($permissions, 'plannining_commission_review')): ?>
                    <li><a class="nav-link <?php echo e(request()->routeIs('commissionreview') ? 'active' : ''); ?>" href="<?php echo e(route('commissionreview')); ?>">Planning Commission Review</a></li>
                    <?php endif; ?>
                    
                    <?php if(hasPermission($permissions, 'assign_puid')): ?>
                    <li><a class="nav-link <?php echo e(request()->routeIs('assign-permit-numbers.index') ? 'active' : ''); ?>" href="<?php echo e(route('assign-permit-numbers.index')); ?>">Assign PUID</a></li>
                    <?php endif; ?>
                    <?php if(hasPermission($permissions, 'assign_permit_number')): ?>
                    <li><a class="nav-link <?php echo e(request()->routeIs('project-department-permits.index') ? 'active' : ''); ?>" href="<?php echo e(route('project-department-permits.index')); ?>">Assign Permit Number</a></li>
                    <?php endif; ?>
                    <?php if(hasPermission($permissions, 'avg_review_time')): ?>
                    <li><a class="nav-link <?php echo e(request()->routeIs('average-review-times.index') ? 'active' : ''); ?>" href="<?php echo e(route('average-review-times.index')); ?>">Avg Review Time</a></li>
                    <?php endif; ?>
                </ul>
            </li>
            <?php endif; ?>

            
            <?php if(hasPermission($permissions, 'active_inspections')): ?>
            <li class="dropdown">
                <a href="javascript:void(0)" class="menu-toggle nav-link has-dropdown"><i class="fas fa-file-alt"></i><span>Physical Inspection</span></a>
                <ul class="dropdown-menu">
                    <li><a class="nav-link" href="<?php echo e(route('physicalinspection.index')); ?>">Active Inspections</a></li>
                    <li><a class="nav-link" href="<?php echo e(route('physicalinspection.report')); ?>">Field Reports Submitted</a></li>
                    <li><a class="nav-link" href="<?php echo e(route('physical-inspection.complianceflag')); ?>">Compliance Flags Raised</a></li>
                </ul>
            </li>
            <?php endif; ?>

            <?php if(hasPermission($permissions, 'backlogs_analysis')): ?>
            <li class="dropdown <?php echo e(request()->routeIs('myprojects.backlogs') ? 'active' : ''); ?>">
                <a href="javascript:void(0)" class="menu-toggle nav-link has-dropdown"><i class="fas fa-landmark"></i><span>Municipal Performance</span></a>
                <ul class="dropdown-menu">
                    <li><a class="nav-link" href="<?php echo e(route('myprojects.backlogs')); ?>">Backlogs Analysis</a></li>
                </ul>
            </li>
            <?php endif; ?>

            <?php if(hasPermission($permissions, 'notification')): ?>
            <li class="dropdown <?php echo e(request()->routeIs('notifications.index') ? 'active' : ''); ?>">
                <a href="javascript:void(0)" class="menu-toggle nav-link has-dropdown"><i class="fas fa-bell"></i><span>Notification</span></a>
                <ul class="dropdown-menu">
                    <li><a class="nav-link" href="<?php echo e(route('notifications.index')); ?>">List Of Notification</a></li>
                </ul>
            </li>
            <?php endif; ?>

            <?php if(hasPermission($permissions, 'compliance')): ?>
            <li class="dropdown <?php echo e(request()->routeIs('compliances.create') ? 'active' : ''); ?>">
                <a href="javascript:void(0)" class="menu-toggle nav-link has-dropdown"><i class="fas fa-clipboard-check"></i><span>Compliance</span></a>
                <ul class="dropdown-menu">
                    <?php if(hasPermission($permissions, 'compliance', 'write')): ?>
                        <li><a class="nav-link <?php echo e(request()->routeIs('compliances.create') ? 'active' : ''); ?>" href="<?php echo e(route('compliances.create')); ?>">Add Compliance</a></li>
                    <?php endif; ?>
                    <li><a class="nav-link" href="<?php echo e(route('compliances.index')); ?>">List Of Compliance</a></li>

                </ul>
            </li>
            <?php endif; ?>

            <?php if(hasPermission($permissions, 'guidelines')): ?>
            <li class="dropdown">
                <a href="javascript:void(0)" class="menu-toggle nav-link has-dropdown"><i class="fas fa-bullhorn"></i><span>Guidelines</span></a>
                <ul class="dropdown-menu">
                    <li><a class="nav-link" href="<?php echo e(route('guidelines.index')); ?>">Guidelines</a></li>
                </ul>
            </li>
            <?php endif; ?>

            <?php if(hasPermission($permissions, 'communication')): ?>
            <li class="dropdown">
                <a href="javascript:void(0)" class="menu-toggle nav-link has-dropdown"><i class="fas fa-comments"></i><span>Communication</span></a>
                <ul class="dropdown-menu">
                    <li><a class="nav-link" href="<?php echo e(route('communication.index')); ?>">Messages</a></li>
                </ul>
            </li>
            <?php endif; ?>

            <?php if(hasPermission($permissions, 'rules')): ?>
            <li class="dropdown <?php echo e(request()->routeIs('rules.index') || request()->routeIs('rules.create') ? 'active' : ''); ?>">
                <a href="javascript:void(0)" class="menu-toggle nav-link has-dropdown"><i class="fas fa-scroll"></i><span>Rules & Regulation</span></a>
                <ul class="dropdown-menu">
                     <?php if(hasPermission($permissions, 'rules', 'write')): ?>
                         <li><a class="nav-link" href="<?php echo e(route('rules.create')); ?>">Add Rules</a></li>
                    <?php endif; ?>
                    <li><a class="nav-link" href="<?php echo e(route('rules.index')); ?>">Rules list</a></li>
                </ul>
            </li>
            <?php endif; ?>

            
            

        </ul>
    </aside>
</div>
<style>
/* Ensure the sidebar has a clean base style */
.main-sidebar {
    width: 257px;
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* Style for the sidebar menu */
.sidebar-menu .nav-link {
    color: #333;
    padding: 10px 15px;
    display: flex;
    align-items: center;
    transition: background-color 0.3s ease;
}
.dropdown-menu > li {
    margin-bottom: 4px; /* Adjust as needed */
}


/* Style for active parent dropdown */
.sidebar-menu .dropdown.active > .nav-link {
    background-color: #cacef5; /* Light gray for active parent */
    color: #000;
    font-weight: 500;
}

/* Style for active submenu item */
.sidebar-menu .dropdown-menu .nav-link.active {
    background-color: #828ef1; /* Slightly darker gray for active submenu */
    color: #060505;
    font-weight: bold;
}

/* Hover effect for menu and submenu items */
.sidebar-menu .nav-link:hover {
    background-color: #f5f5f5; /* Lighter gray for hover */
}

/* Dropdown menu styling */
.sidebar-menu .dropdown-menu {
    background-color: #fafafa;
    padding: 0;
    margin: 0;
    list-style: none;
}

.sidebar-menu .dropdown-menu .nav-link {
    padding-left: 30px; /* Indent submenu items */
}

/* Ensure dropdown toggle works */
.menu-toggle::after {
    content: '\f078'; /* Font Awesome chevron-down */
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    float: right;
}

/* Optional: Style for icons */
.sidebar-menu .nav-link i {
    margin-right: 10px;
}
</style><?php /**PATH C:\xampp\htdocs\mcdconstructions\resources\views/mcdpanel/layouts/sidebar.blade.php ENDPATH**/ ?>