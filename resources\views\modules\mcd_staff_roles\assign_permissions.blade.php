@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4>Assign Permissions to {{ $role->name }}</h4>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('mcd-staff-roles.store-permissions', $role->id) }}" method="POST">
                            @csrf
                            @method('PUT')
                            
                            <div class="row">
                                @foreach($modules as $key => $module)
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" 
                                                       class="custom-control-input" 
                                                       id="{{ $key }}"
                                                       name="permissions[]"
                                                       value="{{ $key }}"
                                                       {{ in_array($key, $role->permissions ?? []) ? 'checked' : '' }}>
                                                <label class="custom-control-label" for="{{ $key }}">
                                                    {{ $module }}
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">Save Permissions</button>
                                <a href="{{ route('mcd-staff-roles.index') }}" class="btn btn-secondary">Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection