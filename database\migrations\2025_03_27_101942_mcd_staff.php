<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    // public function up(): void
    // {
    //     Schema::create('mcd_staff', function (Blueprint $table) {
    //         $table->id();
    //         $table->string('staff_name');
    //         $table->string('email')->unique();
    //         $table->string('password');
    //         $table->foreignId('role_id')->constrained('mcd_staff_roles')->onDelete('restrict');
    //         $table->boolean('status')->default(true);
    //         $table->text('description')->nullable();
    //         $table->string('contact_number')->nullable();
    //         $table->text('address')->nullable();
    //         $table->timestamps();
    //     });
    // }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mcd_staff');
    }
};
