<?php

namespace App\Http\Controllers;

use App\Models\RuleRegulation;
use Illuminate\Http\Request;

class RuleRegulationController extends Controller {
    // Display all rules
    public function index() {
        $rules = RuleRegulation::all();
        return view('rules.index', compact('rules'));
    }

    // Show create form
    public function create() {
        return view('rules.create');
    }

    // Store new rule
    public function store(Request $request) {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'status' => 'required|in:active,inactive',
            'document' => 'nullable|file|mimes:pdf,doc,docx,jpg,png|max:2048' // Max 2MB, adjust as needed
        ]);

        $data = $request->all();

        // Handle file upload
        if ($request->hasFile('document')) {
            $file = $request->file('document');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('documents', $fileName, 'public'); // Stores in storage/app/public/documents
            $data['document'] = $filePath;
            \Log::info("File path: " . storage_path('app/public/' . $filePath));
        }

        RuleRegulation::create($data);
        return redirect()->route('rules.index')->with('success', 'Rule added successfully');
    }

    // Show edit form
    public function edit(RuleRegulation $rule) {
        return view('rules.edit', compact('rule'));
    }

    // Update rule
    public function update(Request $request, RuleRegulation $rule) {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'status' => 'required|in:active,inactive',
            'document' => 'nullable|file|mimes:pdf,doc,docx,jpg,png|max:2048'
        ]);

        $data = $request->all();

        // Handle file upload
        if ($request->hasFile('document')) {
            // Delete old file if exists
            if ($rule->document && \Storage::disk('public')->exists($rule->document)) {
                \Storage::disk('public')->delete($rule->document);
            }
            $file = $request->file('document');
            $fileName = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('documents', $fileName, 'public');
            $data['document'] = $filePath;
        }

        $rule->update($data);
        return redirect()->route('rules.index')->with('success', 'Rule updated successfully');
    }

    // Delete rule
    public function destroy(RuleRegulation $rule) {
        $rule->delete();
        return redirect()->route('rules.index')->with('success', 'Rule deleted successfully');
    }
}
