<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Details</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .table th,
        .table td {
            vertical-align: middle;
        }

        .table-bordered {
            border: 1px solid #dee2e6;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
        }

        .badge {
            font-size: 0.8rem;
            padding: 0.3em 0.6em;
        }
    </style>
</head>
<body>
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-start flex-column flex-md-row">
                        <div>
                            <h3 class="mb-1">
                                Project Details: {{ $project->project_name }}
                                <!-- Conditional Review Step -->
                                <!-- @if(isset($department) && isset($step)) -->
                                | Review Step: {{ $department['department'] }} - {{ $step['step'] }}
                                <!-- @endif -->
                            </h3>
                            <p class="mb-0">
                                {{ $project->project_address ?? 'N/A' }}, {{ $project->city ?? '' }},
                                {{ $project->state ?? '' }} {{ $project->zip ?? '' }}
                            </p>
                            <!-- Conditional Permit Numbers -->
                            <!-- @if ($project->permitNumbers->isNotEmpty()) -->
                            <button type="button" class="btn w-100 text-start mt-2">
                                <strong>Permit Numbers:</strong>
                                <!-- @foreach ($project->permitNumbers as $permit) -->
                                <span class="badge bg-success me-1 my-1">{{ $permit->permit_number }}</span>
                                <!-- @endforeach -->
                            </button>
                            <!-- @else -->
                            <button type="button" class="btn btn-outline-warning w-100 text-start mt-2">
                                <strong><i class="bi bi-exclamation-circle me-2"></i>Permit Number:</strong>
                                <span class="text-danger">Yet to Assign</span>
                            </button>
                            <!-- @endif -->
                            <p class="mb-0">
                                <strong>Engineer Name:</strong> {{ $project->engineer_name ?? 'N/A' }}
                            </p>
                        </div>
                        <div class="btn-group mt-3 mt-md-0">
                            <a href="{{ route('commissionreview') }}" class="btn btn-primary">Back</a>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Documents -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="mb-3">Documents (PDFs)</h5>
                                <form action="{{ route('save.review', ['id' => $project->id]) }}" method="POST" enctype="multipart/form-data">
                                    <!-- CSRF Token -->
                                    <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Document Type</th>
                                                    <th>Documents</th>
                                                    <th>New Attachments</th>
                                                    <th>Remarks</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>Project Image</td>
                                                    <td>
    @if ($project->project_image)
        <a href="{{ $project->project_image }}" target="_blank" class="btn btn-sm btn-primary">
            <i class="fa fa-file-image mr-1"></i> View Image
        </a>
    @else
        <span class="text-muted">Not Available</span>
    @endif
</td>

                                                    <td>
                                                        <input type="file" name="new_attachments[project_image]" class="form-control" accept="image/*">
                                                    </td>
                                                    <td>
                                                        <textarea name="remarks[project_image]" class="form-control" rows="2" placeholder="Add remarks"></textarea>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Full Plan Set</td>
                                                    <td>
                                                        <!-- @if ($project->full_plan_set) -->
                                                        <a href="{{ asset('profile_images/' . basename($project->full_plan_set)) }}"
                                                           target="_blank" class="btn btn-sm btn-primary">
                                                            <i class="fa fa-file-pdf mr-1"></i> View PDF
                                                        </a>
                                                        <!-- @else -->
                                                        <span class="text-muted">Not Available</span>
                                                        <!-- @endif -->
                                                    </td>
                                                    <td>
                                                        <input type="file" name="new_attachments[full_plan_set]" class="form-control" accept=".pdf">
                                                    </td>
                                                    <td>
                                                        <textarea name="remarks[full_plan_set]" class="form-control" rows="2" placeholder="Add remarks"></textarea>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Site Plan</td>
                                                    <td>
                                                        <!-- @if ($project->site_plan) -->
                                                        <a href="{{ asset('profile_images/' . basename($project->site_plan)) }}"
                                                           target="_blank" class="btn btn-sm btn-primary">
                                                            <i class="fa fa-file-pdf mr-1"></i> View PDF
                                                        </a>
                                                        <!-- @else -->
                                                        <span class="text-muted">Not Available</span>
                                                        <!-- @endif -->
                                                    </td>
                                                    <td>
                                                        <input type="file" name="new_attachments[site_plan]" class="form-control" accept=".pdf">
                                                    </td>
                                                    <td>
                                                        <textarea name="remarks[site_plan]" class="form-control" rows="2" placeholder="Add remarks"></textarea>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Structural Calculations</td>
                                                    <td>
                                                        <!-- @if ($project->structural_calculations) -->
                                                        <a href="{{ asset('profile_images/' . basename($project->structural_calculations)) }}"
                                                           target="_blank" class="btn btn-sm btn-primary">
                                                            <i class="fa fa-file-pdf mr-1"></i> View PDF
                                                        </a>
                                                        <!-- @else -->
                                                        <span class="text-muted">Not Available</span>
                                                        <!-- @endif -->
                                                    </td>
                                                    <td>
                                                        <input type="file" name="new_attachments[structural_calculations]" class="form-control" accept=".pdf">
                                                    </td>
                                                    <td>
                                                        <textarea name="remarks[structural_calculations]" class="form-control" rows="2" placeholder="Add remarks"></textarea>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Engineering Reports</td>
                                                    <td>
                                                        <!-- @if ($project->engineering_reports) -->
                                                        <a href="{{ asset('profile_images/' . basename($project->engineering_reports)) }}"
                                                           target="_blank" class="btn btn-sm btn-primary">
                                                            <i class="fa fa-file-pdf mr-1"></i> View PDF
                                                        </a>
                                                        <!-- @else -->
                                                        <span class="text-muted">Not Available</span>
                                                        <!-- @endif -->
                                                    </td>
                                                    <td>
                                                        <input type="file" name="new_attachments[engineering_reports]" class="form-control" accept=".pdf">
                                                    </td>
                                                    <td>
                                                        <textarea name="remarks[engineering_reports]" class="form-control" rows="2" placeholder="Add remarks"></textarea>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Energy Calculations</td>
                                                    <td>
                                                        <!-- @if ($project->energy_calculations) -->
                                                        <a href="{{ asset('profile_images/' . basename($project->energy_calculations)) }}"
                                                           target="_blank" class="btn btn-sm btn-primary">
                                                            <i class="fa fa-file-pdf mr-1"></i> View PDF
                                                        </a>
                                                        <!-- @else -->
                                                        <span class="text-muted">Not Available</span>
                                                        <!-- @endif -->
                                                    </td>
                                                    <td>
                                                        <input type="file" name="new_attachments[energy_calculations]" class="form-control" accept=".pdf">
                                                    </td>
                                                    <td>
                                                        <textarea name="remarks[energy_calculations]" class="form-control" rows="2" placeholder="Add remarks"></textarea>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Special Certifications</td>
                                                    <td>
                                                        <!-- @if ($project->special_certifications) -->
                                                        <a href="{{ asset('profile_images/' . basename($project->special_certifications)) }}"
                                                           target="_blank" class="btn btn-sm btn-primary">
                                                            <i class="fa fa-file-pdf mr-1"></i> View PDF
                                                        </a>
                                                        <!-- @else -->
                                                        <span class="text-muted">Not Available</span>
                                                        <!-- @endif -->
                                                    </td>
                                                    <td>
                                                        <input type="file" name="new_attachments[special_certifications]" class="form-control" accept=".pdf">
                                                    </td>
                                                    <td>
                                                        <textarea name="remarks[special_certifications]" class="form-control" rows="2" placeholder="Add remarks"></textarea>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="text-end mt-3">
                                        <button type="submit" class="btn btn-success">Save Review</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Dependencies -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>