<?php
/**
 * Test script for Annotated PDF functionality
 * Run this in browser to test the API endpoints
 */

// Test data
$projectId = 1; // Replace with actual project ID
$testData = [
    'project_id' => $projectId,
    'department_id' => 1,
    'review_step' => 'Step 1',
    'original_pdf_path' => 'https://example.com/test.pdf',
    'document_type' => 'test_annotation'
];

echo "<h2>Testing Annotated PDF Functionality</h2>";

// Test 1: Check if routes are accessible
echo "<h3>Test 1: Route Accessibility</h3>";
$routes = [
    'GET /projects/' . $projectId . '/annotated-pdfs' => 'http://localhost/mcdconstructions/projects/' . $projectId . '/annotated-pdfs',
    'POST /annotated-pdfs' => 'http://localhost/mcdconstructions/annotated-pdfs'
];

foreach ($routes as $route => $url) {
    echo "<p><strong>$route:</strong> <a href='$url' target='_blank'>$url</a></p>";
}

// Test 2: Check database table
echo "<h3>Test 2: Database Table</h3>";
try {
    $pdo = new PDO('mysql:host=localhost;dbname=mcdconstructions', 'root', '');
    $stmt = $pdo->query("DESCRIBE annotated_pdfs");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Table 'annotated_pdfs' exists with columns:</strong></p>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li>{$column['Field']} - {$column['Type']}</li>";
    }
    echo "</ul>";
} catch (Exception $e) {
    echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
}

// Test 3: Check storage directory
echo "<h3>Test 3: Storage Directory</h3>";
$storagePath = __DIR__ . '/public/storage/annotated_pdfs';
if (is_dir($storagePath)) {
    echo "<p style='color: green;'>✓ Storage directory exists: $storagePath</p>";
} else {
    echo "<p style='color: red;'>✗ Storage directory missing: $storagePath</p>";
}

// Test 4: Check model
echo "<h3>Test 4: Model</h3>";
$modelPath = __DIR__ . '/app/Models/AnnotatedPdf.php';
if (file_exists($modelPath)) {
    echo "<p style='color: green;'>✓ AnnotatedPdf model exists</p>";
} else {
    echo "<p style='color: red;'>✗ AnnotatedPdf model missing</p>";
}

// Test 5: Check controller
echo "<h3>Test 5: Controller</h3>";
$controllerPath = __DIR__ . '/app/Http/Controllers/AnnotatedPdfController.php';
if (file_exists($controllerPath)) {
    echo "<p style='color: green;'>✓ AnnotatedPdfController exists</p>";
} else {
    echo "<p style='color: red;'>✗ AnnotatedPdfController missing</p>";
}

echo "<h3>Test 6: Manual Testing Instructions</h3>";
echo "<ol>";
echo "<li>Go to a project review page (e.g., <a href='http://localhost/mcdconstructions/review/process-complete/1/1/1' target='_blank'>Process Complete</a>)</li>";
echo "<li>Click 'Edit PDF' button to open the annotation tool</li>";
echo "<li>Add some annotations (pen, text, rectangle)</li>";
echo "<li>Click 'Save Annotations' button</li>";
echo "<li>Check if the annotated PDF appears in the 'Annotated PDFs' section</li>";
echo "</ol>";

echo "<h3>Test 7: API Endpoints</h3>";
echo "<p>You can test the API endpoints directly:</p>";
echo "<ul>";
echo "<li><strong>GET</strong> <a href='http://localhost/mcdconstructions/projects/1/annotated-pdfs' target='_blank'>/projects/1/annotated-pdfs</a> - Get annotated PDFs for project 1</li>";
echo "<li><strong>POST</strong> /annotated-pdfs - Upload annotated PDF (requires form data)</li>";
echo "</ul>";
?> 