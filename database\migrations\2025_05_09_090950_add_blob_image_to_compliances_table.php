<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
{
    Schema::table('compliances', function (Blueprint $table) {
        $table->binary('image')->nullable(); // binary column for image content
    });
}

public function down(): void
{
    Schema::table('compliances', function (Blueprint $table) {
        $table->dropColumn('image');
    });
}

};
