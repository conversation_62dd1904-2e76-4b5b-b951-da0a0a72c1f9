@extends('mcdpanel.layouts.master')
@php
    $user = Auth::guard('staff_mcd')->user();
    $permissions = $user && $user->role ? $user->role->permissions : [];
    $isAdmin = Auth::guard('web')->check();
    $hasWritePermission = $isAdmin || (isset($permissions['guidelines']['write']) && $permissions['guidelines']['write'] == 1);
@endphp

@section('content')
<div class="main-content">
    <div class="row">
        <!-- Guideline Type Cards -->
        <div class="col-12">
            <div class="row mb-4">
                <div class="col-md-4">
                    <a href="{{ route('guidelines.index', ['type' => 'Fire Safety']) }}" class="text-decoration-none">
                        <div class="card h-100 {{ request()->query('type') == 'Fire Safety' ? 'border-primary shadow' : '' }}">
                            <div class="card-body text-center">
                                <i class="fas fa-fire fa-2x text-danger mb-2"></i>
                                <h5 class="card-title">Fire Safety</h5>
                                <p class="card-text text-muted">View all Fire Safety guidelines</p>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="{{ route('guidelines.index', ['type' => 'Structural']) }}" class="text-decoration-none">
                        <div class="card h-100 {{ request()->query('type') == 'Structural' ? 'border-primary shadow' : '' }}">
                            <div class="card-body text-center">
                                <i class="fas fa-building fa-2x text-primary mb-2"></i>
                                <h5 class="card-title">Structural</h5>
                                <p class="card-text text-muted">View all Structural guidelines</p>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="{{ route('guidelines.index', ['type' => 'Zoning Laws']) }}" class="text-decoration-none">
                        <div class="card h-100 {{ request()->query('type') == 'Zoning Laws' ? 'border-primary shadow' : '' }}">
                            <div class="card-body text-center">
                                <i class="fas fa-map fa-2x text-success mb-2"></i>
                                <h5 class="card-title">Zoning Laws</h5>
                                <p class="card-text text-muted">View all Zoning Laws guidelines</p>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>

        <!-- Guidelines Table -->
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4>Guidelines > All</h4>
                    @if ($hasWritePermission)
                        <a href="{{ route('guidelines.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create Guideline
                        </a>
                    @else
                        <button type="button" class="btn btn-light btn-sm" onclick="showAccessDenied()">
                                    <i class="fas fa-plus"></i> Create Guideline
                        </button>
                    @endif      
                </div>
                <div class="card-body">
                    @if ($guidelines->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="guidelines-table">
                                <thead>
                                    <tr>
                                        <th>Sr No</th>
                                        <th>Type</th>
                                        <th>Title</th>
                                        <th>Description</th>
                                        <th>Date & Time</th>
                                        <th class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($guidelines as $key => $guideline)
                                        <tr>
                                            <td>{{ $key + 1 }}</td>
                                            <td>{{ $guideline->type }}</td>
                                            <td>{{ $guideline->title }}</td>
                                            <td>{{ Str::limit($guideline->description, 50) }}</td>
                                            <td>{{ $guideline->date_time->format('Y-m-d H:i') }}</td>
                                            <td class="text-center">
                                                @if($hasWritePermission)
                                                <a href="{{ route('guidelines.edit', $guideline->id) }}" 
                                                   class="btn btn-sm btn-primary mr-1" 
                                                   title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                @else
                                                        <button type="button" class="btn btn-light btn-sm" onclick="showAccessDenied()">
                                                           <i class="fa fa-edit"></i>
                                                        </button>
                                                @endif        
                                                @if($hasWritePermission)
                                                <form action="{{ route('guidelines.destroy', $guideline->id) }}" 
                                                      method="POST" 
                                                      class="d-inline delete-form">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" 
                                                            class="btn btn-sm btn-danger delete-btn" 
                                                            title="Delete" 
                                                            data-name="{{ $guideline->title }}">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                    @else
                                                    <button type="button" class="btn btn-danger btn-sm" onclick="showAccessDenied()" title="Delete Notification">
                                                                <i class="fas fa-trash"></i>
                                                        </button>
                                                    @endif    
                                                </form>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle mr-2"></i>
                            No guidelines have been created yet.
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('style')
    <style>
        .btn-sm { 
            padding: 0.25rem 0.5rem; 
        }
        .badge {
            font-size: 0.8rem;
            padding: 0.3em 0.6em;
        }
        .table th, .table td {
            vertical-align: middle;
        }
        .card.h-100 {
            transition: transform 0.2s;
        }
        .card.h-100:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
        }
        .border-primary {
            border: 2px solid #007bff !important;
        }
    </style>
@endpush

@push('script')
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Success Message from Session
            @if(session('success'))
                Swal.fire({
                    title: 'Success!',
                    text: '{{ session('success') }}',
                    icon: 'success',
                    showConfirmButton: false,
                    timer: 4000,
                    timerProgressBar: true,
                    position: 'top-end',
                    toast: true,
                    customClass: {
                        popup: 'swal2-popup-custom',
                        title: 'swal2-title-custom',
                        content: 'swal2-content-custom'
                    }
                });
            @endif

            // Delete Confirmation
            document.querySelectorAll('.delete-btn').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const form = this.closest('form');
                    const guidelineTitle = this.getAttribute('data-name');

                    Swal.fire({
                        title: 'Are you sure?',
                        text: `You are about to delete the guideline "${guidelineTitle}". This action cannot be undone!`,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#3085d6',
                        confirmButtonText: 'Yes, delete it!',
                        cancelButtonText: 'Cancel'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            form.submit();
                        }
                    });
                });
            });
        });

        function showAccessDenied() {
        Swal.fire({
            icon: 'error',
            title: 'Access Denied',
            text: 'You do not have permission to modify the Guidelines.'
        });
    }
    </script>

    <style>
        .swal2-popup-custom {
            border-radius: 8px;
            padding: 15px;
            background: #ffffff;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
        }
        .swal2-title-custom {
            color: #28a745;
            font-weight: bold;
            font-size: 18px;
        }
        .swal2-content-custom {
            font-size: 14px;
            color: #555;
        }
    </style>
@endpush