@extends('mcdpanel.layouts.master')

@section('content')
<div class="main-content">
    <section class="section">
        <div class="section-body">

            <div class="row">
                <div class="col-12 col-md-12 col-lg-12">
                    <div class="card">

                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h4>Set Permissions for Role: {{ $role->name }}</h4>
                            <a href="{{ route('permissions.index') }}" class="btn btn-primary btn-sm">Back to Roles</a>
                        </div>

                        <form action="{{ route('role_permissions.update', $role->id) }}" method="POST">
                            @csrf
                            @method('PUT')

                            <div class="card-body">

                                <table class="table table-bordered">
                                    <thead class="thead-light">
                                        <tr>
                                            <th>Permission</th>
                                            <th class="text-center">Read</th>
                                            <th class="text-center">Write</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($permissions as $permission)
                                            @php
                                                $pivot = $role->permissions->firstWhere('id', $permission->id);
                                            @endphp
                                            <tr>
                                                <td>
                                                    <strong>{{ $permission->name }}</strong><br>
                                                    <small class="text-muted">{{ $permission->description }}</small>
                                                </td>
                                                <td class="text-center align-middle">
                                                    <input type="checkbox" 
                                                           name="permissions[{{ $permission->id }}][can_read]" 
                                                           {{ $pivot && $pivot->pivot->can_read ? 'checked' : '' }}>
                                                </td>
                                                <td class="text-center align-middle">
                                                    <input type="checkbox" 
                                                           name="permissions[{{ $permission->id }}][can_write]" 
                                                           {{ $pivot && $pivot->pivot->can_write ? 'checked' : '' }}>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>

                            </div>

                            <div class="card-footer text-right">
                                <button type="submit" class="btn btn-primary">Save Permissions</button>
                                <a href="{{ route('permissions.index') }}" class="btn btn-secondary">Cancel</a>
                            </div>
                        </form>

                    </div>
                </div>
            </div>

        </div>
    </section>
</div>
@endsection
