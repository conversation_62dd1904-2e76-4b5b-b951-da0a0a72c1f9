<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\RoleMcd;

use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use App\Mail\StaffInviteMail;
use Illuminate\Support\Facades\Auth;
use App\Models\StaffInvite;

class RolesController extends Controller
{
    // roles listing 
    public function index(){
        $roles=RoleMcd::get();
        return view('BuilderWebsiteDashboard.roles.index',compact('roles'));
    }

    


    // status update 
     public function updateStatus(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:roles_mcd,id',
            'status' => 'required|boolean',
        ]);

        $staffRole = RoleMcd::find($request->id);
        $staffRole->status = $request->status;
        $staffRole->save();

        return response()->json(['message' => 'Status updated successfully']);
    }

     // mail send to invite staff
     public function sendInvite(Request $request)
    {
        
        $validated = $request->validate([
            'role_id' => 'required|exists:roles_mcd,id',
            'email' => 'required|email', 
            'message' => 'nullable|string',
        ]);

        $role = RoleMcd::findOrFail($validated['role_id']);
        
        // Generate a unique token for the invite
        $token = Str::random(60);

         // Save the invite to DB
            StaffInvite::create([
                'email' => $validated['email'],
                'role_id' => $role->id,
                'token' => $token,
                'message' => $validated['message'],
    ]);
        
        try {
           
            // Send the email
            Mail::to($validated['email'])->send(new StaffInviteMail([
            'role_id' => $role->id,
            'role_name' => $role->name,
            'message' => $validated['message'] ?? null,
            'invite_url' => route('mcd-staff.create', ['role_id' => $role->id, 'token' => $token]),
           ]));
        } catch (\Exception $e) {
            return redirect()->route('mcd-staff-roles.index')
                ->with('error', 'Failed to send invite: ' . $e->getMessage());
        }

        return redirect()->route('roles.index')
            ->with('success', 'Invite sent successfully');
    }

    //add the roles
    public function create()
    {
        return view('BuilderWebsiteDashboard.roles.create');
    }

    //Store the roles
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|unique:roles_mcd|max:255',
            'description' => 'nullable|string',
            'status' => 'boolean'
        ]);

        RoleMcd::create($validated);

        return redirect()->route('roles.index')
            ->with('success', 'Roles created successfully');
    }

    // edit the roles
     public function edit($id)
    {
        $role = RoleMcd::findOrFail($id);
        return view('BuilderWebsiteDashboard.roles.edit', compact('role'));
    }


    // update the roles 
     public function update(Request $request, $id)
    {
        $role = RoleMcd::findOrFail($id);

        $validated = $request->validate([
            'name' => 'required|max:255|unique:roles_mcd,name,' . $id,
            'description' => 'nullable|string',
            'status' => 'boolean'
        ]);

        $role->update($validated);

        return redirect()->route('roles.index')
            ->with('success', 'Roles updated successfully');
    }

    //deleted the roles 
     public function destroy($id)
    {
        $role = RoleMcd::findOrFail($id);
        $role->delete();

        return redirect()->route('roles.index')
            ->with('success', 'Roles deleted successfully');
    }

}
