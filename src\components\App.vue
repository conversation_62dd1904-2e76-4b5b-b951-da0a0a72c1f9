<template>
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-2 bg-light sidebar">
        <h4>PDF Annotation Tool</h4>

        <!-- User Information Display -->
        <div v-if="currentUserId && currentUserRole" class="user-info mb-3 p-2 bg-info text-white rounded">
          <small>
            <strong>Logged in as:</strong><br>
            {{ currentUserName || 'User ID: ' + currentUserId }}<br>
            <em>{{ currentUserRole }}</em>
          </small>
        </div>
        <div v-else class="user-info mb-3 p-2 bg-warning text-dark rounded">
          <small>
            <strong>⚠️ No user info</strong><br>
            Please open from Edit PDF button
          </small>
        </div>
        
        <!-- Loading indicator -->
        <div v-if="isLoading" class="alert alert-info">
          <i class="bi bi-hourglass-split"></i> Loading PDF...
        </div>
        
        <!-- Error message -->
        <div v-if="errorMessage" class="alert alert-danger">
          <i class="bi bi-exclamation-triangle"></i> {{ errorMessage }}
        </div>
        
        <div class="mb-3">
          <label for="pdfFile" class="form-label">Upload PDF</label>
          <input type="file" class="form-control" id="pdfFile" @change="loadPDF" accept=".pdf">
        </div>
        <div class="mb-3">
          <button class="btn btn-primary w-100" @click="activateTool('pen')">Pen</button>
        </div>
        <div class="mb-3">
          <button class="btn btn-primary w-100" @click="activateTool('text')">Text</button>
        </div>
        <div class="mb-3">
          <button class="btn btn-primary w-100" @click="activateTool('rectangle')">Rectangle</button>
        </div>
        <div class="mb-3">
          <button class="btn btn-primary w-100" @click="activateTool('signature')">Signature</button>
        </div>
        <div class="mb-3">
          <button class="btn btn-secondary w-100" @click="undo">Undo</button>
        </div>
        <div class="mb-3">
          <button class="btn btn-secondary w-100" @click="redo">Redo</button>
        </div>
        <div class="mb-3">
          <button class="btn btn-success w-100" @click="saveAnnotations" :disabled="isSaving">
            <span v-if="isSaving">
              <i class="bi bi-hourglass-split"></i> Saving...
            </span>
            <span v-else>
              <i class="bi bi-download"></i> Save Annotations
            </span>
          </button>
        </div>
        
        <!-- Save success message -->
        <div v-if="saveSuccess" class="alert alert-success">
          <i class="bi bi-check-circle"></i> Annotations saved successfully!
        </div>
        
        <!-- Save error message -->
        <div v-if="saveError" class="alert alert-danger">
          <i class="bi bi-exclamation-triangle"></i> {{ saveError }}
        </div>
      </div>
      <div class="col-md-10">
        <div class="pdf-viewer" ref="pdfViewer"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { onMounted, ref } from 'vue'
import * as pdfjsLib from 'pdfjs-dist'
import { fabric } from 'fabric'
import { initializePDFViewer } from '../pdfRenderer'
import { setupAnnotationLayer, activateAnnotationTool, saveAnnotationsData, getAnnotationCanvases } from '../annotationLayer'
import { annotationTools } from '../annotationTools' // Assume this is a singleton instance

export default {
  name: 'App',
  setup() {
    const pdfViewer = ref(null)
    let pdfDoc = null
    const currentTool = ref('')
    const isLoading = ref(false)
    const errorMessage = ref('')
    const isSaving = ref(false)
    const saveSuccess = ref(false)
    const saveError = ref('')
    const currentPdfUrl = ref('')
    const currentProjectId = ref(null)
    const currentDepartmentId = ref('')
    const currentReviewStep = ref('')
    const currentUserId = ref('')
    const currentUserRole = ref('')
    const currentUserName = ref('')

    onMounted(async () => {
      pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'
      
      // Parse URL parameters
      const urlParams = new URLSearchParams(window.location.search)
      const projectIdParam = urlParams.get('project_id') || urlParams.get('projectId')
      const project_id = projectIdParam && !isNaN(projectIdParam) ? parseInt(projectIdParam) : null
      const pdfUrl = urlParams.get('file')
      const departmentId = urlParams.get('department_id') || urlParams.get('departmentId')
      const reviewStep = urlParams.get('review_step') || urlParams.get('reviewStep')
      const userId = urlParams.get('user_id') || urlParams.get('userId')
      const userRole = urlParams.get('user_role') || urlParams.get('userRole')
      const userName = urlParams.get('user_name') || urlParams.get('userName')
      
      // Debug logging
      console.log('URL Params:', Object.fromEntries(urlParams.entries()));
      console.log('Parsed project_id from URL:', projectIdParam, 'Final project_id:', project_id);
      console.log('Raw userId from URL:', urlParams.get('userId'));
      console.log('Raw userRole from URL:', urlParams.get('userRole'));
      console.log('Raw userName from URL:', urlParams.get('userName'));
      
      // Store parameters for later use
      currentPdfUrl.value = pdfUrl
      currentProjectId.value = project_id
      currentDepartmentId.value = departmentId
      currentReviewStep.value = reviewStep
      currentUserId.value = userId
      currentUserRole.value = userRole
      currentUserName.value = userName
      
      // Additional debug logging
      console.log('Stored currentProjectId.value:', currentProjectId.value);
      console.log('Type of currentProjectId.value:', typeof currentProjectId.value);
      console.log('User ID from URL:', userId);
      console.log('User Role from URL:', userRole);
      console.log('Stored currentUserId.value:', currentUserId.value);
      console.log('Stored currentUserRole.value:', currentUserRole.value);
      
      // Fallback error if project_id is missing/invalid
      if (!currentProjectId.value || currentProjectId.value === '' || isNaN(currentProjectId.value)) {
        saveError.value = 'Project ID is missing or invalid in the URL. Please contact support.';
        errorMessage.value = 'Project ID is missing or invalid in the URL. Please contact support.';
        console.error('Project ID is missing or invalid in the URL.');
      }
      
      if (pdfViewer.value) {
        await initializePDFViewer(pdfViewer.value, pdfDoc)
        setupAnnotationLayer(pdfViewer.value)
        
        // If PDF URL is provided, load it automatically
        if (pdfUrl) {
          await loadPDFFromUrl(pdfUrl)
        }
      }
    })

    const loadPDF = async (event) => {
      const file = event.target.files[0]
      if (file) {
        isLoading.value = true
        errorMessage.value = ''
        
        try {
          const arrayBuffer = await file.arrayBuffer()
          pdfDoc = await pdfjsLib.getDocument({ data: arrayBuffer }).promise
          await initializePDFViewer(pdfViewer.value, pdfDoc)
          setupAnnotationLayer(pdfViewer.value)
        } catch (error) {
          console.error('Error loading PDF file:', error)
          errorMessage.value = 'Failed to load PDF file. Please try again.'
        } finally {
          isLoading.value = false
        }
      }
    }

    const loadPDFFromUrl = async (url) => {
      isLoading.value = true
      errorMessage.value = ''
      
      try {
        console.log('Loading PDF from URL:', url)
        
        console.log('Fetching PDF from URL:', url)
        
        const response = await fetch(url, {
          method: 'GET',
          credentials: 'omit',
          mode: 'cors',
          headers: {
            'Accept': 'application/pdf',
          }
        })
        
        console.log('Response status:', response.status)
        console.log('Response headers:', Object.fromEntries(response.headers.entries()))
        
        if (!response.ok) {
          throw new Error(`Failed to load PDF: ${response.status} ${response.statusText}`)
        }
        
        const arrayBuffer = await response.arrayBuffer()
        console.log('ArrayBuffer size:', arrayBuffer.byteLength)
        pdfDoc = await pdfjsLib.getDocument({ data: arrayBuffer }).promise
        await initializePDFViewer(pdfViewer.value, pdfDoc)
        setupAnnotationLayer(pdfViewer.value)
        
        console.log('PDF loaded successfully from URL')
      } catch (error) {
        console.error('Error loading PDF from URL:', error)
        errorMessage.value = error.message || 'Failed to load PDF from URL. Please try again.'
      } finally {
        isLoading.value = false
      }
    }

    const activateTool = (tool) => {
      currentTool.value = tool
      activateAnnotationTool(tool)
    }

    const saveAnnotations = async () => {
      // Debug logging for project ID
      console.log('saveAnnotations - currentProjectId.value:', currentProjectId.value);
      console.log('saveAnnotations - typeof currentProjectId.value:', typeof currentProjectId.value);
      
      // Force get project_id from URL again as fallback
      const urlParams = new URLSearchParams(window.location.search);
      const projectIdFromUrl = urlParams.get('project_id') || urlParams.get('projectId');
      console.log('saveAnnotations - projectIdFromUrl:', projectIdFromUrl);
      
      // Only allow if project_id is a valid number
      const project_id = (currentProjectId.value && currentProjectId.value !== '' && !isNaN(currentProjectId.value) ? parseInt(currentProjectId.value) : null) || 
                        (projectIdFromUrl && !isNaN(projectIdFromUrl) ? parseInt(projectIdFromUrl) : null);
      console.log('saveAnnotations - final parsed project_id:', project_id);
      
      if (!project_id) {
        saveError.value = 'Project ID is missing or invalid. Cannot save annotations.';
        console.error('Project ID validation failed. currentProjectId.value:', currentProjectId.value, 'projectIdFromUrl:', projectIdFromUrl);
        return;
      }

      isSaving.value = true;
      saveError.value = '';
      saveSuccess.value = false;

      try {
        // Get annotations data
        const annotationCanvases = getAnnotationCanvases()
        const annotations = annotationCanvases.map((canvas, index) => ({
          page: index + 1,
          data: canvas.toJSON(['left', 'top', 'width', 'height'])
        }))
        
        // Create a PDF with annotations using jsPDF
        const { jsPDF } = await import('jspdf')
        const pdf = new jsPDF()
        
        // For now, we'll create a simple PDF with annotation data
        // In a real implementation, you'd merge the original PDF with annotations
        pdf.text('Annotated PDF', 20, 20)
        pdf.text(`Project ID: ${currentProjectId.value}`, 20, 30)
        pdf.text(`Department ID: ${currentDepartmentId.value || 'N/A'}`, 20, 40)
        pdf.text(`Review Step: ${currentReviewStep.value || 'N/A'}`, 20, 50)
        pdf.text(`Original PDF: ${currentPdfUrl.value || 'N/A'}`, 20, 60)
        pdf.text(`Annotations: ${JSON.stringify(annotations)}`, 20, 70)
        
        // Convert to blob and create a proper File object
        const pdfBlob = pdf.output('blob')
        const pdfFile = new File([pdfBlob], 'annotated.pdf', { type: 'application/pdf' })
        
        // Create form data
        const formData = new FormData()
        formData.append('annotated_pdf', pdfFile)
        formData.append('project_id', project_id.toString()) // Ensure it's a string
        formData.append('annotations', JSON.stringify(annotations))
        
        // Add other fields if needed
        if (currentDepartmentId.value) {
          formData.append('department_id', currentDepartmentId.value)
        }
        if (currentReviewStep.value) {
          formData.append('review_step', currentReviewStep.value)
        }
        if (currentPdfUrl.value) {
          formData.append('original_pdf_path', currentPdfUrl.value)
        }
        formData.append('document_type', 'annotated_review')

        // Simple URL parameter extraction - if available use them, if not send empty string
        let finalUserId = '';
        let finalUserRole = '';
        let finalUserName = '';

        // Try to get user parameters from URL
        const currentUrl = window.location.href;
        console.log('🔍 Current URL:', currentUrl);

        const userIdMatch = currentUrl.match(/[?&]userId=([^&]*)/);
        const userRoleMatch = currentUrl.match(/[?&]userRole=([^&]*)/);
        const userNameMatch = currentUrl.match(/[?&]userName=([^&]*)/);

        if (userIdMatch) {
          finalUserId = decodeURIComponent(userIdMatch[1]);
          console.log('✅ Found userId:', finalUserId);
        } else {
          console.log('❌ userId not found in URL');
        }

        if (userRoleMatch) {
          finalUserRole = decodeURIComponent(userRoleMatch[1]);
          console.log('✅ Found userRole:', finalUserRole);
        } else {
          console.log('❌ userRole not found in URL');
        }

        if (userNameMatch) {
          finalUserName = decodeURIComponent(userNameMatch[1]);
          console.log('✅ Found userName:', finalUserName);
        } else {
          console.log('❌ userName not found in URL');
        }

        console.log('📤 Final values being sent:');
        console.log('  userId:', finalUserId);
        console.log('  userRole:', finalUserRole);
        console.log('  userName:', finalUserName);

        // Add user information to FormData
        formData.append('user_id', finalUserId);
        formData.append('user_role', finalUserRole);
        formData.append('user_name', finalUserName);
        


        // Send request to Laravel backend
        const response = await fetch('http://localhost/mcdconstructions/api/annotated-pdfs', {
          method: 'POST',
          body: formData,
          headers: {
            'Accept': 'application/json',
          },
        });

        const result = await response.json();
        
        if (!result.success) {
          saveError.value = result.message || 'Failed to save annotated PDF.';
        } else {
          saveSuccess.value = true;
        }
      } catch (error) {
        saveError.value = 'An unexpected error occurred while saving. Please try again.';
      } finally {
        isSaving.value = false;
      }
    }

    const undo = () => {
      annotationTools.undo()
    }
    const redo = () => {
      annotationTools.redo()
    }

    return {
      pdfViewer,
      isLoading,
      errorMessage,
      isSaving,
      saveSuccess,
      saveError,
      currentUserId,
      currentUserRole,
      currentUserName,
      loadPDF,
      loadPDFFromUrl,
      activateTool,
      saveAnnotations,
      undo,
      redo
    }
  }
}
</script>

<style scoped>
.container-fluid {
  height: 100vh;
}
.row {
  height: 100%;
}
.sidebar {
  padding: 20px;
  overflow-y: auto;
}
.pdf-viewer {
  height: 100%;
  overflow-y: auto;
  position: relative;
}
</style> 