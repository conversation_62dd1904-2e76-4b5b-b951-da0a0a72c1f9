<nav class="navbar navbar-expand-lg main-navbar sticky">
    <div class="form-inline mr-auto">
        <ul class="navbar-nav mr-3">
            <li><a href="#" data-toggle="sidebar"
                    class="nav-link nav-link-lg collapse-btn"> <i
                        data-feather="align-justify"></i>
                </a></li>
                 @if (Auth::guard('web')->check())
                        <li style="margin-top: 8px;">Municipal Admin Panel 
                        </li>
                    @elseif (Auth::guard('staff_mcd')->check())
                        <li style="margin-top: 8px;">Municipal Staff 
            </li>       @endif
           
            <li></li>
        </ul>
    </div>
    <ul class="navbar-nav navbar-right">
        <li class="dropdown"><a href="#" data-toggle="dropdown"
                class="nav-link dropdown-toggle nav-link-lg nav-link-user"> 
                <span style="color: rgb(12, 5, 5); margin-right: 10px; margin-top: 20px;" class="d-sm-none d-lg-inline-block">
                    @if (Auth::guard('web')->check())
                        {{ Auth::guard('web')->user()->name }}
                    @elseif (Auth::guard('staff_mcd')->check())
                        Staff: {{ Auth::guard('staff_mcd')->user()->staff_name }}-({{ Auth::guard('staff_mcd')->user()->department->name }})
                    @endif
                </span>               
                <img alt="image"
                    src="{{ asset('assets\img\users\download.jfif') }}" 
                    class="user-img-radious-style"> 
                <span class="d-sm-none d-lg-inline-block"></span></a>
            <div class="dropdown-menu dropdown-menu-right pullDown">
                <a href="{{ route('profile.edit') }}" class="dropdown-item has-icon"> 
                    <i class="far fa-user"></i> Profile
                </a>
                <div class="dropdown-divider"></div>
                <form method="POST" action="{{ route('logout') }}">
                    @csrf
                    <x-dropdown-link :href="route('logout')"
                        onclick="event.preventDefault();
                        this.closest('form').submit();">
                        {{ __('Log Out') }}
                    </x-dropdown-link>
                </form>
            </div>
        </li>
    </ul>
</nav>

<!-- JavaScript to display the current date -->
<script>
    // Get the current date
    const today = new Date();
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    const formattedDate = today.toLocaleDateString('en-US', options);

    // Insert the date into the span element
    document.getElementById('current-date').textContent = formattedDate;
</script>