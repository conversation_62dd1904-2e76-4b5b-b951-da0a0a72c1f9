<?php

namespace App\Http\Controllers;

use App\Models\McdStaff;
use Illuminate\Support\Facades\URL;

use App\Models\McdStaffRole;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Models\StaffInvite;
use App\Models\RoleMcd;

class McdStaffController extends Controller
{
    public function index()
    {
        $staff = McdStaff::with('role','department')->get();
        return view('modules.mcd_staff.index', compact('staff'));
    }

    public function create(Request $request)
    {
        $roles = RoleMcd::all();
        $selectedRole = null;
         $token = $request->query('token');
         
        if ($request->has('role_id')) {
            $selectedRole = RoleMcd::findOrFail($request->input('role_id'));
        }
          $departments = McdStaffRole::all();
        //dd($token); 
        return view('modules.mcd_staff.create', compact('roles', 'selectedRole', 'token','departments'));

    }
    
    public function thankyou()
    {
      
        return view('modules.mcd_staff.thankyou');
    }


    public function store(Request $request)
    {
        //dd($request->all());
        $validated = $request->validate([
            'staff_name' => 'required|string|max:255',
            'email' => 'required|email|unique:mcd_staff,email',
            'password' => 'required|string|min:8',
            'role_id' => 'required|exists:roles_mcd,id',
            'contact_number' => 'nullable|string|max:20',
            'status' => 'boolean',
            'description' => 'nullable|string',
            'address' => 'nullable|string',
            'token' => 'required|string',
            'department_id'=>'required'
        ]);

        
        
    $invite = StaffInvite::where('role_id', $validated['role_id'])
        ->where('token', $validated['token'])
        ->first();

    if (!$invite) {
        return back()->with('error', 'Invalid invitation details.');
    }

    if ($invite->is_submitted) {
        return redirect()->route('mcd.already.submitted.page')
            ->with('error', 'This invite has already been used.');
    }
           
            


         McdStaff::create([
            'staff_name' => $validated['staff_name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'role_id' => $validated['role_id'],
            'status' => $validated['status'] ?? 1,
            'description' => $validated['description'],
            'contact_number' => $validated['contact_number'],
            'address' => $validated['address'],
            'department_id'=>$validated['department_id'],
        ]);
        
        

        // ✅ Mark invite as submitted
         $invite->update(['is_submitted' => true]);

         $signedUrl = URL::signedRoute('mcd.thankyou.page');
            return redirect($signedUrl)->with('success', 'Staff created successfully');

    }


    public function edit(Request $request, $id)
    {
        $staff = McdStaff::findOrFail($id);
        $roles = RoleMcd::all();
        $selectedRole = null;
        $token = $request->query('token');

        if ($request->has('role_id')) {
            $selectedRole = RoleMcd::findOrFail($request->input('role_id'));
        } else {
            // Default to the staff's assigned role
            $selectedRole = RoleMcd::find($staff->role_id);
        }
        $departments = McdStaffRole::all();
    return view('modules.mcd_staff.edit', compact('staff', 'departments', 'roles', 'selectedRole'));
    }

    public function update(Request $request, $id)
{
    $staff = McdStaff::findOrFail($id);

    $validated = $request->validate([
        'staff_name' => 'required|max:255',
        'email' => 'required|email|unique:mcd_staff,email,'.$id, // Excludes current record
        'password' => 'nullable|min:6',
        'role_id' => 'required|integer', // Fixed typo
        'status' => 'boolean',
        'description' => 'nullable|string',
        'contact_number' => 'nullable|string|max:15',
        'address' => 'nullable|string',
        'department_id'=>'required'

    ]);

    if ($request->filled('password')) {
        $validated['password'] = Hash::make($validated['password']);
    } else {
        unset($validated['password']);
    }

    $staff->update($validated);

    return redirect()->route('mcd-staff.index')
        ->with('success', 'Staff updated successfully');
}


    public function destroy($id)
    {
        $staff = McdStaff::findOrFail($id);
        $staff->delete();

        return redirect()->route('mcd-staff.index')
            ->with('success', 'Staff deleted successfully');
    }
    public function updateStatus(Request $request, $id)
    {
        $staff = McdStaff::findOrFail($id);
        $staff->status = $request->status;
        $staff->save();

        return redirect()->route('mcd-staff.index')
            ->with('success', 'Staff updated successfully');
    }
}
