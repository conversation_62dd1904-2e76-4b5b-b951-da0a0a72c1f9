<!DOCTYPE html>
<html>
<head>
    <title>Inspector Details</title>
    <link href="{{ asset('css/app.css') }}" rel="stylesheet">
    <style>
        .detail-label { font-weight: bold; }
        .status-active { color: green; }
        .status-inactive { color: red; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="mb-4">Inspector Details</h1>
        <div class="card">
            <div class="card-body">
                <p><span class="detail-label">Full Name:</span> {{ $inspector->first_name }} {{ $inspector->last_name }}</p>
                <p><span class="detail-label">Email:</span> {{ $inspector->email }}</p>
                <p><span class="detail-label">Phone:</span> {{ $inspector->phone }}</p>
                <p><span class="detail-label">Designation:</span> {{ $inspector->designation }}</p>
                <p><span class="detail-label">Department:</span> {{ $inspector->department }}</p>
                <p><span class="detail-label">Address:</span> {{ $inspector->address }}</p>
                <p><span class="detail-label">Status:</span> <span class="status-{{ strtolower($inspector->status) }}">{{ ucfirst($inspector->status) }}</span></p>
                <p><span class="detail-label">Created At:</span> {{ $inspector->created_at->format('d M Y H:i') }}</p>
                <p><span class="detail-label">Updated At:</span> {{ $inspector->updated_at->format('d M Y H:i') }}</p>
            </div>
        </div>
        <div class="mt-3">
            <a href="{{ route('inspectors.edit', $inspector) }}" class="btn btn-primary">Edit</a>
            <a href="{{ route('inspectors.index') }}" class="btn btn-secondary">Back to List</a>
        </div>
    </div>
</body>
</html>