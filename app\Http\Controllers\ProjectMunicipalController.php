<?php

namespace App\Http\Controllers;

use App\Models\WebsiteBuilderProject;
use App\Models\Municipal;
use App\Models\ProjectMunicipal;
use Illuminate\Http\Request;

class ProjectMunicipalController extends Controller
{
    public function index()
    {
        $projectMunicipals = ProjectMunicipal::with(['project', 'municipal'])->get();
        $projects = WebsiteBuilderProject::all();
        $municipals = Municipal::all();
        
        return view('project_municipals.index', compact('projectMunicipals', 'projects', 'municipals'));
    }

    public function create()
    {
        $projects = WebsiteBuilderProject::all();
        $municipals = Municipal::all();
        return view('project_municipals.create', compact('projects', 'municipals'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'project_id' => 'required|exists:website_builder_projects,id',
            'municipal_id' => 'required|exists:municipals,id',
            'status' => 'nullable|string|max:255',
            'description' => 'nullable|string',
        ]);

        ProjectMunicipal::create($validated);

        return redirect()->route('project_municipals.index')->with('success', 'Record created successfully.');
    }

    public function edit(ProjectMunicipal $projectMunicipal)
    {
        $projects = WebsiteBuilderProject::all();
        $municipals = Municipal::all();
        return view('project_municipals.edit', compact('projectMunicipal', 'projects', 'municipals'));
    }

    public function update(Request $request, ProjectMunicipal $projectMunicipal)
    {
        $validated = $request->validate([
            'project_id' => 'required|exists:website_builder_projects,id',
            'municipal_id' => 'required|exists:municipals,id',
            'status' => 'nullable|string|max:255',
            'description' => 'nullable|string',
        ]);

        $projectMunicipal->update($validated);

        return redirect()->route('project_municipals.index')->with('success', 'Record updated successfully.');
    }

    public function destroy(ProjectMunicipal $projectMunicipal)
    {
        $projectMunicipal->delete();
        return redirect()->route('project_municipals.index')->with('success', 'Record deleted successfully.');
    }
}