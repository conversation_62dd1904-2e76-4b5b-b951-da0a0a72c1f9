<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProjectMunicipal extends Model
{
    protected $fillable = [
        'project_id',
        'municipal_id',
        'status',
        'description',
    ];

    public function project()
    {
        return $this->belongsTo(WebsiteBuilderProject::class, 'project_id');
    }

    public function municipal()
    {
        return $this->belongsTo(Municipal::class, 'municipal_id');
    }
}