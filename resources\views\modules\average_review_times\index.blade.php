@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4>Project Management > Avg Review Time</h4>
                        <div class="form-group mb-0">
                            <select class="form-control" onchange="this.options[this.selectedIndex].value && (window.location = this.options[this.selectedIndex].value);">
                                <option value="{{ route('average-review-times.index') }}">All Departments</option>
                                @foreach($departments as $id => $name)
                                    <option value="{{ route('average-review-times.index', ['trade' => $id]) }}"
                                            {{ $selectedTrade == $id ? 'selected' : '' }}>
                                        {{ $name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="card-body">
                        @if ($reviews->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="table-1">
                                    <thead>
                                        <tr>
                                            <th>Sr No</th>
                                            <th>Project Name</th>
                                            <th>Department (Trade)</th>
                                            <th>PUID </th>
                                            <th>Department Review Time</th>
                                            <th>PC Review Time</th>
                                            <th>Physical Inspection Review Time</th>
                                            <th>Average Days</th>
                                            {{-- <th>End Date</th> --}}
                                            {{-- <th class="text-center">Actions</th> --}}
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($reviews as $key => $review)
                                            <tr>
                                                <td>{{ $key + 1 }}</td>
                                                {{-- <td>{{ $review->project_name }}</td> --}}
                                                 <td>
    @if ($review->project_name)
        <a href="{{ route('myprojects.show.status', ['id' => $review->id]) }}">
            {{ $review->project_name }}
        </a>
    @else
        N/A
    @endif
</td>
                                                <td>{{ $review->department_trade }}</td>
                                                <td>{{ $review->permit_number }}</td>
                                                <td>{{ $review->department_review_time }}</td>
                                                <td>{{ $review->pc_review_time }}</td>
                                                <td>{{ $review->physical_inspection_review_time }}</td>
                                                <td>{{ $review->days }} days</td>
                                                {{-- <td>
                                                    @php
                                                        $endDate = \Carbon\Carbon::parse($review->end_date);
                                                        $today = \Carbon\Carbon::today();
                                                        $delayDays = $today->diffInDays($endDate, false);
                                                    @endphp
                                                    @if ($delayDays < 0)
                                                        <span class="badge badge-danger">
                                                            Delayed ({{ abs($delayDays) }} days)
                                                        </span>
                                                    @else
                                                        {{ $endDate->format('Y-m-d') }}
                                                    @endif
                                                </td> --}}
                                                {{-- <td class="text-center">
                                                    <a href="{{ route('average-review-times.edit', $review->id) }}" 
                                                       class="btn btn-sm btn-primary mr-1" 
                                                       title="Edit">
                                                        <i class="fa fa-edit"></i>
                                                    </a>
                                                    <form action="{{ route('average-review-times.destroy', $review->id) }}" 
                                                          method="POST" 
                                                          class="d-inline delete-form">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" 
                                                                class="btn btn-sm btn-danger delete-btn" 
                                                                title="Delete" 
                                                                data-name="{{ $review->project_name }}">
                                                            <i class="fa fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </td> --}}
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="alert alert-info text-center">
                                <i class="fa fa-info-circle mr-2"></i>
                                No average review times have been assigned yet.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('style')
    <style>
        .btn-sm { 
            padding: 0.25rem 0.5rem; 
        }
        .badge {
            font-size: 0.8rem;
            padding: 0.3em 0.6em;
        }
        .table th, .table td {
            vertical-align: middle;
        }
        .form-control {
            width: 200px;
        }
    </style>
@endpush

@push('script')
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Success Message from Session
            @if(session('success'))
                Swal.fire({
                    title: 'Success!',
                    text: '{{ session('success') }}',
                    icon: 'success',
                    showConfirmButton: false,
                    timer: 4000,
                    timerProgressBar: true,
                    position: 'top-end',
                    toast: true,
                    customClass: {
                        popup: 'swal2-popup-custom',
                        title: 'swal2-title-custom',
                        content: 'swal2-content-custom'
                    }
                });
            @endif

            // Delete Confirmation
            document.querySelectorAll('.delete-btn').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const form = this.closest('form');
                    const projectName = this.getAttribute('data-name');

                    Swal.fire({
                        title: 'Are you sure?',
                        text: `You are about to delete the average review time for "${projectName}". This action cannot be undone!`,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#3085d6',
                        confirmButtonText: 'Yes, delete it!',
                        cancelButtonText: 'Cancel'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            form.submit();
                        }
                    });
                });
            });
        });
    </script>

    <style>
        .swal2-popup-custom {
            border-radius: 8px;
            padding: 15px;
            background: #ffffff;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
        }
        .swal2-title-custom {
            color: #28a745;
            font-weight: bold;
            font-size: 18px;
        }
        .swal2-content-custom {
            font-size: 14px;
            color: #555;
        }
    </style>
@endpush