<template>
  <div class="signature-modal" v-if="show">
    <div class="modal-content">
      <div class="modal-header">
        <h5>Add Signature</h5>
        <button class="btn-close" @click="close"></button>
      </div>
      <div class="modal-body">
        <div class="signature-container" ref="signatureContainer">
          <canvas 
            ref="canvas" 
            width="400" 
            height="200"
            @mousedown="startDrawing"
            @mousemove="draw"
            @mouseup="stopDrawing"
            @mouseleave="stopDrawing"
            @touchstart="handleTouchStart"
            @touchmove="handleTouchMove"
            @touchend="stopDrawing"
          ></canvas>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-secondary" @click="clear">Clear</button>
        <button class="btn btn-primary" @click="save">Save</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'

const props = defineProps({
  show: Boolean
})

const emit = defineEmits(['update:show', 'save'])
const canvas = ref(null)
const ctx = ref(null)
const isDrawing = ref(false)
const lastX = ref(0)
const lastY = ref(0)

watch(() => props.show, (val) => {
  if (val) {
    initCanvas()
  }
})

function initCanvas() {
  if (!canvas.value) return
  
  ctx.value = canvas.value.getContext('2d')
  ctx.value.fillStyle = '#fff'
  ctx.value.fillRect(0, 0, canvas.value.width, canvas.value.height)
  ctx.value.strokeStyle = '#000'
  ctx.value.lineWidth = 2
  ctx.value.lineCap = 'round'
  ctx.value.lineJoin = 'round'
}

function getCoordinates(event) {
  const rect = canvas.value.getBoundingClientRect()
  return {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top
  }
}

function getTouchCoordinates(event) {
  const rect = canvas.value.getBoundingClientRect()
  const touch = event.touches[0]
  return {
    x: touch.clientX - rect.left,
    y: touch.clientY - rect.top
  }
}

function startDrawing(event) {
  isDrawing.value = true
  const coords = getCoordinates(event)
  lastX.value = coords.x
  lastY.value = coords.y
}

function handleTouchStart(event) {
  event.preventDefault()
  const coords = getTouchCoordinates(event)
  lastX.value = coords.x
  lastY.value = coords.y
  isDrawing.value = true
}

function draw(event) {
  if (!isDrawing.value || !ctx.value) return
  
  const coords = getCoordinates(event)
  ctx.value.beginPath()
  ctx.value.moveTo(lastX.value, lastY.value)
  ctx.value.lineTo(coords.x, coords.y)
  ctx.value.stroke()
  
  lastX.value = coords.x
  lastY.value = coords.y
}

function handleTouchMove(event) {
  event.preventDefault()
  if (!isDrawing.value || !ctx.value) return
  
  const coords = getTouchCoordinates(event)
  ctx.value.beginPath()
  ctx.value.moveTo(lastX.value, lastY.value)
  ctx.value.lineTo(coords.x, coords.y)
  ctx.value.stroke()
  
  lastX.value = coords.x
  lastY.value = coords.y
}

function stopDrawing() {
  isDrawing.value = false
}

function clear() {
  if (!ctx.value || !canvas.value) return
  ctx.value.fillStyle = '#fff'
  ctx.value.fillRect(0, 0, canvas.value.width, canvas.value.height)
  ctx.value.strokeStyle = '#000'
}

function save() {
  if (!canvas.value) return
  const signature = canvas.value.toDataURL()
    emit('save', signature)
    close()
}

function close() {
  emit('update:show', false)
  clear()
}

onMounted(() => {
  if (props.show) {
    initCanvas()
  }
})
</script>

<style scoped>
.signature-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 450px;
  padding: 1rem;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.signature-container {
  border: 1px solid #dee2e6;
  border-radius: 4px;
  margin: 1rem 0;
  width: 400px;
  height: 200px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

canvas {
  width: 100%;
  height: 100%;
  touch-action: none;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-top: 1rem;
}
</style> 