@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <section class="section">
            <div class="section-body">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h4><i class="fas fa-bell"></i> Notification Details</h4>
                                <div class="card-header-action">
                                    <a href="{{ route('notifications.index') }}" class="btn btn-light btn-sm"><i class="fas fa-arrow-left"></i> Back to List</a>
                                </div>
                            </div>
                            <div class="card-body">
                                @if (session('success'))
                                    <div class="alert alert-success">
                                        {{ session('success') }}
                                    </div>
                                @endif

                                <div class="row">
                                    <div class="col-md-6">
    <h5>Project</h5>
    <p>
        @if ($notification->project)
            {{ $notification->project->project_name }}
        @else
            <span class="text-danger">Project Not Found</span>
        @endif
    </p>
</div>

                                    <div class="col-md-6">
                                        <h5>Title</h5>
                                        <p><strong>{{ $notification->title }}</strong></p>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>Inspector</h5>
                                        <p>
                                            @if ($notification->inspector)
                                                {{ $notification->inspector->first_name }} {{ $notification->inspector->last_name }}
                                            @else
                                                <span class="text-danger">Inspector Not Found</span>
                                            @endif
                                        </p>
                                    </div>
                                    {{-- <div class="col-md-6">
                                        <h5>Priority</h5>
                                        <p>
                                            <span class="badge badge-{{ $notification->priority == 'high' ? 'danger' : ($notification->priority == 'medium' ? 'warning' : 'success') }}">
                                                {{ ucfirst($notification->priority) }}
                                            </span>
                                        </p>
                                    </div> --}}
                                    <div class="col-md-6">
    <h5>Tag</h5>
    <p>
        @php
            $tagClass = match($notification->tag) {
                'Approved' => 'success',
                'Violation' => 'danger',
                'Message' => 'info',
                default => 'secondary',
            };
        @endphp
        <span class="badge badge-{{ $tagClass }}">{{ $notification->tag }}</span>
    </p>
</div>

                                    <div class="col-md-6">
                                        <h5>Sent At</h5>
                                        <p>{{ \Carbon\Carbon::parse($notification->sent_at)->format('d M, Y H:i') }}</p>
                                    </div>
                                    <div class="col-12">
                                        <h5>Message</h5>
                                        <p>{{ $notification->message }}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>Status</h5>
                                        <p>
                                            @if ($notification->sent_at)
                                                <span class="badge badge-success">Sent</span>
                                            @else
                                                <span class="badge badge-secondary">Pending</span>
                                            @endif
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
@endsection