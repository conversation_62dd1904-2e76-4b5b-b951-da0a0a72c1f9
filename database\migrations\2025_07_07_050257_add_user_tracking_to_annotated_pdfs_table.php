<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('annotated_pdfs', function (Blueprint $table) {
            $table->unsignedBigInteger('uploaded_by')->nullable()->after('document_type');
            $table->string('uploaded_by_role')->nullable()->after('uploaded_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('annotated_pdfs', function (Blueprint $table) {
            $table->dropColumn(['uploaded_by', 'uploaded_by_role']);
        });
    }
};
