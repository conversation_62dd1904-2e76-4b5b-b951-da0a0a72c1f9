/**
 * @license
 * Copyright (c) 2018 amCharts (<PERSON><PERSON><PERSON>, <PERSON><PERSON>)
 *
 * This sofware is provided under multiple licenses. Please see below for
 * links to appropriate usage.
 *
 * Free amCharts linkware license. Details and conditions:
 * https://github.com/amcharts/amcharts4/blob/master/LICENSE
 *
 * One of the amCharts commercial licenses. Details and pricing:
 * https://www.amcharts.com/online-store/
 * https://www.amcharts.com/online-store/licenses-explained/
 *
 * If in doubt, contact <NAME_EMAIL>
 *
 * PLEASE DO NOT REMOVE THIS COPYRIGHT NOTICE.
 * @hidden
 */
webpackJsonp(["689e"],{"2zgF":function(e,t,i){"use strict";i.d(t,"a",function(){return y});var a=i("m4/l"),n=i("Meme"),s=i("Lrmi"),r=i("yrKf"),o=i("IbTV"),l=i("aCit"),h=i("tjMS"),u=i("Gg2j"),d=i("hGwe"),c=i("v9UT"),p=i("Mtpk"),y=function(e){function t(){var t=e.call(this)||this;return t.pixelRadiusReal=0,t.layout="none",t.className="AxisRendererCircular",t.isMeasured=!1,t.startAngle=-90,t.endAngle=270,t.useChartAngles=!0,t.radius=Object(h.c)(100),t.isMeasured=!1,t.grid.template.location=0,t.labels.template.location=0,t.labels.template.radius=15,t.ticks.template.location=0,t.ticks.template.pixelPerfect=!1,t.tooltipLocation=0,t.line.strokeOpacity=0,t.applyTheme(),t}return a.c(t,e),t.prototype.setAxis=function(t){var i=this;e.prototype.setAxis.call(this,t),t.isMeasured=!1;var a=t.tooltip;a.adapter.add("dx",function(e,t){var a=c.svgPointToSprite({x:t.pixelX,y:t.pixelY},i);return i.pixelRadius*Math.cos(Math.atan2(a.y,a.x))-a.x}),a.adapter.add("dy",function(e,t){var a=c.svgPointToSprite({x:t.pixelX,y:t.pixelY},i);return i.pixelRadius*Math.sin(Math.atan2(a.y,a.x))-a.y})},t.prototype.validate=function(){this.chart&&this.chart.invalid&&this.chart.validate(),e.prototype.validate.call(this)},Object.defineProperty(t.prototype,"axisLength",{get:function(){return 2*Math.PI*this.pixelRadius},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!1,!1,10,!1)&&this.axis&&this.axis.invalidate()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"pixelRadius",{get:function(){return c.relativeRadiusToValue(this.radius,this.pixelRadiusReal)||0},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(e){this.setPercentProperty("innerRadius",e,!1,!1,10,!1)&&this.axis&&this.axis.invalidate()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"useChartAngles",{get:function(){return this.getPropertyValue("useChartAngles")},set:function(e){this.setPropertyValue("useChartAngles",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"pixelInnerRadius",{get:function(){return c.relativeRadiusToValue(this.innerRadius,this.pixelRadiusReal)||0},enumerable:!0,configurable:!0}),t.prototype.positionToPoint=function(e){var t=this.positionToCoordinate(e),i=this.startAngle+(this.endAngle-this.startAngle)*t/this.axisLength;return{x:this.pixelRadius*u.cos(i),y:this.pixelRadius*u.sin(i)}},t.prototype.positionToAngle=function(e){var t,i=this.axis,a=(this.endAngle-this.startAngle)/(i.end-i.start);return t=i.renderer.inversed?this.startAngle+(i.end-e)*a:this.startAngle+(e-i.start)*a,u.round(t,3)},t.prototype.updateAxisLine=function(){var e=this.pixelRadius,t=this.startAngle,i=this.endAngle-t;this.line.path=d.moveTo({x:e*u.cos(t),y:e*u.sin(t)})+d.arcTo(t,i,e,e)},t.prototype.updateGridElement=function(e,t,i){t+=(i-t)*e.location;var a=this.positionToPoint(t);if(e.element){var n=u.DEGREES*Math.atan2(a.y,a.x),s=c.relativeRadiusToValue(p.hasValue(e.radius)?e.radius:Object(h.c)(100),this.pixelRadius),r=c.relativeRadiusToValue(e.innerRadius,this.pixelRadius);e.zIndex=0;var o=c.relativeRadiusToValue(p.isNumber(r)?r:this.innerRadius,this.pixelRadius,!0);e.path=d.moveTo({x:o*u.cos(n),y:o*u.sin(n)})+d.lineTo({x:s*u.cos(n),y:s*u.sin(n)})}this.toggleVisibility(e,t,0,1)},t.prototype.updateTickElement=function(e,t,i){t+=(i-t)*e.location;var a=this.positionToPoint(t);if(e.element){var n=this.pixelRadius,s=u.DEGREES*Math.atan2(a.y,a.x),r=e.length;e.inside&&(r=-r),e.zIndex=1,e.path=d.moveTo({x:n*u.cos(s),y:n*u.sin(s)})+d.lineTo({x:(n+r)*u.cos(s),y:(n+r)*u.sin(s)})}this.toggleVisibility(e,t,0,1)},t.prototype.updateLabelElement=function(e,t,i,a){p.hasValue(a)||(a=e.location),t+=(i-t)*a;var n=this.positionToPoint(t);e.fixPoint(n,this.pixelRadius),e.zIndex=2,this.positionItem(e,n),this.toggleVisibility(e,t,this.minLabelPosition,this.maxLabelPosition)},t.prototype.fitsToBounds=function(e){return!0},Object.defineProperty(t.prototype,"startAngle",{get:function(){return this.getPropertyValue("startAngle")},set:function(e){this.setPropertyValue("startAngle",e)&&(this.invalidateAxisItems(),this.axis&&this.axis.invalidateSeries())},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endAngle",{get:function(){return this.getPropertyValue("endAngle")},set:function(e){this.setPropertyValue("endAngle",e)&&(this.invalidateAxisItems(),this.axis&&this.axis.invalidateSeries())},enumerable:!0,configurable:!0}),t.prototype.getPositionRangePath=function(e,t,i,a,n){var s="";if(p.isNumber(e)&&p.isNumber(t)){p.hasValue(i)||(i=this.radius),e=u.max(e,this.axis.start),(t=u.min(t,this.axis.end))<e&&(t=e);var r=c.relativeRadiusToValue(i,this.pixelRadius),o=c.relativeRadiusToValue(a,this.pixelRadius,!0),l=this.positionToAngle(e),h=this.positionToAngle(t)-l;s=d.arc(l,h,r,o,r,n)}return s},t.prototype.createGrid=function(){return new r.a},t.prototype.createFill=function(e){return new s.a(e)},t.prototype.createLabel=function(){return new o.a},t.prototype.pointToPosition=function(e){var t=u.fitAngleToRange(u.getAngle(e),this.startAngle,this.endAngle);return this.coordinateToPosition((t-this.startAngle)/360*this.axisLength)},t}(n.a);l.b.registeredClasses.AxisRendererCircular=y},IbTV:function(e,t,i){"use strict";i.d(t,"a",function(){return h});var a=i("m4/l"),n=i("TDx+"),s=i("aCit"),r=i("Gg2j"),o=i("Mtpk"),l=i("v9UT"),h=function(e){function t(){var t=e.call(this)||this;return t.fdx=0,t.fdy=0,t.className="AxisLabelCircular",t.padding(0,0,0,0),t.location=.5,t.radius=0,t.isMeasured=!1,t.applyTheme(),t}return a.c(t,e),Object.defineProperty(t.prototype,"relativeRotation",{get:function(){return this.getPropertyValue("relativeRotation")},set:function(e){this.setPropertyValue("relativeRotation",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),t.prototype.pixelRadius=function(e){var t=1;return this.inside&&(t=-1),l.relativeToValue(this.radius,e)*t},t.prototype.fixPoint=function(e,t,i){var a=r.DEGREES*Math.atan2(e.y,e.x);o.isNumber(i)||(i=t),this.invalid&&this.validate();var n=this.relativeRotation;this.dy=-this._measuredHeight*(1-(e.y+t)/(2*t)),this.dx=-this._measuredWidth*(1-(e.x+t)/(2*t));var s=this.pixelRadius(t);if(o.isNumber(n)){var l=this.bbox.width,h=this.bbox.height;a>90||a<-90?-90==n&&(n=90,l=0):(-90==n&&(h=-h),90==n&&(n=-90,l=0,h=-h)),this.rotation=n+a+90;var u=r.sin(n)/2,d=r.cos(n)/2,c=this.rotation;this.dx=h*u*r.sin(c)-l*d*r.cos(c),this.dy=-h*u*r.cos(c)-l*d*r.sin(c);var p=this.pixelPaddingBottom,y=this.pixelPaddingTop,g=this.pixelPaddingLeft,f=this.pixelPaddingRight;this.inside?a>90||a<-90?s-=(p+y)*r.cos(n)+(g+f)*r.sin(n):s+=(p+this.bbox.height+y)*r.cos(n)+(g+f+this.bbox.width)*r.sin(n):s+=(h+p+y)*r.cos(n)+(l+g+f)*r.sin(n)}return this.fdx=this.dx,this.fdy=this.dy,e.x+=r.cos(a)*s,e.y+=r.sin(a)*s*i/t,e},t}(n.a);s.b.registeredClasses.AxisLabelCircular=h},Lrmi:function(e,t,i){"use strict";i.d(t,"a",function(){return l});var a=i("m4/l"),n=i("8EhG"),s=i("tjMS"),r=i("aCit"),o=i("Mtpk"),l=function(e){function t(t){var i=e.call(this,t)||this;return i.className="AxisFillCircular",i.element=i.paper.add("path"),i.radius=Object(s.c)(100),i.applyTheme(),i}return a.c(t,e),t.prototype.draw=function(){if(e.prototype.draw.call(this),this.axis){var t=this.axis.renderer;this.fillPath=t.getPositionRangePath(this.startPosition,this.endPosition,this.radius,o.hasValue(this.innerRadius)?this.innerRadius:t.innerRadius,this.cornerRadius),this.path=this.fillPath}},Object.defineProperty(t.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(e){this.setPercentProperty("innerRadius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"cornerRadius",{get:function(){return this.getPropertyValue("cornerRadius")},set:function(e){this.setPropertyValue("cornerRadius",e,!0)},enumerable:!0,configurable:!0}),t}(n.a);r.b.registeredClasses.AxisFillCircular=l},XFs4:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a={};i.d(a,"GaugeChartDataItem",function(){return pe}),i.d(a,"GaugeChart",function(){return ye}),i.d(a,"RadarChartDataItem",function(){return he}),i.d(a,"RadarChart",function(){return ue}),i.d(a,"XYChartDataItem",function(){return K}),i.d(a,"XYChart",function(){return G}),i.d(a,"SerialChartDataItem",function(){return s.b}),i.d(a,"SerialChart",function(){return s.a}),i.d(a,"PieChart3DDataItem",function(){return Se}),i.d(a,"PieChart3D",function(){return Re}),i.d(a,"PieChartDataItem",function(){return _e}),i.d(a,"PieChart",function(){return Ie}),i.d(a,"SlicedChart",function(){return Oe}),i.d(a,"SlicedChartDataItem",function(){return Fe}),i.d(a,"FlowDiagramDataItem",function(){return ke.b}),i.d(a,"FlowDiagram",function(){return ke.a}),i.d(a,"SankeyDiagramDataItem",function(){return we.b}),i.d(a,"SankeyDiagram",function(){return we.a}),i.d(a,"ChordDiagramDataItem",function(){return Be}),i.d(a,"ChordDiagram",function(){return We}),i.d(a,"TreeMapDataItem",function(){return qe}),i.d(a,"TreeMap",function(){return Je}),i.d(a,"XYChart3DDataItem",function(){return at}),i.d(a,"XYChart3D",function(){return nt}),i.d(a,"ChartDataItem",function(){return st.b}),i.d(a,"Chart",function(){return st.a}),i.d(a,"LegendDataItem",function(){return rt.b}),i.d(a,"Legend",function(){return rt.a}),i.d(a,"LegendSettings",function(){return rt.c}),i.d(a,"HeatLegend",function(){return ot.a}),i.d(a,"SeriesDataItem",function(){return A.b}),i.d(a,"Series",function(){return A.a}),i.d(a,"XYSeriesDataItem",function(){return w}),i.d(a,"XYSeries",function(){return L}),i.d(a,"LineSeriesDataItem",function(){return ee}),i.d(a,"LineSeries",function(){return te}),i.d(a,"LineSeriesSegment",function(){return q}),i.d(a,"CandlestickSeriesDataItem",function(){return ht}),i.d(a,"CandlestickSeries",function(){return ut}),i.d(a,"OHLCSeriesDataItem",function(){return ct}),i.d(a,"OHLCSeries",function(){return pt}),i.d(a,"ColumnSeriesDataItem",function(){return Ue}),i.d(a,"ColumnSeries",function(){return He}),i.d(a,"StepLineSeriesDataItem",function(){return gt}),i.d(a,"StepLineSeries",function(){return ft}),i.d(a,"RadarSeriesDataItem",function(){return ie}),i.d(a,"RadarSeries",function(){return ae}),i.d(a,"RadarColumnSeriesDataItem",function(){return xt}),i.d(a,"RadarColumnSeries",function(){return vt}),i.d(a,"PieSeriesDataItem",function(){return Pe}),i.d(a,"PieSeries",function(){return Ce}),i.d(a,"FunnelSeries",function(){return bt.a}),i.d(a,"FunnelSeriesDataItem",function(){return bt.b}),i.d(a,"PyramidSeries",function(){return Pt}),i.d(a,"PyramidSeriesDataItem",function(){return At}),i.d(a,"PictorialStackedSeries",function(){return _t}),i.d(a,"PictorialStackedSeriesDataItem",function(){return Ct}),i.d(a,"PieTick",function(){return Ae}),i.d(a,"FunnelSlice",function(){return It.a}),i.d(a,"PieSeries3DDataItem",function(){return Te}),i.d(a,"PieSeries3D",function(){return Ve}),i.d(a,"TreeMapSeriesDataItem",function(){return Ke}),i.d(a,"TreeMapSeries",function(){return Ge}),i.d(a,"ColumnSeries3DDataItem",function(){return tt}),i.d(a,"ColumnSeries3D",function(){return it}),i.d(a,"ConeSeriesDataItem",function(){return Vt}),i.d(a,"ConeSeries",function(){return St}),i.d(a,"CurvedColumnSeries",function(){return Ot}),i.d(a,"CurvedColumnSeriesDataItem",function(){return Ft}),i.d(a,"AxisDataItem",function(){return u.b}),i.d(a,"Axis",function(){return u.a}),i.d(a,"Grid",function(){return kt.a}),i.d(a,"AxisTick",function(){return wt.a}),i.d(a,"AxisLabel",function(){return Lt.a}),i.d(a,"AxisLine",function(){return Xt.a}),i.d(a,"AxisFill",function(){return Yt.a}),i.d(a,"AxisRenderer",function(){return jt.a}),i.d(a,"AxisBreak",function(){return p.a}),i.d(a,"ValueAxisDataItem",function(){return C.b}),i.d(a,"ValueAxis",function(){return C.a}),i.d(a,"CategoryAxisDataItem",function(){return v}),i.d(a,"CategoryAxis",function(){return b}),i.d(a,"CategoryAxisBreak",function(){return y}),i.d(a,"DateAxisDataItem",function(){return S}),i.d(a,"DateAxis",function(){return R}),i.d(a,"DurationAxisDataItem",function(){return Nt}),i.d(a,"DurationAxis",function(){return Mt}),i.d(a,"DateAxisBreak",function(){return D}),i.d(a,"ValueAxisBreak",function(){return I.a}),i.d(a,"AxisRendererX",function(){return l.a}),i.d(a,"AxisRendererY",function(){return h.a}),i.d(a,"AxisRendererRadial",function(){return le}),i.d(a,"AxisLabelCircular",function(){return ve.a}),i.d(a,"AxisRendererCircular",function(){return re.a}),i.d(a,"AxisFillCircular",function(){return Bt.a}),i.d(a,"GridCircular",function(){return Wt.a}),i.d(a,"AxisRendererX3D",function(){return Qe}),i.d(a,"AxisRendererY3D",function(){return $e}),i.d(a,"Tick",function(){return be.a}),i.d(a,"Bullet",function(){return Xe.a}),i.d(a,"LabelBullet",function(){return Et.a}),i.d(a,"CircleBullet",function(){return zt}),i.d(a,"ErrorBullet",function(){return Ut}),i.d(a,"XYChartScrollbar",function(){return H}),i.d(a,"ClockHand",function(){return ce}),i.d(a,"FlowDiagramNode",function(){return Le.a}),i.d(a,"FlowDiagramLink",function(){return je.a}),i.d(a,"SankeyNode",function(){return Ht.a}),i.d(a,"SankeyLink",function(){return Kt.a}),i.d(a,"ChordNode",function(){return Ye}),i.d(a,"ChordLink",function(){return Me}),i.d(a,"NavigationBarDataItem",function(){return Qt}),i.d(a,"NavigationBar",function(){return $t}),i.d(a,"Column",function(){return Ee.a}),i.d(a,"Candlestick",function(){return lt}),i.d(a,"OHLC",function(){return dt}),i.d(a,"RadarColumn",function(){return mt}),i.d(a,"Column3D",function(){return et.a}),i.d(a,"ConeColumn",function(){return Tt}),i.d(a,"CurvedColumn",function(){return Rt}),i.d(a,"XYCursor",function(){return W}),i.d(a,"Cursor",function(){return N}),i.d(a,"RadarCursor",function(){return se});var n=i("m4/l"),s=i("2I/e"),r=i("C6dT"),o=i("vMqJ"),l=i("k6kv"),h=i("OXm9"),u=i("AAkI"),d=i("aCit"),c=i("+qIf"),p=i("Uslz"),y=function(e){function t(){var t=e.call(this)||this;return t.className="CategoryAxisBreak",t.applyTheme(),t}return n.c(t,e),Object.defineProperty(t.prototype,"startPosition",{get:function(){if(this.axis)return this.axis.indexToPosition(this.adjustedStartValue)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endPosition",{get:function(){if(this.axis)return this.axis.indexToPosition(this.adjustedEndValue)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"startCategory",{get:function(){return this.getPropertyValue("startCategory")},set:function(e){this.setPropertyValue("startCategory",e)&&this.axis&&(this.axis.invalidateDataItems(),this.axis.invalidateSeries())},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endCategory",{get:function(){return this.getPropertyValue("endCategory")},set:function(e){this.setPropertyValue("endCategory",e)&&this.axis&&(this.axis.invalidateDataItems(),this.axis.invalidateSeries())},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"startValue",{get:function(){var e=this.getPropertyValue("startCategory");return e?this.axis.categoryToIndex(e):this.getPropertyValue("startValue")},set:function(e){this.setPropertyValue("startValue",e)&&this.axis&&(this.axis.invalidateDataItems(),this.axis.invalidateSeries())},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endValue",{get:function(){var e=this.getPropertyValue("endCategory");return e?this.axis.categoryToIndex(e):this.getPropertyValue("endValue")},set:function(e){this.setPropertyValue("endValue",e)&&this.axis&&(this.axis.invalidateDataItems(),this.axis.invalidateSeries())},enumerable:!0,configurable:!0}),t}(p.a);d.b.registeredClasses.CategoryAxisBreak=y;var g=i("Gg2j"),f=i("Mtpk"),m=i("Wglt"),x=i("x79X"),v=function(e){function t(){var t=e.call(this)||this;return t.adapter=new x.a(t),t.className="CategoryAxisDataItem",t.text="{category}",t.locations.category=0,t.locations.endCategory=1,t.applyTheme(),t}return n.c(t,e),Object.defineProperty(t.prototype,"category",{get:function(){return this.adapter.isEnabled("category")?this.adapter.apply("category",this.properties.category):this.properties.category},set:function(e){this.setProperty("category",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endCategory",{get:function(){return this.properties.endCategory},set:function(e){this.setProperty("endCategory",e)},enumerable:!0,configurable:!0}),t}(u.b),b=function(e){function t(){var t=e.call(this)||this;return t.dataItemsByCategory=new c.a,t.className="CategoryAxis",t.axisFieldName="category",t._lastDataItem=t.createDataItem(),t._lastDataItem.component=t,t._disposers.push(t._lastDataItem),t.applyTheme(),t}return n.c(t,e),t.prototype.createDataItem=function(){return new v},t.prototype.createAxisBreak=function(){return new y},t.prototype.validateDataRange=function(){var i=this;e.prototype.validateDataRange.call(this),m.each(this._series.iterator(),function(e){if(e.xAxis instanceof t&&e.yAxis instanceof t)e.invalidateDataRange();else{var a=i.getSeriesDataItem(e,i.start),n=i.getSeriesDataItem(e,i.end,!0);a?e.startIndex=a.index:e.start=i.start,n?e.endIndex=n.index+1:e.end=i.end,i.axisBreaks.length>0&&e.invalidateDataRange()}})},t.prototype.validate=function(){var t=this;e.prototype.validate.call(this);var i=this.dataItems.length,a=g.fitToRange(Math.floor(this.start*i-1),0,i),n=g.fitToRange(Math.ceil(this.end*i),0,i);this.renderer.invalid&&this.renderer.validate();var s=this.renderer.axisLength/this.renderer.minGridDistance,r=Math.min(this.dataItems.length,Math.ceil((n-a)/s));if(this._startIndex=Math.floor(a/r)*r,this._endIndex=Math.ceil(this.end*i),this.fixAxisBreaks(),this._startIndex==this._endIndex&&this._endIndex++,this._frequency=r,!(this.axisLength<=0)){this.maxZoomFactor=this.dataItems.length,this.dataItems.length<=0&&(this.maxZoomFactor=1),this.resetIterators(),a=g.max(0,this._startIndex-this._frequency),n=g.min(this.dataItems.length,this._endIndex+this._frequency);for(var o=0,l=0;l<a;l++){(u=this.dataItems.getIndex(l)).__disabled=!0}l=n;for(var h=this.dataItems.length;l<h;l++){(u=this.dataItems.getIndex(l)).__disabled=!0}for(l=a;l<n;l++)if(l<this.dataItems.length){var u=this.dataItems.getIndex(l);if(l/this._frequency==Math.round(l/this._frequency))this.isInBreak(l)||(this.appendDataItem(u),this.validateDataElement(u,o)),o++;else this.validateDataElement(u,o),u.__disabled=!0}this.appendDataItem(this._lastDataItem),this.validateDataElement(this._lastDataItem,o+1,this.dataItems.length),this.axisBreaks.each(function(e){var i=e.adjustedStartValue,a=e.adjustedEndValue;if(g.intersect({start:i,end:a},{start:t._startIndex,end:t._endIndex}))for(var n=g.fitToRange(Math.ceil(t._frequency/e.breakSize),1,a-i),s=0,r=i;r<=a;r+=n){var o=t.dataItems.getIndex(r);t.appendDataItem(o),t.validateDataElement(o,s),s++}}),this.validateBreaks(),this.validateAxisRanges(),this.ghostLabel.invalidate(),this.renderer.invalidateLayout()}},t.prototype.validateDataElement=function(t,i,a){e.prototype.validateDataElement.call(this,t);var n=this.renderer;f.isNumber(a)||(a=this.categoryToIndex(t.category));var s=this.categoryToIndex(t.endCategory);f.isNumber(s)||(s=a);var r,o,l,h=this.indexToPosition(a,t.locations.category),u=this.indexToPosition(s,t.locations.endCategory);t.position=h,t.isRange&&(r=s,o=this.indexToPosition(a,t.locations.category),l=this.indexToPosition(r,t.locations.endCategory)),t.point=n.positionToPoint(h);var d=t.tick;d&&!d.disabled&&n.updateTickElement(d,h,u);var c=t.grid;c&&!c.disabled&&n.updateGridElement(c,h,u);var p=t.label;p&&!p.disabled&&(t.isRange&&void 0!=p.text||(t.text=t.text),n.updateLabelElement(p,h,u),(t.label.measuredWidth>this.ghostLabel.measuredWidth||t.label.measuredHeight>this.ghostLabel.measuredHeight)&&(this.ghostLabel.text=t.label.currentText));var y=t.axisFill;y&&!y.disabled&&(t.isRange||(r=a+this._frequency,o=this.indexToPosition(a,y.location),l=this.indexToPosition(r,y.location)),n.updateFillElement(y,o,l),t.isRange||this.fillRule(t,i));var g=t.mask;g&&n.updateFillElement(g,o,l)},t.prototype.processDataItem=function(t,i){e.prototype.processDataItem.call(this,t,i);var a=this.dataItemsByCategory.getKey(t.category);a!=t&&this.dataItems.remove(a),this.dataItemsByCategory.setKey(t.category,t)},t.prototype.indexToPosition=function(e,t){f.isNumber(t)||(t=.5);var i=this.startIndex,a=this.endIndex,n=this.adjustDifference(i,a),s=this.startLocation;n-=s,n-=1-this.endLocation;var r=this.axisBreaks;return m.eachContinue(r.iterator(),function(t){var n=t.adjustedStartValue,s=t.adjustedEndValue;if(e<i)return!1;if(g.intersect({start:n,end:s},{start:i,end:a})){n=Math.max(i,n),s=Math.min(a,s);var r=t.breakSize;e>s?i+=(s-n)*(1-r):e<n||(e=n+(e-n)*r)}return!0}),g.round((e+t-s-i)/n,5)},t.prototype.categoryToPosition=function(e,t){var i=this.categoryToIndex(e);return this.indexToPosition(i,t)},t.prototype.categoryToPoint=function(e,t){var i=this.categoryToPosition(e,t),a=this.renderer.positionToPoint(i),n=this.renderer.positionToAngle(i);return{x:a.x,y:a.y,angle:n}},t.prototype.anyToPoint=function(e,t){return this.categoryToPoint(e,t)},t.prototype.anyToPosition=function(e,t){return this.categoryToPosition(e,t)},t.prototype.categoryToIndex=function(e){if(f.hasValue(e)){var t=this.dataItemsByCategory.getKey(e);if(t)return t.index}},t.prototype.zoomToCategories=function(e,t){this.zoomToIndexes(this.categoryToIndex(e),this.categoryToIndex(t)+1)},t.prototype.getAnyRangePath=function(e,t,i,a){var n=this.categoryToPosition(e,i),s=this.categoryToPosition(t,a);return this.getPositionRangePath(n,s)},t.prototype.roundPosition=function(e,t){var i=this.positionToIndex(e);return this.indexToPosition(i,t)},t.prototype.getSeriesDataItem=function(e,t,i){var a=this;if(f.isNumber(t)){var n=this.positionToIndex(t),s=this.dataItems.getIndex(n);if(s){var r,o=s.category;if(!i){var l=e.dataItems.getIndex(n);return e.xAxis==this&&l.categoryX==o?l:e.yAxis==this&&l.categoryY==o?l:(m.eachContinue(e.dataItems.iterator(),function(t){return e.xAxis==a&&t.categoryX==o?(r=t,!1):e.yAxis!=a||t.categoryY!=o||(r=t,!1)}),r)}for(var h=e.dataItems.length-1;h>=0;h--){var u=e.dataItems.getIndex(h);if(e.xAxis==this&&u.categoryX==o)return u;if(e.yAxis==this&&u.categoryY==o)return u}}}},t.prototype.getX=function(e,t,i){var a;return f.hasValue(t)&&(a=this.categoryToPosition(e.categories[t],i)),f.isNaN(a)?this.basePoint.x:this.renderer.positionToPoint(a).x},t.prototype.getY=function(e,t,i){var a;return f.hasValue(t)&&(a=this.categoryToPosition(e.categories[t],i)),f.isNaN(a)?this.basePoint.y:this.renderer.positionToPoint(a).y},t.prototype.getAngle=function(e,t,i,a){return this.positionToAngle(this.categoryToPosition(e.categories[t],i))},t.prototype.getCellStartPosition=function(e){return this.roundPosition(e,0)},t.prototype.getCellEndPosition=function(e){return this.roundPosition(e,1)},t.prototype.getTooltipText=function(e){var t=this.dataItems.getIndex(this.positionToIndex(e));if(t)return this.adapter.apply("getTooltipText",t.category)},t.prototype.positionToIndex=function(e){(e=g.round(e,10))<0&&(e=0);var t=this.startIndex,i=this.endIndex,a=i-t,n=this.axisBreaks,s=null;return m.eachContinue(n.iterator(),function(n){var r=n.startPosition,o=n.endPosition,l=n.adjustedStartValue,h=n.adjustedEndValue;l=g.max(l,t),h=g.min(h,i);var u=n.breakSize;if(a-=(h-l)*(1-u),e>o)t+=(h-l)*(1-u);else if(!(e<r)){var d=(e-r)/(o-r);return s=l+Math.round(d*(h-l)),!1}return!0}),f.isNumber(s)||(s=Math.floor(e*a+t)),s>=i&&s--,s},t.prototype.getPositionLabel=function(e){var t=this.dataItems.getIndex(this.positionToIndex(e));if(t)return t.category},Object.defineProperty(t.prototype,"basePoint",{get:function(){return this.renderer.positionToPoint(1)},enumerable:!0,configurable:!0}),t.prototype.initRenderer=function(){e.prototype.initRenderer.call(this),this.renderer.baseGrid.disabled=!0},t}(u.a);d.b.registeredClasses.CategoryAxis=b,d.b.registeredClasses.CategoryAxisDataItem=v;var A=i("aM7D"),P=i("Vs7R"),C=i("pR7v"),_=i("hD5A"),I=i("ZoDA"),D=function(e){function t(){var t=e.call(this)||this;return t.className="DateAxisBreak",t.applyTheme(),t}return n.c(t,e),Object.defineProperty(t.prototype,"startDate",{get:function(){return this.getPropertyValue("startDate")},set:function(e){this.setPropertyValue("startDate",e)&&(this.startValue=e.getTime(),this.axis&&(this.axis.invalidate(),this.axis.invalidateSeries()))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endDate",{get:function(){return this.getPropertyValue("endDate")},set:function(e){this.setPropertyValue("endDate",e)&&(this.endValue=e.getTime(),this.axis&&(this.axis.invalidate(),this.axis.invalidateSeries()))},enumerable:!0,configurable:!0}),t}(I.a);d.b.registeredClasses.DateAxisBreak=D;var T=i("L91H"),V=i("Qkdp"),S=function(e){function t(){var t=e.call(this)||this;return t.className="DateAxisDataItem",t.applyTheme(),t.values.date={},t.values.endDate={},t}return n.c(t,e),Object.defineProperty(t.prototype,"date",{get:function(){return this.dates.date},set:function(e){this.setDate("date",e),this.value=e.getTime()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endDate",{get:function(){return this.dates.endDate},set:function(e){this.setDate("endDate",e),this.endValue=e.getTime()},enumerable:!0,configurable:!0}),t}(C.b),R=function(e){function t(){var t=e.call(this)||this;return t.gridIntervals=new o.b,t.dateFormats=new c.a,t.periodChangeDateFormats=new c.a,t._baseIntervalReal={timeUnit:"day",count:1},t._minDifference={},t.className="DateAxis",t.setPropertyValue("markUnitChange",!0),t.snapTooltip=!0,t.gridIntervals.pushAll([{timeUnit:"millisecond",count:1},{timeUnit:"millisecond",count:5},{timeUnit:"millisecond",count:10},{timeUnit:"millisecond",count:50},{timeUnit:"millisecond",count:100},{timeUnit:"millisecond",count:500},{timeUnit:"second",count:1},{timeUnit:"second",count:5},{timeUnit:"second",count:10},{timeUnit:"second",count:30},{timeUnit:"minute",count:1},{timeUnit:"minute",count:5},{timeUnit:"minute",count:10},{timeUnit:"minute",count:15},{timeUnit:"minute",count:30},{timeUnit:"hour",count:1},{timeUnit:"hour",count:3},{timeUnit:"hour",count:6},{timeUnit:"hour",count:12},{timeUnit:"day",count:1},{timeUnit:"day",count:2},{timeUnit:"day",count:3},{timeUnit:"day",count:4},{timeUnit:"day",count:5},{timeUnit:"week",count:1},{timeUnit:"month",count:1},{timeUnit:"month",count:2},{timeUnit:"month",count:3},{timeUnit:"month",count:6},{timeUnit:"year",count:1},{timeUnit:"year",count:2},{timeUnit:"year",count:5},{timeUnit:"year",count:10},{timeUnit:"year",count:50},{timeUnit:"year",count:100},{timeUnit:"year",count:200},{timeUnit:"year",count:500},{timeUnit:"year",count:1e3},{timeUnit:"year",count:2e3},{timeUnit:"year",count:5e3},{timeUnit:"year",count:1e4},{timeUnit:"year",count:1e5}]),t.axisFieldName="date",t.applyTheme(),t}return n.c(t,e),t.prototype.fillRule=function(e){var t=e.value,i=e.component,a=i._gridInterval,n=T.getDuration(a.timeUnit,a.count);Math.round((t-i.min)/n)/2==Math.round(Math.round((t-i.min)/n)/2)?e.axisFill.__disabled=!0:e.axisFill.__disabled=!1},t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),this.dateFormats.hasKey("millisecond")||this.dateFormats.setKey("millisecond",this.language.translate("_date_millisecond")),this.dateFormats.hasKey("second")||this.dateFormats.setKey("second",this.language.translate("_date_second")),this.dateFormats.hasKey("minute")||this.dateFormats.setKey("minute",this.language.translate("_date_minute")),this.dateFormats.hasKey("hour")||this.dateFormats.setKey("hour",this.language.translate("_date_hour")),this.dateFormats.hasKey("day")||this.dateFormats.setKey("day",this.language.translate("_date_day")),this.dateFormats.hasKey("week")||this.dateFormats.setKey("week",this.language.translate("_date_day")),this.dateFormats.hasKey("month")||this.dateFormats.setKey("month",this.language.translate("_date_month")),this.dateFormats.hasKey("year")||this.dateFormats.setKey("year",this.language.translate("_date_year")),this.periodChangeDateFormats.hasKey("millisecond")||this.periodChangeDateFormats.setKey("millisecond",this.language.translate("_date_millisecond")),this.periodChangeDateFormats.hasKey("second")||this.periodChangeDateFormats.setKey("second",this.language.translate("_date_second")),this.periodChangeDateFormats.hasKey("minute")||this.periodChangeDateFormats.setKey("minute",this.language.translate("_date_minute")),this.periodChangeDateFormats.hasKey("hour")||this.periodChangeDateFormats.setKey("hour",this.language.translate("_date_hour")),this.periodChangeDateFormats.hasKey("day")||this.periodChangeDateFormats.setKey("day",this.language.translate("_date_day")),this.periodChangeDateFormats.hasKey("week")||this.periodChangeDateFormats.setKey("week",this.language.translate("_date_day")),this.periodChangeDateFormats.hasKey("month")||this.periodChangeDateFormats.setKey("month",this.language.translate("_date_month")+" "+this.language.translate("_date_year"))},t.prototype.createDataItem=function(){return new S},t.prototype.createAxisBreak=function(){return new D},t.prototype.validateDataItems=function(){var t=this.start,i=this.end,a=(this.max-this.min)/this.baseDuration;e.prototype.validateDataItems.call(this),this.maxZoomFactor=(this.max-this.min)/this.baseDuration,t+=(i-t)*(1-a/((this.max-this.min)/this.baseDuration)),this.zoom({start:t,end:i},!1,!0)},t.prototype.handleSelectionExtremesChange=function(){},t.prototype.calculateZoom=function(){var t=this;e.prototype.calculateZoom.call(this);var i=this.chooseInterval(0,this.adjustDifference(this._minZoomed,this._maxZoomed),this._gridCount);T.getDuration(i.timeUnit,i.count)<this.baseDuration&&(i=n.a({},this.baseInterval)),this._gridInterval=i,this._gridDate=T.round(new Date(this.min),i.timeUnit,i.count),this._nextGridUnit=T.getNextUnit(i.timeUnit),this._intervalDuration=T.getDuration(i.timeUnit,i.count);var a=Math.ceil(this._difference/this._intervalDuration);a=Math.floor(this.start*a)-3,T.add(this._gridDate,i.timeUnit,a*i.count),m.each(this.series.iterator(),function(e){if(e.baseAxis==t){var i=e.getAxisField(t),a=e.dataItems.findClosestIndex(t._minZoomed,function(e){return e[i]},"left"),n=e.dataItems.findClosestIndex(t._maxZoomed-1,function(e){return e[i]},"left")+1;e.startIndex=a,e.endIndex=n}})},t.prototype.validateData=function(){e.prototype.validateData.call(this),f.isNumber(this.baseInterval.count)||(this.baseInterval.count=1)},Object.defineProperty(t.prototype,"minDifference",{get:function(){var e=this,t=Number.MAX_VALUE;return this.series.each(function(i){t>e._minDifference[i.uid]&&(t=e._minDifference[i.uid])}),t!=Number.MAX_VALUE&&0!=t||(t=T.getDuration("day")),t},enumerable:!0,configurable:!0}),t.prototype.seriesDataChangeUpdate=function(e){this._minDifference[e.uid]=Number.MAX_VALUE},t.prototype.postProcessSeriesDataItems=function(){var e=this;m.each(this.series.iterator(),function(t){m.each(t.dataItems.iterator(),function(t){e.postProcessSeriesDataItem(t)})}),this.addEmptyUnitsBreaks()},t.prototype.postProcessSeriesDataItem=function(e){var t=this,i=this.baseInterval;V.each(e.dates,function(a){var n=e.getDate(a),s=T.round(T.copy(n),i.timeUnit,i.count),r=T.add(T.copy(s),i.timeUnit,i.count);e.setCalculatedValue(a,s.getTime(),"open"),e.setCalculatedValue(a,r.getTime(),"close"),e.component.dataItemsByAxis.getKey(t.uid).setKey(s.getTime().toString(),e)})},t.prototype.addEmptyUnitsBreaks=function(){var e=this;if(this.skipEmptyPeriods&&f.isNumber(this.min)&&f.isNumber(this.max)){var t=this.baseInterval.timeUnit,i=this.baseInterval.count;this.axisBreaks.clear();for(var a=T.round(new Date(this.min),t,i),n=void 0,s=function(){T.add(a,t,i);var s=a.getTime(),o=s.toString();m.contains(r.series.iterator(),function(t){return!!t.dataItemsByAxis.getKey(e.uid).getKey(o)})?n&&(n.endDate=new Date(s-1),n=void 0):n||((n=r.axisBreaks.create()).startDate=new Date(s))},r=this;a.getTime()<this.max-this.baseDuration;)s()}},t.prototype.fixAxisBreaks=function(){var t=this;e.prototype.fixAxisBreaks.call(this);var i=this._axisBreaks;m.each(i.iterator(),function(e){var i=Math.ceil(t._gridCount*(Math.min(t.end,e.endPosition)-Math.max(t.start,e.startPosition))/(t.end-t.start));e.gridInterval=t.chooseInterval(0,e.adjustedEndValue-e.adjustedStartValue,i);var a=T.round(new Date(e.adjustedStartValue),e.gridInterval.timeUnit,e.gridInterval.count);a.getTime()>e.startDate.getTime()&&T.add(a,e.gridInterval.timeUnit,e.gridInterval.count),e.gridDate=a})},t.prototype.getGridDate=function(e,t){var i=this._gridInterval.timeUnit,a=this._gridInterval.count;T.round(e,i);var n=e.getTime(),s=T.copy(e),r=T.add(s,i,t).getTime(),o=this.isInBreak(r);o&&(s=new Date(o.endDate.getTime()),T.round(s,i,a),s.getTime()<o.endDate.getTime()&&T.add(s,i,a),r=s.getTime());var l=this.adjustDifference(n,r);return Math.round(l/T.getDuration(i))<a?this.getGridDate(e,t+a):s},t.prototype.getBreaklessDate=function(e,t,i){var a=new Date(e.endValue);T.round(a,t,i),T.add(a,t,i);var n=a.getTime();return(e=this.isInBreak(n))?this.getBreaklessDate(e,t,i):a},t.prototype.validateAxisElements=function(){var e=this;if(f.isNumber(this.max)&&f.isNumber(this.min)){this.calculateZoom();var t=this._gridDate.getTime(),i=this._gridInterval.timeUnit,a=this._gridInterval.count,n=T.copy(this._gridDate),s=this._dataItemsIterator;this.resetIterators();for(var r=function(){var e=o.getGridDate(new Date(n),a);t=e.getTime();var r=T.copy(e);r=T.add(r,i,a);var l=o.dateFormats.getKey(i);o.markUnitChange&&n&&T.checkChange(e,n,o._nextGridUnit)&&"year"!==i&&(l=o.periodChangeDateFormats.getKey(i));var h=o.dateFormatter.format(e,l),u=s.find(function(e){return e.text===h});o.appendDataItem(u),u.axisBreak=void 0,u.date=e,u.endDate=r,u.text=h,n=e,o.validateDataElement(u)},o=this;t<=this._maxZoomed;)r();var l=this.renderer;m.each(this.axisBreaks.iterator(),function(t){if(t.breakSize>0){var i=t.gridInterval.timeUnit,a=t.gridInterval.count;if(g.getDistance(t.startPoint,t.endPoint)>4*l.minGridDistance)for(var n,r=t.gridDate.getTime(),o=0,h=function(){var l=T.copy(t.gridDate);if(r=T.add(l,i,a*o).getTime(),o++,r>t.adjustedStartValue&&r<t.adjustedEndValue){var h=T.copy(l);h=T.add(h,i,a);var u=e.dateFormats.getKey(i);e.markUnitChange&&n&&T.checkChange(l,n,e._nextGridUnit)&&"year"!==i&&(u=e.periodChangeDateFormats.getKey(i));var d=e.dateFormatter.format(l,u),c=s.find(function(e){return e.text===d});e.appendDataItem(c),c.axisBreak=t,t.dataItems.moveValue(c),c.date=l,c.endDate=h,c.text=d,n=l,e.validateDataElement(c)}};r<=t.adjustedMax;)h()}})}},t.prototype.validateDataElement=function(e){if(f.isNumber(this.max)&&f.isNumber(this.min)){var t=this.renderer,i=e.value,a=e.endValue;f.isNumber(a)||(a=i);var n=this.valueToPosition(i),s=this.valueToPosition(a),r=s;!e.isRange&&this._gridInterval.count>1&&(s=n+(s-n)/this._gridInterval.count),e.position=n;var o=e.tick;o&&!o.disabled&&t.updateTickElement(o,n,s);var l=e.grid;l&&!l.disabled&&t.updateGridElement(l,n,s);var h=e.axisFill;h&&!h.disabled&&(t.updateFillElement(h,n,r),e.isRange||this.fillRule(e));var u=e.mask;u&&t.updateFillElement(u,n,s);var d=e.label;if(d&&!d.disabled){var c=d.location;0==c&&(c=1!=this._gridInterval.count||"week"==this._gridInterval.timeUnit||e.isRange?0:.5),t.updateLabelElement(d,n,s,c)}}},Object.defineProperty(t.prototype,"baseDuration",{get:function(){return T.getDuration(this.baseInterval.timeUnit,this.baseInterval.count)},enumerable:!0,configurable:!0}),t.prototype.adjustMinMax=function(e,t){return{min:e,max:t,step:this.baseDuration}},t.prototype.fixMin=function(e){return T.round(new Date(e),this.baseInterval.timeUnit,this.baseInterval.count).getTime()+this.baseDuration*this.startLocation},t.prototype.fixMax=function(e){return T.round(new Date(e),this.baseInterval.timeUnit,this.baseInterval.count).getTime()+this.baseDuration*this.endLocation},t.prototype.chooseInterval=function(e,t,i){var a=this.gridIntervals,s=a.getIndex(e),r=T.getDuration(s.timeUnit,s.count),o=a.length-1;if(e>=o)return n.a({},a.getIndex(o));var l=Math.ceil(t/r);return t<r&&e>0?n.a({},a.getIndex(e-1)):l<=i?n.a({},a.getIndex(e)):e+1<a.length?this.chooseInterval(e+1,t,i):n.a({},a.getIndex(e))},t.prototype.formatLabel=function(e){return this.dateFormatter.format(e)},t.prototype.dateToPosition=function(e){return this.valueToPosition(e.getTime())},t.prototype.anyToPosition=function(e){return e instanceof Date?this.dateToPosition(e):this.valueToPosition(e)},t.prototype.dateToPoint=function(e){var t=this.dateToPosition(e),i=this.renderer.positionToPoint(t),a=this.renderer.positionToAngle(t);return{x:i.x,y:i.y,angle:a}},t.prototype.anyToPoint=function(e){return e instanceof Date?this.dateToPoint(e):this.valueToPoint(e)},t.prototype.positionToDate=function(e){return new Date(this.positionToValue(e))},t.prototype.getX=function(e,t,i){var a=this.getTimeByLocation(e,t,i);return f.isNumber(a)||(a=this.baseValue),this.renderer.positionToPoint(this.valueToPosition(a)).x},t.prototype.getY=function(e,t,i){var a=this.getTimeByLocation(e,t,i),n=e.getValue("valueX","stack");return f.isNumber(a)||(a=this.baseValue),this.renderer.positionToPoint(this.valueToPosition(a+n)).y},t.prototype.getAngle=function(e,t,i,a){var n=this.getTimeByLocation(e,t,i),s=e.getValue(a,"stack");return f.isNumber(n)||(n=this.baseValue),this.positionToAngle(this.valueToPosition(n+s))},t.prototype.getTimeByLocation=function(e,t,i){if(f.hasValue(t)){f.isNumber(i)||(i=e.workingLocations[t],f.isNumber(i)||(i=0));var a=e.values[t].open,n=e.values[t].close;return f.isNumber(a)&&f.isNumber(n)?a+(n-a)*i:void 0}},t.prototype.processSeriesDataItem=function(e,t){var i,a=e.component,n=e["date"+t];if(n){i=n.getTime();var s,r=e["openDate"+t],o=this._prevSeriesTime;if(r&&(s=r.getTime()),f.isNumber(s)){var l=Math.abs(i-s);this._minDifference[a.uid]>l&&(this._minDifference[a.uid]=l)}var h=i-o;h>0&&this._minDifference[a.uid]>h&&(this._minDifference[a.uid]=h),this._prevSeriesTime=i}},t.prototype.updateAxisBySeries=function(){e.prototype.updateAxisBySeries.call(this);var t=this.chooseInterval(0,this.minDifference,1);this.minDifference>=T.getDuration("day",27)&&"week"==t.timeUnit&&(t.timeUnit="month",t.count=1),this.minDifference>=T.getDuration("hour",23)&&"hour"==t.timeUnit&&(t.timeUnit="day",t.count=1),this._baseIntervalReal=t},Object.defineProperty(t.prototype,"baseInterval",{get:function(){return this._baseInterval?this._baseInterval:this._baseIntervalReal},set:function(e){this._baseInterval=e},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"skipEmptyPeriods",{get:function(){return this.getPropertyValue("skipEmptyPeriods")},set:function(e){if(this.setPropertyValue("skipEmptyPeriods",e)&&this.invalidateData(),e){var t=this.axisBreaks.template;t.startLine.disabled=!0,t.endLine.disabled=!0,t.fillShape.disabled=!0,t.breakSize=0}},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"tooltipDateFormat",{get:function(){return this.getPropertyValue("tooltipDateFormat")},set:function(e){this.setPropertyValue("tooltipDateFormat",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"markUnitChange",{get:function(){return this.getPropertyValue("markUnitChange")},set:function(e){this.setPropertyValue("markUnitChange",e)&&this.invalidateData()},enumerable:!0,configurable:!0}),t.prototype.getTooltipText=function(e){var t,i=this.positionToDate(e);if(i=T.round(i,this.baseInterval.timeUnit,this.baseInterval.count),f.hasValue(this.tooltipDateFormat))t=this.dateFormatter.format(i,this.tooltipDateFormat);else{var a=this.dateFormats.getKey(this.baseInterval.timeUnit);t=a?this.dateFormatter.format(i,a):this.getPositionLabel(e)}return this.adapter.apply("getTooltipText",t)},t.prototype.roundPosition=function(e,t){var i=this.baseInterval,a=i.timeUnit,n=i.count,s=this.positionToDate(e);if(T.round(s,a,n),t>0&&T.add(s,a,t*n),this.isInBreak(s.getTime()))for(;s.getTime()<this.max&&(T.add(s,a,n),this.isInBreak(s.getTime())););return this.dateToPosition(s)},t.prototype.getCellStartPosition=function(e){return this.roundPosition(e,0)},t.prototype.getCellEndPosition=function(e){return this.roundPosition(e,1)},t.prototype.getSeriesDataItem=function(e,t,i){var a=this.positionToValue(t),n=T.round(new Date(a),this.baseInterval.timeUnit,this.baseInterval.count),s=e.dataItemsByAxis.getKey(this.uid).getKey(n.getTime().toString());if(!s&&i){for(var r=0,o=void 0,l=new Date(n.getTime());l.getTime()>this.minZoomed&&(l=T.add(l,this.baseInterval.timeUnit,-this.baseInterval.count),!(o=e.dataItemsByAxis.getKey(this.uid).getKey(l.getTime().toString())))&&!(++r>5e3););for(var h=0,u=void 0,d=new Date(n.getTime());d.getTime()<this.maxZoomed&&(d=T.add(d,this.baseInterval.timeUnit,this.baseInterval.count),!(u=e.dataItemsByAxis.getKey(this.uid).getKey(d.getTime().toString())))&&!(++h>5e3););if(o&&!u)return o;if(!o&&u)return u;if(o&&u)return r<h?o:u}return s},t.prototype.getPositionLabel=function(e){var t=this.positionToDate(e);return this.dateFormatter.format(t,this.getCurrentLabelFormat())},t.prototype.getCurrentLabelFormat=function(){return this.dateFormats.getKey(this._gridInterval?this._gridInterval.timeUnit:"day")},t.prototype.initRenderer=function(){e.prototype.initRenderer.call(this);var t=this.renderer;t&&(t.ticks.template.location=0,t.grid.template.location=0,t.labels.template.location=0,t.baseGrid.disabled=!0)},Object.defineProperty(t.prototype,"basePoint",{get:function(){return{x:0,y:0}},enumerable:!0,configurable:!0}),t.prototype.zoomToDates=function(e,t,i,a){e=this.dateFormatter.parse(e),t=this.dateFormatter.parse(t),this.zoomToValues(e.getTime(),t.getTime(),i,a)},t.prototype.asIs=function(t){return"baseInterval"==t||e.prototype.asIs.call(this,t)},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.dateFormats=t.dateFormats,this.periodChangeDateFormats=t.periodChangeDateFormats,t._baseInterval&&(this.baseInterval=t.baseInterval)},t.prototype.showTooltipAtPosition=function(t,i){var a=this;if(i||(t=this.toAxisPosition(t)),this.snapTooltip){var n,s=T.round(this.positionToDate(t),this.baseInterval.timeUnit,1).getTime();this.series.each(function(e){var i=a.getSeriesDataItem(e,t,!0);if(i){var r=void 0;e.xAxis==a&&(r=i.dateX),e.yAxis==a&&(r=i.dateY),n?Math.abs(n.getTime()-s)>Math.abs(r.getTime()-s)&&(n=r):n=r}}),n&&(n=T.round(new Date(n.getTime()),this.baseInterval.timeUnit,this.baseInterval.count),n=new Date(n.getTime()+this.baseDuration/2),t=this.dateToPosition(n))}e.prototype.showTooltipAtPosition.call(this,t,!0);var r=this.toGlobalPosition(t);this.series.each(function(e){e.xAxis==a&&e.showTooltipAtPosition(r,void 0),e.yAxis==a&&e.showTooltipAtPosition(void 0,r)})},Object.defineProperty(t.prototype,"snapTooltip",{get:function(){return this.getPropertyValue("snapTooltip")},set:function(e){this.setPropertyValue("snapTooltip",e)},enumerable:!0,configurable:!0}),t}(C.a);d.b.registeredClasses.DateAxis=R,d.b.registeredClasses.DateAxisDataItem=S;var F=i("v9UT"),O=i("hJ5i"),k=i("hGwe"),w=function(e){function t(){var t=e.call(this)||this;return t.className="XYSeriesDataItem",t.values.valueX={stack:0},t.values.valueY={stack:0},t.values.openValueX={},t.values.openValueY={},t.values.dateX={},t.values.dateY={},t.values.openDateX={},t.values.openDateY={},t.setLocation("dateX",.5,0),t.setLocation("dateY",.5,0),t.setLocation("categoryX",.5,0),t.setLocation("categoryY",.5,0),t.applyTheme(),t}return n.c(t,e),Object.defineProperty(t.prototype,"valueX",{get:function(){return this.values.valueX.value},set:function(e){this.setValue("valueX",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"valueY",{get:function(){return this.values.valueY.value},set:function(e){this.setValue("valueY",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"dateX",{get:function(){return this.getDate("dateX")},set:function(e){this.setDate("dateX",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"dateY",{get:function(){return this.getDate("dateY")},set:function(e){this.setDate("dateY",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"categoryX",{get:function(){return this.categories.categoryX},set:function(e){this.setCategory("categoryX",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"categoryY",{get:function(){return this.categories.categoryY},set:function(e){this.setCategory("categoryY",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"openValueX",{get:function(){return this.values.openValueX.value},set:function(e){this.setValue("openValueX",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"openValueY",{get:function(){return this.values.openValueY.value},set:function(e){this.setValue("openValueY",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"openDateX",{get:function(){return this.getDate("openDateX")},set:function(e){this.setDate("openDateX",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"openDateY",{get:function(){return this.getDate("openDateY")},set:function(e){this.setDate("openDateY",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"openCategoryX",{get:function(){return this.categories.openCategoryX},set:function(e){this.setProperty("openCategoryX",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"openCategoryY",{get:function(){return this.categories.openCategoryY},set:function(e){this.setProperty("openCategoryY",e)},enumerable:!0,configurable:!0}),t.prototype.getMin=function(e,t,i){var a,n=this;return f.isNumber(i)||(i=0),O.each(e,function(e){var s;s=t?n.getWorkingValue(e):n.getValue(e),((s+=i)<a||!f.isNumber(a))&&(a=s)}),a},t.prototype.getMax=function(e,t,i){var a,n=this;return f.isNumber(i)||(i=0),O.each(e,function(e){var s;s=t?n.getWorkingValue(e):n.getValue(e),((s+=i)>a||!f.isNumber(a))&&(a=s)}),a},t}(A.b),L=function(e){function t(){var t=e.call(this)||this;return t._xAxis=new _.d,t._yAxis=new _.d,t._xValueFields=[],t._yValueFields=[],t.className="XYSeries",t.isMeasured=!1,t.cursorTooltipEnabled=!0,t.mainContainer.mask=new P.a,t.mainContainer.mask.setElement(t.paper.add("path")),t.stacked=!1,t.snapTooltip=!1,t.tooltip.pointerOrientation="horizontal",t.tooltip.events.on("hidden",function(){t.returnBulletDefaultState()},void 0,!1),t._disposers.push(t._xAxis),t._disposers.push(t._yAxis),t.applyTheme(),t}return n.c(t,e),t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),f.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("X/Y Series"))},t.prototype.createDataItem=function(){return new w},t.prototype.dataChangeUpdate=function(){this._tmin.clear(),this._tmax.clear(),this._smin.clear(),this._smax.clear(),this.xAxis&&this.xAxis.seriesDataChangeUpdate(this),this.yAxis&&this.yAxis.seriesDataChangeUpdate(this)},t.prototype.validateData=function(){if(this.defineFields(),this.data.length>0&&this.dataChangeUpdate(),e.prototype.validateData.call(this),this.updateItemReaderText(),!f.hasValue(this.dataFields[this._xField])||!f.hasValue(this.dataFields[this._yField]))throw Error('Data fields for series "'+(this.name?this.name:this.uid)+'" are not properly defined.')},t.prototype.processDataItem=function(t,i){try{e.prototype.processDataItem.call(this,t,i),t.events.disable(),this.xAxis.processSeriesDataItem(t,"X"),this.yAxis.processSeriesDataItem(t,"Y"),t.events.enable(),this.setInitialWorkingValues(t)}catch(e){this._chart.raiseCriticalError(e)}},t.prototype.setInitialWorkingValues=function(e){},t.prototype.disposeData=function(){if(e.prototype.disposeData.call(this),this.xAxis){var t=this.dataItemsByAxis.getKey(this.xAxis.uid);t&&t.clear()}if(this.yAxis){var i=this.dataItemsByAxis.getKey(this.yAxis.uid);i&&i.clear()}},t.prototype.defineFields=function(){var e=this.xAxis,t=this.yAxis,i=e.axisFieldName,a=i+"X",n="open"+F.capitalize(i)+"X",s=t.axisFieldName,r=s+"Y",o="open"+F.capitalize(s)+"Y";this._xField=a,this._yField=r,this.dataFields[n]&&(this._xOpenField=n),this.dataFields[o]&&(this._yOpenField=o),this.dataFields[o]||this.baseAxis!=this.yAxis||(this._yOpenField=r),this.dataFields[n]||this.baseAxis!=this.xAxis||(this._xOpenField=a),this.stacked&&this.baseAxis==this.xAxis&&(this._xOpenField=a),this.stacked&&this.baseAxis==this.yAxis&&(this._yOpenField=r),this.xAxis instanceof b&&this.yAxis instanceof b&&(this._yOpenField||(this._yOpenField=r)),this._xValueFields=[],this._yValueFields=[],this.addValueField(this.xAxis,this._xValueFields,this._xField),this.addValueField(this.xAxis,this._xValueFields,this._xOpenField),this.addValueField(this.yAxis,this._yValueFields,this._yField),this.addValueField(this.yAxis,this._yValueFields,this._yOpenField)},t.prototype.addValueField=function(e,t,i){e instanceof C.a&&f.hasValue(this.dataFields[i])&&-1==t.indexOf(i)&&t.push(i)},t.prototype.setCategoryAxisField=function(e,t){f.hasValue(this.dataFields[e])||(this.dataFields[e]=t.dataFields.category)},t.prototype.setDateAxisField=function(e,t){f.hasValue(this.dataFields[e])||(this.dataFields[e]=t.dataFields.date)},t.prototype.afterDraw=function(){e.prototype.afterDraw.call(this),this.createMask()},t.prototype.createMask=function(){if(this.mainContainer.mask){var e=this.getMaskPath();m.each(this.axisRanges.iterator(),function(t){t.axisFill.fillPath&&(t.axisFill.validate(),e+=t.axisFill.fillPath)}),this.mainContainer.mask.path=e}},t.prototype.getMaskPath=function(){return k.rectToPath({x:0,y:0,width:this.xAxis.axisLength,height:this.yAxis.axisLength})},t.prototype.getAxisField=function(e){return e==this.xAxis?this.xField:e==this.yAxis?this.yField:void 0},t.prototype.validateDataItems=function(){this.xAxis.updateAxisBySeries(),this.yAxis.updateAxisBySeries(),e.prototype.validateDataItems.call(this),this.xAxis.postProcessSeriesDataItems(),this.yAxis.postProcessSeriesDataItems()},t.prototype.validateDataRange=function(){this.xAxis.dataRangeInvalid&&this.xAxis.validateDataRange(),this.yAxis.dataRangeInvalid&&this.yAxis.validateDataRange(),e.prototype.validateDataRange.call(this)},t.prototype.validate=function(){this.xAxis.invalid&&this.xAxis.validate(),this.yAxis.invalid&&this.yAxis.validate(),this._showBullets=!0;var t=this.minBulletDistance;f.isNumber(t)&&this.baseAxis.axisLength/(this.endIndex-this.startIndex)<t&&(this._showBullets=!1),e.prototype.validate.call(this)},Object.defineProperty(t.prototype,"xAxis",{get:function(){if(this.chart){if(!this._xAxis.get()){var e=this.chart.xAxes.getIndex(0);if(!e)throw Error("There are no X axes on chart.");this.xAxis=e}return this._xAxis.get()}},set:function(e){var t=this._xAxis.get();t!=e&&(t&&(this.dataItemsByAxis.removeKey(t.uid),this._xAxis.dispose(),t.series.removeValue(this)),this._xAxis.set(e,e.registerSeries(this)),this.dataItemsByAxis.setKey(e.uid,new c.a),this.invalidateData())},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"yAxis",{get:function(){if(this.chart){if(!this._yAxis.get()){var e=this.chart.yAxes.getIndex(0);if(!e)throw Error("There are no Y axes on chart.");this.yAxis=e}return this._yAxis.get()}},set:function(e){var t=this._yAxis.get();t!=e&&(t&&(this.dataItemsByAxis.removeKey(t.uid),this._yAxis.dispose(),t.series.removeValue(this)),this._yAxis.set(e,e.registerSeries(this)),this.dataItemsByAxis.setKey(e.uid,new c.a),this.invalidateData())},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"baseAxis",{get:function(){return this._baseAxis||(this.yAxis instanceof R&&(this._baseAxis=this.yAxis),this.xAxis instanceof R&&(this._baseAxis=this.xAxis),this.yAxis instanceof b&&(this._baseAxis=this.yAxis),this.xAxis instanceof b&&(this._baseAxis=this.xAxis),this._baseAxis||(this._baseAxis=this.xAxis)),this._baseAxis},set:function(e){this._baseAxis!=e&&(this._baseAxis=e,this.invalidate())},enumerable:!0,configurable:!0}),t.prototype.processValues=function(t){e.prototype.processValues.call(this,t);for(var i=this.dataItems,a=1/0,n=-1/0,s=1/0,r=-1/0,o=this.startIndex,l=this.endIndex,h=o;h<l;h++){var u=i.getIndex(h);this.getStackValue(u,t);var d=u.getValue("valueX","stack"),c=u.getValue("valueY","stack");a=g.min(u.getMin(this._xValueFields,t,d),a),s=g.min(u.getMin(this._yValueFields,t,c),s),n=g.max(u.getMax(this._xValueFields,t,d),n),r=g.max(u.getMax(this._yValueFields,t,c),r),this.stacked&&(this.baseAxis==this.xAxis&&(s=g.min(s,c)),this.baseAxis==this.yAxis&&(a=g.min(a,d)))}this.xAxis.processSeriesDataItems(),this.yAxis.processSeriesDataItems();var p=this.xAxis.uid,y=this.yAxis.uid;t||this._tmin.getKey(p)==a&&this._tmax.getKey(p)==n&&this._tmin.getKey(y)==s&&this._tmax.getKey(y)==r||(this._tmin.setKey(p,a),this._tmax.setKey(p,n),this._tmin.setKey(y,s),this._tmax.setKey(y,r),this.stackedSeries&&this.stackedSeries.processValues(!1),this.dispatchImmediately("extremeschanged")),this._smin.getKey(p)==a&&this._smax.getKey(p)==n&&this._smin.getKey(y)==s&&this._smax.getKey(y)==r||(this._smin.setKey(p,a),this._smax.setKey(p,n),this._smin.setKey(y,s),this._smax.setKey(y,r),this.appeared&&this.dispatchImmediately("selectionextremeschanged"))},t.prototype.hideTooltip=function(){e.prototype.hideTooltip.call(this),this.returnBulletDefaultState(),this._prevTooltipDataItem=void 0},t.prototype.showTooltipAtPosition=function(e,t){if(this.cursorTooltipEnabled){var i=void 0;if(this.visible&&!this.isHiding&&!this.isShowing){var a=this._xAxis.get(),s=this._yAxis.get();if(a==this.baseAxis&&(i=a.getSeriesDataItem(this,a.toAxisPosition(e),this.snapTooltip)),s==this.baseAxis&&(i=s.getSeriesDataItem(this,s.toAxisPosition(t),this.snapTooltip)),this.returnBulletDefaultState(i),i&&i.visible){this.updateLegendValue(i),this.tooltipDataItem=i;var r=this.tooltipXField,o=this.tooltipYField;if(f.hasValue(i[r])&&f.hasValue(i[o])){var l=this.getPoint(i,r,o,i.locations[r],i.locations[o]);if(l){this.tooltipX=l.x,this.tooltipY=l.y,this._prevTooltipDataItem!=i&&(this.dispatchImmediately("tooltipshownat",{type:"tooltipshownat",target:this,dataItem:i}),this._prevTooltipDataItem=i);try{for(var h=n.g(i.bullets),u=h.next();!u.done;u=h.next()){u.value[1].isHover=!0}}catch(e){d={error:e}}finally{try{u&&!u.done&&(c=h.return)&&c.call(h)}finally{if(d)throw d.error}}return this.showTooltip()?F.spritePointToSvg({x:l.x,y:l.y},this):void 0}}}if(!this.tooltipText)return}this.hideTooltip()}var d,c},t.prototype.returnBulletDefaultState=function(e){if(this._prevTooltipDataItem&&this._prevTooltipDataItem!=e)try{for(var t=n.g(this._prevTooltipDataItem.bullets),i=t.next();!i.done;i=t.next()){var a=i.value[1];a.isDisposed()?this._prevTooltipDataItem=void 0:a.isHover=!1}}catch(e){s={error:e}}finally{try{i&&!i.done&&(r=t.return)&&r.call(t)}finally{if(s)throw s.error}}var s,r},t.prototype.positionBullet=function(t){e.prototype.positionBullet.call(this,t);var i=t.dataItem,a=t.xField;f.hasValue(a)||(a=this.xField);var n=t.yField;if(f.hasValue(n)||(n=this.yField),this.xAxis instanceof C.a&&!i.hasValue([a])||this.yAxis instanceof C.a&&!i.hasValue([n]))t.visible=!1;else{var s=this.getBulletLocationX(t,a),r=this.getBulletLocationY(t,n),o=this.getPoint(i,a,n,s,r);if(o){var l=o.x,h=o.y;if(f.isNumber(t.locationX)&&this.xOpenField!=this.xField)l-=(l-this.xAxis.getX(i,this.xOpenField))*t.locationX;if(f.isNumber(t.locationY)&&this.yOpenField!=this.yField)h-=(h-this.yAxis.getY(i,this.yOpenField))*t.locationY;t.moveTo({x:l,y:h})}else t.visible=!1}},t.prototype.getBulletLocationX=function(e,t){var i=e.locationX,a=e.dataItem;return f.isNumber(i)||(i=a.workingLocations[t]),i},t.prototype.getBulletLocationY=function(e,t){var i=e.locationY,a=e.dataItem;return f.isNumber(i)||(i=a.workingLocations[t]),i},Object.defineProperty(t.prototype,"stacked",{get:function(){return this.getPropertyValue("stacked")},set:function(e){this.setPropertyValue("stacked",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"snapTooltip",{get:function(){return this.getPropertyValue("snapTooltip")},set:function(e){this.setPropertyValue("snapTooltip",e)},enumerable:!0,configurable:!0}),t.prototype.show=function(t){var i,a=this;this.xAxis instanceof C.a&&this.xAxis!=this.baseAxis&&(i=this._xValueFields),this.yAxis instanceof C.a&&this.yAxis!=this.baseAxis&&(i=this._yValueFields);var n,s=this.startIndex,r=this.endIndex,o=0,l=this.defaultState.transitionDuration;f.isNumber(t)&&(l=t),m.each(m.indexed(this.dataItems.iterator()),function(e){var t=e[0],h=e[1];a.sequencedInterpolation&&l>0&&(o=a.sequencedInterpolationDelay*t+l*(t-s)/(r-s)),n=h.show(l,o,i)});var h=e.prototype.show.call(this,t);return n&&!n.isFinished()&&(h=n),h},t.prototype.hide=function(t){var i,a,n=this,s=this.xAxis;s instanceof C.a&&s!=this.baseAxis&&(i=this._xValueFields,a=this.stacked||s.minZoomed<0&&s.maxZoomed>0||this.stackedSeries?0:s.min);var r=this.yAxis;r instanceof C.a&&r!=this.baseAxis&&(i=this._yValueFields,a=this.stacked||r.minZoomed<0&&r.maxZoomed>0||this.stackedSeries?0:r.min);var o=this.startIndex,l=this.endIndex,h=this.hiddenState.transitionDuration;f.isNumber(t)&&(h=t);var u,d=0;m.each(m.indexed(this.dataItems.iterator()),function(e){var t=e[0],s=e[1];0==h?s.hide(0,0,a,i):(n.sequencedInterpolation&&h>0&&(d=n.sequencedInterpolationDelay*t+h*(t-o)/(l-o)),u=s.hide(h,d,a,i))});var c=e.prototype.hide.call(this,h);return c&&!c.isFinished()&&c.delay(d),u&&!u.isFinished()&&(c=u),this.validateDataElements(),c},t.prototype.handleDataItemWorkingValueChange=function(t,i){e.prototype.handleDataItemWorkingValueChange.call(this,t,i);var a=this.baseAxis.series;m.each(a.iterator(),function(e){e.stacked&&e.invalidateProcessedData()})},t.prototype.getStackValue=function(e,t){var i=this;if(this.stacked){var a,n=this.chart,s=n.series.indexOf(this);this.xAxis!=this.baseAxis&&this.xAxis instanceof C.a&&(a=this.xField),this.yAxis!=this.baseAxis&&this.yAxis instanceof C.a&&(a=this.yField),e.setCalculatedValue(a,0,"stack"),m.eachContinue(n.series.range(0,s).backwards().iterator(),function(n){if(n.xAxis==i.xAxis&&n.yAxis==i.yAxis){n.stackedSeries=i;var s=n.dataItems.getIndex(e.index);if(s&&s.hasValue(i._xValueFields)&&s.hasValue(i._yValueFields)){var r=e.getValue(a),o=void 0;if(o=t?s.getWorkingValue(a)+s.getValue(a,"stack"):s.getValue(a)+s.getValue(a,"stack"),r>=0&&o>=0||r<0&&o<0)return e.setCalculatedValue(a,o,"stack"),!1}else if(!n.stacked)return!1}return!0})}},Object.defineProperty(t.prototype,"xField",{get:function(){return this._xField},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"yField",{get:function(){return this._yField},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"xOpenField",{get:function(){return this._xOpenField},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"yOpenField",{get:function(){return this._yOpenField},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"tooltipXField",{get:function(){return this._tooltipXField?this._tooltipXField:this._xField},set:function(e){this._tooltipXField=e},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"tooltipYField",{get:function(){return this._tooltipYField?this._tooltipYField:this._yField},set:function(e){this._tooltipYField=e},enumerable:!0,configurable:!0}),t.prototype.min=function(e){return this._tmin.getKey(e.uid)},t.prototype.max=function(e){return this._tmax.getKey(e.uid)},t.prototype.selectionMin=function(e){var t=this._smin.getKey(e.uid);return f.isNumber(t)||(t=this.min(e)),t},t.prototype.selectionMax=function(e){var t=this._smax.getKey(e.uid);return f.isNumber(t)||(t=this.max(e)),t},t.prototype.processConfig=function(t){if(t){if(f.hasValue(t.xAxis)&&f.isString(t.xAxis)&&this.map.hasKey(t.xAxis)&&(t.xAxis=this.map.getKey(t.xAxis)),f.hasValue(t.yAxis)&&f.isString(t.yAxis)&&this.map.hasKey(t.yAxis)&&(t.yAxis=this.map.getKey(t.yAxis)),f.hasValue(t.axisRanges)&&f.isArray(t.axisRanges))for(var i=0,a=t.axisRanges.length;i<a;i++){var n=t.axisRanges[i];f.hasValue(n.type)||(n.type="AxisDataItem"),f.hasValue(n.axis)&&f.isString(n.axis)&&this.map.hasKey(n.axis)?n.component=this.map.getKey(n.axis):f.hasValue(n.component)&&f.isString(n.component)&&this.map.hasKey(n.component)&&(n.component=this.map.getKey(n.component))}if(!f.hasValue(t.dataFields)||!f.isObject(t.dataFields))throw Error("`dataFields` is not set for series ["+this.name+"]")}e.prototype.processConfig.call(this,t)},t.prototype.getPoint=function(e,t,i,a,n,s,r){var o=this.xAxis.getX(e,t,a),l=this.yAxis.getY(e,i,n);return{x:o=g.fitToRange(o,-2e4,2e4),y:l=g.fitToRange(l,-2e4,2e4)}},t.prototype.updateItemReaderText=function(){var e="";V.each(this.dataFields,function(t,i){e+="{"+t+"} "}),this.itemReaderText=e},Object.defineProperty(t.prototype,"cursorTooltipEnabled",{get:function(){return this.getPropertyValue("cursorTooltipEnabled")},set:function(e){this.setPropertyValue("cursorTooltipEnabled",e)},enumerable:!0,configurable:!0}),t}(A.a);d.b.registeredClasses.XYSeries=L,d.b.registeredClasses.XYSeriesDataItem=w;var X=i("zhwk"),Y=i("tjMS"),j=i("qCRI"),N=function(e){function t(){var t=e.call(this)||this;t.point={x:0,y:0},t._stick="none",t.className="Cursor",t.width=Object(Y.c)(100),t.height=Object(Y.c)(100),t.shouldClone=!1,t.hide(0),t.trackable=!0,t.clickable=!0,t.isMeasured=!1;var i=Object(X.b)();return t._disposers.push(i.body.events.on("down",t.handleCursorDown,t)),t._disposers.push(i.body.events.on("up",t.handleCursorUp,t)),t._disposers.push(i.body.events.on("track",t.handleCursorMove,t)),t.applyTheme(),t}return n.c(t,e),t.prototype.handleCursorMove=function(e){if(this.interactionsEnabled){if(("zoom"==this._generalBehavior||"pan"==this._generalBehavior)&&this.downPoint||Object(X.b)().isLocalElement(e.pointer,this.paper.svg,this.uid)){var t=F.documentPointToSprite(e.pointer.point,this);return"hard"==this._stick&&this._stickPoint&&(t=this._stickPoint),"soft"==this._stick&&this._stickPoint&&(this.fitsToBounds(t)||(t=this._stickPoint)),this.triggerMove(t),t}this.isHidden&&this.isHiding||this.hide()}},t.prototype.hideReal=function(t){if("hard"!=this._stick&&"soft"!=this._stick||!this._stickPoint)return e.prototype.hideReal.call(this,t)},t.prototype.triggerMove=function(e,t){e.x=g.round(e.x,1),e.y=g.round(e.y,1),t&&(this._stick=t),"hard"!=t&&"soft"!=t||(this._stickPoint=e),this.triggerMoveReal(e)},t.prototype.triggerMoveReal=function(e){this.point.x==e.x&&this.point.y==e.y||(this.point=e,this.invalidatePosition(),this.fitsToBounds(e)?this.show(0):this.downPoint||this.hide(0),this.visible&&(this.getPositions(),this.dispatch("cursorpositionchanged")))},t.prototype.triggerDown=function(e){this.triggerDownReal(e)},t.prototype.triggerDownReal=function(e){switch(this._generalBehavior){case"zoom":this.dispatchImmediately("zoomstarted");break;case"select":this.dispatchImmediately("selectstarted");break;case"pan":this.dispatchImmediately("panstarted"),Object(X.b)().setGlobalStyle(j.a.grabbing)}},t.prototype.triggerUp=function(e){this.triggerUpReal(e)},t.prototype.triggerUpReal=function(e){this.updatePoint(this.upPoint);var t=Object(X.b)();if(g.getDistance(this.upPoint,this.downPoint)>t.getHitOption(this.interactions,"hitTolerance"))switch(this._generalBehavior){case"zoom":this.dispatchImmediately("zoomended");break;case"select":this.dispatchImmediately("selectended");break;case"pan":this.dispatchImmediately("panended"),t.setGlobalStyle(j.a.default)}else this.dispatchImmediately("behaviorcanceled"),t.setGlobalStyle(j.a.default);this.downPoint=void 0,this.updateSelection()},t.prototype.updateSelection=function(){},t.prototype.getPositions=function(){this.xPosition=this.point.x/this.innerWidth,this.yPosition=1-this.point.y/this.innerHeight},t.prototype.handleCursorDown=function(e){if(this.interactionsEnabled&&Object(X.b)().isLocalElement(e.pointer,this.paper.svg,this.uid)){var t=F.documentPointToSprite(e.pointer.point,this);e.event.cancelable&&this.fitsToBounds(t)&&e.event.preventDefault(),this.triggerMove(t),this.triggerDown(t)}},t.prototype.updatePoint=function(e){},t.prototype.handleCursorUp=function(e){if(this.interactionsEnabled&&(("zoom"==this._generalBehavior||"pan"==this._generalBehavior)&&this.downPoint||Object(X.b)().isLocalElement(e.pointer,this.paper.svg,this.uid))){var t=F.documentPointToSprite(e.pointer.point,this);this.triggerMove(t),this.triggerUp(t)}},Object.defineProperty(t.prototype,"chart",{get:function(){return this._chart},set:function(e){this._chart=e,f.hasValue(this._chart.plotContainer)&&Object(X.b)().lockElement(this._chart.plotContainer.interactions)},enumerable:!0,configurable:!0}),t}(r.a);d.b.registeredClasses.Cursor=N;var M=i("8ZqG"),B=i("MIZb"),W=function(e){function t(){var t=e.call(this)||this;t._lineX=new _.d,t._lineY=new _.d,t._xAxis=new _.d,t._yAxis=new _.d,t.className="XYCursor",t.behavior="zoomX",t.maxPanOut=.1;var i=new B.a,a=t.createChild(P.a);a.shouldClone=!1,a.fillOpacity=.2,a.fill=i.getFor("alternativeBackground"),a.isMeasured=!1,a.interactionsEnabled=!1,t.selection=a,t._disposers.push(t.selection);var n=t.createChild(P.a);n.shouldClone=!1,n.stroke=i.getFor("grid"),n.fill=Object(M.c)(),n.strokeDasharray="3,3",n.isMeasured=!1,n.strokeOpacity=.4,n.interactionsEnabled=!1,n.y=0,t.lineX=n,t._disposers.push(t.lineX);var s=t.createChild(P.a);return s.shouldClone=!1,s.stroke=i.getFor("grid"),s.fill=Object(M.c)(),s.strokeDasharray="3,3",s.isMeasured=!1,s.strokeOpacity=.4,s.interactionsEnabled=!1,s.x=0,t.lineY=s,t._disposers.push(t.lineY),t.events.on("sizechanged",t.updateSize,t,!1),t._disposers.push(t._lineX),t._disposers.push(t._lineY),t._disposers.push(t._xAxis),t._disposers.push(t._yAxis),t.mask=t,t.applyTheme(),t}return n.c(t,e),t.prototype.updateSize=function(){this.lineX&&(this.lineX.path=k.moveTo({x:0,y:0})+k.lineTo({x:0,y:this.innerHeight})),this.lineY&&(this.lineY.path=k.moveTo({x:0,y:0})+k.lineTo({x:this.innerWidth,y:0}))},t.prototype.updateSelection=function(){if(this._usesSelection){var e=this.downPoint;if(e){var t=this.point;this.lineX&&(t.x=this.lineX.pixelX),this.lineY&&(t.y=this.lineY.pixelY);var i=this.selection,a=Math.min(t.x,e.x),n=Math.min(t.y,e.y),s=g.round(Math.abs(e.x-t.x),this._positionPrecision),r=g.round(Math.abs(e.y-t.y),this._positionPrecision);switch(this.behavior){case"zoomX":n=0,r=this.pixelHeight;break;case"zoomY":a=0,s=this.pixelWidth;break;case"selectX":n=0,r=this.pixelHeight;break;case"selectY":a=0,s=this.pixelWidth}i.x=a,i.y=n,i.path=k.rectangle(s,r),i.validatePosition()}else this.selection.hide()}},t.prototype.fixPoint=function(e){return e.x=Math.max(0,e.x),e.y=Math.max(0,e.y),e.x=Math.min(this.pixelWidth,e.x),e.y=Math.min(this.pixelHeight,e.y),e},t.prototype.triggerMoveReal=function(t){e.prototype.triggerMoveReal.call(this,t),this.snapToSeries&&!this.snapToSeries.isHidden||this.updateLinePositions(t),this.downPoint&&g.getDistance(this.downPoint,t)>3&&"pan"==this._generalBehavior&&(this.getPanningRanges(),this.dispatch("panning"))},t.prototype.updateLinePositions=function(e){e=this.fixPoint(this.point),this.lineX&&this.lineX.visible&&!this.xAxis&&(this.lineX.x=e.x),this.lineY&&this.lineY.visible&&!this.yAxis&&(this.lineY.y=e.y),this.updateSelection()},t.prototype.triggerDownReal=function(t){if(this.visible&&!this.isHiding)if(this.fitsToBounds(t)){this.downPoint={x:t.x,y:t.y},this.updatePoint(t),this.point.x=this.downPoint.x,this.point.y=this.downPoint.y;var i=this.selection,a=this.downPoint.x,n=this.downPoint.y;this._usesSelection&&(i.x=a,i.y=n,i.path="",i.show()),e.prototype.triggerDownReal.call(this,t)}else this.downPoint=void 0;else this.downPoint=void 0},t.prototype.updatePoint=function(e){this.lineX&&(e.x=this.lineX.pixelX),this.lineY&&(e.y=this.lineY.pixelY)},t.prototype.triggerUpReal=function(t){this.downPoint&&(this.upPoint=t,this.updatePoint(this.upPoint),this.getRanges(),"selectX"==this.behavior||"selectY"==this.behavior||"selectXY"==this.behavior||this.selection.hide(),e.prototype.triggerUpReal.call(this,t)),this.downPoint=void 0,this.updateSelection()},t.prototype.getPanningRanges=function(){var e=g.round(this.downPoint.x/this.innerWidth,5),t=g.round(this.downPoint.y/this.innerHeight,5),i=e-g.round(this.point.x/this.innerWidth,5),a=-t+g.round(this.point.y/this.innerHeight,5);this.xRange={start:i,end:1+i},this.yRange={start:a,end:1+a},"panX"==this.behavior&&(this.yRange.start=0,this.yRange.end=1),"panY"==this.behavior&&(this.xRange.start=0,this.xRange.end=1)},t.prototype.getRanges=function(){this.lineX&&(this.upPoint.x=this.lineX.pixelX),this.lineY&&(this.upPoint.y=this.lineY.pixelY),this.selection;var e=g.round(this.downPoint.x/this.innerWidth,5),t=g.round(this.upPoint.x/this.innerWidth,5),i=g.round(this.downPoint.y/this.innerHeight,5),a=g.round(this.upPoint.y/this.innerHeight,5);this.xRange={start:g.min(e,t),end:g.max(e,t)},this.yRange={start:g.min(i,a),end:g.max(i,a)}},Object.defineProperty(t.prototype,"behavior",{get:function(){return this.getPropertyValue("behavior")},set:function(e){this.setPropertyValue("behavior",e,!0),this._usesSelection=!1,-1!=e.indexOf("zoom")&&(this._generalBehavior="zoom",this._usesSelection=!0),-1!=e.indexOf("select")&&(this._generalBehavior="select",this._usesSelection=!0),-1!=e.indexOf("pan")&&(this._generalBehavior="pan",this._usesSelection=!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"fullWidthLineX",{get:function(){return this.getPropertyValue("fullWidthLineX")},set:function(e){this.setPropertyValue("fullWidthLineX",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"fullWidthLineY",{get:function(){return this.getPropertyValue("fullWidthLineY")},set:function(e){this.setPropertyValue("fullWidthLineY",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"maxPanOut",{get:function(){return this.getPropertyValue("maxPanOut")},set:function(e){this.setPropertyValue("maxPanOut",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"xAxis",{get:function(){return this._xAxis.get()},set:function(e){if(this._xAxis.get()!=e){e.chart;this._xAxis.set(e,new _.c([e.tooltip.events.on("positionchanged",this.handleXTooltipPosition,this,!1)]))}},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"yAxis",{get:function(){return this._yAxis.get()},set:function(e){if(this._yAxis.get()!=e){e.chart;this._yAxis.set(e,new _.c([e.tooltip.events.on("positionchanged",this.handleYTooltipPosition,this,!1)]))}},enumerable:!0,configurable:!0}),t.prototype.handleXTooltipPosition=function(e){var t=this.xAxis.tooltip,i=F.svgPointToSprite({x:t.pixelX,y:t.pixelY},this),a=i.x;if(this.lineX&&(this.lineX.x=a,this.fitsToBounds(i)||this.hide()),this.xAxis&&this.fullWidthLineX){var n=this.xAxis.currentItemStartPoint,s=this.xAxis.currentItemEndPoint;if(n&&s){this.lineX.x=a;var r=s.x-n.x;this.lineX.path=k.rectangle(r,this.innerHeight,-r/2)}}},t.prototype.handleYTooltipPosition=function(e){var t=this.yAxis.tooltip,i=F.svgPointToSprite({x:t.pixelX,y:t.pixelY},this),a=i.y;if(this.lineY&&(this.lineY.y=a,this.fitsToBounds(i)||this.hide()),this.yAxis&&this.fullWidthLineY){var n=this.yAxis.currentItemStartPoint,s=this.yAxis.currentItemEndPoint;if(n&&s){this.lineY.y=a;var r=s.y-n.y;this.lineY.path=k.rectangle(this.innerWidth,r,0,-r/2)}}},Object.defineProperty(t.prototype,"lineX",{get:function(){return this._lineX.get()},set:function(e){e?(e.setElement(this.paper.add("path")),this._lineX.set(e,e.events.on("positionchanged",this.updateSelection,this,!1)),e.interactionsEnabled=!1,e.parent=this):this._lineX.reset()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"lineY",{get:function(){return this._lineY.get()},set:function(e){e?(e.setElement(this.paper.add("path")),this._lineY.set(e,e.events.on("positionchanged",this.updateSelection,this,!1)),e.parent=this,e.interactionsEnabled=!1):this._lineY.reset()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"selection",{get:function(){return this._selection},set:function(e){this._selection=e,e&&(e.element=this.paper.add("path"),e.parent=this)},enumerable:!0,configurable:!0}),t.prototype.processConfig=function(t){t&&(f.hasValue(t.xAxis)&&f.isString(t.xAxis)&&this.map.hasKey(t.xAxis)&&(t.xAxis=this.map.getKey(t.xAxis)),f.hasValue(t.yAxis)&&f.isString(t.yAxis)&&this.map.hasKey(t.yAxis)&&(t.yAxis=this.map.getKey(t.yAxis))),e.prototype.processConfig.call(this,t)},Object.defineProperty(t.prototype,"snapToSeries",{get:function(){return this.getPropertyValue("snapToSeries")},set:function(e){this.setPropertyValue("snapToSeries",e)&&(this._snapToDisposer&&this._snapToDisposer.dispose(),e&&(this._snapToDisposer=e.events.on("tooltipshownat",this.handleSnap,this,!1)))},enumerable:!0,configurable:!0}),t.prototype.handleSnap=function(){var e=this.snapToSeries,t=e.tooltipY,i=e.tooltipX;this.xAxis&&this.xAxis.renderer.opposite&&(t-=this.pixelHeight),this.point={x:i,y:t},this.getPositions(),i-=this.pixelWidth,this.yAxis&&this.yAxis.renderer.opposite&&(i+=this.pixelWidth);var a=e.tooltip,n=a.animationDuration,s=a.animationEasing;e.baseAxis==e.xAxis&&e.yAxis.showTooltipAtPosition(this.yPosition),e.baseAxis==e.yAxis&&e.xAxis.showTooltipAtPosition(this.xPosition),this.lineX.animate([{property:"y",to:t}],n,s),this.lineY.animate([{property:"x",to:i}],n,s),this.xAxis||this.lineX.animate([{property:"x",to:i}],n,s),this.yAxis||this.lineY.animate([{property:"y",to:t}],n,s)},t}(N);d.b.registeredClasses.XYCursor=W;var E=i("BEgH"),z=i("ISWh"),U=i("85D4"),H=function(e){function t(){var t=e.call(this)||this;t._chart=new _.d,t.className="XYChartScrollbar";var i=new B.a;t.padding(0,0,0,0);var a=t.createChild(G);a.shouldClone=!1,a.margin(0,0,0,0),a.padding(0,0,0,0),a.interactionsEnabled=!1,t._scrollbarChart=a,t._disposers.push(t._scrollbarChart),t.minHeight=60,t.minWidth=60;var n=t.createChild(P.a);n.shouldClone=!1,n.setElement(t.paper.add("path")),n.fill=i.getFor("background"),n.fillOpacity=.8,n.interactionsEnabled=!1,n.isMeasured=!1,n.toBack(),t._unselectedOverlay=n,t._disposers.push(t._unselectedOverlay),a.toBack(),t.background.cornerRadius(0,0,0,0);var s=t.thumb.background;s.cornerRadius(0,0,0,0),s.fillOpacity=0,s.fill=i.getFor("background");var r=s.states.getKey("hover");r&&(r.properties.fillOpacity=.2);var o=s.states.getKey("down");return o&&(o.properties.fillOpacity=.4),t._disposers.push(t._chart),t.applyTheme(),t}return n.c(t,e),Object.defineProperty(t.prototype,"series",{get:function(){return this._series||(this._series=new o.b,this._disposers.push(this._series.events.on("inserted",this.handleSeriesAdded,this,!1)),this._disposers.push(this._series.events.on("removed",this.handleSeriesRemoved,this,!1))),this._series},enumerable:!0,configurable:!0}),t.prototype.handleSeriesAdded=function(e){var t=e.newValue,i=this.scrollbarChart;i.zoomOutButton.disabled=!0,this.chart=t.chart;var a=!0,n=!0;m.each(this.series.iterator(),function(e){e!=t&&(e.xAxis==t.xAxis&&(a=!1),e.yAxis==t.yAxis&&(n=!1))});var s=new B.a,r=t.clone();if(a){var o=t.xAxis.clone();i.xAxes.moveValue(o),o.title.disabled=!0,o.rangeChangeDuration=0,o.id=t.uid,o.title.disabled=!0,(l=o.renderer).ticks.template.disabled=!0,l.inside=!0,l.line.strokeOpacity=0,l.minLabelPosition=.02,l.maxLabelPosition=.98,l.line.disabled=!0,l.axisFills.template.disabled=!0,l.baseGrid.disabled=!0,l.grid.template.strokeOpacity=.05,l.labels.template.fillOpacity=.5,r.xAxis=o}if(n){var l,h=t.yAxis.clone();i.yAxes.moveValue(h),h.title.disabled=!0,h.rangeChangeDuration=0,(l=h.renderer).ticks.template.disabled=!0,l.inside=!0,l.line.strokeOpacity=0,l.minLabelPosition=.02,l.maxLabelPosition=.98,l.line.disabled=!0,l.axisFills.template.disabled=!0,l.grid.template.stroke=s.getFor("background"),l.baseGrid.disabled=!0,l.grid.template.strokeOpacity=.05,l.labels.template.fillOpacity=.5,r.yAxis=h}r.rangeChangeDuration=0,r.interpolationDuration=0,r.defaultState.transitionDuration=0,r.showOnInit=!1,this._disposers.push(r.events.on("validated",this.zoomOutAxes,this,!1)),this._disposers.push(t.events.on("datavalidated",function(){r.data!=t.data&&(r.data=t.data)},void 0,!1)),r.defaultState.properties.visible=!0,r.filters.push(new U.a),i.series.push(r),this.updateByOrientation()},t.prototype.updateByOrientation=function(){var e=this;this._scrollbarChart&&(m.each(this._scrollbarChart.xAxes.iterator(),function(t){var i=t.renderer;"vertical"==e.orientation?(i.grid.template.disabled=!0,i.labels.template.disabled=!0,i.minGridDistance=10):(i.grid.template.disabled=!1,i.labels.template.disabled=!1,i.minGridDistance=t.clonedFrom.renderer.minGridDistance)}),m.each(this._scrollbarChart.yAxes.iterator(),function(t){var i=t.renderer;"horizontal"==e.orientation?(i.grid.template.disabled=!0,i.labels.template.disabled=!0,i.minGridDistance=10):(i.grid.template.disabled=!1,i.labels.template.disabled=!1,i.minGridDistance=t.clonedFrom.renderer.minGridDistance)}))},t.prototype.handleSeriesRemoved=function(e){e.oldValue.events.off("validated",this.zoomOutAxes,this)},Object.defineProperty(t.prototype,"scrollbarChart",{get:function(){return this._scrollbarChart},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"chart",{get:function(){return this._chart.get()},set:function(e){this._chart.get()!==e&&(this._chart.set(e,e.events.on("datavalidated",this.handleDataChanged,this,!1)),this.handleDataChanged(),this._scrollbarChart.dataProvider=e)},enumerable:!0,configurable:!0}),t.prototype.handleDataChanged=function(){this.chart.data!=this.scrollbarChart.data&&(this.scrollbarChart.data=this.chart.data)},t.prototype.zoomOutAxes=function(){var e=this.scrollbarChart;m.each(e.xAxes.iterator(),function(e){e.zoom({start:0,end:1},!0,!0)}),m.each(e.yAxes.iterator(),function(e){e.zoom({start:0,end:1},!0,!0)})},t.prototype.updateThumb=function(){if(e.prototype.updateThumb.call(this),this._unselectedOverlay){var t=this.thumb,i=t.pixelX||0,a=t.pixelY||0,n=t.pixelWidth||0,s=t.pixelHeight||0,r="";"horizontal"==this.orientation?(r=k.rectToPath({x:-1,y:0,width:i,height:s}),r+=k.rectToPath({x:i+n,y:0,width:(this.pixelWidth||0)-i-n,height:s})):(r=k.rectToPath({x:0,y:0,width:n,height:a}),r+=k.rectToPath({x:0,y:a+s,width:n,height:(this.pixelHeight||0)-a-s})),this._unselectedOverlay.path=r}},t.prototype.processConfig=function(t){if(t&&f.hasValue(t.series)&&f.isArray(t.series))for(var i=0,a=t.series.length;i<a;i++){var n=t.series[i];if(f.hasValue(n)&&f.isString(n)){if(!this.map.hasKey(n))throw Error("XYChartScrollbar error: Series with id `"+n+"` does not exist.");t.series[i]=this.map.getKey(n)}}e.prototype.processConfig.call(this,t)},t}(z.a);d.b.registeredClasses.XYChartScrollbar=H;var K=function(e){function t(){var t=e.call(this)||this;return t.className="XYChartDataItem",t.applyTheme(),t}return n.c(t,e),t}(s.b),G=function(e){function t(){var t=e.call(this)||this;t._axisRendererX=l.a,t._axisRendererY=h.a,t.className="XYChart",t.maskBullets=!0;var i=t.chartContainer;i.layout="vertical",t.padding(15,15,15,15);var a=i.createChild(r.a);a.shouldClone=!1,a.layout="vertical",a.width=Object(Y.c)(100),a.zIndex=1,t.topAxesContainer=a;var n=i.createChild(r.a);n.shouldClone=!1,n.layout="horizontal",n.width=Object(Y.c)(100),n.height=Object(Y.c)(100),n.zIndex=0,t.yAxesAndPlotContainer=n;var s=i.createChild(r.a);s.shouldClone=!1,s.width=Object(Y.c)(100),s.layout="vertical",s.zIndex=1,t.bottomAxesContainer=s;var o=n.createChild(r.a);o.shouldClone=!1,o.layout="horizontal",o.height=Object(Y.c)(100),o.contentAlign="right",o.events.on("transformed",t.updateXAxesMargins,t,!1),o.zIndex=1,t.leftAxesContainer=o;var u=n.createChild(r.a);u.shouldClone=!1,u.height=Object(Y.c)(100),u.width=Object(Y.c)(100),u.background.fillOpacity=0,t.plotContainer=u,t.mouseWheelBehavior="none",t._cursorContainer=u;var d=n.createChild(r.a);d.shouldClone=!1,d.layout="horizontal",d.height=Object(Y.c)(100),d.zIndex=1,d.events.on("transformed",t.updateXAxesMargins,t,!1),t.rightAxesContainer=d,t.seriesContainer.parent=u,t.bulletsContainer.parent=u;var c=u.createChild(E.a);return c.shouldClone=!1,c.align="right",c.valign="top",c.zIndex=Number.MAX_SAFE_INTEGER,c.marginTop=5,c.marginRight=5,c.hide(0),t.zoomOutButton=c,t._bulletMask=t.plotContainer,t.applyTheme(),t}return n.c(t,e),t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),this.zoomOutButton.exportable=!1,f.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("X/Y chart"))},t.prototype.draw=function(){e.prototype.draw.call(this),this.seriesContainer.toFront(),this.bulletsContainer.toFront(),this.maskBullets&&(this.bulletsContainer.mask=this._bulletMask),this.updateSeriesLegend()},t.prototype.updatePlotElements=function(){m.each(this.series.iterator(),function(e){e.invalidate()})},t.prototype.validateData=function(){0==this._parseDataFrom&&(m.each(this.xAxes.iterator(),function(e){e.dataChangeUpdate()}),m.each(this.yAxes.iterator(),function(e){e.dataChangeUpdate()}),m.each(this.series.iterator(),function(e){e.dataChangeUpdate()})),e.prototype.validateData.call(this)},t.prototype.updateXAxesMargins=function(){var e=this.leftAxesContainer.measuredWidth,t=this.rightAxesContainer.measuredWidth,i=this.bottomAxesContainer;i.paddingLeft==e&&i.paddingRight==t||(i.paddingLeft=e,i.paddingRight=t);var a=this.topAxesContainer;a.paddingLeft==e&&a.paddingRight==t||(a.paddingLeft=e,a.paddingRight=t)},t.prototype.handleXAxisChange=function(e){this.updateXAxis(e.target)},t.prototype.handleYAxisChange=function(e){this.updateYAxis(e.target)},t.prototype.processXAxis=function(e){var t=e.newValue;t.chart=this,t.renderer=new this._axisRendererX,t.axisLetter="X",t.renderer.observe(["opposite","inside","inversed","minGridDistance"],this.handleXAxisChange,this),t.events.on("startchanged",this.handleXAxisRangeChange,this,!1),t.events.on("endchanged",this.handleXAxisRangeChange,this,!1),t.dataProvider=this,this.updateXAxis(t.renderer),this.processAxis(t)},t.prototype.processYAxis=function(e){var t=e.newValue;t.chart=this,t.renderer=new this._axisRendererY,t.axisLetter="Y",t.renderer.observe(["opposite","inside","inversed","minGridDistance"],this.handleYAxisChange,this),t.events.on("startchanged",this.handleYAxisRangeChange,this,!1),t.events.on("endchanged",this.handleYAxisRangeChange,this,!1),t.dataProvider=this,this.updateYAxis(t.renderer),this.processAxis(t)},t.prototype.handleXAxisRangeChange=function(){var e=this.getCommonAxisRange(this.xAxes);this.scrollbarX&&this.zoomAxes(this.xAxes,e,!0),this.toggleZoomOutButton(),this.updateScrollbar(this.scrollbarX,e)},t.prototype.toggleZoomOutButton=function(){if(this.zoomOutButton){var e=!1;m.eachContinue(this.xAxes.iterator(),function(t){return 0==g.round(t.start,3)&&1==g.round(t.end,3)||(e=!0,!1)}),m.eachContinue(this.yAxes.iterator(),function(t){return 0==g.round(t.start,3)&&1==g.round(t.end,3)||(e=!0,!1)}),this.seriesAppeared||(e=!1),e?this.zoomOutButton.show():this.zoomOutButton.hide()}},t.prototype.seriesAppeared=function(){var e=!1;return m.each(this.series.iterator(),function(t){if(!t.appeared)return e=!1,!1}),e},t.prototype.handleYAxisRangeChange=function(){var e=this.getCommonAxisRange(this.yAxes);this.scrollbarY&&this.zoomAxes(this.yAxes,e,!0),this.toggleZoomOutButton(),this.updateScrollbar(this.scrollbarY,e)},t.prototype.updateScrollbar=function(e,t){e&&(e.skipRangeEvents(),e.start=t.start,e.end=t.end)},t.prototype.getCommonAxisRange=function(e){var t,i;return m.each(e.iterator(),function(e){var a=e.start,n=e.end;e.renderer.inversed&&(a=1-e.end,n=1-e.start),(!f.isNumber(t)||a<t)&&(t=a),(!f.isNumber(i)||n>i)&&(i=n)}),{start:t,end:i}},t.prototype.updateXAxis=function(e){var t=e.axis;e.opposite?(t.parent=this.topAxesContainer,t.toFront()):(t.parent=this.bottomAxesContainer,t.toBack()),t.renderer&&t.renderer.processRenderer()},t.prototype.updateYAxis=function(e){var t=e.axis;e.opposite?(t.parent=this.rightAxesContainer,t.toBack()):(t.parent=this.leftAxesContainer,t.toFront()),t.renderer&&t.renderer.processRenderer()},t.prototype.processAxis=function(e){var t=this;e instanceof b&&this._dataUsers.moveValue(e);var i=e.renderer;i.gridContainer.parent=this.plotContainer,i.gridContainer.toBack(),i.breakContainer.parent=this.plotContainer,i.breakContainer.toFront(),i.breakContainer.zIndex=10,e.addDisposer(new _.b(function(){t.dataUsers.removeValue(e)})),this.plotContainer.events.on("maxsizechanged",function(){t.inited&&e.invalidateDataItems()},e,!1)},Object.defineProperty(t.prototype,"xAxes",{get:function(){return this._xAxes||(this._xAxes=new o.b,this._xAxes.events.on("inserted",this.processXAxis,this,!1),this._xAxes.events.on("removed",this.handleAxisRemoval,this,!1)),this._xAxes},enumerable:!0,configurable:!0}),t.prototype.handleAxisRemoval=function(e){var t=e.oldValue;this.dataUsers.removeValue(t),t.autoDispose&&t.dispose()},Object.defineProperty(t.prototype,"yAxes",{get:function(){return this._yAxes||(this._yAxes=new o.b,this._yAxes.events.on("inserted",this.processYAxis,this,!1),this._yAxes.events.on("removed",this.handleAxisRemoval,this,!1)),this._yAxes},enumerable:!0,configurable:!0}),t.prototype.handleSeriesAdded=function(t){try{e.prototype.handleSeriesAdded.call(this,t);var i=t.newValue;i.xAxis,i.yAxis,void 0==i.fill&&(i.fill=this.colors.next()),void 0==i.stroke&&(i.stroke=i.fill)}catch(e){this.raiseCriticalError(e)}},Object.defineProperty(t.prototype,"cursor",{get:function(){return this._cursor},set:function(e){this._cursor!=e&&(this._cursor&&this.removeDispose(this._cursor),this._cursor=e,e&&(this._disposers.push(e),e.chart=this,e.parent=this._cursorContainer,e.events.on("cursorpositionchanged",this.handleCursorPositionChange,this,!1),e.events.on("zoomstarted",this.handleCursorZoomStart,this,!1),e.events.on("zoomended",this.handleCursorZoomEnd,this,!1),e.events.on("panstarted",this.handleCursorPanStart,this,!1),e.events.on("panning",this.handleCursorPanning,this,!1),e.events.on("panended",this.handleCursorPanEnd,this,!1),e.events.on("behaviorcanceled",this.handleCursorCanceled,this,!1),e.events.on("hidden",this.handleHideCursor,this,!1),e.zIndex=Number.MAX_SAFE_INTEGER-1))},enumerable:!0,configurable:!0}),t.prototype.createCursor=function(){return new W},t.prototype.handleCursorPositionChange=function(){var e=this.cursor;if(e.visible&&!e.isHiding){var t=this.cursor.xPosition,i=this.cursor.yPosition;this.showSeriesTooltip({x:t,y:i});var a=void 0,n=e.snapToSeries;n&&(n.baseAxis==n.xAxis&&(a=n.yAxis),n.baseAxis==n.yAxis&&(a=n.xAxis)),this.showAxisTooltip(this.xAxes,t,a),this.showAxisTooltip(this.yAxes,i,a)}},t.prototype.handleHideCursor=function(){this.hideObjectTooltip(this.xAxes),this.hideObjectTooltip(this.yAxes),this.hideObjectTooltip(this.series),this.updateSeriesLegend()},t.prototype.updateSeriesLegend=function(){m.each(this.series.iterator(),function(e){e.updateLegendValue()})},t.prototype.hideObjectTooltip=function(e){m.each(e.iterator(),function(e){e.hideTooltip(0)})},t.prototype.showSeriesTooltip=function(e){var t=this;if(e){var i=F.spritePointToSvg({x:-.5,y:-.5},this.plotContainer),a=F.spritePointToSvg({x:this.plotContainer.pixelWidth+.5,y:this.plotContainer.pixelHeight+.5},this.plotContainer),n=[],s=0;this.series.each(function(r){var o=r.showTooltipAtPosition(e.x,e.y);o&&r.tooltip.setBounds({x:0,y:0,width:t.pixelWidth,height:t.pixelHeight}),o&&g.isInRectangle(o,{x:i.x,y:i.y,width:a.x-i.x,height:a.y-i.y})&&(n.push({point:o,series:r}),s+=o.y)}),n.sort(function(e,t){return e.point.y>t.point.y?1:e.point.y<t.point.y?-1:0});var r=s/n.length,o=F.svgPointToDocument({x:0,y:0},this.svgContainer.SVGContainer).y;if(n.length>0){var l=i.y,h=a.y,u=(F.spritePointToDocument({x:0,y:l},this),!1);if(r>l+(h-l)/2)for(var d=h,c=n.length-1;c>=0;c--){var p=(x=n[c].series).tooltip,y=n[c].point.y;if(p.setBounds({x:0,y:-o,width:this.pixelWidth,height:d+o}),p.invalid&&p.validate(),p.toBack(),(d=F.spritePointToSvg({x:0,y:p.label.pixelY-p.pixelY+y-p.pixelMarginTop},p).y)<-o){u=!0;break}}if(r<=l+(h-l)/2||u)for(var f=l,m=(c=0,n.length);c<m;c++){var x=n[c].series;y=n[c].point.y;(p=x.tooltip).setBounds({x:0,y:f,width:this.pixelWidth,height:h}),p.invalid&&p.validate(),p.toBack(),f=F.spritePointToSvg({x:0,y:p.label.pixelY+p.label.measuredHeight-p.pixelY+y+p.pixelMarginBottom},p).y}}}else this.series.each(function(e){e.hideTooltip()})},t.prototype.showAxisTooltip=function(e,t,i){var a=this;m.each(e.iterator(),function(e){e!=i&&(a.dataItems.length>0||e.dataItems.length>0)&&e.showTooltipAtPosition(t)})},t.prototype.getUpdatedRange=function(e,t){if(e){var i,a,n=e.renderer.inversed;e.renderer instanceof h.a&&(t=g.invertRange(t)),n?(g.invertRange(t),i=1-e.end,a=1-e.start):(i=e.start,a=e.end);var s=a-i;return{start:i+t.start*s,end:i+t.end*s}}},t.prototype.handleCursorZoomEnd=function(e){var t=this.cursor,i=t.behavior;if("zoomX"==i||"zoomXY"==i){var a=t.xRange;a&&this.xAxes.length>0&&((a=this.getUpdatedRange(this.xAxes.getIndex(0),a)).priority="start",this.zoomAxes(this.xAxes,a))}if("zoomY"==i||"zoomXY"==i){var n=t.yRange;n&&this.yAxes.length>0&&((n=this.getUpdatedRange(this.yAxes.getIndex(0),n)).priority="start",this.zoomAxes(this.yAxes,n))}this.handleHideCursor()},t.prototype.handleCursorPanStart=function(e){var t=this.xAxes.getIndex(0);t&&(this._panStartXRange={start:t.start,end:t.end});var i=this.yAxes.getIndex(0);i&&(this._panStartYRange={start:i.start,end:i.end})},t.prototype.handleCursorPanEnd=function(e){var t=this.cursor.behavior;if(this._panEndXRange&&("panX"==t||"panXY"==t)){var i=0;(a=this._panEndXRange).start<0&&(i=a.start),a.end>1&&(i=a.end-1),this.zoomAxes(this.xAxes,{start:a.start-i,end:a.end-i},!1,!0),this._panEndXRange=void 0,this._panStartXRange=void 0}if(this._panEndYRange&&("panY"==t||"panXY"==t)){var a;i=0;(a=this._panEndYRange).start<0&&(i=a.start),a.end>1&&(i=a.end-1),this.zoomAxes(this.yAxes,{start:a.start-i,end:a.end-i},!1,!0),this._panEndYRange=void 0,this._panStartYRange=void 0}},t.prototype.handleCursorCanceled=function(){this._panEndXRange=void 0,this._panStartXRange=void 0},t.prototype.handleCursorPanning=function(e){var t=this.cursor,i=t.behavior,a=t.maxPanOut;if(this._panStartXRange&&("panX"==i||"panXY"==i)){var n=this._panStartXRange,s=t.xRange,r=n.end-n.start,o=s.start,l=Math.max(-a,o+n.start),h=Math.min(s.start+n.end,1+a);l<=0&&(h=l+r),h>=1&&(l=h-r);var u={start:l,end:h};this._panEndXRange=u,this.zoomAxes(this.xAxes,u,!1,!1,t.maxPanOut)}if(this._panStartYRange&&("panY"==i||"panXY"==i)){n=this._panStartYRange,s=t.yRange,r=n.end-n.start,o=s.start,l=Math.max(-a,o+n.start),h=Math.min(s.start+n.end,1+a);l<=0&&(h=l+r),h>=1&&(l=h-r);u={start:l,end:h};this._panEndYRange=u,this.zoomAxes(this.yAxes,u,!1,!1,t.maxPanOut)}this.handleHideCursor()},t.prototype.handleCursorZoomStart=function(e){},Object.defineProperty(t.prototype,"scrollbarX",{get:function(){return this._scrollbarX},set:function(e){var t=this;this._scrollbarX&&this.removeDispose(this._scrollbarX),this._scrollbarX=e,e&&(this._disposers.push(e),e.parent=this.topAxesContainer,e.startGrip.exportable=!1,e.endGrip.exportable=!1,e.toBack(),e.orientation="horizontal",e.events.on("rangechanged",this.handleXScrollbarChange,this,!1),e.adapter.add("positionValue",function(e){var i=t.xAxes.getIndex(0);return i&&(e.value=i.getPositionLabel(e.position)),e}))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"scrollbarY",{get:function(){return this._scrollbarY},set:function(e){var t=this;this._scrollbarY&&this.removeDispose(this._scrollbarY),this._scrollbarY=e,e&&(this._disposers.push(e),e.parent=this.rightAxesContainer,e.startGrip.exportable=!1,e.endGrip.exportable=!1,e.toFront(),e.orientation="vertical",e.events.on("rangechanged",this.handleYScrollbarChange,this,!1),e.adapter.add("positionValue",function(e){var i=t.yAxes.getIndex(0);return i&&(e.value=i.getPositionLabel(e.position)),e}))},enumerable:!0,configurable:!0}),t.prototype.handleXScrollbarChange=function(e){var t=e.target,i=this.zoomAxes(this.xAxes,t.range);t.fixRange(i)},t.prototype.handleYScrollbarChange=function(e){var t=e.target,i=this.zoomAxes(this.yAxes,t.range);t.fixRange(i)},t.prototype.zoomAxes=function(e,t,i,a,n){var s={start:0,end:1};return this.showSeriesTooltip(),this.dataInvalid||m.each(e.iterator(),function(e){if(e.renderer.inversed&&(t=g.invertRange(t)),e.hideTooltip(0),a){var r=t.end-t.start;t.start=e.roundPosition(t.start+1e-4,0),t.end=t.start+r}var o=e.zoom(t,i,i,n);e.renderer.inversed&&(o=g.invertRange(o)),s=o}),s},Object.defineProperty(t.prototype,"maskBullets",{get:function(){return this.getPropertyValue("maskBullets")},set:function(e){this.setPropertyValue("maskBullets",e,!0)},enumerable:!0,configurable:!0}),t.prototype.handleWheel=function(e){var t=this.plotContainer,i=F.documentPointToSvg(e.point,this.htmlContainer),a=F.svgPointToSprite(i,t),n=e.shift.y,s=this.getCommonAxisRange(this.xAxes),r=this.getCommonAxisRange(this.yAxes),o=this.mouseWheelBehavior;if("panX"==o||"panXY"==o){var l=s.end-s.start,h=Math.max(-0,s.start+.05*n/100),u=Math.min(s.end+.05*n/100,1);h<=0&&(u=h+l),u>=1&&(h=u-l),this.zoomAxes(this.xAxes,{start:h,end:u})}if("panY"==o||"panXY"==o){n*=-1;var d=r.end-r.start,c=Math.max(-0,r.start+.05*n/100),p=Math.min(r.end+.05*n/100,1);c<=0&&(p=c+d),p>=1&&(c=p-d),this.zoomAxes(this.yAxes,{start:c,end:p})}if("zoomX"==o||"zoomXY"==o){var y=a.x/t.maxWidth;h=Math.max(-0,s.start-.05*n/100*y);h=Math.min(h,y);u=Math.min(s.end+.05*n/100*(1-y),1);u=Math.max(u,y),this.zoomAxes(this.xAxes,{start:h,end:u})}if("zoomY"==o||"zoomXY"==o){var g=a.y/t.maxHeight;c=Math.max(-0,r.start-.05*n/100*(1-g));c=Math.min(c,g);p=Math.min(r.end+.05*n/100*g,1);p=Math.max(p,g),this.zoomAxes(this.yAxes,{start:c,end:p})}},Object.defineProperty(t.prototype,"mouseWheelBehavior",{get:function(){return this.getPropertyValue("mouseWheelBehavior")},set:function(e){this.setPropertyValue("mouseWheelBehavior",e)&&("none"!=e?(this._mouseWheelDisposer=this.plotContainer.events.on("wheel",this.handleWheel,this,!1),this._disposers.push(this._mouseWheelDisposer)):this._mouseWheelDisposer&&(this.plotContainer.wheelable=!1,this.plotContainer.hoverable=!1,this._mouseWheelDisposer.dispose()))},enumerable:!0,configurable:!0}),t.prototype.dataSourceDateFields=function(t){var i=this;return t=e.prototype.dataSourceDateFields.call(this,t),m.each(this.series.iterator(),function(e){t=i.populateDataSourceFields(t,e.dataFields,["dateX","dateY","openDateX","openDateY"])}),t},t.prototype.dataSourceNumberFields=function(t){var i=this;return t=e.prototype.dataSourceDateFields.call(this,t),m.each(this.series.iterator(),function(e){t=i.populateDataSourceFields(t,e.dataFields,["valueX","valueY","openValueX","openValueY"])}),t},t.prototype.processConfig=function(t){if(t){var i=[],a=[];if(f.hasValue(t.xAxes)&&f.isArray(t.xAxes))for(var n=0,s=t.xAxes.length;n<s;n++){if(!t.xAxes[n].type)throw Error("[XYChart error] No type set for xAxes["+n+"].");f.hasValue(t.xAxes[n].axisRanges)&&(i.push({axisRanges:t.xAxes[n].axisRanges,index:n}),delete t.xAxes[n].axisRanges)}if(f.hasValue(t.yAxes)&&f.isArray(t.yAxes))for(n=0,s=t.yAxes.length;n<s;n++){if(!t.yAxes[n].type)throw Error("[XYChart error] No type set for yAxes["+n+"].");f.hasValue(t.yAxes[n].axisRanges)&&(a.push({axisRanges:t.yAxes[n].axisRanges,index:n}),delete t.yAxes[n].axisRanges)}if(f.hasValue(t.series)&&f.isArray(t.series))for(n=0,s=t.series.length;n<s;n++)t.series[n].type=t.series[n].type||"LineSeries";if(f.hasValue(t.cursor)&&!f.hasValue(t.cursor.type)&&(t.cursor.type="XYCursor"),f.hasValue(t.scrollbarX)&&!f.hasValue(t.scrollbarX.type)&&(t.scrollbarX.type="Scrollbar"),f.hasValue(t.scrollbarY)&&!f.hasValue(t.scrollbarY.type)&&(t.scrollbarY.type="Scrollbar"),e.prototype.processConfig.call(this,t),a.length)for(n=0,s=a.length;n<s;n++)this.yAxes.getIndex(a[n].index).config={axisRanges:a[n].axisRanges};if(i.length)for(n=0,s=i.length;n<s;n++)this.xAxes.getIndex(i[n].index).config={axisRanges:i[n].axisRanges}}},t.prototype.configOrder=function(t,i){return t==i?0:"scrollbarX"==t?1:"scrollbarX"==i?-1:"scrollbarY"==t?1:"scrollbarY"==i?-1:"series"==t?1:"series"==i?-1:e.prototype.configOrder.call(this,t,i)},t.prototype.createSeries=function(){return new L},Object.defineProperty(t.prototype,"zoomOutButton",{get:function(){return this._zoomOutButton},set:function(e){var t=this;this._zoomOutButton=e,e&&e.events.on("hit",function(){t.zoomAxes(t.xAxes,{start:0,end:1}),t.zoomAxes(t.yAxes,{start:0,end:1})},void 0,!1)},enumerable:!0,configurable:!0}),t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.xAxes.copyFrom(t.xAxes),this.yAxes.copyFrom(t.yAxes),this.zoomOutButton.copyFrom(t.zoomOutButton)},t.prototype.disposeData=function(){e.prototype.disposeData.call(this);var t=this.scrollbarX;t&&t instanceof H&&t.scrollbarChart.disposeData();var i=this.scrollbarY;i&&i instanceof H&&i.scrollbarChart.disposeData(),this.xAxes.each(function(e){e.disposeData()}),this.yAxes.each(function(e){e.disposeData()})},t.prototype.addData=function(t,i){e.prototype.addData.call(this,t,i),this.scrollbarX instanceof H&&this.scrollbarX.scrollbarChart.addData(t,i),this.scrollbarY instanceof H&&this.scrollbarY.scrollbarChart.addData(t,i)},t}(s.a);d.b.registeredClasses.XYChart=G;var Z=i("aFzC"),q=function(e){function t(){var t=e.call(this)||this;t.className="LineSeriesSegment",t.isMeasured=!1,t.interactionsEnabled=!1,t.layout="none";var i=t.createChild(P.a);t.fillSprite=i,i.shouldClone=!1,i.setElement(t.paper.add("path")),i.isMeasured=!1,t._disposers.push(i);var a=t.createChild(P.a);return t.strokeSprite=a,a.shouldClone=!1,a.setElement(t.paper.add("path")),a.isMeasured=!1,t._disposers.push(a),t}return n.c(t,e),t.prototype.drawSegment=function(e,t,i,a){if(!this.disabled)if(e.length>0&&t.length>0){var n=k.moveTo({x:e[0].x-.2,y:e[0].y-.2})+k.moveTo(e[0])+new Z.b(i,a).smooth(e);0==this.strokeOpacity||0==this.strokeSprite.strokeOpacity||(this.strokeSprite.path=n),(this.fillOpacity>0||this.fillSprite.fillOpacity>0)&&(n+=k.lineTo(t[0])+new Z.b(i,a).smooth(t),n+=k.lineTo(e[0]),n+=k.closePath(),this.fillSprite.path=n)}else this.fillSprite.path="",this.strokeSprite.path=""},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t);var i=this.strokeSprite;V.copyProperties(t,i.properties,P.b),i.fillOpacity=0;var a=this.fillSprite;V.copyProperties(t,a.properties,P.b),a.strokeOpacity=0},t}(r.a);d.b.registeredClasses.LineSeriesSegment=q;var J=i("PTiM"),Q=i("p9TX"),$=i("GtDR"),ee=function(e){function t(){var t=e.call(this)||this;return t.className="LineSeriesDataItem",t}return n.c(t,e),t}(w),te=function(e){function t(){var t=e.call(this)||this;return t.minDistance=.5,t.segments=new o.e(t.createSegment()),t.segments.template.applyOnClones=!0,t._disposers.push(new o.c(t.segments)),t._disposers.push(t.segments.template),t._segmentsIterator=new m.ListIterator(t.segments,function(){return t.segments.create()}),t._segmentsIterator.createNewItems=!0,t.className="LineSeries",t.strokeOpacity=1,t.fillOpacity=0,t.connect=!0,t.tensionX=1,t.tensionY=1,t.segmentsContainer=t.mainContainer.createChild(r.a),t.segmentsContainer.isMeasured=!1,t.bulletsContainer.toFront(),t.applyTheme(),t}return n.c(t,e),t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),f.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Line Series"))},t.prototype.createSegment=function(){return new q},t.prototype.createDataItem=function(){return new ee},t.prototype.setInitialWorkingValues=function(e){if(this.appeared&&this.visible){var t=this._yAxis.get(),i=this._xAxis.get(),a=this.dataItems.getIndex(e.index-1);if(e.component=this,this.baseAxis==i&&t instanceof C.a){var n=t.minZoomed;a&&(n=a.values.valueY.workingValue),e.setWorkingValue("valueY",n,0),e.setWorkingValue("valueY",e.values.valueY.value),i instanceof R&&(e.setWorkingLocation("dateX",-.5,0),e.setWorkingLocation("dateX",.5))}if(this.baseAxis==t&&i instanceof C.a){var s=i.minZoomed;a&&(s=a.values.valueX.workingValue),e.setWorkingValue("valueX",s,0),e.setWorkingValue("valueX",e.values.valueX.value),t instanceof R&&(e.setWorkingLocation("dateY",-.5,0),e.setWorkingLocation("dateY",.5))}}},t.prototype.updateLegendValue=function(t){e.prototype.updateLegendValue.call(this,t),t&&t.segment&&(this.tooltipColorSource=t.segment)},t.prototype.validate=function(){var t=this;e.prototype.validate.call(this),this._segmentsIterator.reset(),this.openSegment(this._workingStartIndex),m.each(this.axisRanges.iterator(),function(e){t.openSegment(t._workingStartIndex,e)}),m.each(this._segmentsIterator.iterator(),function(e){e.__disabled=!0})},t.prototype.sliceData=function(){for(var e=this.startIndex,t=this.endIndex,i=this.startIndex-1;i>=0;i--){if((n=this.dataItems.getIndex(i))&&n.hasValue(this._xValueFields)&&n.hasValue(this._yValueFields)){e=i;break}}i=this.endIndex;for(var a=this.dataItems.length;i<a;i++){var n;if((n=this.dataItems.getIndex(i))&&n.hasValue(this._xValueFields)&&n.hasValue(this._yValueFields)){t=i+1;break}}this._workingStartIndex=e,this._workingEndIndex=t},t.prototype.openSegment=function(e,t){var i,a=[],n=this._workingEndIndex,s=!1,r=this._segmentsIterator.getFirst();r.__disabled=!1,t?(r.parent=t.contents,V.copyProperties(t.contents,r,P.b)):(V.copyProperties(this,r,P.b),r.filters.clear(),r.parent=this.segmentsContainer);for(var o=e;o<n;o++){var l=this.dataItems.getIndex(o);if(l.segment=r,l.hasProperties&&(o==e?this.updateSegmentProperties(l.properties,r):s=this.updateSegmentProperties(l.properties,r,!0)),l.hasValue(this._xValueFields)&&l.hasValue(this._yValueFields))this.addPoints(a,l,this.xField,this.yField);else{if(o==e)continue;if(!this.connect){i=o;break}}if(i=o,s)break}this.closeSegment(r,a,e,i,t)},t.prototype.addPoints=function(e,t,i,a,n){var s=this.getPoint(t,i,a,t.workingLocations[i],t.workingLocations[a]);n||(t.point=s),e.push(s)},t.prototype.closeSegment=function(e,t,i,a,n){var s=[];if(this.dataFields[this._xOpenField]||this.dataFields[this._yOpenField]||this.stacked)for(var r=a;r>=i;r--){var o=this.dataItems.getIndex(r);o.hasValue(this._xValueFields)&&o.hasValue(this._yValueFields)&&this.addPoints(s,o,this.xOpenField,this.yOpenField,!0)}else{var l=this.baseAxis,h=t.length,u=this.xAxis,d=this.yAxis;l==u?(s.push({x:t[h-1].x,y:d.basePoint.y}),s.push({x:t[0].x,y:d.basePoint.y})):(s.push({x:u.basePoint.x,y:t[h-1].y}),s.push({x:u.basePoint.x,y:t[0].y}))}this.drawSegment(e,t,s),a<this._workingEndIndex-1&&this.openSegment(a,n)},t.prototype.drawSegment=function(e,t,i){e.drawSegment(t,i,this.tensionX,this.tensionY)},t.prototype.updateSegmentProperties=function(e,t,i){var a=!1;return V.each(e,function(e,n){if(f.hasValue(n)){var s=t[e],r=void 0;s&&(r=s.toString?s.toString():s);var o=void 0;n&&(o=n.toString?n.toString():n),s==n||void 0!=r&&void 0!=o&&r==o||(i||(t[e]=n),a=!0)}}),a},Object.defineProperty(t.prototype,"connect",{get:function(){return this.getPropertyValue("connect")},set:function(e){this.setPropertyValue("connect",e)&&this.invalidate()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"tensionX",{get:function(){return this.getPropertyValue("tensionX")},set:function(e){this.setPropertyValue("tensionX",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"tensionY",{get:function(){return this.getPropertyValue("tensionY")},set:function(e){this.setPropertyValue("tensionY",e,!0)},enumerable:!0,configurable:!0}),t.prototype.createLegendMarker=function(e){var t=this,i=e.pixelWidth,a=e.pixelHeight;e.disposeChildren();var n=e.createChild(J.a);if(n.shouldClone=!1,V.copyProperties(this,n,P.b),n.x2=i,n.y=a/2,n.visible=!0,this.fillOpacity>0){var s=e.createChild($.a);V.copyProperties(this,s,P.b),s.width=i,s.height=a,s.y=0,s.strokeOpacity=0,s.visible=!0,n.y=0}m.eachContinue(this.bullets.iterator(),function(n){if(n.copyToLegendMarker){var s=!1;if(m.each(n.children.iterator(),function(e){if(e instanceof Q.a)return s=!0,!0}),!s){var r=n.clone();return r.parent=e,r.isMeasured=!0,r.tooltipText=void 0,r.x=i/2,t.fillOpacity>0?r.y=0:r.y=a/2,r.visible=!0,f.hasValue(r.fill)||(r.fill=t.fill),f.hasValue(r.stroke)||(r.stroke=t.stroke),!1}}})},t.prototype.disposeData=function(){e.prototype.disposeData.call(this),this.segments.clear()},t}(L);d.b.registeredClasses.LineSeries=te,d.b.registeredClasses.LineSeriesDataItem=ee;var ie=function(e){function t(){var t=e.call(this)||this;return t.className="RadarSeriesDataItem",t.setLocation("dateX",0,0),t.setLocation("dateY",0,0),t.setLocation("categoryX",0,0),t.setLocation("categoryY",0,0),t.applyTheme(),t}return n.c(t,e),t}(ee),ae=function(e){function t(){var t=e.call(this)||this;return t.className="RadarSeries",t.connectEnds=!0,t.applyTheme(),t}return n.c(t,e),t.prototype.validate=function(){this.chart.invalid&&this.chart.validate(),e.prototype.validate.call(this)},t.prototype.createDataItem=function(){return new ie},t.prototype.getPoint=function(e,t,i,a,n,s,r){s||(s="valueX"),r||(r="valueY");var o=this.yAxis.getX(e,i,n,r),l=this.yAxis.getY(e,i,n,r),h=g.getDistance({x:o,y:l});0==h&&(h=1e-5);var u=this.xAxis.getAngle(e,t,a,s),d=this.chart.startAngle,c=this.chart.endAngle;return u<d||u>c?void 0:{x:h*g.cos(u),y:h*g.sin(u)}},t.prototype.addPoints=function(e,t,i,a,n){var s=this.getPoint(t,i,a,t.locations[i],t.locations[a]);s&&e.push(s)},t.prototype.getMaskPath=function(){var e=this.yAxis.renderer;return k.arc(e.startAngle,e.endAngle-e.startAngle,e.pixelRadius,e.pixelInnerRadius)},t.prototype.drawSegment=function(t,i,a){var n=this.yAxis.renderer;this.connectEnds&&360==Math.abs(n.endAngle-n.startAngle)&&(this.dataFields[this._xOpenField]||this.dataFields[this._yOpenField]||this.stacked)&&(i.push(i[0]),a.length>0&&a.unshift(a[a.length-1])),e.prototype.drawSegment.call(this,t,i,a)},Object.defineProperty(t.prototype,"connectEnds",{get:function(){return this.getPropertyValue("connectEnds")},set:function(e){this.setPropertyValue("connectEnds",e,!0)},enumerable:!0,configurable:!0}),t}(te);d.b.registeredClasses.RadarSeries=ae,d.b.registeredClasses.RadarSeriesDataItem=ie;var ne=i("FzPm"),se=function(e){function t(){var t=e.call(this)||this;return t.className="RadarCursor",t.radius=Object(Y.c)(100),t.innerRadius=Object(Y.c)(0),t.applyTheme(),t.mask=void 0,t}return n.c(t,e),t.prototype.fitsToBounds=function(e){var t=g.getDistance(e);g.getAngle(e);return t<this.truePixelRadius+1&&t>this.pixelInnerRadius-1},Object.defineProperty(t.prototype,"startAngle",{get:function(){return this.getPropertyValue("startAngle")},set:function(e){this.setPropertyValue("startAngle",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endAngle",{get:function(){return this.getPropertyValue("endAngle")},set:function(e){this.setPropertyValue("endAngle",e,!0)},enumerable:!0,configurable:!0}),t.prototype.triggerMoveReal=function(t){this.xAxis&&(!this.xAxis||this.xAxis.cursorTooltipEnabled&&!this.xAxis.tooltip.disabled)||this.updateLineX(this.point),this.yAxis&&(!this.yAxis||this.yAxis.cursorTooltipEnabled&&!this.yAxis.tooltip.disabled)||this.updateLineY(this.point),this.updateSelection(),e.prototype.triggerMoveReal.call(this,t)},t.prototype.updateLineX=function(e){var t=this.pixelRadius,i=this.startAngle,a=this.endAngle,n=this.pixelInnerRadius;if(t>0&&f.isNumber(i)&&f.isNumber(a)&&f.isNumber(n)){var s=g.fitAngleToRange(g.getAngle(e),i,a),r=void 0;if(this.lineX&&this.lineX.visible){if(this.lineX.moveTo({x:0,y:0}),this.xAxis&&this.fullWidthLineX){var o=this.xAxis.currentItemStartPoint,l=this.xAxis.currentItemEndPoint;if(o&&l){var h=g.fitAngleToRange(g.getAngle(o),i,a),u=g.fitAngleToRange(g.getAngle(l),i,a)-h;i<a?u<0&&(u+=360):u>0&&(u-=360),s-=u/2,r=k.moveTo({x:n*g.cos(s),y:n*g.sin(s)})+k.lineTo({x:t*g.cos(s),y:t*g.sin(s)})+k.arcTo(s,u,t)+k.lineTo({x:n*g.cos(s+u),y:n*g.sin(s+u)})+k.arcTo(s+u,-u,n)}}r||(r=k.moveTo({x:n*g.cos(s),y:n*g.sin(s)})+k.lineTo({x:t*g.cos(s),y:t*g.sin(s)})),this.lineX.path=r}}},t.prototype.updateLineY=function(e){if(this.lineY&&this.lineY.visible){var t=this.startAngle,i=this.endAngle,a=this.truePixelRadius,n=g.fitToRange(g.getDistance(e),0,this.truePixelRadius);if(f.isNumber(n)&&f.isNumber(t)){this.lineY.moveTo({x:0,y:0});var s=void 0,r=i-t;if(this.yAxis&&this.fullWidthLineY){var o=this.yAxis.currentItemStartPoint,l=this.yAxis.currentItemEndPoint;if(o&&l){var h=g.fitToRange(g.getDistance(o),0,a);n=g.fitToRange(g.getDistance(l),0,a),s=k.moveTo({x:n*g.cos(t),y:n*g.sin(t)})+k.arcTo(t,r,n),s+=k.moveTo({x:h*g.cos(i),y:h*g.sin(i)})+k.arcTo(i,-r,h)}}s||(s=k.moveTo({x:n*g.cos(t),y:n*g.sin(t)})+k.arcTo(t,i-t,n)),this.lineY.path=s}}},t.prototype.updateSelection=function(){if(this._usesSelection){var e=this.downPoint;if(e){var t=this.point,i=this.pixelRadius,a=this.truePixelRadius,n=this.pixelInnerRadius,s=Math.min(this.startAngle,this.endAngle),r=Math.max(this.startAngle,this.endAngle),o=g.fitAngleToRange(g.getAngle(e),s,r),l=g.fitAngleToRange(g.getAngle(t),s,r),h=g.getDistance(e);if(h<a){var u=g.fitToRange(g.getDistance(t),0,a);this._prevAngle=l;var d=k.moveTo({x:0,y:0}),c=g.sin(o),p=g.cos(o),y=g.sin(l),f=g.cos(l),m=this.behavior;"zoomX"==m||"selectX"==m?d+=k.lineTo({x:i*p,y:i*c})+k.arcTo(o,l-o,i)+k.lineTo({x:n*f,y:n*y})+k.arcTo(l,o-l,n):"zoomY"==m||"selectY"==m?d=k.moveTo({x:u*g.cos(s),y:u*g.sin(s)})+k.arcTo(s,r-s,u)+k.lineTo({x:h*g.cos(r),y:h*g.sin(r)})+k.arcTo(r,s-r,h)+k.closePath():"zoomXY"==m&&(d=k.moveTo({x:u*g.cos(o),y:u*g.sin(o)})+k.arcTo(o,l-o,u)+k.lineTo({x:h*g.cos(l),y:h*g.sin(l)})+k.arcTo(l,o-l,h)+k.closePath()),this.selection.path=d}this.selection.moveTo({x:0,y:0})}}},t.prototype.getPositions=function(){if(this.chart){var e=this.pixelInnerRadius,t=this.truePixelRadius-e,i=this.startAngle,a=this.endAngle,n=(g.fitAngleToRange(g.getAngle(this.point),i,a)-i)/(a-i);this.xPosition=n,this.yPosition=g.fitToRange((g.getDistance(this.point)-e)/t,0,1)}},t.prototype.updatePoint=function(e){},t.prototype.handleXTooltipPosition=function(e){if(this.xAxis.cursorTooltipEnabled){var t=this.xAxis.tooltip;this.updateLineX(F.svgPointToSprite({x:t.pixelX,y:t.pixelY},this))}},t.prototype.handleYTooltipPosition=function(e){if(this.yAxis.cursorTooltipEnabled){var t=this.yAxis.tooltip;this.updateLineY(F.svgPointToSprite({x:t.pixelX,y:t.pixelY},this))}},t.prototype.updateLinePositions=function(e){},t.prototype.getRanges=function(){var e=this.downPoint;if(e){var t=this.upPoint;if(this.chart){var i=this.pixelRadius,a=this.startAngle,n=this.endAngle,s=g.fitAngleToRange(g.getAngle(e),this.startAngle,this.endAngle),r=g.fitAngleToRange(g.getAngle(t),this.startAngle,this.endAngle),o=g.fitToRange(g.getDistance(e),0,i),l=g.fitToRange(g.getDistance(t),0,i),h=0,u=1,d=0,c=1,p=this.behavior;if("zoomX"==p||"selectX"==p||"zoomXY"==p||"selectXY"==p){var y=n-a;h=g.round((s-a)/y,5),u=g.round((r-a)/y,5)}"zoomY"!=p&&"selectY"!=p&&"zoomXY"!=p&&"selectXY"!=p||(d=g.round(o/i,5),c=g.round(l/i,5)),this.xRange={start:Math.min(h,u),end:Math.max(h,u)},this.yRange={start:Math.min(d,c),end:Math.max(d,c)},"selectX"==this.behavior||"selectY"==this.behavior||"selectXY"==this.behavior||this.selection.hide()}}},t.prototype.updateSize=function(){},Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!1,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"pixelRadius",{get:function(){return F.relativeRadiusToValue(this.radius,this.truePixelRadius)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"truePixelRadius",{get:function(){return F.relativeToValue(Object(Y.c)(100),g.min(this.innerWidth/2,this.innerHeight/2))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(e){this.setPercentProperty("innerRadius",e,!1,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"pixelInnerRadius",{get:function(){var e=this.innerRadius;return e instanceof Y.a&&(e=Object(Y.c)(100*e.value*this.chart.innerRadiusModifyer)),F.relativeRadiusToValue(e,this.truePixelRadius)||0},enumerable:!0,configurable:!0}),t.prototype.fixPoint=function(e){return e},t}(W);d.b.registeredClasses.RadarCursor=se;var re=i("2zgF"),oe=i("Vk33"),le=function(e){function t(){var t=e.call(this)||this;return t._chart=new _.d,t.pixelRadiusReal=0,t.className="AxisRendererRadial",t.isMeasured=!1,t.startAngle=-90,t.endAngle=270,t.minGridDistance=30,t.gridType="circles",t.axisAngle=-90,t.isMeasured=!1,t.layout="none",t.radius=Object(Y.c)(100),t.line.strokeOpacity=0,t.labels.template.horizontalCenter="middle",t._disposers.push(t._chart),t.applyTheme(),t}return n.c(t,e),t.prototype.validate=function(){this.chart&&this.chart.invalid&&this.chart.validate(),e.prototype.validate.call(this)},Object.defineProperty(t.prototype,"axisLength",{get:function(){return this.pixelRadius-this.pixelInnerRadius},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!1,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"pixelRadius",{get:function(){return F.relativeRadiusToValue(this.radius,this.pixelRadiusReal)||0},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(e){this.setPercentProperty("innerRadius",e,!1,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"pixelInnerRadius",{get:function(){return F.relativeRadiusToValue(this.innerRadius,this.pixelRadiusReal)||0},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"chart",{get:function(){return this._chart.get()},set:function(e){this._chart.set(e,null)},enumerable:!0,configurable:!0}),t.prototype.positionToPoint=function(e){var t=g.fitToRange(this.positionToCoordinate(e),0,1/0);return{x:t*g.cos(this.axisAngle),y:t*g.sin(this.axisAngle)}},t.prototype.updateAxisLine=function(){this.line.path=k.moveTo({x:this.pixelInnerRadius*g.cos(this.axisAngle),y:this.pixelInnerRadius*g.sin(this.axisAngle)})+k.lineTo({x:this.pixelRadius*g.cos(this.axisAngle),y:this.pixelRadius*g.sin(this.axisAngle)});var e=this.axis.title;e.valign="none",e.horizontalCenter="middle",e.verticalCenter="bottom",e.y=-this.axisLength/2;var t=90;this.opposite?this.inside||(t=-90):this.inside&&(t=-90),e.rotation=t},t.prototype.updateGridElement=function(e,t,i){t+=(i-t)*e.location;var a,n=this.positionToPoint(t),s=g.getDistance(n),r=this.startAngle,o=this.endAngle;if(f.isNumber(s)&&e.element){var l=this.chart,h=l.xAxes.getIndex(0),u=l.dataItems.length,d=l.series.getIndex(0);if("polygons"==this.gridType&&u>0&&d&&h&&h instanceof b){var c=h.renderer.grid.template.location,p=h.getAngle(d.dataItems.getIndex(0),"categoryX",c);a=k.moveTo({x:s*g.cos(p),y:s*g.sin(p)});for(var y=l.dataItems.length,m=1;m<y;m++)p=h.getAngle(d.dataItems.getIndex(m),"categoryX",c),a+=k.lineTo({x:s*g.cos(p),y:s*g.sin(p)});p=h.getAngle(d.dataItems.getIndex(y-1),"categoryX",h.renderer.cellEndLocation),a+=k.lineTo({x:s*g.cos(p),y:s*g.sin(p)})}else a=k.moveTo({x:s*g.cos(r),y:s*g.sin(r)})+k.arcTo(r,o-r,s,s);e.path=a}this.toggleVisibility(e,t,0,1)},t.prototype.updateLabelElement=function(e,t,i,a){f.hasValue(a)||(a=e.location),t+=(i-t)*a;var n=this.positionToPoint(t);this.positionItem(e,n),this.toggleVisibility(e,t,this.minLabelPosition,this.maxLabelPosition)},t.prototype.updateBaseGridElement=function(){},t.prototype.fitsToBounds=function(e){return!0},Object.defineProperty(t.prototype,"startAngle",{get:function(){return this.getPropertyValue("startAngle")},set:function(e){this.setPropertyValue("startAngle",e)&&this.invalidateAxisItems()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endAngle",{get:function(){return this.getPropertyValue("endAngle")},set:function(e){this.setPropertyValue("endAngle",e)&&this.invalidateAxisItems()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"axisAngle",{get:function(){return this.getPropertyValue("axisAngle")},set:function(e){this.setPropertyValue("axisAngle",g.normalizeAngle(e)),this.invalidateAxisItems()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"gridType",{get:function(){return this.chart.xAxes.getIndex(0)instanceof b?this.getPropertyValue("gridType"):"circles"},set:function(e){this.setPropertyValue("gridType",e,!0)},enumerable:!0,configurable:!0}),t.prototype.getPositionRangePath=function(e,t){var i,a=this.pixelInnerRadius,n=this.axisLength+a,s=g.fitToRange(this.positionToCoordinate(e),a,n),r=g.fitToRange(this.positionToCoordinate(t),a,n),o=this.startAngle,l=this.endAngle-o,h=this.chart,u=h.xAxes.getIndex(0),d=h.dataItems.length,c=h.series.getIndex(0);if("polygons"==this.gridType&&d>0&&c&&u&&u instanceof b){var p=u.renderer.grid.template.location,y=u.getAngle(c.dataItems.getIndex(0),"categoryX",p);i=k.moveTo({x:r*g.cos(y),y:r*g.sin(y)});for(var f=h.dataItems.length,m=1;m<f;m++)y=u.getAngle(c.dataItems.getIndex(m),"categoryX",p),i+=k.lineTo({x:r*g.cos(y),y:r*g.sin(y)});y=u.getAngle(c.dataItems.getIndex(f-1),"categoryX",u.renderer.cellEndLocation),i+=k.lineTo({x:r*g.cos(y),y:r*g.sin(y)}),i+=k.moveTo({x:s*g.cos(y),y:s*g.sin(y)});for(m=f-1;m>=0;m--)y=u.getAngle(c.dataItems.getIndex(m),"categoryX",p),i+=k.lineTo({x:s*g.cos(y),y:s*g.sin(y)})}else i=k.arc(o,l,r,s);return i},t.prototype.updateBreakElement=function(e){var t=e.startLine,i=e.endLine,a=e.fillShape,n=e.startPoint,s=e.endPoint;t.radius=Math.abs(n.y),i.radius=Math.abs(s.y),a.radius=Math.abs(s.y),a.innerRadius=Math.abs(n.y)},t.prototype.createBreakSprites=function(e){e.startLine=new oe.a,e.endLine=new oe.a,e.fillShape=new oe.a},t.prototype.updateTooltip=function(){if(this.axis){var e=this.axisAngle;e<0&&(e+=360);var t="vertical";(e>45&&e<135||e>225&&e<315)&&(t="horizontal"),this.axis.updateTooltip(t,{x:-4e3,y:-4e3,width:8e3,height:8e3})}},t.prototype.updateTickElement=function(e,t){var i=this.positionToPoint(t);if(e.element){var a=g.normalizeAngle(this.axisAngle+90);a/90!=Math.round(a/90)?e.pixelPerfect=!1:e.pixelPerfect=!0;var n=-e.length;e.inside&&(n*=-1),e.path=k.moveTo({x:0,y:0})+k.lineTo({x:n*g.cos(a),y:n*g.sin(a)})}this.positionItem(e,i),this.toggleVisibility(e,t,0,1)},t.prototype.positionToCoordinate=function(e){var t,i=this.axis,a=i.axisFullLength,n=this.pixelInnerRadius;return t=i.renderer.inversed?(i.end-e)*a+n:(e-i.start)*a+n,g.round(t,1)},t.prototype.pointToPosition=function(e){var t=g.getDistance(e)-this.pixelInnerRadius;return this.coordinateToPosition(t)},t}(h.a);d.b.registeredClasses.AxisRendererRadial=le;var he=function(e){function t(){var t=e.call(this)||this;return t.className="RadarChartDataItem",t.applyTheme(),t}return n.c(t,e),t}(K),ue=function(e){function t(){var t=e.call(this)||this;t._axisRendererX=re.a,t._axisRendererY=le,t.innerRadiusModifyer=1,t.className="RadarChart",t.startAngle=-90,t.endAngle=270,t.radius=Object(Y.c)(80),t.innerRadius=0;var i=t.plotContainer.createChild(r.a);return i.shouldClone=!1,i.layout="absolute",i.align="center",i.valign="middle",t.seriesContainer.parent=i,t.radarContainer=i,t.bulletsContainer.parent=i,t._cursorContainer=i,t._bulletMask=i.createChild(ne.a),t._bulletMask.shouldClone=!1,t._bulletMask.element=t.paper.add("path"),t._bulletMask.opacity=0,t.applyTheme(),t}return n.c(t,e),t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),f.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Radar chart"))},t.prototype.processAxis=function(t){e.prototype.processAxis.call(this,t);var i=t.renderer;i.gridContainer.parent=i,i.breakContainer.parent=i,t.parent=this.radarContainer,i.toBack()},t.prototype.handleXAxisRangeChange=function(){e.prototype.handleXAxisRangeChange.call(this),m.each(this.yAxes.iterator(),function(e){e.invalidate()})},t.prototype.handleYAxisRangeChange=function(){e.prototype.handleYAxisRangeChange.call(this),m.each(this.xAxes.iterator(),function(e){e.invalidate()})},t.prototype.createCursor=function(){return new se},t.prototype.processConfig=function(t){if(t&&(f.hasValue(t.cursor)&&!f.hasValue(t.cursor.type)&&(t.cursor.type="RadarCursor"),f.hasValue(t.series)&&f.isArray(t.series)))for(var i=0,a=t.series.length;i<a;i++)t.series[i].type=t.series[i].type||"RadarSeries";e.prototype.processConfig.call(this,t)},t.prototype.beforeDraw=function(){e.prototype.beforeDraw.call(this);this.radarContainer;var t=this.plotContainer,i=g.getArcRect(this.startAngle,this.endAngle,1),a={x:0,y:0,width:0,height:0},n=t.innerWidth/i.width,s=t.innerHeight/i.height,r=this.innerRadius;if(r instanceof Y.a){var o=r.value,l=Math.min(n,s);o=Math.max(l*o,l-Math.min(t.innerHeight,t.innerWidth))/l,a=g.getArcRect(this.startAngle,this.endAngle,o),this.innerRadiusModifyer=o/r.value,r=Object(Y.c)(100*o)}i=g.getCommonRectangle([i,a]);var h=Math.min(t.innerWidth/i.width,t.innerHeight/i.height),u=2*F.relativeRadiusToValue(this.radius,h)||0,d=u/2,c=this.startAngle,p=this.endAngle;this._pixelInnerRadius=F.relativeRadiusToValue(r,d),this._bulletMask.path=k.arc(c,p-c,d,this._pixelInnerRadius),m.each(this.xAxes.iterator(),function(e){e.renderer.useChartAngles&&(e.renderer.startAngle=c,e.renderer.endAngle=p),e.width=u,e.height=u,e.renderer.pixelRadiusReal=d,e.renderer.innerRadius=r}),m.each(this.yAxes.iterator(),function(e){e.renderer.startAngle=c,e.renderer.endAngle=p,e.width=u,e.height=u,e.renderer.pixelRadiusReal=d,e.renderer.innerRadius=r});var y=this.cursor;y&&(y.width=u,y.height=u,y.startAngle=c,y.endAngle=p),this.radarContainer.definedBBox={x:d*i.x,y:d*i.y,width:d*i.width,height:d*i.height},this.radarContainer.validatePosition()},t.prototype.createSeries=function(){return new ae},Object.defineProperty(t.prototype,"startAngle",{get:function(){return this.getPropertyValue("startAngle")},set:function(e){this.setPropertyValue("startAngle",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endAngle",{get:function(){return this.getPropertyValue("endAngle")},set:function(e){this.setPropertyValue("endAngle",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"pixelInnerRadius",{get:function(){return this._pixelInnerRadius},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(e){this.setPercentProperty("innerRadius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),t.prototype.updateXAxis=function(e){e&&e.processRenderer()},t.prototype.updateYAxis=function(e){e&&e.processRenderer()},t}(G);d.b.registeredClasses.RadarChart=ue;var de=i("DziZ"),ce=function(e){function t(){var t=e.call(this)||this;t._axis=new _.d,t.className="ClockHand";var i=new B.a;t.fill=i.getFor("alternativeBackground"),t.stroke=t.fill;var a=new ne.a;a.radius=5,t.pin=a,t.isMeasured=!1,t.startWidth=5,t.endWidth=1,t.width=Object(Y.c)(100),t.height=Object(Y.c)(100),t.radius=Object(Y.c)(100),t.innerRadius=Object(Y.c)(0);var n=new de.a;return t.hand=n,t._disposers.push(t._axis),t.applyTheme(),t}return n.c(t,e),t.prototype.validate=function(){e.prototype.validate.call(this);var t=this.hand;t.width=this.pixelWidth;var i=Math.max(this.startWidth,this.endWidth);if(t.height=i,t.leftSide=Object(Y.c)(this.startWidth/i*100),t.rightSide=Object(Y.c)(this.endWidth/i*100),this.axis){var a=this.axis.renderer,n=F.relativeRadiusToValue(this.innerRadius,a.pixelRadius),s=F.relativeRadiusToValue(this.radius,a.pixelRadius);t.x=n,t.y=-i/2,t.width=s-n}},Object.defineProperty(t.prototype,"pin",{get:function(){return this._pin},set:function(e){this._pin&&this.removeDispose(this._pin),e&&(this._pin=e,e.parent=this,this._disposers.push(e))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"hand",{get:function(){return this._hand},set:function(e){this._hand&&this.removeDispose(this._hand),e&&(this._hand=e,e.parent=this,this._disposers.push(e))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(e){this.setPercentProperty("innerRadius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"startWidth",{get:function(){return this.getPropertyValue("startWidth")},set:function(e){this.setPropertyValue("startWidth",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endWidth",{get:function(){return this.getPropertyValue("endWidth")},set:function(e){this.setPropertyValue("endWidth",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"rotationDirection",{get:function(){return this.getPropertyValue("rotationDirection")},set:function(e){this.setPropertyValue("rotationDirection",e)},enumerable:!0,configurable:!0}),t.prototype.showValue=function(e,t,i){if(this._value=e,void 0!=e&&(f.isNumber(t)||(t=0),this.axis)){var a=this.axis.renderer.positionToAngle(this.axis.anyToPosition(e)),n=this.rotation;"clockWise"==this.rotationDirection&&a<n&&(this.rotation=n-360),"counterClockWise"==this.rotationDirection&&a>n&&(this.rotation=n+360),this.animate({property:"rotation",to:a},t,i)}},Object.defineProperty(t.prototype,"value",{get:function(){return this._value},set:function(e){this.showValue(e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"axis",{get:function(){return this._axis.get()},set:function(e){if(this.axis!=e&&this._axis.set(e,new _.c([e.events.on("datavalidated",this.updateValue,this,!1),e.events.on("datarangechanged",this.updateValue,this,!1),e.events.on("dataitemsvalidated",this.updateValue,this,!1),e.events.on("propertychanged",this.invalidate,this,!1)])),e){var t=e.chart;t&&(this.rotation=t.startAngle)}this.parent=e.renderer,this.zIndex=5},enumerable:!0,configurable:!0}),t.prototype.updateValue=function(){this.value=this.value},t.prototype.processConfig=function(t){t&&f.hasValue(t.axis)&&f.isString(t.axis)&&this.map.hasKey(t.axis)&&(t.axis=this.map.getKey(t.axis)),e.prototype.processConfig.call(this,t)},t}(r.a);d.b.registeredClasses.ClockHand=ce;var pe=function(e){function t(){var t=e.call(this)||this;return t.className="GaugeChartDataItem",t.applyTheme(),t}return n.c(t,e),t}(he),ye=function(e){function t(){var t=e.call(this)||this;return t.className="GaugeChart",t.startAngle=180,t.endAngle=360,t.hands=new o.e(new ce),t.hands.events.on("inserted",t.processHand,t,!1),t._disposers.push(new o.c(t.hands)),t._disposers.push(t.hands.template),t.applyTheme(),t}return n.c(t,e),t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),f.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Gauge chart"))},t.prototype.processHand=function(e){var t=e.newValue;t.axis||(t.axis=this.xAxes.getIndex(0))},t}(ue);d.b.registeredClasses.GaugeChart=ye;var ge=i("AC2I"),fe=function(e){function t(){var t=e.call(this)||this;return t.className="PercentChartDataItem",t.applyTheme(),t}return n.c(t,e),t}(s.b),me=function(e){function t(){var t=e.call(this)||this;return t.className="PercentChart",t.align="none",t.valign="none",t.chartContainer.minHeight=50,t.chartContainer.minWidth=50,t.applyTheme(),t}return n.c(t,e),t.prototype.validateData=function(){e.prototype.validateData.call(this),this.feedLegend()},t.prototype.feedLegend=function(){var e=this.legend;if(e){var t=[];m.each(this.series.iterator(),function(i){m.each(i.dataItems.iterator(),function(a){if(!a.hiddenInLegend){t.push(a);var n=i.legendSettings;n&&(n.labelText&&(e.labels.template.text=n.labelText),n.itemLabelText&&(e.labels.template.text=n.itemLabelText),n.valueText&&(e.valueLabels.template.text=n.valueText),n.itemValueText&&(e.valueLabels.template.text=n.itemValueText))}})}),e.data=t,e.dataFields.name="category"}},t.prototype.createSeries=function(){return new ge.a},t.prototype.setLegend=function(t){e.prototype.setLegend.call(this,t),t&&(t.labels.template.text="{category}",t.valueLabels.template.text="{value.percent.formatNumber('#.0')}%",t.itemContainers.template.events.on("over",function(e){var t=e.target.dataItem.dataContext;t.visible&&!t.isHiding&&(t.slice.isHover=!0)}),t.itemContainers.template.events.on("out",function(e){e.target.dataItem.dataContext.slice.isHover=!1}))},t}(s.a);d.b.registeredClasses.PercentChart=me,d.b.registeredClasses.PercentChartDataItem=fe;var xe=i("Inf5"),ve=i("IbTV"),be=i("qzbU"),Ae=function(e){function t(){var t=e.call(this)||this;return t._label=new _.d,t._slice=new _.d,t.className="PieTick",t.element=t.paper.add("polyline"),t._disposers.push(t._label),t._disposers.push(t._slice),t.applyTheme(),t}return n.c(t,e),t.prototype.draw=function(){e.prototype.draw.call(this);var t=this.slice,i=this.label,a=t.dataItem.component;if(t&&t.radius>0&&i&&i.text){var n=t.dx+t.slice.dx+t.pixelX+t.ix*t.radius*t.scale,s=t.dy+t.slice.dy+t.pixelY+t.iy*t.radius*t.scale,r=void 0,o=void 0,l=void 0,h=void 0;if(a.alignLabels)r=i.pixelX-this.length,o=i.pixelY,l=i.pixelX,h=o,"right"==i.horizontalCenter&&(l=(r+=2*this.length)-this.length);else{var u=i.pixelRadius(t.radius);l=r=n+u*t.ix,h=o=s+u*t.iy}this.element.attr({points:[n,s,r,o,l,h]})}},Object.defineProperty(t.prototype,"slice",{get:function(){return this._slice.get()},set:function(e){this._slice.set(e,new _.c([e.events.on("transformed",this.invalidate,this),e.events.on("validated",this.invalidate,this)]))},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"label",{get:function(){return this._label.get()},set:function(e){this._label.set(e,e.events.on("transformed",this.invalidate,this,!1))},enumerable:!0,configurable:!0}),t}(be.a);d.b.registeredClasses.PieTick=Ae;var Pe=function(e){function t(){var t=e.call(this)||this;return t.className="PieSeriesDataItem",t.values.radiusValue={},t.applyTheme(),t}return n.c(t,e),Object.defineProperty(t.prototype,"radiusValue",{get:function(){return this.values.radiusValue.value},set:function(e){this.setValue("radiusValue",e)},enumerable:!0,configurable:!0}),t.prototype.hide=function(t,i,a,n){return e.prototype.hide.call(this,t,i,0,["value","radiusValue"])},t.prototype.show=function(t,i,a){return e.prototype.show.call(this,t,i,["value","radiusValue"])},t}(ge.b),Ce=function(e){function t(){var t=e.call(this)||this;return t.className="PieSeries",t.alignLabels=!0,t.startAngle=-90,t.endAngle=270,t.labels.template.radius=Object(Y.c)(5),t.applyTheme(),t}return n.c(t,e),t.prototype.createSlice=function(){return new xe.a},t.prototype.createTick=function(){return new Ae},t.prototype.createLabel=function(){return new ve.a},t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),f.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Pie Slice Series"))},t.prototype.createDataItem=function(){return new Pe},t.prototype.initSlice=function(e){e.isMeasured=!1,e.defaultState.properties.scale=1,e.observe("scale",this.handleSliceScale,this),e.observe(["dx","dy","x","y","shiftRadius"],this.handleSliceMove,this),e.tooltipText="{category}: {value.percent.formatNumber('#.#')}% ({value.value})",e.states.create("hover").properties.scale=1.05,e.defaultState.properties.shiftRadius=0,e.togglable=!0,e.events.on("toggled",function(e){e.target.hideTooltip()}),e.states.create("active").properties.shiftRadius=.1},t.prototype.validate=function(){this._leftItems=[],this._rightItems=[],this._currentStartAngle=this.startAngle,this._arcRect=g.getArcRect(this.startAngle,this.endAngle),this._maxRadiusPercent=0;for(var t=this.startIndex;t<this.endIndex;t++){var i=this.dataItems.getIndex(t).values.radiusValue.percent;i>this._maxRadiusPercent&&(this._maxRadiusPercent=i)}e.prototype.validate.call(this),this.alignLabels&&(this.startAngle>this.endAngle?this._rightItems.reverse():this._leftItems.reverse(),this._rightItems.sort(function(e,t){var i=(e.slice.middleAngle+360)%360,a=(t.slice.middleAngle+360)%360;return i>270&&(i-=360),a>270&&(a-=360),i<a?-1:1}),this._leftItems.sort(function(e,t){return(e.slice.middleAngle+360)%360<(t.slice.middleAngle+360)%360?1:-1}),this.arrangeLabels(this._rightItems),this.arrangeLabels(this._leftItems))},t.prototype.validateDataElement=function(t){if(this.pixelRadius>0){t.values.value.percent;var i=t.slice;i.radius=this.pixelRadius,f.isNumber(t.radiusValue)&&(i.radius=this.pixelInnerRadius+(this.pixelRadius-this.pixelInnerRadius)*t.values.radiusValue.percent/this._maxRadiusPercent),i.innerRadius instanceof Y.a||(i.innerRadius=this.pixelInnerRadius),i.startAngle=this._currentStartAngle,i.arc=t.values.value.percent*(this.endAngle-this.startAngle)/100;var a=t.label,n=t.tick;n.slice=i,n.label=a;var s=(i.middleAngle+360)%360,r=void 0;if(this.alignLabels){var o=a.pixelRadius(i.radius),l=n.length+o;a.dx=0,a.dy=0,a.verticalCenter="middle";var h=this._arcRect;s>=270||s<=90?(l+=(h.width+h.x)*this.pixelRadius,a.horizontalCenter="left",this._rightItems.push(t)):(l-=h.x*this.pixelRadius,a.horizontalCenter="right",this._leftItems.push(t),l*=-1);var u=i.radius+n.length+o;r={x:l,y:i.iy*u}}else{a.horizontalCenter=void 0,a.verticalCenter=void 0;l=i.ix*i.radius;var d=i.iy*i.radiusY;r=a.fixPoint({x:l,y:d},i.radius,i.radiusY)}a.moveTo(r),this._currentStartAngle+=i.arc,e.prototype.validateDataElement.call(this,t)}},Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!0,!1,10,!1)&&this.invalidateDataItems()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"pixelRadius",{get:function(){return this._pixelRadius},set:function(e){this._pixelRadius!=e&&(this._pixelRadius=e,this.invalidateDataItems())},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"pixelInnerRadius",{get:function(){return this._pixelInnerRadius},set:function(e){this._pixelInnerRadius!=e&&(this._pixelInnerRadius=e,this.invalidateDataItems())},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(e){this.setPercentProperty("innerRadius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"startAngle",{get:function(){return this.getPropertyValue("startAngle")},set:function(e){this.setPropertyValue("startAngle",g.normalizeAngle(e),!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endAngle",{get:function(){return this.getPropertyValue("endAngle")},set:function(e){this.setPropertyValue("endAngle",e,!0)},enumerable:!0,configurable:!0}),t.prototype.positionBullet=function(t){e.prototype.positionBullet.call(this,t);var i=t.dataItem.slice,a=t.locationX;f.isNumber(a)||(a=.5);var n=t.locationY;f.isNumber(n)||(n=1);var s=i.startAngle+i.arc*a,r=n*i.radius;t.x=r*g.cos(s),t.y=r*g.sin(s)},t.prototype.handleSliceMove=function(e){if(!this.alignLabels){var t=e.target,i=t.dataItem;if(i){var a=i.label;a&&(a.dx=a.fdx+t.dx+t.pixelX,a.dy=a.fdy+t.dy+t.pixelY)}}},Object.defineProperty(t.prototype,"bbox",{get:function(){if(this.definedBBox)return this.definedBBox;var e=this.chart;return e?g.getArcRect(e.startAngle,e.endAngle,this.pixelRadius):g.getArcRect(this.startAngle,this.endAngle,this.pixelRadius)},enumerable:!0,configurable:!0}),t}(ge.a);d.b.registeredClasses.PieSeries=Ce,d.b.registeredClasses.PieSeriesDataItem=Pe;var _e=function(e){function t(){var t=e.call(this)||this;return t.className="PieChartDataItem",t.applyTheme(),t}return n.c(t,e),t}(fe),Ie=function(e){function t(){var t=e.call(this)||this;t.className="PieChart",t.innerRadius=0,t.radius=Object(Y.c)(80),t.align="none",t.valign="none",t.startAngle=-90,t.endAngle=270;var i=t.seriesContainer;return i.isMeasured=!0,i.valign="middle",i.align="center",i.layout="absolute",i.width=void 0,i.height=void 0,t.chartContainer.minHeight=50,t.chartContainer.minWidth=50,t.chartContainer.events.on("maxsizechanged",t.updateRadius,t,!1),t.applyTheme(),t}return n.c(t,e),t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),f.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Pie chart"))},t.prototype.validateLayout=function(){e.prototype.validateLayout.call(this),this.updateRadius()},t.prototype.handleSeriesAdded=function(t){e.prototype.handleSeriesAdded.call(this,t),this.updateSeriesAngles()},t.prototype.updateSeriesAngles=function(){var e=this;this.series.each(function(t){t.startAngle=e.startAngle,t.endAngle=e.endAngle,t.defaultState.properties.startAngle=e.startAngle,t.defaultState.properties.endAngle=e.endAngle})},t.prototype.updateRadius=function(){var e=this.chartContainer,t=g.getArcRect(this.startAngle,this.endAngle,1),i={x:0,y:0,width:0,height:0},a=this.innerRadius;a instanceof Y.a&&(i=g.getArcRect(this.startAngle,this.endAngle,a.value)),t=g.getCommonRectangle([t,i]);var n=Math.min(e.innerWidth/t.width,e.innerHeight/t.height);f.isNumber(n)||(n=0);var s=F.relativeRadiusToValue(this.radius,n),r=F.relativeRadiusToValue(this.innerRadius,n),o=(s-r)/this.series.length;m.each(m.indexed(this.series.iterator()),function(e){var t=e[0],i=e[1],a=r+F.relativeRadiusToValue(i.radius,s-r),n=r+F.relativeRadiusToValue(i.innerRadius,s-r);f.isNumber(a)||(a=r+o*(t+1)),f.isNumber(n)||(n=r+o*t),i.pixelRadius=a,i.pixelInnerRadius=n}),this.seriesContainer.definedBBox={x:s*t.x,y:s*t.y,width:s*t.width,height:s*t.height},this.seriesContainer.invalidateLayout()},Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!0,!1,10,!1)&&this.invalidateLayout()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(e){this.setPercentProperty("innerRadius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),t.prototype.createSeries=function(){return new Ce},Object.defineProperty(t.prototype,"startAngle",{get:function(){return this.getPropertyValue("startAngle")},set:function(e){this.setPropertyValue("startAngle",e)&&(this.updateRadius(),this.updateSeriesAngles())},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endAngle",{get:function(){return this.getPropertyValue("endAngle")},set:function(e){this.setPropertyValue("endAngle",e)&&(this.updateRadius(),this.updateSeriesAngles())},enumerable:!0,configurable:!0}),t}(me);d.b.registeredClasses.PieChart=Ie,d.b.registeredClasses.PieChartDataItem=_e;var De=i("nPzZ"),Te=function(e){function t(){var t=e.call(this)||this;return t.className="PieSeries3DDataItem",t.values.depthValue={},t.applyTheme(),t}return n.c(t,e),Object.defineProperty(t.prototype,"depthValue",{get:function(){return this.values.depthValue.value},set:function(e){this.setValue("depthValue",e)},enumerable:!0,configurable:!0}),t}(Pe),Ve=function(e){function t(){var t=e.call(this)||this;return t.className="PieSeries3D",t.applyTheme(),t}return n.c(t,e),t.prototype.createDataItem=function(){return new Te},t.prototype.createSlice=function(){return new De.a},t.prototype.validateDataElement=function(t){var i=t.slice,a=this.depth;f.isNumber(a)||(a=this.chart.depth);var n=t.values.depthValue.percent;f.isNumber(n)||(n=100),i.depth=n*a/100;var s=this.angle;f.isNumber(s)||(s=this.chart.angle),i.angle=s,e.prototype.validateDataElement.call(this,t)},t.prototype.validate=function(){e.prototype.validate.call(this);for(var t=this._workingStartIndex;t<this._workingEndIndex;t++){var i=this.dataItems.getIndex(t).slice,a=i.startAngle;a>=-90&&a<90?i.toFront():a>=90&&i.toBack()}},Object.defineProperty(t.prototype,"depth",{get:function(){return this.getPropertyValue("depth")},set:function(e){this.setPropertyValue("depth",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"angle",{get:function(){return this.getPropertyValue("angle")},set:function(e){this.setPropertyValue("angle",e)},enumerable:!0,configurable:!0}),t}(Ce);d.b.registeredClasses.PieSeries3D=Ve,d.b.registeredClasses.PieSeries3DDataItem=Te;var Se=function(e){function t(){var t=e.call(this)||this;return t.className="PieChart3DDataItem",t.applyTheme(),t}return n.c(t,e),t}(_e),Re=function(e){function t(){var t=e.call(this)||this;return t.className="PieChart3D",t.depth=20,t.angle=10,t.applyTheme(),t}return n.c(t,e),Object.defineProperty(t.prototype,"depth",{get:function(){return this.getPropertyValue("depth")},set:function(e){this.setPropertyValue("depth",e)&&this.invalidateDataUsers()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"angle",{get:function(){return this.getPropertyValue("angle")},set:function(e){this.setPropertyValue("angle",e)&&this.invalidateDataUsers()},enumerable:!0,configurable:!0}),t.prototype.createSeries=function(){return new Ve},t}(Ie);d.b.registeredClasses.PieChart3D=Re;var Fe=function(e){function t(){var t=e.call(this)||this;return t.className="SlicedChartDataItem",t.applyTheme(),t}return n.c(t,e),t}(fe),Oe=function(e){function t(){var t=e.call(this)||this;return t.className="SlicedChart",t.seriesContainer.layout="horizontal",t.padding(15,15,15,15),t.applyTheme(),t}return n.c(t,e),t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),f.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Sliced chart"))},t.prototype.validate=function(){e.prototype.validate.call(this)},t}(me);d.b.registeredClasses.SlicedChart=Oe,d.b.registeredClasses.SlicedChartDataItem=Fe;var ke=i("Hg48"),we=i("ZpHf"),Le=i("eske"),Xe=i("TXRX"),Ye=function(e){function t(){var t=e.call(this)||this;t.className="ChordNode",t.label=t.createChild(ve.a),t.label.location=.5,t.label.radius=5,t.label.text="{name}",t.label.zIndex=1,t.label.shouldClone=!1,t.layout="none",t.events.on("positionchanged",t.updateRotation,t,!1),t.isMeasured=!1,t.slice=t.createChild(xe.a),t.slice.isMeasured=!1;var i=t.hiddenState;return i.properties.fill=(new B.a).getFor("disabledBackground"),i.properties.opacity=.5,i.properties.visible=!0,t.setStateOnChildren=!1,t.slice.hiddenState.properties.visible=!0,t.adapter.add("tooltipX",function(e,t){return t.slice.ix*(t.slice.radius-(t.slice.radius-t.slice.pixelInnerRadius)/2)}),t.adapter.add("tooltipY",function(e,t){return t.slice.iy*(t.slice.radius-(t.slice.radius-t.slice.pixelInnerRadius)/2)}),t}return n.c(t,e),t.prototype.invalidateLinks=function(){var t=this;e.prototype.invalidateLinks.call(this);var i=this.label,a=this.slice,n=this.chart;if(n&&a){var s=this.total,r=a.arc,o=a.startAngle;this.children.each(function(e){if(e instanceof Xe.a){var t=e.locationX;f.isNumber(t)||(t=.5);var i=e.locationY;f.isNumber(i)||(i=1);var n=o+r*t,s=i*a.radius;e.x=s*g.cos(n),e.y=s*g.sin(n)}});var l=o+r*i.location,h=o+(1-s/this.adjustedTotal)*r*.5;f.isNaN(h)&&(h=o);var u={x:a.radius*g.cos(l),y:a.radius*g.sin(l)};i.fixPoint(u,a.radius),i.moveTo(u),this.nextAngle=h,this._outgoingSorted&&m.each(this._outgoingSorted,function(e){var i=e.link;i.parent=t.chart.linksContainer;var s=e.getWorkingValue("value");if(f.isNumber(s)){if(n.nonRibbon){var l=i.percentWidth;f.isNumber(l)||(l=5),l/=100,i.startAngle=o+r/2-r/2*l,i.arc=r*l}else i.arc=s*n.valueAngle,i.startAngle=t.nextAngle,t.nextAngle+=i.arc;e.toNode||(i.endAngle=i.startAngle),i.radius=a.pixelInnerRadius}}),this._incomingSorted&&m.each(this._incomingSorted,function(e){var i=e.link;if(i.radius=a.pixelInnerRadius,n.nonRibbon){var s=i.percentWidth;f.isNumber(s)||(s=5),s/=100,i.endAngle=o+r/2-r/2*s,i.arc=r*s}else{i.endAngle=t.nextAngle;var l=e.getWorkingValue("value");f.isNumber(l)&&(i.arc=l*n.valueAngle,t.nextAngle+=i.arc)}e.fromNode||(i.startAngle=i.endAngle)})}},t.prototype.updateRotation=function(){var e=this.slice,t=this.trueStartAngle+e.arc/2,i=e.radius,a=i*g.cos(t),n=i*g.sin(t),s=g.getAngle({x:a+this.pixelX,y:n+this.pixelY});e.startAngle=this.trueStartAngle+(s-t),this.dx=-this.pixelX,this.dy=-this.pixelY},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.label.copyFrom(t.label),this.slice.copyFrom(t.slice)},t}(Le.a);d.b.registeredClasses.ChordNode=Ye;var je=i("s9al"),Ne=function(e){function t(){var t=e.call(this)||this;return t.className="QuadraticCurve",t.element=t.paper.add("path"),t.pixelPerfect=!1,t.fill=Object(M.c)(),t.applyTheme(),t}return n.c(t,e),t.prototype.draw=function(){if(f.isNumber(this.x1+this.x2+this.y1+this.y2+this.cpx+this.cpy)){var e={x:this.x1,y:this.y1},t={x:this.x2,y:this.y2},i={x:this.cpx,y:this.cpy},a=k.moveTo(e)+k.quadraticCurveTo(t,i);this.path=a}},Object.defineProperty(t.prototype,"cpx",{get:function(){return this.getPropertyValue("cpx")},set:function(e){this.setPropertyValue("cpx",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"cpy",{get:function(){return this.getPropertyValue("cpy")},set:function(e){this.setPropertyValue("cpy",e,!0)},enumerable:!0,configurable:!0}),t.prototype.positionToPoint=function(e){var t={x:this.x1,y:this.y1},i={x:this.cpx,y:this.cpy},a={x:this.x2,y:this.y2},n=g.getPointOnQuadraticCurve(t,a,i,e),s=g.getPointOnQuadraticCurve(t,a,i,e+.001);return{x:n.x,y:n.y,angle:g.getAngle(n,s)}},t}(J.a),Me=function(e){function t(){var t=e.call(this)||this;return t.className="ChordLink",t.middleLine=t.createChild(Ne),t.middleLine.shouldClone=!1,t.middleLine.strokeOpacity=0,t.applyTheme(),t}return n.c(t,e),t.prototype.validate=function(){if(e.prototype.validate.call(this),!this.isTemplate){var t=this.startAngle,i=this.endAngle,a=this.arc,n=this.radius,s=this.dataItem.fromNode,r=this.dataItem.toNode,o=0,l=0;s&&(o=s.pixelX+s.dx,l=s.pixelY+s.dy);var h=0,u=0;if(r&&(h=r.pixelX+r.dx,u=r.pixelY+r.dy),n>0){var d=n*g.cos(t)+o,c=n*g.sin(t)+l,p=n*g.cos(i)+h,y=n*g.sin(i)+u,f=(g.cos(i+a),g.sin(i+a),g.cos(t+a),g.sin(t+a),{x:0,y:0}),m=k.moveTo({x:d,y:c});m+=k.arcTo(t,a,n),m+=k.quadraticCurveTo({x:p,y:y},f),m+=k.arcTo(i,a,n),m+=k.quadraticCurveTo({x:d,y:c},f),this.link.path=a>0?m:"",this.maskBullets&&(this.bulletsMask.path=m,this.bulletsContainer.mask=this.bulletsMask);var x=t+a/2,v=i+a/2,b=this.middleLine;b.x1=n*g.cos(x)+o,b.y1=n*g.sin(x)+l,b.x2=n*g.cos(v)+h,b.y2=n*g.sin(v)+u,b.cpx=0,b.cpy=0,b.stroke=this.fill,this.positionBullets()}}},Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPropertyValue("radius",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"arc",{get:function(){return this.getPropertyValue("arc")},set:function(e){this.setPropertyValue("arc",e,!0)},enumerable:!0,configurable:!0}),t}(je.a);d.b.registeredClasses.ChordLink=Me;var Be=function(e){function t(){var t=e.call(this)||this;return t.className="ChordDiagramDataItem",t.applyTheme(),t}return n.c(t,e),t}(ke.b),We=function(e){function t(){var t=e.call(this)||this;t.valueAngle=0,t.className="ChordDiagram",t.startAngle=-90,t.endAngle=270,t.radius=Object(Y.c)(80),t.innerRadius=-15,t.nodePadding=5;var i=t.chartContainer.createChild(r.a);return i.align="center",i.valign="middle",i.shouldClone=!1,i.layout="absolute",t.chordContainer=i,t.nodesContainer.parent=i,t.linksContainer.parent=i,t.chartContainer.events.on("maxsizechanged",t.invalidate,t,!1),t.applyTheme(),t}return n.c(t,e),t.prototype.validate=function(){var t=this,i=this.chartContainer,a=(this.nodesContainer,this.endAngle),n=this.startAngle+this.nodePadding/2,s=g.getArcRect(this.startAngle,this.endAngle,1);s=g.getCommonRectangle([s,{x:0,y:0,width:0,height:0}]);var r=Math.min(i.innerWidth/s.width,i.innerHeight/s.height);f.isNumber(r)||(r=0);F.relativeRadiusToValue(this.radius,r);var o=F.relativeRadiusToValue(this.radius,r),l=F.relativeRadiusToValue(this.innerRadius,o,!0),h=this.dataItem.values.value.sum,u=0,d=0;m.each(this._sorted,function(e){var i=e[1];t.getNodeValue(i),u++;var a=i.total;i.total/h<t.minNodeSize&&(a=h*t.minNodeSize),d+=a}),this.valueAngle=(a-this.startAngle-this.nodePadding*u)/d,m.each(this._sorted,function(e){var i=e[1],s=i.slice;s.radius=o,s.innerRadius=l;var r,d=i.total;i.total/h<t.minNodeSize&&(d=h*t.minNodeSize),i.adjustedTotal=d,r=t.nonRibbon?(a-t.startAngle)/u-t.nodePadding:t.valueAngle*d,s.arc=r,s.startAngle=n,i.trueStartAngle=n,i.parent=t.nodesContainer,i.validate(),n+=r+t.nodePadding}),this.chordContainer.definedBBox={x:o*s.x,y:o*s.y,width:o*s.width,height:o*s.height},this.chordContainer.invalidateLayout(),e.prototype.validate.call(this)},t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),f.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Chord diagram"))},t.prototype.createDataItem=function(){return new Be},Object.defineProperty(t.prototype,"startAngle",{get:function(){return this.getPropertyValue("startAngle")},set:function(e){this.setPropertyValue("startAngle",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endAngle",{get:function(){return this.getPropertyValue("endAngle")},set:function(e){this.setPropertyValue("endAngle",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(e){this.setPercentProperty("innerRadius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"nonRibbon",{get:function(){return this.getPropertyValue("nonRibbon")},set:function(e){this.setPropertyValue("nonRibbon",e,!0),this.links.template.middleLine.strokeOpacity=1,this.links.template.link.fillOpacity=0},enumerable:!0,configurable:!0}),t.prototype.createNode=function(){var e=new Ye;return this._disposers.push(e),e},t.prototype.createLink=function(){var e=new Me;return this._disposers.push(e),e},t}(ke.a);d.b.registeredClasses.ChordDiagram=We;var Ee=i("DG6Q"),ze=i("CnhP"),Ue=function(e){function t(){var t=e.call(this)||this;return t.className="ColumnSeriesDataItem",t.locations.dateX=.5,t.locations.dateY=.5,t.locations.categoryX=.5,t.locations.categoryY=.5,t.applyTheme(),t}return n.c(t,e),Object.defineProperty(t.prototype,"column",{get:function(){return this._column},set:function(e){this.setColumn(e)},enumerable:!0,configurable:!0}),t.prototype.setColumn=function(e){var t=this;if(this._column&&e!=this._column&&O.remove(this.sprites,this._column),this._column=e,e){var i=e.dataItem;i&&i!=this&&(i.column=void 0),this.addSprite(e),this._disposers.push(new _.b(function(){t.component.columns.removeValue(e)}))}},Object.defineProperty(t.prototype,"rangesColumns",{get:function(){return this._rangesColumns||(this._rangesColumns=new c.a),this._rangesColumns},enumerable:!0,configurable:!0}),t}(w),He=function(e){function t(){var t=e.call(this)||this;t._startLocation=0,t._endLocation=1,t.simplifiedProcessing=!1,t.className="ColumnSeries",t.width=Object(Y.c)(100),t.height=Object(Y.c)(100),t.strokeOpacity=0,t.fillOpacity=1,t.clustered=!0;var i=t.mainContainer.createChild(r.a);return i.shouldClone=!1,i.isMeasured=!1,i.layout="none",t._columnsContainer=i,t.columns,t.columns.template.pixelPerfect=!1,t.tooltipColorSource=t.columns.template,t.applyTheme(),t}return n.c(t,e),Object.defineProperty(t.prototype,"columnsContainer",{get:function(){return this._columnsContainer},enumerable:!0,configurable:!0}),t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),f.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Column Series"))},t.prototype.createDataItem=function(){return new Ue},t.prototype.validate=function(){var i=this,a=this.chart.series,n=0,s=0;m.each(a.iterator(),function(e){e instanceof t&&i.baseAxis==e.baseAxis&&((!e.stacked&&e.clustered||0===n)&&n++,e==i&&(s=n-1))});var r=this.baseAxis.renderer,o=r.cellStartLocation,l=r.cellEndLocation;this._startLocation=o+s/n*(l-o),this._endLocation=o+(s+1)/n*(l-o),e.prototype.validate.call(this),this.dataItems.each(function(e){(e.index<i.startIndex||e.index>=i.endIndex)&&i.disableUnusedColumns(e)})},t.prototype.validateDataElement=function(t){this.validateDataElementReal(t),e.prototype.validateDataElement.call(this,t)},t.prototype.getStartLocation=function(e){var t=this._startLocation;return this.baseAxis==this.xAxis?t+=e.locations[this.xOpenField]-.5:t+=e.locations[this.yOpenField]-.5,t},t.prototype.handleDataItemWorkingValueChange=function(t,i){this.simplifiedProcessing?this.validateDataElement(t):e.prototype.handleDataItemWorkingValueChange.call(this,t,i)},t.prototype.getEndLocation=function(e){var t=this._endLocation;return this.baseAxis==this.xAxis?t+=e.locations[this.xField]-.5:t+=e.locations[this.yField]-.5,t},t.prototype.validateDataElementReal=function(e){var t,i,a,n,s=this,r=this.getStartLocation(e),o=this.getEndLocation(e),l=this.xField,h=this.xOpenField,u=this.yField,d=this.yOpenField,c=this.columns.template,p=c.percentWidth,y=c.percentHeight,x=c.pixelWidth,v=c.pixelHeight,A=c.maxWidth,_=c.maxHeight,I=c.pixelPaddingLeft,D=c.pixelPaddingRight,T=c.pixelPaddingTop,S=c.pixelPaddingBottom,R=!1;if(this.xAxis instanceof b&&this.yAxis instanceof b){if(!e.hasValue(this._xValueFields)||!e.hasValue(this._yValueFields))return;if(r=0,o=1,!f.isNaN(p))r+=k=g.round((o-r)*(1-p/100)/2,5),o-=k;if(t=this.xAxis.getX(e,h,r),i=this.xAxis.getX(e,l,o),f.isNaN(p))t+=k=(i-t-x)/2,i-=k;if(!f.isNaN(A))t+=k=(i-t-A)/2,i-=k;if(r=0,o=1,!f.isNaN(y))r+=k=g.round((1-y/100)/2,5),o-=k;if(a=this.yAxis.getY(e,d,r),n=this.yAxis.getY(e,u,o),f.isNaN(y))n+=k=(n-a-v)/2,a-=k;if(!f.isNaN(_))n+=k=(n-a-_)/2,a-=k;i=this.fixHorizontalCoordinate(i),t=this.fixHorizontalCoordinate(t),a=this.fixVerticalCoordinate(a),n=this.fixVerticalCoordinate(n)}else if(this.baseAxis==this.xAxis){if(!e.hasValue(this._yValueFields))return;if(!f.isNaN(p))r+=k=g.round((o-r)*(1-p/100)/2,5),o-=k;if(t=this.xAxis.getX(e,h,r),i=this.xAxis.getX(e,l,o),f.isNaN(p))t+=k=(i-t-x)/2,i-=k;if(!f.isNaN(A))t+=k=(i-t-A)/2,i-=k;var F=e.locations[d],O=e.locations[u];this.yAxis instanceof C.a&&(F=0,O=0),n=this.yAxis.getY(e,d,F),a=this.yAxis.getY(e,u,O),a=this.fixVerticalCoordinate(a),n=this.fixVerticalCoordinate(n),Math.abs(i-t)-I-D==0&&(R=!0)}else{if(!e.hasValue(this._xValueFields))return;var k;if(!f.isNaN(y))r+=k=g.round((o-r)*(1-y/100)/2,5),o-=k;if(a=this.yAxis.getY(e,d,r),n=this.yAxis.getY(e,u,o),f.isNaN(y))n-=k=(n-a-v)/2,a+=k;if(!f.isNaN(_))n-=k=(n-a-_)/2,a+=k;var w=e.locations[l],L=e.locations[h];this.xAxis instanceof C.a&&(w=0,L=0),i=this.xAxis.getX(e,l,w),t=this.xAxis.getX(e,h,L),i=this.fixHorizontalCoordinate(i),t=this.fixHorizontalCoordinate(t),Math.abs(a-n)-T-S==0&&(R=!0)}var X,Y=Math.abs(i-t),j=Math.abs(n-a),N=Math.min(t,i),M=Math.min(a,n);R?this.disableUnusedColumns(e):(e.column?X=e.column:(X=this.columns.create(),V.copyProperties(this,X,P.b),V.copyProperties(this.columns.template,X,P.b),e.addSprite(X),e.column=X,this.itemsFocusable()?(X.role="menuitem",X.focusable=!0):(X.role="listitem",X.focusable=!1),X.focusable&&(X.events.once("focus",function(t){X.readerTitle=s.populateString(s.itemReaderText,e)},void 0,!1),X.events.once("blur",function(e){X.readerTitle=""},void 0,!1)),X.hoverable&&(X.events.once("over",function(t){X.readerTitle=s.populateString(s.itemReaderText,e)},void 0,!1),X.events.once("out",function(e){X.readerTitle=""},void 0,!1))),X.width=Y,X.height=j,X.x=N,X.y=M,X.realX=t,X.realY=a,X.realWidth=i-t,X.realHeight=n-a,X.parent=this.columnsContainer,X.virtualParent=this,this.setColumnStates(X),X.invalid&&X.validate(),X.__disabled=!1,m.each(this.axisRanges.iterator(),function(t){var i=e.rangesColumns.getKey(t.uid);i||(i=s.columns.create(),V.copyProperties(t.contents,i,P.b),e.addSprite(i),e.rangesColumns.setKey(t.uid,i)),i.parent=t.contents,i.width=Y,i.height=j,i.x=N,i.y=M,s.setColumnStates(i),i.invalid&&i.validate(),i.__disabled=!1}));e.itemWidth=Y,e.itemHeight=j},t.prototype.disableUnusedColumns=function(e){e.column&&(e.column.width=0,e.column.height=0,e.column.__disabled=!0),m.each(this.axisRanges.iterator(),function(t){var i=e.rangesColumns.getKey(t.uid);i&&(i.width=0,i.height=0,i.__disabled=!0)})},t.prototype.setColumnStates=function(e){var t=e.dataItem;if(this.xAxis instanceof C.a||this.yAxis instanceof C.a){var i,a=void 0,n=void 0;this.baseAxis==this.yAxis?this.xOpenField&&this.xField&&(i=t.getValue(this.xOpenField),a=t.getValue(this.xField),n=t.getValue(this.xAxis.axisFieldName+"X","previousChange")):this.yOpenField&&this.yField&&(i=t.getValue(this.yOpenField),a=t.getValue(this.yField),n=t.getValue(this.yAxis.axisFieldName+"Y","previousChange")),a<i?(t.droppedFromOpen=!0,e.defaultState.copyFrom(this._dropFromOpenState),e.setState(this._dropFromOpenState,0)):(t.droppedFromOpen=!1,e.defaultState.copyFrom(this._riseFromOpenState),e.setState(this._riseFromOpenState,0)),n<0?(t.droppedFromPrevious=!0,e.defaultState.copyFrom(this._dropFromPreviousState),e.setState(this._dropFromPreviousState,0)):(t.droppedFromPrevious=!1,e.defaultState.copyFrom(this._riseFromPreviousState),e.setState(this._riseFromPreviousState,0))}},Object.defineProperty(t.prototype,"columns",{get:function(){return this._columns||(this._columns=new o.e(this.createColumnTemplate()),this._disposers.push(new o.c(this._columns)),this._disposers.push(this._columns.template)),this._columns},enumerable:!0,configurable:!0}),t.prototype.createColumnTemplate=function(){return new Ee.a},Object.defineProperty(t.prototype,"clustered",{get:function(){return this.getPropertyValue("clustered")},set:function(e){this.setPropertyValue("clustered",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"dropFromOpenState",{get:function(){return this._dropFromOpenState||(this._dropFromOpenState=this.states.create("dropFromOpenState")),this._dropFromOpenState},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"dropFromPreviousState",{get:function(){return this._dropFromPreviousState||(this._dropFromPreviousState=this.states.create("dropFromPreviousState")),this._dropFromPreviousState},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"riseFromOpenState",{get:function(){return this._riseFromOpenState||(this._riseFromOpenState=this.states.create("riseFromOpenState")),this._riseFromOpenState},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"riseFromPreviousState",{get:function(){return this._riseFromPreviousState||(this._riseFromPreviousState=this.states.create("riseFromPreviousState")),this._riseFromPreviousState},enumerable:!0,configurable:!0}),t.prototype.updateLegendValue=function(t){var i=this;if(e.prototype.updateLegendValue.call(this,t),this.legendDataItem){var a,n,s=this.legendDataItem.marker;t&&(a=t.droppedFromOpen?this._dropFromOpenState:this._riseFromOpenState,n=t.droppedFromPrevious?this._dropFromPreviousState:this._riseFromPreviousState),m.each(s.children.iterator(),function(e){t?(e.setState(n),e.setState(a)):(e.setState(i._riseFromPreviousState),e.setState(i._riseFromOpenState))})}},t.prototype.createLegendMarker=function(e){var t=e.pixelWidth,i=e.pixelHeight;e.removeChildren();var a=e.createChild(ze.a);a.shouldClone=!1,V.copyProperties(this,a,P.b),a.copyFrom(this.columns.template),a.padding(0,0,0,0),a.width=t,a.height=i},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.columns.template.copyFrom(t.columns.template)},t.prototype.getBulletLocationX=function(t,i){return this.baseAxis==this.xAxis?(this._startLocation+this._endLocation)/2:e.prototype.getBulletLocationX.call(this,t,i)},t.prototype.getBulletLocationY=function(t,i){return this.baseAxis==this.yAxis?(this._startLocation+this._endLocation)/2:e.prototype.getBulletLocationY.call(this,t,i)},t.prototype.fixVerticalCoordinate=function(e){var t=this.columns.template.pixelPaddingBottom,i=-this.columns.template.pixelPaddingTop,a=this.yAxis.axisLength+t;return g.fitToRange(e,i,a)},t.prototype.fixHorizontalCoordinate=function(e){var t=this.columns.template.pixelPaddingLeft,i=this.columns.template.pixelPaddingRight,a=-t,n=this.xAxis.axisLength+i;return g.fitToRange(e,a,n)},t}(L);d.b.registeredClasses.ColumnSeries=He,d.b.registeredClasses.ColumnSeriesDataItem=Ue;var Ke=function(e){function t(){var t=e.call(this)||this;return t.className="TreeMapSeriesDataItem",t.applyTheme(),t}return n.c(t,e),Object.defineProperty(t.prototype,"parentName",{get:function(){var e=this.treeMapDataItem;if(e&&e.parent)return e.parent.name},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"value",{get:function(){return this.treeMapDataItem.value},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"treeMapDataItem",{get:function(){return this._dataContext},enumerable:!0,configurable:!0}),t}(Ue),Ge=function(e){function t(){var t=e.call(this)||this;t.className="TreeMapSeries",t.applyTheme(),t.fillOpacity=1,t.strokeOpacity=1,t.minBulletDistance=0,t.columns.template.tooltipText="{parentName} {name}: {value}",t.columns.template.configField="config";var i=new B.a;return t.stroke=i.getFor("background"),t.dataFields.openValueX="x0",t.dataFields.valueX="x1",t.dataFields.openValueY="y0",t.dataFields.valueY="y1",t.sequencedInterpolation=!1,t.showOnInit=!1,t.columns.template.pixelPerfect=!1,t}return n.c(t,e),t.prototype.processDataItem=function(t,i){i.seriesDataItem=t,e.prototype.processDataItem.call(this,t,i)},t.prototype.createDataItem=function(){return new Ke},t.prototype.show=function(t){var i=this.defaultState.transitionDuration;f.isNumber(t)&&(i=t),this.dataItems.each(function(e){e.treeMapDataItem.setWorkingValue("value",e.treeMapDataItem.values.value.value)});var a=e.prototype.showReal.call(this,i),n=this.chart;return n&&(a&&!a.isFinished()?a.events.on("animationended",function(){n.invalidateLayout()}):n.invalidateLayout(),n.invalidateLayout()),a},t.prototype.hide=function(t){var i=this.defaultState.transitionDuration;f.isNumber(t)&&(i=t);var a=e.prototype.hideReal.call(this,i);this.dataItems.each(function(e){e.treeMapDataItem.setWorkingValue("value",0)});var n=this.chart;return n&&(a&&!a.isFinished()?a.events.on("animationended",function(){n.invalidateLayout()}):n.invalidateLayout(),n.invalidateLayout()),a},t.prototype.processValues=function(){},t.prototype.dataChangeUpdate=function(){},t.prototype.processConfig=function(t){t&&(f.hasValue(t.dataFields)&&f.isObject(t.dataFields)||(t.dataFields={})),e.prototype.processConfig.call(this,t)},t.prototype.createLegendMarker=function(e){var t=e.pixelWidth,i=e.pixelHeight;e.removeChildren();var a=e.createChild(ze.a);a.shouldClone=!1,V.copyProperties(this,a,P.b),a.padding(0,0,0,0),a.width=t,a.height=i},t}(He);d.b.registeredClasses.TreeMapSeries=Ge,d.b.registeredClasses.TreeMapSeriesDataItem=Ke;var Ze=i("DHte"),qe=function(e){function t(){var t=e.call(this)||this;return t.rows=[],t.className="TreeMapDataItem",t.values.value={},t.values.x0={},t.values.y0={},t.values.x1={},t.values.y1={},t.hasChildren.children=!0,t.applyTheme(),t}return n.c(t,e),Object.defineProperty(t.prototype,"value",{get:function(){var e=this.values.value.workingValue;return f.isNumber(e)||(e=0,this.children&&m.each(this.children.iterator(),function(t){f.isNumber(t.value)&&(e+=t.value)})),e},set:function(e){this.setValue("value",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"percent",{get:function(){return this.parent?this.value/this.parent.value*100:100},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"x0",{get:function(){return this.values.x0.value},set:function(e){this.setValue("x0",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"x1",{get:function(){return this.values.x1.value},set:function(e){this.setValue("x1",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"y0",{get:function(){return this.values.y0.value},set:function(e){this.setValue("y0",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"y1",{get:function(){return this.values.y1.value},set:function(e){this.setValue("y1",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"name",{get:function(){return this.properties.name},set:function(e){this.setProperty("name",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"children",{get:function(){return this.properties.children},set:function(e){this.setProperty("children",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"level",{get:function(){return this.parent?this.parent.level+1:0},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"color",{get:function(){var e=this.properties.color;return void 0==e&&this.parent&&(e=this.parent.color),void 0==e&&this.component&&(e=this.component.colors.getIndex(this.component.colors.step*this.index)),e},set:function(e){this.setProperty("color",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"series",{get:function(){return this._series},set:function(e){this._series&&(this.component.series.removeValue(this._series),this._series.dispose()),this._series=e,this._disposers.push(e)},enumerable:!0,configurable:!0}),t}(K),Je=function(e){function t(){var t=e.call(this)||this;t.layoutAlgorithm=t.squarify,t.zoomable=!0,t.className="TreeMap",t.maxLevels=2,t.currentLevel=0,t.colors=new Ze.a,t.sorting="descending";var i=t.xAxes.push(new C.a);i.title.disabled=!0,i.strictMinMax=!0;var a=i.renderer;a.inside=!0,a.labels.template.disabled=!0,a.ticks.template.disabled=!0,a.grid.template.disabled=!0,a.axisFills.template.disabled=!0,a.minGridDistance=100,a.line.disabled=!0,a.baseGrid.disabled=!0;var n=t.yAxes.push(new C.a);n.title.disabled=!0,n.strictMinMax=!0;var s=n.renderer;s.inside=!0,s.labels.template.disabled=!0,s.ticks.template.disabled=!0,s.grid.template.disabled=!0,s.axisFills.template.disabled=!0,s.minGridDistance=100,s.line.disabled=!0,s.baseGrid.disabled=!0,s.inversed=!0,t.xAxis=i,t.yAxis=n;var r=new Ge;return t.seriesTemplates=new c.c(r),t._disposers.push(new c.b(t.seriesTemplates)),t._disposers.push(r),t.zoomOutButton.events.on("hit",function(){t.zoomToChartDataItem(t._homeDataItem)},void 0,!1),t.seriesTemplates.events.on("insertKey",function(e){e.newValue.isTemplate=!0},void 0,!1),t.applyTheme(),t}return n.c(t,e),Object.defineProperty(t.prototype,"navigationBar",{get:function(){return this._navigationBar},set:function(e){var t=this;this._navigationBar!=e&&(this._navigationBar=e,e.parent=this,e.toBack(),e.links.template.events.on("hit",function(e){var i=e.target.dataItem.dataContext;t.zoomToChartDataItem(i),t.createTreeSeries(i)},void 0,!0),this._disposers.push(e))},enumerable:!0,configurable:!0}),t.prototype.validateData=function(){this.series.clear(),e.prototype.validateData.call(this),this._homeDataItem&&this._homeDataItem.dispose();var t=this.dataItems.template.clone();this._homeDataItem=t,m.each(this.dataItems.iterator(),function(e){e.parent=t}),t.children=this.dataItems,t.x0=0,t.y0=0,t.name=this._homeText;var i=1e3*this.pixelHeight/this.pixelWidth||1e3;t.x1=1e3,t.y1=i,this.xAxis.min=0,this.xAxis.max=1e3,this.yAxis.min=0,this.yAxis.max=i,this.layoutItems(t),this.createTreeSeries(t)},t.prototype.layoutItems=function(e,t){if(e){var i=e.children;t||(t=this.sorting),"ascending"==t&&i.values.sort(function(e,t){return e.value-t.value}),"descending"==t&&i.values.sort(function(e,t){return t.value-e.value}),this.layoutAlgorithm(e);for(var a=0,n=i.length;a<n;a++){var s=i.getIndex(a);s.children&&this.layoutItems(s)}}},t.prototype.createTreeSeries=function(e){var t=this;this._tempSeries=[];for(var i=[e],a=e.parent;void 0!=a;)this.initSeries(a),i.push(a),a=a.parent;i.reverse(),this.navigationBar&&(this.navigationBar.data=i),this.createTreeSeriesReal(e),O.each(this._tempSeries,function(e){-1==t.series.indexOf(e)&&t.series.push(e),e.zIndex=e.level})},t.prototype.createTreeSeriesReal=function(e){if(e.children&&e.level<this.currentLevel+this.maxLevels){this.initSeries(e);for(var t=0;t<e.children.length;t++){var i=e.children.getIndex(t);i.children&&this.createTreeSeriesReal(i)}}},t.prototype.seriesAppeared=function(){return!0},t.prototype.initSeries=function(e){var t=this;if(!e.series){var i=void 0,a=this.seriesTemplates.getKey(e.level.toString());(i=a?a.clone():this.series.create()).name=e.name,i.parentDataItem=e,e.series=i;var n=e.level;i.level=n;var s=e.dataContext;s&&(i.config=s.config),this.dataUsers.removeValue(i),i.data=e.children.values,i.fill=e.color,i.columnsContainer.hide(0),i.bulletsContainer.hide(0),i.columns.template.adapter.add("fill",function(e,t){var i=t.dataItem;if(i){var a=i.treeMapDataItem;if(a)return t.fill=a.color,t.adapter.remove("fill"),a.color}}),this.zoomable&&(e.level>this.currentLevel||e.children&&e.children.length>0)&&(i.columns.template.cursorOverStyle=j.a.pointer,this.zoomable&&i.columns.template.events.on("hit",function(i){var a=i.target.dataItem;e.level>t.currentLevel?t.zoomToChartDataItem(a.treeMapDataItem.parent):t.zoomToSeriesDataItem(a)},this,void 0))}this._tempSeries.push(e.series)},t.prototype.toggleBullets=function(e){var t=this;m.each(this.series.iterator(),function(i){-1==t._tempSeries.indexOf(i)?(i.columnsContainer.hide(),i.bulletsContainer.hide(e)):(i.columnsContainer.show(),i.bulletsContainer.show(e),i.level<t.currentLevel&&i.bulletsContainer.hide(e))})},t.prototype.zoomToSeriesDataItem=function(e){this.zoomToChartDataItem(e.treeMapDataItem)},t.prototype.zoomToChartDataItem=function(e){var t=this;if(e&&e.children){this.xAxis.zoomToValues(e.x0,e.x1),this.yAxis.zoomToValues(e.y0,e.y1),this.currentLevel=e.level,this.currentlyZoomed=e,this.createTreeSeries(e);var i=this.xAxis.rangeChangeAnimation||this.yAxis.rangeChangeAnimation;i&&!i.isFinished()?(this._dataDisposers.push(i),i.events.once("animationended",function(){t.toggleBullets()})):this.toggleBullets()}},t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),f.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("TreeMap chart"))},t.prototype.createDataItem=function(){return new qe},Object.defineProperty(t.prototype,"maxLevels",{get:function(){return this.getPropertyValue("maxLevels")},set:function(e){this.setPropertyValue("maxLevels",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"currentLevel",{get:function(){return this.getPropertyValue("currentLevel")},set:function(e){this.setPropertyValue("currentLevel",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"sorting",{get:function(){return this.getPropertyValue("sorting")},set:function(e){this.setPropertyValue("sorting",e,!0)},enumerable:!0,configurable:!0}),t.prototype.createSeries=function(){return new Ge},Object.defineProperty(t.prototype,"homeText",{get:function(){return this._homeText},set:function(e){this._homeText=e,this._homeDataItem&&(this._homeDataItem.name=this._homeText)},enumerable:!0,configurable:!0}),t.prototype.processConfig=function(t){if(t){if(f.hasValue(t.layoutAlgorithm)&&f.isString(t.layoutAlgorithm))switch(t.layoutAlgorithm){case"squarify":t.layoutAlgorithm=this.squarify;break;case"binaryTree":t.layoutAlgorithm=this.binaryTree;break;case"slice":t.layoutAlgorithm=this.slice;break;case"dice":t.layoutAlgorithm=this.dice;break;case"sliceDice":t.layoutAlgorithm=this.sliceDice;break;default:delete t.layoutAlgorithm}f.hasValue(t.navigationBar)&&!f.hasValue(t.navigationBar.type)&&(t.navigationBar.type="NavigationBar"),e.prototype.processConfig.call(this,t)}},t.prototype.validateLayout=function(){e.prototype.validateLayout.call(this),this.layoutItems(this.currentlyZoomed)},t.prototype.validateDataItems=function(){e.prototype.validateDataItems.call(this),this.layoutItems(this._homeDataItem),m.each(this.series.iterator(),function(e){e.validateRawData()}),this.zoomToChartDataItem(this._homeDataItem)},t.prototype.binaryTree=function(e){var t,i,a=e.children,n=a.length,s=new Array(n+1);for(s[0]=i=t=0;t<n;++t)s[t+1]=i+=a.getIndex(t).value;!function e(t,i,n,r,o,l,h){if(t>=i-1){var u=a.getIndex(t);return u.x0=r,u.y0=o,u.x1=l,void(u.y1=h)}var d=s[t],c=n/2+d,p=t+1,y=i-1;for(;p<y;){var g=p+y>>>1;s[g]<c?p=g+1:y=g}c-s[p-1]<s[p]-c&&t+1<p&&--p;var f=s[p]-d,m=n-f;if(l-r>h-o){var x=(r*m+l*f)/n;e(t,p,f,r,o,x,h),e(p,i,m,x,o,l,h)}else{var v=(o*m+h*f)/n;e(t,p,f,r,o,l,v),e(p,i,m,r,v,l,h)}}(0,n,e.value,e.x0,e.y0,e.x1,e.y1)},t.prototype.slice=function(e){for(var t,i=e.x0,a=e.x1,n=e.y0,s=e.y1,r=e.children,o=-1,l=r.length,h=e.value&&(s-n)/e.value;++o<l;)(t=r.getIndex(o)).x0=i,t.x1=a,t.y0=n,t.y1=n+=t.value*h},t.prototype.dice=function(e){for(var t,i=e.x0,a=e.x1,n=e.y0,s=e.y1,r=e.children,o=-1,l=r.length,h=e.value&&(a-i)/e.value;++o<l;)(t=r.getIndex(o)).y0=n,t.y1=s,t.x0=i,t.x1=i+=t.value*h},t.prototype.sliceDice=function(e){1&e.level?this.slice(e):this.dice(e)},t.prototype.squarify=function(e){for(var t,i,a,n,s,r,o,l,h,u,d=(1+Math.sqrt(5))/2,c=e.x0,p=e.x1,y=e.y0,g=e.y1,f=e.children,m=0,x=0,v=f.length,b=e.value;m<v;){i=p-c,a=g-y;do{n=f.getIndex(x++).value}while(!n&&x<v);for(s=r=n,u=n*n*(h=Math.max(a/i,i/a)/(b*d)),l=Math.max(r/u,u/s);x<v;++x){if(n+=t=f.getIndex(x).value,t<s&&(s=t),t>r&&(r=t),u=n*n*h,(o=Math.max(r/u,u/s))>l){n-=t;break}l=o}var A=this.dataItems.template.clone();A.value=n,A.dice=i<a,A.children=f.slice(m,x),A.x0=c,A.y0=y,A.x1=p,A.y1=g,A.dice?(A.y1=b?y+=a*n/b:g,this.dice(A)):(A.x1=b?c+=i*n/b:p,this.slice(A)),b-=n,m=x}},t.prototype.handleDataItemValueChange=function(e,t){"value"==t&&this.invalidateDataItems()},t.prototype.handleDataItemWorkingValueChange=function(e,t){"value"==t&&this.invalidateDataItems()},t.prototype.feedLegend=function(){var e=this.legend;if(e){var t=[];m.each(this.series.iterator(),function(e){1==e.level&&(e.hiddenInLegend||t.push(e))}),e.dataFields.name="name",e.data=t}},t.prototype.disposeData=function(){e.prototype.disposeData.call(this),this._homeDataItem=void 0,this.series.clear(),this.navigationBar&&this.navigationBar.disposeData(),this.xAxis.disposeData(),this.yAxis.disposeData()},t}(G);d.b.registeredClasses.TreeMap=Je;var Qe=function(e){function t(){var t=e.call(this)||this;return t._chart=new _.d,t.className="AxisRendererX3D",t._disposers.push(t._chart),t.applyTheme(),t}return n.c(t,e),t.prototype.updateGridElement=function(e,t,i){t+=(i-t)*e.location;var a=this.positionToPoint(t);if(e.element){var n=this.chart.dx3D,s=this.chart.dy3D,r=this.getHeight();e.path=k.moveTo({x:n,y:s})+k.lineTo({x:n,y:r+s})+k.lineTo({x:0,y:r})}this.positionItem(e,a),this.toggleVisibility(e,t,0,1)},t.prototype.updateBaseGridElement=function(){e.prototype.updateBaseGridElement.call(this);var t=this.getHeight(),i=this.chart.dx3D,a=this.chart.dy3D;this.baseGrid.path=k.moveTo({x:i,y:a})+k.lineTo({x:i,y:t+a})+k.lineTo({x:0,y:t})},Object.defineProperty(t.prototype,"chart",{get:function(){return this._chart.get()},set:function(e){e&&this._chart.set(e,e.events.on("propertychanged",this.handle3DChanged,this,!1))},enumerable:!0,configurable:!0}),t.prototype.handle3DChanged=function(e){"depth"!=e.property&&"angle"!=e.property||this.invalidate()},t}(l.a);d.b.registeredClasses.AxisRendererX3D=Qe;var $e=function(e){function t(){var t=e.call(this)||this;return t._chart=new _.d,t.className="AxisRendererY3D",t._disposers.push(t._chart),t.applyTheme(),t}return n.c(t,e),t.prototype.updateGridElement=function(e,t,i){t+=(i-t)*e.location;var a=this.positionToPoint(t);if(e.element){var n=this.chart.dx3D,s=this.chart.dy3D,r=this.getWidth();e.path=k.moveTo({x:0,y:0})+k.lineTo({x:n,y:s})+k.lineTo({x:r+n,y:s})}this.positionItem(e,a),this.toggleVisibility(e,t,0,1)},t.prototype.updateBaseGridElement=function(){e.prototype.updateBaseGridElement.call(this);var t=this.getWidth();this.baseGrid.path=k.moveTo({x:0,y:0})+k.lineTo({x:t,y:0})+k.lineTo({x:t+this.chart.dx3D,y:this.chart.dy3D})},Object.defineProperty(t.prototype,"chart",{get:function(){return this._chart.get()},set:function(e){e&&this._chart.set(e,e.events.on("propertychanged",this.handle3DChanged,this,!1))},enumerable:!0,configurable:!0}),t.prototype.handle3DChanged=function(e){"depth"!=e.property&&"angle"!=e.property||this.invalidate()},t}(h.a),et=i("qUC/"),tt=function(e){function t(){var t=e.call(this)||this;return t.className="ColumnSeries3DDataItem",t.applyTheme(),t}return n.c(t,e),t}(Ue),it=function(e){function t(){var t=e.call(this)||this;return t.className="ColumnSeries3D",t.columns.template.column3D.applyOnClones=!0,t.columns.template.hiddenState.properties.visible=!0,t.applyTheme(),t}return n.c(t,e),Object.defineProperty(t.prototype,"columnsContainer",{get:function(){return this.chart&&this.chart.columnsContainer?this.chart.columnsContainer:this._columnsContainer},enumerable:!0,configurable:!0}),t.prototype.validateDataElementReal=function(t){e.prototype.validateDataElementReal.call(this,t),t.column&&(t.column.dx=this.dx,t.column.dy=this.dy)},t.prototype.validateDataElements=function(){e.prototype.validateDataElements.call(this),this.chart&&this.chart.invalidateLayout()},t.prototype.createColumnTemplate=function(){return new et.a},Object.defineProperty(t.prototype,"depth",{get:function(){return this.getPropertyValue("depth")},set:function(e){this.setPropertyValue("depth",e,!0),this.columns.template.column3D.depth=e},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"angle",{get:function(){return this.getPropertyValue("angle")},set:function(e){this.setPropertyValue("angle",e),this.columns.template.column3D.angle=e},enumerable:!0,configurable:!0}),t}(He);d.b.registeredClasses.ColumnSeries3D=it,d.b.registeredClasses.ColumnSeries3DDataItem=tt;var at=function(e){function t(){var t=e.call(this)||this;return t.className="XYChart3DDataItem",t.applyTheme(),t}return n.c(t,e),t}(K),nt=function(e){function t(){var t=e.call(this)||this;t._axisRendererX=Qe,t._axisRendererY=$e,t.className="XYChart3D",t.depth=30,t.angle=30;var i=t.seriesContainer.createChild(r.a);return i.shouldClone=!1,i.isMeasured=!1,i.layout="none",t.columnsContainer=i,t.columnsContainer.mask=t.createChild(P.a),t.applyTheme(),t}return n.c(t,e),Object.defineProperty(t.prototype,"depth",{get:function(){return this.getPropertyValue("depth")},set:function(e){this.setPropertyValue("depth",e),this.fixLayout(),this.invalidateDataUsers()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"angle",{get:function(){return this.getPropertyValue("angle")},set:function(e){this.setPropertyValue("angle",e),this.fixLayout(),this.invalidateDataUsers()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"dx3D",{get:function(){return g.cos(this.angle)*this.depth},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"dy3D",{get:function(){return-g.sin(this.angle)*this.depth},enumerable:!0,configurable:!0}),t.prototype.validateLayout=function(){e.prototype.validateLayout.call(this),this.fixLayout()},t.prototype.fixLayout=function(){this.chartContainer.marginTop=-this.dy3D,this.chartContainer.paddingRight=this.dx3D,this.scrollbarX&&(this.scrollbarX.dy=this.dy3D,this.scrollbarX.dx=this.dx3D),this.scrollbarY&&(this.scrollbarY.dy=this.dy3D,this.scrollbarY.dx=this.dx3D),this.fixColumns()},t.prototype.fixColumns=function(){var e=this,t=1,i=0;m.each(this.series.iterator(),function(e){e instanceof it&&(!e.clustered&&i>0&&t++,e.depthIndex=t-1,i++)});var a=0;m.each(this.series.iterator(),function(i){if(i instanceof it){i.depth=e.depth/t,i.angle=e.angle,i.dx=e.depth/t*g.cos(e.angle)*i.depthIndex,i.dy=-e.depth/t*g.sin(e.angle)*i.depthIndex;var n=1;i.columns.each(function(e){e.zIndex=1e3*n+a-100*i.depthIndex,n++}),a++}}),this.maskColumns()},t.prototype.processConfig=function(t){if(t&&f.hasValue(t.series)&&f.isArray(t.series))for(var i=0,a=t.series.length;i<a;i++)t.series[i].type=t.series[i].type||"ColumnSeries3D";e.prototype.processConfig.call(this,t)},t.prototype.maskColumns=function(){var e=this.plotContainer.pixelWidth,t=this.plotContainer.pixelHeight,i=this.dx3D,a=this.dy3D,n=k.moveTo({x:0,y:0})+k.lineTo({x:i,y:a})+k.lineTo({x:e+i,y:a})+k.lineTo({x:e+i,y:t+a})+k.lineTo({x:e,y:t})+k.lineTo({x:e,y:t})+k.lineTo({x:0,y:t})+k.closePath(),s=this.columnsContainer;s&&s.mask&&(s.mask.path=n)},t}(G);d.b.registeredClasses.XYChart3D=nt;var st=i("VIOb"),rt=i("uWmK"),ot=i("2OXf"),lt=function(e){function t(){var t=e.call(this)||this;return t.className="Candlestick",t.layout="none",t}return n.c(t,e),t.prototype.createAssets=function(){e.prototype.createAssets.call(this),this.lowLine=this.createChild(J.a),this.lowLine.shouldClone=!1,this.highLine=this.createChild(J.a),this.highLine.shouldClone=!1},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.lowLine&&this.lowLine.copyFrom(t.lowLine),this.highLine&&this.highLine.copyFrom(t.highLine)},t}(Ee.a);d.b.registeredClasses.Candlestick=lt;var ht=function(e){function t(){var t=e.call(this)||this;return t.values.lowValueX={},t.values.lowValueY={},t.values.highValueX={},t.values.highValueY={},t.className="CandlestickSeriesDataItem",t.applyTheme(),t}return n.c(t,e),Object.defineProperty(t.prototype,"lowValueX",{get:function(){return this.values.lowValueX.value},set:function(e){this.setValue("lowValueX",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"lowValueY",{get:function(){return this.values.lowValueY.value},set:function(e){this.setValue("lowValueY",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"highValueX",{get:function(){return this.values.highValueX.value},set:function(e){this.setValue("highValueX",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"highValueY",{get:function(){return this.values.highValueY.value},set:function(e){this.setValue("highValueY",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"closeValueX",{get:function(){return this.values.valueX.value},set:function(e){this.setValue("valueX",e)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"closeValueY",{get:function(){return this.values.valueY.value},set:function(e){this.setValue("valueY",e)},enumerable:!0,configurable:!0}),t}(Ue),ut=function(e){function t(){var t=e.call(this)||this;t.className="CandlestickSeries",t.strokeOpacity=1;var i=new B.a,a=i.getFor("positive"),n=i.getFor("negative");return t.dropFromOpenState.properties.fill=n,t.dropFromOpenState.properties.stroke=n,t.riseFromOpenState.properties.fill=a,t.riseFromOpenState.properties.stroke=a,t.applyTheme(),t}return n.c(t,e),t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),f.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Candlestick Series"))},t.prototype.createDataItem=function(){return new ht},t.prototype.validateDataElementReal=function(t){e.prototype.validateDataElementReal.call(this,t),this.validateCandlestick(t)},t.prototype.validateCandlestick=function(e){var t=e.column;if(t){var i=t.lowLine,a=t.highLine;if(this.baseAxis==this.xAxis){var n=t.pixelWidth/2;i.x=n,a.x=n;var s=e.getWorkingValue(this.yOpenField),r=e.getWorkingValue(this.yField),o=this.yAxis.getY(e,this.yOpenField),l=this.yAxis.getY(e,this.yField),h=this.yAxis.getY(e,this.yLowField),u=this.yAxis.getY(e,this.yHighField),d=t.pixelY;i.y1=h-d,a.y1=u-d,s<r?(i.y2=o-d,a.y2=l-d):(i.y2=l-d,a.y2=o-d)}if(this.baseAxis==this.yAxis){var c=t.pixelHeight/2;i.y=c,a.y=c;var p=e.getWorkingValue(this.xOpenField),y=e.getWorkingValue(this.xField),g=this.xAxis.getX(e,this.xOpenField),f=this.xAxis.getX(e,this.xField),x=this.xAxis.getX(e,this.xLowField),v=this.xAxis.getX(e,this.xHighField),b=t.pixelX;i.x1=x-b,a.x1=v-b,p<y?(i.x2=g-b,a.x2=f-b):(i.x2=f-b,a.x2=g-b)}m.each(this.axisRanges.iterator(),function(t){var n=e.rangesColumns.getKey(t.uid);if(n){var s=n.lowLine;s.x=i.x,s.y=i.y,s.x1=i.x1,s.x2=i.x2,s.y1=i.y1,s.y2=i.y2;var r=n.highLine;r.x=a.x,r.y=a.y,r.x1=a.x1,r.x2=a.x2,r.y1=a.y1,r.y2=a.y2}})}},Object.defineProperty(t.prototype,"xLowField",{get:function(){return this._xLowField},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"yLowField",{get:function(){return this._yLowField},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"xHighField",{get:function(){return this._xHighField},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"yHighField",{get:function(){return this._yHighField},enumerable:!0,configurable:!0}),t.prototype.defineFields=function(){if(e.prototype.defineFields.call(this),this.baseAxis==this.xAxis){var t=F.capitalize(this.yAxis.axisFieldName);this._yLowField="low"+t+"Y",this._yHighField="high"+t+"Y"}if(this.baseAxis==this.yAxis){var i=F.capitalize(this.xAxis.axisFieldName);this._xLowField="low"+i+"X",this._xHighField="high"+i+"X"}this.addValueField(this.xAxis,this._xValueFields,this._xLowField),this.addValueField(this.xAxis,this._xValueFields,this._xHighField),this.addValueField(this.yAxis,this._yValueFields,this._yLowField),this.addValueField(this.yAxis,this._yValueFields,this._yHighField)},t.prototype.createLegendMarker=function(e){var t=e.pixelWidth,i=e.pixelHeight;e.removeChildren();var a,n,s=e.createChild(lt);s.shouldClone=!1,s.copyFrom(this.columns.template);var r=s.lowLine,o=s.highLine;this.baseAxis==this.yAxis?(a=t/3,n=i,r.y=i/2,o.y=i/2,r.x2=t/3,o.x2=t/3,o.x=t/3*2,s.column.x=t/3):(a=t,n=i/3,r.x=t/2,o.x=t/2,r.y2=i/3,o.y2=i/3,o.y=i/3*2,s.column.y=i/3),s.width=a,s.height=n,V.copyProperties(this,e,P.b),V.copyProperties(this.columns.template,s,P.b),s.stroke=this.riseFromOpenState.properties.stroke,s.fill=s.stroke},t.prototype.createColumnTemplate=function(){return new lt},t}(He);d.b.registeredClasses.CandlestickSeries=ut,d.b.registeredClasses.CandlestickSeriesDataItem=ht;var dt=function(e){function t(){var t=e.call(this)||this;return t.className="OHLC",t.layout="none",t}return n.c(t,e),t.prototype.createAssets=function(){this.openLine=this.createChild(J.a),this.openLine.shouldClone=!1,this.highLowLine=this.createChild(J.a),this.highLowLine.shouldClone=!1,this.closeLine=this.createChild(J.a),this.closeLine.shouldClone=!1},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.openLine&&this.openLine.copyFrom(t.openLine),this.highLowLine&&this.highLowLine.copyFrom(t.highLowLine),this.closeLine&&this.closeLine.copyFrom(t.closeLine)},t}(lt);d.b.registeredClasses.OHLC=dt;var ct=function(e){function t(){var t=e.call(this)||this;return t.className="OHLCSeriesDataItem",t.applyTheme(),t}return n.c(t,e),t}(ht),pt=function(e){function t(){var t=e.call(this)||this;return t.className="OHLCSeries",t.applyTheme(),t}return n.c(t,e),t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),f.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("OHLC Series"))},t.prototype.createDataItem=function(){return new ct},t.prototype.validateCandlestick=function(e){var t=e.column;if(t){var i=t.openLine,a=t.highLowLine,n=t.closeLine;if(this.baseAxis==this.xAxis){var s=t.pixelWidth/2;a.x=s;e.getWorkingValue(this.yOpenField),e.getWorkingValue(this.yField);var r=this.yAxis.getY(e,this.yOpenField),o=this.yAxis.getY(e,this.yField),l=this.yAxis.getY(e,this.yLowField),h=this.yAxis.getY(e,this.yHighField),u=t.pixelY;i.y1=r-u,i.y2=r-u,i.x1=0,i.x2=s,n.y1=o-u,n.y2=o-u,n.x1=s,n.x2=2*s,a.y1=h-u,a.y2=l-u}if(this.baseAxis==this.yAxis){var d=t.pixelHeight/2;a.y=d;e.getWorkingValue(this.xOpenField),e.getWorkingValue(this.xField);var c=this.xAxis.getX(e,this.xOpenField),p=this.xAxis.getX(e,this.xField),y=this.xAxis.getX(e,this.xLowField),g=this.xAxis.getX(e,this.xHighField),f=t.pixelX;i.x1=c-f,i.x2=c-f,i.y1=d,i.y2=2*d,n.x1=p-f,n.x2=p-f,n.y1=0,n.y2=d,a.x1=g-f,a.x2=y-f}m.each(this.axisRanges.iterator(),function(t){var s=e.rangesColumns.getKey(t.uid);if(s){var r=s.openLine;r.x=i.x,r.y=i.y,r.x1=i.x1,r.x2=i.x2,r.y1=i.y1,r.y2=i.y2;var o=s.closeLine;o.x=n.x,o.y=n.y,o.x1=n.x1,o.x2=n.x2,o.y1=n.y1,o.y2=n.y2;var l=s.highLowLine;l.x=a.x,l.y=a.y,l.x1=a.x1,l.x2=a.x2,l.y1=a.y1,l.y2=a.y2}})}},t.prototype.createLegendMarker=function(e){var t=e.pixelWidth,i=e.pixelHeight;e.removeChildren();var a,n,s=e.createChild(dt);s.shouldClone=!1,s.copyFrom(this.columns.template);var r=s.openLine,o=s.closeLine,l=s.highLowLine;this.baseAxis==this.yAxis?(a=t/3,n=i,l.y=i/2,l.x2=t,r.x=t/3*2,r.y2=i/2,o.x=t/3,o.y2=i,o.y1=i/2):(a=t,n=i/3,l.x=t/2,l.y2=i,r.y=i/3*2,r.x2=t/2,o.y=i/3,o.x2=t,o.x1=t/2),s.width=a,s.height=n,V.copyProperties(this,e,P.b),V.copyProperties(this.columns.template,s,P.b),s.stroke=this.riseFromOpenState.properties.stroke},t.prototype.createColumnTemplate=function(){return new dt},t}(ut);d.b.registeredClasses.OHLCSeries=pt,d.b.registeredClasses.OHLCSeriesDataItem=ct;var yt=function(e){function t(){var t=e.call(this)||this;return t.className="StepLineSeriesSegment",t}return n.c(t,e),t.prototype.drawSegment=function(e,t,i,a,n,s){if(e.length>0&&t.length>0)if(n){var r=k.moveTo(e[0]);if(e.length>0)for(var o=1;o<e.length;o++){var l=e[o];o/2==Math.round(o/2)?r+=k.moveTo(l):r+=k.lineTo(l)}this.strokeSprite.path=r,(this.fillOpacity>0||this.fillSprite.fillOpacity>0)&&(r=k.moveTo(e[0])+k.polyline(e),r+=k.lineTo(t[0])+k.polyline(t),r+=k.lineTo(e[0]),r+=k.closePath(),this.fillSprite.path=r)}else{r=k.moveTo(e[0])+k.polyline(e);this.strokeSprite.path=r,(this.fillOpacity>0||this.fillSprite.fillOpacity>0)&&(r+=k.lineTo(t[0])+k.polyline(t),r+=k.lineTo(e[0]),r+=k.closePath(),this.fillSprite.path=r)}},t}(q);d.b.registeredClasses.StepLineSeriesSegment=yt;var gt=function(e){function t(){var t=e.call(this)||this;return t.className="StepLineSeriesDataItem",t.applyTheme(),t}return n.c(t,e),t}(ee),ft=function(e){function t(){var t=e.call(this)||this;return t.className="StepLineSeries",t.applyTheme(),t.startLocation=0,t.endLocation=1,t}return n.c(t,e),t.prototype.createDataItem=function(){return new gt},t.prototype.addPoints=function(e,t,i,a,n){var s=this.startLocation,r=this.endLocation,o=this.xAxis.getX(t,i,s),l=this.yAxis.getY(t,a,s),h=this.xAxis.getX(t,i,r),u=this.yAxis.getY(t,a,r);if(o=g.fitToRange(o,-2e4,2e4),l=g.fitToRange(l,-2e4,2e4),h=g.fitToRange(h,-2e4,2e4),u=g.fitToRange(u,-2e4,2e4),!this.noRisers&&this.connect&&e.length>1){var d=e[e.length-1];this.baseAxis==this.xAxis&&(n?e.push({x:d.x,y:u}):e.push({x:o,y:d.y})),this.baseAxis==this.yAxis&&(n?e.push({x:h,y:d.y}):e.push({x:d.x,y:l}))}var c={x:o,y:l},p={x:h,y:u};n?e.push(p,c):e.push(c,p)},t.prototype.drawSegment=function(e,t,i){var a=!1;this.yAxis==this.baseAxis&&(a=!0),e.drawSegment(t,i,this.tensionX,this.tensionY,this.noRisers,a)},t.prototype.createSegment=function(){return new yt},Object.defineProperty(t.prototype,"noRisers",{get:function(){return this.getPropertyValue("noRisers")},set:function(e){this.setPropertyValue("noRisers",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"startLocation",{get:function(){return this.getPropertyValue("startLocation")},set:function(e){this.setPropertyValue("startLocation",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"endLocation",{get:function(){return this.getPropertyValue("endLocation")},set:function(e){this.setPropertyValue("endLocation",e,!0)},enumerable:!0,configurable:!0}),t}(te);d.b.registeredClasses.StepLineSeries=ft,d.b.registeredClasses.StepLineSeriesDataItem=gt;var mt=function(e){function t(){var t=e.call(this)||this;return t.className="RadarColumn",t}return n.c(t,e),t.prototype.createAssets=function(){this.radarColumn=this.createChild(xe.a),this.radarColumn.shouldClone=!1,this.radarColumn.strokeOpacity=void 0,this.column=this.radarColumn},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.radarColumn&&this.radarColumn.copyFrom(t.radarColumn)},t.prototype.getTooltipX=function(){var e=this.getPropertyValue("tooltipX");return f.isNumber(e)||(e=this.radarColumn.tooltipX),e},t.prototype.getTooltipY=function(){var e=this.getPropertyValue("tooltipX");return f.isNumber(e)||(e=this.radarColumn.tooltipY),e},t}(Ee.a);d.b.registeredClasses.RadarColumn=mt;var xt=function(e){function t(){var t=e.call(this)||this;return t.className="ColumnSeriesDataItem",t.applyTheme(),t}return n.c(t,e),t}(Ue),vt=function(e){function t(){var t=e.call(this)||this;return t.className="RadarColumnSeries",t.applyTheme(),t}return n.c(t,e),t.prototype.createColumnTemplate=function(){return new mt},t.prototype.validate=function(){this.chart.invalid&&this.chart.validate(),e.prototype.validate.call(this)},t.prototype.validateDataElementReal=function(e){var t,i,a,n,s=this,r=this.chart.startAngle,o=this.chart.endAngle,l=this.yField,h=this.yOpenField,u=this.xField,d=this.xOpenField,c=this.getStartLocation(e),p=this.getEndLocation(e),y=(o-r)/(this.dataItems.length*(this.end-this.start));r+=c*y,o-=(1-p)*y;var x=this.columns.template.percentWidth;f.isNaN(x)&&(x=100);var v=g.round((p-c)*(1-x/100)/2,5);if(c+=v,p-=v,this.baseAxis==this.xAxis?(a=g.getDistance({x:this.yAxis.getX(e,l,e.locations[l],"valueY"),y:this.yAxis.getY(e,l,e.locations[l],"valueY")}),n=g.getDistance({x:this.yAxis.getX(e,h,e.locations[h],"valueY"),y:this.yAxis.getY(e,h,e.locations[h],"valueY")}),t=this.xAxis.getAngle(e,d,c,"valueX"),i=this.xAxis.getAngle(e,u,p,"valueX")):(a=g.getDistance({x:this.yAxis.getX(e,l,c,"valueY"),y:this.yAxis.getY(e,l,c,"valueY")}),n=g.getDistance({x:this.yAxis.getX(e,h,p,"valueY"),y:this.yAxis.getY(e,h,p,"valueY")}),t=this.xAxis.getAngle(e,u,e.locations[u],"valueX"),i=this.xAxis.getAngle(e,d,e.locations[d],"valueX")),i<t){var b=i;i=t,t=b}t=g.fitToRange(t,r,o),i=g.fitToRange(i,r,o);var A=e.column;A||(A=this.columns.create(),e.column=A,V.forceCopyProperties(this.columns.template,A,P.b),e.addSprite(A),this.setColumnStates(A));var C=A.radarColumn;C.startAngle=t;var _=i-t;_>0?(C.arc=_,C.radius=a,C.innerRadius=n,A.__disabled=!1,A.parent=this.columnsContainer,m.each(this.axisRanges.iterator(),function(i){var r=e.rangesColumns.getKey(i.uid);r||(r=s.columns.create(),V.forceCopyProperties(s.columns.template,r,P.b),V.copyProperties(i.contents,r,P.b),r.dataItem&&O.remove(r.dataItem.sprites,r),e.addSprite(r),s.setColumnStates(r),e.rangesColumns.setKey(i.uid,r));var o=A.radarColumn;o.startAngle=t,o.arc=_,o.radius=a,o.innerRadius=n,o.invalid&&o.validate(),r.__disabled=!1,r.parent=s.columnsContainer})):this.disableUnusedColumns(e)},t.prototype.getPoint=function(e,t,i,a,n,s,r){s||(s="valueX"),r||(r="valueY");var o=this.yAxis.getX(e,i,n,r),l=this.yAxis.getY(e,i,n,r),h=g.getDistance({x:o,y:l});0==h&&(h=1e-5);var u=this.xAxis.getAngle(e,t,a,s);return{x:h*g.cos(u),y:h*g.sin(u)}},t.prototype.getMaskPath=function(){var e=this.yAxis.renderer;return k.arc(e.startAngle,e.endAngle-e.startAngle,e.pixelRadius,e.pixelInnerRadius)},t}(He);d.b.registeredClasses.RadarColumnSeries=vt,d.b.registeredClasses.RadarColumnSeriesDataItem=xt;var bt=i("HfWV"),At=function(e){function t(){var t=e.call(this)||this;return t.className="PyramidSeriesDataItem",t.applyTheme(),t}return n.c(t,e),t}(bt.b),Pt=function(e){function t(){var t=e.call(this)||this;return t.className="PyramidSeries",t.topWidth=Object(Y.c)(0),t.bottomWidth=Object(Y.c)(100),t.pyramidHeight=Object(Y.c)(100),t.valueIs="area",t.sliceLinks.template.width=0,t.sliceLinks.template.height=0,t.applyTheme(),t}return n.c(t,e),t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),f.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Pyramid Series"))},t.prototype.createDataItem=function(){return new At},t.prototype.validate=function(){e.prototype.validate.call(this),this._nextWidth=void 0},t.prototype.getNextValue=function(e){var t=e.index,i=e.getWorkingValue("value");t<this.dataItems.length-1&&(i=this.dataItems.getIndex(t+1).getWorkingValue("value"));return 0==i&&(i=1e-6),i},t.prototype.validateDataElements=function(){var t=this,i=this.slicesContainer.innerWidth,a=this.slicesContainer.innerHeight;if(this.dataItems.each(function(e){var n=e.getWorkingValue("value")/e.value,s=e.sliceLink;"vertical"==t.orientation?a-=s.pixelHeight*n:i-=s.pixelWidth*n}),this._pyramidHeight=F.relativeToValue(this.pyramidHeight,a),this._pyramidWidth=F.relativeToValue(this.pyramidHeight,i),"vertical"==this.orientation){var n=(a-this._pyramidHeight)/2;this.slicesContainer.y=n,this.labelsContainer.y=n,this.ticksContainer.y=n}else{var s=(i-this._pyramidWidth)/2;this.slicesContainer.x=s,this.labelsContainer.x=s,this.ticksContainer.x=s}e.prototype.validateDataElements.call(this)},t.prototype.decorateSlice=function(e){var t=this.dataItem.values.value.sum;if(0!=t){var i=e.slice,a=e.sliceLink,n=e.label,s=e.tick,r=(this.getNextValue(e),e.getWorkingValue("value"));0==r&&(r=1e-6);var o=this._pyramidWidth,l=this._pyramidHeight,h=this.slicesContainer.innerWidth,u=this.slicesContainer.innerHeight,d=a.pixelWidth,c=a.pixelHeight;if("vertical"==this.orientation){var p=F.relativeToValue(this.topWidth,h);f.isNumber(this._nextWidth)||(this._nextWidth=p);var y=F.relativeToValue(this.bottomWidth,h),g=this._nextWidth,m=Math.atan2(l,p-y);0==(P=Math.tan(Math.PI/2-m))&&(P=1e-8);var x=void 0,v=void 0;if("area"==this.valueIs){var b=(p+y)/2*l*r/t,A=Math.abs(g*g-2*b*P);v=(2*b-(x=(g-Math.sqrt(A))/P)*g)/x}else{v=g-(x=l*r/this.dataItem.values.value.sum)*P}i.height=x,i.width=h,i.bottomWidth=v,i.topWidth=g,a.topWidth=i.bottomWidth,a.bottomWidth=i.bottomWidth,i.y=this._nextY,this.alignLabels?n.x=0:n.x=h/2,n.y=i.pixelY+i.pixelHeight*s.locationY,this._nextY+=i.pixelHeight+c*r/e.value,a.y=this._nextY-c,a.x=h/2}else{p=F.relativeToValue(this.topWidth,u);f.isNumber(this._nextWidth)||(this._nextWidth=p);var P;y=F.relativeToValue(this.bottomWidth,u),g=this._nextWidth,m=Math.atan2(o,p-y);0==(P=Math.tan(Math.PI/2-m))&&(P=1e-8);var C=void 0;v=void 0;if("area"==this.valueIs)v=(2*(b=(p+y)/2*o*r/this.dataItem.values.value.sum)-(C=(g-Math.sqrt(g*g-2*b*P))/P)*g)/C;else v=g-(C=o*r/this.dataItem.values.value.sum)*P;i.width=C,i.height=u,i.bottomWidth=v,i.topWidth=g,a.topWidth=i.bottomWidth,a.bottomWidth=i.bottomWidth,i.x=this._nextY,this.alignLabels?n.y=this.labelsContainer.measuredHeight:n.y=u/2,n.x=i.pixelX+i.pixelWidth*s.locationX,this._nextY+=i.pixelWidth+d*r/e.value,a.x=this._nextY-d,a.y=u/2}this._nextWidth=i.bottomWidth}},Object.defineProperty(t.prototype,"topWidth",{get:function(){return this.getPropertyValue("topWidth")},set:function(e){this.setPercentProperty("topWidth",e,!1,!1,10,!1)&&this.invalidate()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"pyramidHeight",{get:function(){return this.getPropertyValue("pyramidHeight")},set:function(e){this.setPercentProperty("pyramidHeight",e,!1,!1,10,!1)&&this.invalidate()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"bottomWidth",{get:function(){return this.getPropertyValue("bottomWidth")},set:function(e){this.setPercentProperty("bottomWidth",e,!1,!1,10,!1)&&this.invalidate()},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"valueIs",{get:function(){return this.getPropertyValue("valueIs")},set:function(e){this.setPropertyValue("valueIs",e)&&this.invalidate()},enumerable:!0,configurable:!0}),t}(bt.a);d.b.registeredClasses.PyramidSeries=Pt,d.b.registeredClasses.PyramidSeriesDataItem=At;var Ct=function(e){function t(){var t=e.call(this)||this;return t.className="PictorialStackedSeriesDataItem",t.applyTheme(),t}return n.c(t,e),t}(At),_t=function(e){function t(){var t=e.call(this)||this;return t.className="PictorialStackedSeries",t.topWidth=Object(Y.c)(100),t.bottomWidth=Object(Y.c)(100),t.valueIs="height",t.applyTheme(),t._maskSprite=t.slicesContainer.createChild(P.a),t._maskSprite.visible=!1,t._maskSprite.zIndex=100,t}return n.c(t,e),t.prototype.validateDataElements=function(){var t=this.slicesContainer.maxWidth,i=this.slicesContainer.maxHeight,a=this._maskSprite,n=a.measuredWidth/a.scale,s=a.measuredHeight/a.scale,r=g.min(i/s,t/n);r==1/0&&(r=1),r=g.max(.001,r);var o=g.min(t,n*r),l=g.min(i,s*r);a.scale=r,"vertical"==this.orientation?(this.topWidth=o+4,this.bottomWidth=o+4,this.pyramidHeight=l,a.x=t/2,a.y=l/2):(this.topWidth=l+4,this.bottomWidth=l+4,this.pyramidHeight=o,a.valign="middle",a.x=o/2,a.y=i/2),a.verticalCenter="middle",a.horizontalCenter="middle",this.slicesContainer.mask=this._maskSprite,e.prototype.validateDataElements.call(this)},t.prototype.applyInternalDefaults=function(){e.prototype.applyInternalDefaults.call(this),f.hasValue(this.readerTitle)||(this.readerTitle=this.language.translate("Pyramid Series"))},t.prototype.createDataItem=function(){return new Ct},Object.defineProperty(t.prototype,"maskSprite",{get:function(){return this._maskSprite},enumerable:!0,configurable:!0}),t.prototype.initSlice=function(t){e.prototype.initSlice.call(this,t);var i=t.states.getKey("hover");i&&(i.properties.expandDistance=0)},t}(Pt);d.b.registeredClasses.PictorialStackedSeries=_t,d.b.registeredClasses.PictorialStackedSeriesDataItem=Ct;var It=i("d+vC"),Dt=i("ncT3"),Tt=function(e){function t(){var t=e.call(this)||this;return t.className="ConeColumn",t}return n.c(t,e),t.prototype.createAssets=function(){this.coneColumn=this.createChild(Dt.a),this.coneColumn.shouldClone=!1,this.column=this.coneColumn},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.coneColumn&&this.coneColumn.copyFrom(t.coneColumn)},t}(Ee.a);d.b.registeredClasses.ConeColumn=Tt;var Vt=function(e){function t(){var t=e.call(this)||this;return t.className="ConeSeriesDataItem",t.applyTheme(),t}return n.c(t,e),t}(Ue),St=function(e){function t(){var t=e.call(this)||this;return t.className="ConeSeries",t.applyTheme(),t}return n.c(t,e),t.prototype.createColumnTemplate=function(){return new Tt},t.prototype.getMaskPath=function(){var e=0,t=0,i=this.columns.getIndex(0);if(i)return this.baseAxis==this.xAxis?t=i.coneColumn.bottom.radiusY+1:e=i.coneColumn.bottom.radiusY+1,k.rectToPath({x:-e,y:0,width:this.xAxis.axisLength+e,height:this.yAxis.axisLength+t})},t.prototype.validateDataElementReal=function(t){if(e.prototype.validateDataElementReal.call(this,t),t.column){var i=t.column.coneColumn;i.fill=t.column.fill,this.baseAxis==this.yAxis?i.orientation="horizontal":i.orientation="vertical"}},t}(He);d.b.registeredClasses.ConeSeries=St,d.b.registeredClasses.ConeSeriesDataItem=Vt;var Rt=function(e){function t(){var t=e.call(this)||this;return t.className="CurvedColumn",t}return n.c(t,e),t.prototype.createAssets=function(){this.curvedColumn=this.createChild(P.a),this.curvedColumn.shouldClone=!1,this.setPropertyValue("tension",.7),this.width=Object(Y.c)(120),this.height=Object(Y.c)(120),this.column=this.curvedColumn},t.prototype.draw=function(){e.prototype.draw.call(this);var t,i=this.realWidth,a=this.realHeight,n=this.realX-this.pixelX,s=this.realY-this.pixelY,r=(this.width,1),o=1;"vertical"==this.orientation?(r=this.tension,t=[{x:0,y:a+s},{x:i/2,y:s},{x:i,y:a+s}]):(o=this.tension,t=[{x:n,y:0},{x:n+i,y:a/2},{x:n,y:a}]);var l=k.moveTo(t[0])+new Z.b(r,o).smooth(t);this.column.path=l},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.curvedColumn&&this.curvedColumn.copyFrom(t.curvedColumn)},Object.defineProperty(t.prototype,"tension",{get:function(){return this.getPropertyValue("tension")},set:function(e){this.setPropertyValue("tension",e,!0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"orientation",{get:function(){return this.getPropertyValue("orientation")},set:function(e){this.setPropertyValue("orientation",e,!0)},enumerable:!0,configurable:!0}),t}(Ee.a);d.b.registeredClasses.CurvedColumn=Rt;var Ft=function(e){function t(){var t=e.call(this)||this;return t.className="CurvedColumnSeriesDataItem",t.applyTheme(),t}return n.c(t,e),t}(Ue),Ot=function(e){function t(){var t=e.call(this)||this;return t.className="CurvedColumnSeries",t.applyTheme(),t}return n.c(t,e),t.prototype.createColumnTemplate=function(){return new Rt},t.prototype.validateDataElementReal=function(t){var i=t.column;i&&(t.column.curvedColumn.fill=t.column.fill,this.baseAxis==this.yAxis?i.orientation="horizontal":i.orientation="vertical");e.prototype.validateDataElementReal.call(this,t)},t}(He);d.b.registeredClasses.CurvedColumnSeries=Ot,d.b.registeredClasses.CurvedColumnSeriesDataItem=Ft;var kt=i("AaJ4"),wt=i("eN1s"),Lt=i("TDx+"),Xt=i("eAid"),Yt=i("8EhG"),jt=i("Meme"),Nt=function(e){function t(){var t=e.call(this)||this;return t.className="DurationAxisDataItem",t.applyTheme(),t}return n.c(t,e),t}(C.b),Mt=function(e){function t(){var t=e.call(this)||this;return t._baseUnit="second",t.className="DurationAxis",t.setPropertyValue("maxZoomFactor",1e6),t.applyTheme(),t}return n.c(t,e),t.prototype.formatLabel=function(e,t){return this.durationFormatter.format(e,t||this.axisDurationFormat)},t.prototype.adjustMinMax=function(t,i,a,s,r){var o,l,h,u=this.baseUnit;if(this.setPropertyValue("maxPrecision",0),"millisecond"==u||"second"==u||"minute"==u||"hour"==u){s<=1&&(s=1),s=Math.round(s);var d=t,c=i;0===a&&(a=Math.abs(i));var p,y=[60,30,20,15,10,2,1],f=1;"hour"==u&&(y=[24,12,6,4,2,1]);try{for(var m=n.g(y),x=m.next();!x.done;x=m.next()){var v=x.value;if(a/v>s){f=v;break}}}catch(e){l={error:e}}finally{try{x&&!x.done&&(h=m.return)&&h.call(m)}finally{if(l)throw l.error}}var b=Math.ceil((i-t)/f/s),A=Math.log(Math.abs(b))*Math.LOG10E,P=Math.pow(10,Math.floor(A))/10,C=b/P;p=f*(b=g.closest(y,C)*P);this.durationFormatter.getValueUnit(p,this.baseUnit);t=Math.floor(t/p)*p,i=Math.ceil(i/p)*p,r&&((t-=p)<0&&d>=0&&(t=0),(i+=p)>0&&c<=0&&(i=0)),o={min:t,max:i,step:p}}else o=e.prototype.adjustMinMax.call(this,t,i,a,s,r);return this.axisDurationFormat=this.durationFormatter.getFormat(o.step,o.max,this.baseUnit),o},Object.defineProperty(t.prototype,"tooltipDurationFormat",{get:function(){return this._tooltipDurationFormat},set:function(e){this._tooltipDurationFormat=e},enumerable:!0,configurable:!0}),t.prototype.getTooltipText=function(e){var t=g.round(this.positionToValue(e),this._stepDecimalPlaces);return this.adapter.apply("getTooltipText",this.formatLabel(t,this.tooltipDurationFormat))},Object.defineProperty(t.prototype,"baseUnit",{get:function(){return this._baseUnit},set:function(e){this._baseUnit!=e&&(this._baseUnit=e,this.durationFormatter.baseUnit=e,this.invalidate())},enumerable:!0,configurable:!0}),t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.baseUnit=t.baseUnit},t}(C.a);d.b.registeredClasses.DurationAxis=Mt,d.b.registeredClasses.DurationAxisDataItem=Nt;var Bt=i("Lrmi"),Wt=i("yrKf"),Et=i("Jwb0"),zt=function(e){function t(){var t=e.call(this)||this;t.className="CircleBullet";var i=t.createChild(ne.a);return i.shouldClone=!1,i.radius=5,i.isMeasured=!1,t.circle=i,t}return n.c(t,e),t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.circle.copyFrom(t.circle)},t}(Xe.a);d.b.registeredClasses.CircleBullet=zt;var Ut=function(e){function t(){var t=e.call(this)||this;return t.className="ErrorBullet",t.errorLine=t.createChild(P.a),t.errorLine.shouldClone=!1,t.width=20,t.height=20,t.strokeOpacity=1,t.isDynamic=!0,t}return n.c(t,e),t.prototype.validatePosition=function(){e.prototype.validatePosition.call(this);var t=this.pixelWidth/2,i=this.pixelHeight/2;this.errorLine.path=k.moveTo({x:-t,y:-i})+k.lineTo({x:t,y:-i})+k.moveTo({x:0,y:-i})+k.lineTo({x:0,y:i})+k.moveTo({x:-t,y:i})+k.lineTo({x:t,y:i})},t.prototype.copyFrom=function(t){e.prototype.copyFrom.call(this,t),this.errorLine.copyFrom(t.errorLine)},t}(Xe.a);d.b.registeredClasses.ErrorBullet=Ut;var Ht=i("MtRv"),Kt=i("uLGy"),Gt=i("Y9w3"),Zt=i("A6AV"),qt=i("Trvg"),Jt=i("Rnbi"),Qt=function(e){function t(){var t=e.call(this)||this;return t.className="NavigationBarDataItem",t.applyTheme(),t}return n.c(t,e),Object.defineProperty(t.prototype,"name",{get:function(){return this.properties.name},set:function(e){this.setProperty("name",e)},enumerable:!0,configurable:!0}),t}(Zt.a),$t=function(e){function t(){var t=e.call(this)||this;t.className="NavigationBar";var i=new B.a,a=new qt.a;a.valign="middle",a.paddingTop=8,a.paddingBottom=8,t.paddingBottom=2,t.links=new o.e(a),t._disposers.push(new o.c(t.links)),t._disposers.push(a),t._linksIterator=new m.ListIterator(t.links,function(){return t.links.create()}),t._linksIterator.createNewItems=!0;var n=new Jt.a;n.direction="right",n.width=8,n.height=12,n.fill=i.getFor("alternativeBackground"),n.fillOpacity=.5,n.valign="middle",n.marginLeft=10,n.marginRight=10,t.separators=new o.e(n),t._disposers.push(new o.c(t.separators)),t._disposers.push(n);var s=new qt.a;return t.activeLink=s,s.copyFrom(a),s.valign="middle",s.fontWeight="bold",t.width=Object(Y.c)(100),t.layout="grid",t.dataFields.name="name",t.applyTheme(),t}return n.c(t,e),t.prototype.validateDataElements=function(){this.removeChildren(),this._linksIterator.reset(),e.prototype.validateDataElements.call(this)},t.prototype.validateDataElement=function(t){var i;if(e.prototype.validateDataElement.call(this,t),t.index<this.dataItems.length-1){(i=this._linksIterator.getLast()).parent=this;var a=this.separators.create();a.parent=this,a.valign="middle"}else(i=this.activeLink).events.copyFrom(this.links.template.events),i.hide(0),i.show(),i.parent=this;i.dataItem=t,i.text=t.name,i.validate()},t}(Gt.a);d.b.registeredClasses.NavigationBar=$t,d.b.registeredClasses.NavigationBarDataItem=Qt,window.am4charts=a},yrKf:function(e,t,i){"use strict";i.d(t,"a",function(){return r});var a=i("m4/l"),n=i("AaJ4"),s=i("aCit"),r=function(e){function t(){var t=e.call(this)||this;return t.className="GridCircular",t.pixelPerfect=!1,t.applyTheme(),t}return a.c(t,e),Object.defineProperty(t.prototype,"innerRadius",{get:function(){return this.getPropertyValue("innerRadius")},set:function(e){this.setPercentProperty("innerRadius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"radius",{get:function(){return this.getPropertyValue("radius")},set:function(e){this.setPercentProperty("radius",e,!0,!1,10,!1)},enumerable:!0,configurable:!0}),t}(n.a);s.b.registeredClasses.GridCircular=r}},["XFs4"]);
//# sourceMappingURL=charts.js.map