<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('annotations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('project_id');
            $table->unsignedBigInteger('department_id')->nullable();
            $table->string('review_step')->nullable();
            $table->json('annotation_data');
            $table->timestamps();
            
            $table->foreign('project_id')->references('id')->on('website_builder_projects')->onDelete('cascade');
            $table->foreign('department_id')->references('id')->on('mcd_staff_roles')->onDelete('set null');
            $table->index(['project_id', 'department_id', 'review_step']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('annotations');
    }
};
