@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <section class="section">
            <div class="section-body">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4>Review Times > Edit Review Time</h4>
                            </div>
                            <form method="POST" action="{{ route('average-review-times.update', $averageReviewTime->id) }}">
                                @csrf
                                @method('PUT')
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Project Name <span class="text-danger">*</span></label>
                                                <select name="builder_project_id" class="form-control @error('builder_project_id') is-invalid @enderror">
                                                    <option value="">Select Project</option>
                                                    @foreach($projects as $project)
                                                        <option value="{{ $project->id }}" {{ $project->id == $averageReviewTime->builder_project_id ? 'selected' : '' }}>
                                                            {{ $project->name }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                @error('builder_project_id') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Days <span class="text-danger">*</span></label>
                                                <input type="number" id="days" name="days" class="form-control @error('days') is-invalid @enderror" value="{{ old('days', $averageReviewTime->days) }}" min="0" step="1" required>
                                                @error('days') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>End Date <span class="text-danger">*</span></label>
                                                <input type="date" id="end_date" name="end_date" class="form-control @error('end_date') is-invalid @enderror" value="{{ old('end_date', $averageReviewTime->end_date) }}" required>
                                                @error('end_date') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer text-right">
                                    <button class="btn btn-primary mr-1" type="submit">Update</button>
                                    <a href="{{ route('average-review-times.index') }}" class="btn btn-secondary">Cancel</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <script>
        document.getElementById('days').addEventListener('input', function () {
            let days = parseInt(this.value);
            if (!isNaN(days) && days >= 0) {
                let today = new Date();
                today.setDate(today.getDate() + days);
                let formattedDate = today.toISOString().split('T')[0]; // Format YYYY-MM-DD
                document.getElementById('end_date').value = formattedDate;
            } else {
                document.getElementById('end_date').value = ''; // Reset if invalid input
            }
        });
    </script>
@endsection