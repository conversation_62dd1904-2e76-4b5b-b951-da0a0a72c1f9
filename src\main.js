// Patch Fabric.js to fix 'alphabetical' bug
const origSet = Object.getOwnPropertyDescriptor(CanvasRenderingContext2D.prototype, 'textBaseline').set;
Object.defineProperty(CanvasRenderingContext2D.prototype, 'textBaseline', {
  set(value) {
    if (value === 'alphabetical') value = 'alphabetic';
    origSet.call(this, value);
  }
});

import { createApp } from 'vue'
import App from './App.vue'
import * as pdfjsLib from 'pdfjs-dist'
// pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf-annotator/pdf.worker.min.js'
pdfjsLib.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js`


createApp(App).mount('#app') 