<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Physical Inspection Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12pt;
            margin: 20px;
        }
        h1 {
            text-align: center;
            color: #333;
        }
        h2 {
            color: #444;
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
        }
        .section {
            margin-bottom: 20px;
        }
        .row {
            display: flex;
            flex-wrap: wrap;
        }
        .col-md-6 {
            width: 48%;
            margin-right: 2%;
        }
        p {
            margin: 5px 0;
        }
        strong {
            font-weight: bold;
        }
        .badge {
            padding: 5px 10px;
            border-radius: 3px;
            display: inline-block;
        }
        .badge-success { background-color: #28a745; color: white; }
        .badge-danger { background-color: #dc3545; color: white; }
        .badge-warning { background-color: #ffc107; color: black; }
        .badge-light { background-color: #f8f9fa; color: black; }
        img {
            max-width: 200px;
            margin: 10px 0;
        }
        .image-gallery {
            display: flex;
            flex-wrap: wrap;
        }
        .image-gallery img {
            margin-right: 10px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>Physical Inspection Report</h1>

    <div class="section">
        <h2>Project Details</h2>
        <div class="row">
            <div class="col-md-6">
                <p><strong>Project Name:</strong> {{ $project->project_name }}</p>
                <p><strong>Address:</strong> {{ $project->full_address }}</p>
                <p><strong>Project Status:</strong> {{ $project->status }}</p>
                <p><strong>Departments:</strong> {{ implode(', ', $project->department_names) }}</p>
            </div>
            <div class="col-md-6">
                <p><strong>Scope of Work:</strong> {{ $project->scope_of_work ?? 'N/A' }}</p>
                <p><strong>Expected Start Date:</strong> {{ $project->expected_start_date ?? 'N/A' }}</p>
                <p><strong>Expected End Date:</strong> {{ $project->expected_end_date ?? 'N/A' }}</p>
                <p><strong>Property Owner:</strong> {{ $project->property_owner_name ?? 'N/A' }}</p>
            </div>
        </div>
        @if ($project->project_image)
            <p><strong>Project Image:</strong></p>
            <img src="{{ public_path($project->project_image) }}" alt="Project Image">
        @endif
    </div>

    @if ($report)
        <div class="section">
            <h2>Inspection Report Details</h2>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Inspectors:</strong>
                        @foreach ($inspectorData as $inspector)
                            {{ $inspector['name'] }}@if (!$loop->last), @endif
                        @endforeach
                    </p>
                    <p><strong>Departments:</strong>
                        @foreach ($inspectorData as $inspector)
                            {{ $inspector['department'] }}@if (!$loop->last), @endif
                        @endforeach
                    </p>
                    <p><strong>Inspection Date:</strong> {{ $report->inspection_done_at ? $report->inspection_done_at->format('Y-m-d H:i:s') : 'N/A' }}</p>
                    <p><strong>Note:</strong> {{ $report->note ?? 'N/A' }}</p>
                    <p><strong>Violation Note:</strong> {{ $report->violation_note ?? 'N/A' }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>Inspection Report Status:</strong> <span class="badge {{ $statusText == 'Approval' ? 'badge-success' : ($statusText == 'Violation' ? 'badge-danger' : ($statusText == 'Pending' ? 'badge-warning' : 'badge-light')) }}">{{ $statusText }}</span></p>
                    <p><strong>Potential Violations:</strong> {{ implode(', ', $report->potential_violations ?? []) ?: 'None' }}</p>
                </div>
            </div>
            @if (!empty($report->report_images))
                <p><strong>Images:</strong></p>
                <div class="image-gallery">
                    @foreach ($report->report_images as $image)
                        <img src="{{ public_path($image) }}" alt="Report Image">
                    @endforeach
                </div>
            @endif
        </div>
    @else
        <div class="section">
            <p style="color: red;">No physical inspection report available for this project.</p>
        </div>
    @endif
</body>
</html>