<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Annotation extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'department_id',
        'review_step',
        'annotation_data'
    ];

    protected $casts = [
        'annotation_data' => 'array'
    ];

    /**
     * Get the project that owns the annotation.
     */
    public function project()
    {
        return $this->belongsTo(WebsiteBuilderProject::class, 'project_id');
    }

    /**
     * Get the department associated with the annotation.
     */
    public function department()
    {
        return $this->belongsTo(McdStaffRole::class, 'department_id');
    }
}
