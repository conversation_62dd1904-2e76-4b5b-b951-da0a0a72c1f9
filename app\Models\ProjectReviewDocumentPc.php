<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProjectReviewDocumentPc extends Model
{
    protected $fillable = [
        'project_id',
        'department_id',
        'review_step',
        'document_type',
        'remarks',
        'uploaded_by',
        'uploaded_by_role',
        'reviewed_by'
    ];

    protected $table = 'project_review_documents_pc';

    public function reviewer()
{
    return $this->belongsTo(\App\Models\McdStaff::class, 'reviewed_by');
}
}