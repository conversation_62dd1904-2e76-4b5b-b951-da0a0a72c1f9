<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;


class Review extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'review_type',
        'started_at',
        'completed_at',
        'remarks',
    ];

    protected $dates = ['started_at', 'completed_at'];

    public function project()
{
    return $this->belongsTo(WebsiteBuilderProject::class, 'project_id');
}
}
