<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProjectDepartmentPermit extends Model
{
    protected $fillable = [
        'website_builder_project_id',
        'mcd_staff_role_id',
        'permit_number',
    ];

    public function project()
    {
        return $this->belongsTo(WebsiteBuilderProject::class, 'website_builder_project_id');
    }

    public function department()
    {
        return $this->belongsTo(McdStaffRole::class, 'mcd_staff_role_id');
    }
}