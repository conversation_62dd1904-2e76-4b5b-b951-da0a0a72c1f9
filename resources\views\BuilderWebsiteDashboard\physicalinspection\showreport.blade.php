@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4>Physical Inspection > Field Reports Submitted</h4>
                    </div>
                    <div class="card-body">
                        @if ($projects->count() > 0)
                            <!-- Filter Section -->
                            <div class="row mb-4">
                                <div class="col-md-3">
                                    <label for="filter-project" class="form-label">Filter by Project Name</label>
                                    <input type="text" class="form-control" id="filter-project"
                                        placeholder="Search project name...">
                                </div>
                                <div class="col-md-3">
                                    <label for="filter-department" class="form-label">Filter by Department</label>
                                    <form method="GET" action="{{ route('physicalinspection.report') }}">
                                        <select class="form-select" id="filter-department" name="department" onchange="this.form.submit()">
                                            <option value="">All Departments</option>
                                            @foreach ($departments as $id => $name)
                                                <option value="{{ $id }}" {{ request('department') == $id ? 'selected' : '' }}>{{ $name }}</option>
                                            @endforeach
                                        </select>
                                    </form>
                                </div>
                                <div class="col-md-3">
                                    <label for="filter-status" class="form-label">Filter by Status</label>
                                    <select class="form-select" id="filter-status">
                                        <option value="">All Status</option>
                                        <option value="Approved">Approved</option>
                                        <option value="Violation">Violation</option>
                                        <option value="Pending">Pending</option>
                                        <option value="No Report">No Report</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="filter-address" class="form-label">Filter by Address</label>
                                    <input type="text" class="form-control" id="filter-address"
                                        placeholder="Search address...">
                                </div>
                            </div>

                            <!-- Clear Filters Button -->
                            <div class="row mb-3">
                                <div class="col-12">
                                    <button type="button" class="btn btn-secondary btn-sm" id="clear-filters">
                                        <i class="fas fa-times"></i> Clear All Filters
                                    </button>
                                    <span class="ms-3 text-muted" id="filter-info">Showing all records</span>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="table-1">
                                    <thead class="bg-gray-200 text-gray-800">
                                        <tr>
                                            <th>
                                                Project Name
                                                <button class="btn btn-sm p-0 ms-1 sort-btn" data-column="0"
                                                    data-order="asc">
                                                    <i class="fas fa-sort text-muted"></i>
                                                </button>
                                            </th>
                                            <th>
                                                Address
                                                <button class="btn btn-sm p-0 ms-1 sort-btn" data-column="1"
                                                    data-order="asc">
                                                    <i class="fas fa-sort text-muted"></i>
                                                </button>
                                            </th>
                                            <th>
                                                Department (Trade)
                                                <button class="btn btn-sm p-0 ms-1 sort-btn" data-column="2"
                                                    data-order="asc">
                                                    <i class="fas fa-sort text-muted"></i>
                                                </button>
                                            </th>
                                            <th>
                                                Inspection Status
                                                <button class="btn btn-sm p-0 ms-1 sort-btn" data-column="3"
                                                    data-order="asc">
                                                    <i class="fas fa-sort text-muted"></i>
                                                </button>
                                            </th>
                                            <th>
                                                Inspection Date
                                                <button class="btn btn-sm p-0 ms-1 sort-btn" data-column="4"
                                                    data-order="asc">
                                                    <i class="fas fa-sort text-muted"></i>
                                                </button>
                                            </th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody id="table-body">
                                        @foreach ($projects as $project)
                                            <tr class="table-row"
                                                data-project="{{ strtolower($project->project_name ?? '') }}"
                                                data-address="{{ strtolower($project->full_address ?? '') }}"
                                                data-department="{{ strtolower(implode(', ', $project->department_names ?? [])) }}"
                                                data-status="{{ $project->physicalInspectionReportnew
                                                    ? (function () use ($project) {
                                                        $statusMap = ['0' => 'Approved', '1' => 'Violation', '2' => 'Pending'];
                                                        return strtolower($statusMap[$project->physicalInspectionReportnew->report_status] ?? 'no report');
                                                    })()
                                                    : 'no report' }}">
                                                <td>
                                                    @if ($project->project_name)
                                                        <a href="{{ route('myprojects.show.status', ['id' => $project->id]) }}">
                                                            {{ $project->project_name }}
                                                        </a>
                                                    @else
                                                        N/A
                                                    @endif
                                                </td>
                                                <td>{{ $project->full_address ?? 'N/A' }}</td>
                                                <td>
                                                    @if (!empty($project->department_names))
                                                        {{ implode(', ', $project->department_names) }}
                                                    @else
                                                        N/A
                                                    @endif
                                                </td>
                                                <td>
                                                    @php
                                                        $statusMap = [
                                                            '0' => 'Approved',
                                                            '1' => 'Violation',
                                                            '2' => 'Pending',
                                                        ];
                                                        $status = $project->physicalInspectionReportnew
                                                            ? ($statusMap[$project->physicalInspectionReportnew->report_status] ?? 'No Report')
                                                            : 'No Report';
                                                    @endphp
                                                    <span
                                                        class="badge 
                                                        @if ($status == 'Approved') bg-success
                                                        @elseif($status == 'Violation') bg-danger
                                                        @elseif($status == 'Pending') bg-warning
                                                        @else bg-secondary @endif">
                                                        {{ $status }}
                                                    </span>
                                                </td>
                                                <td
                                                    data-sort="{{ $project->physicalInspectionReportnew?->inspection_done_at ?? '' }}">
                                                    {{ $project->physicalInspectionReportnew?->inspection_done_at ? \Carbon\Carbon::parse($project->physicalInspectionReportnew->inspection_done_at)->format('d M Y, h:i A') : 'N/A' }}
                                                </td>
                                                <td>
                                                    @if ($project->physicalInspectionReportnew)
                                                        <a href="{{ route('physical-inspection.detailreport', ['id' => $project->id]) }}"
                                                            class="btn btn-primary btn-sm">View Report</a>
                                                    @else
                                                        <span class="text-muted">No Report Available</span>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination Info -->
                            <div class="row mt-3">
                                <div class="col-sm-6">
                                    <div class="dataTables_info" id="table-info">
                                        Showing <span id="showing-start">{{ $projects->firstItem() }}</span> to
                                        <span id="showing-end">{{ $projects->lastItem() }}</span>
                                        of <span id="total-entries">{{ $projects->total() }}</span> entries
                                        <span id="filtered-info" class="text-muted" style="display: none;">(filtered from
                                            <span id="total-original">{{ $projects->total() }}</span> total entries)</span>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="dataTables_paginate">
                                        {{ $projects->appends(request()->query())->links() }}
                                    </div>
                                </div>
                            </div>
                        @else
                            <div class="alert alert-info text-center">
                                No projects found.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .sort-btn {
            border: none;
            background: none;
        }

        .sort-btn:hover {
            background: rgba(0, 0, 0, 0.1);
        }

        .sort-btn.active {
            color: #007bff;
        }

        .table th {
            white-space: nowrap;
        }

        .badge {
            font-size: 0.8em;
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const filterProject = document.getElementById('filter-project');
            const filterDepartment = document.getElementById('filter-department');
            const filterStatus = document.getElementById('filter-status');
            const filterAddress = document.getElementById('filter-address');
            const clearFiltersBtn = document.getElementById('clear-filters');
            const tableRows = document.querySelectorAll('.table-row');
            const filterInfo = document.getElementById('filter-info');
            const filteredInfo = document.getElementById('filtered-info');
            const showingStart = document.getElementById('showing-start');
            const showingEnd = document.getElementById('showing-end');
            const totalEntries = document.getElementById('total-entries');
            const totalOriginal = document.getElementById('total-original');
            const sortBtns = document.querySelectorAll('.sort-btn');

            const totalRows = tableRows.length;

            // Filter function
            function filterTable() {
                const projectFilter = filterProject.value.toLowerCase();
                const departmentFilter = filterDepartment.value.toLowerCase();
                const statusFilter = filterStatus.value.toLowerCase();
                const addressFilter = filterAddress.value.toLowerCase();

                let visibleCount = 0;
                let hasActiveFilter = projectFilter || departmentFilter || statusFilter || addressFilter;

                tableRows.forEach(row => {
                    const projectText = row.dataset.project;
                    const departmentText = row.dataset.department;
                    const statusText = row.dataset.status;
                    const addressText = row.dataset.address;

                    const matchProject = !projectFilter || projectText.includes(projectFilter);
                    const matchDepartment = !departmentFilter || departmentText.includes(departmentFilter);
                    const matchStatus = !statusFilter || statusText.includes(statusFilter);
                    const matchAddress = !addressFilter || addressText.includes(addressFilter);

                    if (matchProject && matchDepartment && matchStatus && matchAddress) {
                        row.style.display = '';
                        visibleCount++;
                    } else {
                        row.style.display = 'none';
                    }
                });

                // Update info
                if (hasActiveFilter) {
                    filterInfo.textContent = `Showing filtered results (${visibleCount} of ${totalRows})`;
                    filteredInfo.style.display = 'inline';
                    totalEntries.textContent = visibleCount;
                    totalOriginal.textContent = totalRows;
                } else {
                    filterInfo.textContent = 'Showing all records';
                    filteredInfo.style.display = 'none';
                    totalEntries.textContent = totalRows;
                }

                showingStart.textContent = visibleCount > 0 ? '1' : '0';
                showingEnd.textContent = visibleCount;
            }

            // Clear filters function
            function clearFilters() {
                filterProject.value = '';
                filterDepartment.value = '';
                filterStatus.value = '';
                filterAddress.value = '';
                window.location = '{{ route('physicalinspection.report') }}';
            }

            // Sort function
            function sortTable(columnIndex, order) {
                const tbody = document.getElementById('table-body');
                const rows = Array.from(tbody.querySelectorAll('.table-row'));

                rows.sort((a, b) => {
                    let aVal, bVal;

                    if (columnIndex === 4) { // Date column
                        aVal = a.children[columnIndex].dataset.sort || '';
                        bVal = b.children[columnIndex].dataset.sort || '';
                    } else {
                        aVal = a.children[columnIndex].textContent.trim().toLowerCase();
                        bVal = b.children[columnIndex].textContent.trim().toLowerCase();
                    }

                    if (order === 'asc') {
                        return aVal.localeCompare(bVal);
                    } else {
                        return bVal.localeCompare(aVal);
                    }
                });

                // Re-append sorted rows
                rows.forEach(row => tbody.appendChild(row));
            }

            // Event listeners
            filterProject.addEventListener('input', filterTable);
            filterDepartment.addEventListener('change', function() {
                this.form.submit();
            });
            filterStatus.addEventListener('change', filterTable);
            filterAddress.addEventListener('input', filterTable);
            clearFiltersBtn.addEventListener('click', clearFilters);

            // Sort event listeners
            sortBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const column = parseInt(this.dataset.column);
                    const currentOrder = this.dataset.order;
                    const newOrder = currentOrder === 'asc' ? 'desc' : 'asc';

                    // Reset all sort buttons
                    sortBtns.forEach(b => {
                        b.classList.remove('active');
                        b.innerHTML = '<i class="fas fa-sort text-muted"></i>';
                        b.dataset.order = 'asc';
                    });

                    // Update current button
                    this.classList.add('active');
                    this.innerHTML = newOrder === 'asc' ?
                        '<i class="fas fa-sort-up"></i>' :
                        '<i class="fas fa-sort-down"></i>';
                    this.dataset.order = newOrder;

                    sortTable(column, newOrder);
                });
            });

            // Initialize
            filterTable();
        });
    </script>
@endsection