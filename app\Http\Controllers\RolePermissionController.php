<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\RoleMcd;
use App\Models\Permission;
use App\Models\McdStaff;
use Illuminate\Support\Facades\Auth;



class RolePermissionController extends Controller
{

    public function index()
{
    $roles = RoleMcd::all(); // fetch all roles
    return view('BuilderWebsiteDashboard.permissions.index', compact('roles'));
}

public function assignPermissions($id)
{ 
    $user = Auth::guard('web')->user() ?? Auth::guard('staff_mcd')->user();
    if (!$user || $user->role_id !== 3) {
        return redirect()->route('dashboard')->with('error', 'Unauthorized access. Only builders can assign permissions.');
    }

    $role = RoleMcd::findOrFail($id);
    $allModules = [
        'dashboard' => 'Dashboard',
        'department_trade' => 'Department (Trades)',
        'inspectors' => 'Inspectors',
        'review_processes'=> 'Review Process',
        'departmental_review' => 'Departmental Review',
        'plannining_commission_review' => 'Planning Commission Review',
        'total_review_process' => 'Total Projects Review',
        'assign_puid' => 'Assign PUID',
        'assign_permit_number' => 'Assign Permit Number',
        'avg_review_time' => 'Avg Review Time',
        'active_inspections' => 'Active Inspections',
        'field_reports_submitted'=>'Field Reports Submitted',
        'compliance_flags_raised'=>'Compliance Flags Raised',
        'backlogs_analysis'=>'Backlogs Analysis',
        'notification'=>'List Of Notification',
        'compliance'=>'Compliance',
        'guidelines'=>'Guidelines',
        'communication'=>'Communication',
        'rules'=>'Rules',


        
    ];

    $permissions = array_filter((array) $role->permissions, 'is_string');
    // Check if role has any permissions set
    $hasPermissions = $role->permissions && count($role->permissions) > 0;
    $modules = $allModules;
    $permissions = is_array($role->permissions) ? array_keys($role->permissions) : [];

    //dd($modules);    

    return view('BuilderWebsiteDashboard.roles.assign_permissions', compact('role', 'modules'));
}

public function storePermissions(Request $request, $id)
{
    $user = Auth::guard('web')->user() ?? Auth::guard('staff_mcd')->user();
    if (!$user || $user->role_id !== 3) {
        return redirect()->route('dashboard')->with('error', 'Unauthorized access. Only mcd can assign permissions.');
    }

    $role = RoleMcd::findOrFail($id);
    $permissionsInput = $request->input('permissions', []);
    $permissions = [];

    foreach ($permissionsInput as $module => $data) {
        if (isset($data['enabled']) && $data['enabled'] == 1) {
            if ($data['access'] === 'no_permission') {
                // Skip adding this module to permissions if "No Permission" is selected
                continue;
            }
            $permissions[$module] = [
                'read' => ($data['access'] === 'read' || $data['access'] === 'write') ? 1 : 0,
                'write' => $data['access'] === 'write' ? 1 : 0
            ];
        }
    }

    $role->update(['permissions' => $permissions]);

    return redirect()->route('mcd-permission-roles.assign-permissions',$id)
        ->with('success', 'Permissions assigned successfully');
}

// public function storePermissions(Request $request, $id)
//     {
//         $user = Auth::guard('web')->user() ?? Auth::guard('mcd')->user();

//         // Only builders (role_id = 2) can save permissions
//         if (!$user || $user->role_id !== null) {
//             return redirect()->route('dashboard')->with('error', 'Unauthorized access. Only builders can assign permissions.');
//         }

//         $role = RoleMcd::findOrFail($id);

//         $permissionsInput = $request->input('permissions', []);
//         $permissions = [];

//         foreach ($permissionsInput as $module => $data) {
//             if (isset($data['enabled']) && $data['enabled'] == 1) {
//                 if ($data['access'] === 'no_permission') {
//                     continue;
//                 }

//                 $permissions[$module] = [
//                     'read' => ($data['access'] === 'read' || $data['access'] === 'write') ? 1 : 0,
//                     'write' => $data['access'] === 'write' ? 1 : 0,
//                 ];
//             }
//         }

//         // Store as JSON
//         $role->update([
//             'permissions' => json_encode($permissions),
//         ]);

//         return redirect()->route('mcd-permission-roles.assign-permissions', $id)
//                          ->with('success', 'Permissions assigned successfully');
//     }


//    public function edit($roleId)
// {
//     $role = RoleMcd::with('permissions')->findOrFail($roleId);
//     $permissions = Permission::all();
//     $staffs = McdStaff::pluck('staff_name','id');

//     return view('BuilderWebsiteDashboard.permissions.edit', compact('role', 'permissions','staffs'));
// }

public function update(Request $request, $roleId)
{
    $role = RoleMcd::findOrFail($roleId);

    $permissions = $request->input('permissions', []);

    $syncData = [];
    foreach ($permissions as $permissionId => $actions) {
        $syncData[$permissionId] = [
            'can_read' => isset($actions['can_read']),
            'can_write' => isset($actions['can_write']),
        ];
    }

    $role->permissions()->sync($syncData);

    return redirect()->back()->with('success', 'Permissions updated.');
}

}
