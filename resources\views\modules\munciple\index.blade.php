@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">Municipal > List</h4>
                        <form action="{{ route('Municipals.create') }}" method="GET">
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-plus"></i> Add New Municipal +
                            </button>
                        </form>
                    </div>
                    <div class="card-body">
                        {{-- @if (session('success'))
                            <div class="alert alert-success">{{ session('success') }}</div>
                        @endif --}}
                        <div class="table-responsive">
                            <table class="table table-striped" id="table-1">
                                <thead>
                                    <tr>
                                        <th>S.No</th>
                                        <th>Title</th>
                                        <th>Location</th>
                                        <th>Priority</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($Municipals as $key => $Municipal)
                                        <tr>
                                            <td>{{ $key + 1 }}</td>
                                            <td>{{ $Municipal->title }}</td>
                                            <td>{{ $Municipal->location }}</td>
                                            
                                            <td>{{ $Municipal->priority }}</td>
                                            <td>
                                                <label class="switch">
                                                    <input type="checkbox" class="status-toggle" 
                                                        data-id="{{ $Municipal->id }}" 
                                                        {{ $Municipal->status == 'active' ? 'checked' : '' }}>
                                                    <span class="slider round"></span>
                                                </label>
                                            </td>
                                            <td>
                                               
                                                <a href="{{ route('Municipals.edit', $Municipal->id) }}" class="btn btn-sm btn-primary">
                                                    <i class="fa fa-edit"></i>
                                                </a>
                                                <form action="{{ route('Municipals.destroy', $Municipal->id) }}" method="POST" style="display:inline;">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger delete-btn" data-name="{{ $Municipal->title }}">
                                                        <i class="fa fa-trash"></i>
                                                    </button>
                                                </form>
                                            </td>
                                           
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- CSS for Toggle Switch -->
    <style>
        .switch {
            position: relative;
            display: inline-block;
            width: 30px;
            height: 17px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #dc3545; /* Red for inactive */
            transition: .4s;
            border-radius: 17px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 13px;
            width: 13px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #28a745; /* Green for active */
        }

        input:checked + .slider:before {
            transform: translateX(13px);
        }
    </style>

    <!-- JavaScript -->
    {{-- <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Success Message
            @if(session('success'))
                Swal.fire({
                    title: "Success!",
                    text: "{{ session('success') }}",
                    icon: "success",
                    showConfirmButton: false,
                    timer: 4000,
                    timerProgressBar: true,
                    position: "top-end",
                    toast: true,
                    customClass: {
                        popup: 'swal2-popup-custom',
                        title: 'swal2-title-custom',
                        content: 'swal2-content-custom'
                    }
                });
            @endif

            // Delete Confirmation
            document.querySelectorAll(".delete-btn").forEach(button => {
                button.addEventListener("click", function(event) {
                    event.preventDefault();
                    let form = this.closest("form");
                    let name = this.getAttribute("data-name");
                    
                    Swal.fire({
                        title: "Are you sure?",
                        text: "You are about to delete " + name,
                        icon: "warning",
                        showCancelButton: true,
                        confirmButtonColor: "#d33",
                        cancelButtonColor: "#3085d6",
                        confirmButtonText: "Yes, delete it!"
                    }).then((result) => {
                        if (result.isConfirmed) {
                            form.submit();
                        }
                    });
                });
            });

            // Status Toggle Functionality
           
        });
    </script> --}}


    <script>
        document.addEventListener("DOMContentLoaded", function() {
            // Success Message
            @if(session('success'))
                Swal.fire({
                    title: "Success!",
                    text: "{{ session('success') }}",
                    icon: "success",
                    showConfirmButton: false,
                    timer: 4000,
                    timerProgressBar: true,
                    position: "top-end",
                    toast: true,
                    customClass: {
                        popup: 'swal2-popup-custom',
                        title: 'swal2-title-custom',
                        content: 'swal2-content-custom'
                    }
                });
            @endif
    
            // Delete Confirmation
            document.querySelectorAll(".delete-btn").forEach(button => {
                button.addEventListener("click", function(event) {
                    event.preventDefault();
                    let form = this.closest("form");
                    let name = this.getAttribute("data-name");
                    
                    Swal.fire({
                        title: "Are you sure?",
                        text: "You are about to delete " + name,
                        icon: "warning",
                        showCancelButton: true,
                        confirmButtonColor: "#d33",
                        cancelButtonColor: "#3085d6",
                        confirmButtonText: "Yes, delete it!"
                    }).then((result) => {
                        if (result.isConfirmed) {
                            form.submit();
                        }
                    });
                });
            });
    
            // Status Toggle Functionality
            document.querySelectorAll(".status-toggle").forEach(toggle => {
                toggle.addEventListener("change", function(event) {
                    event.preventDefault();
                    
                    // Revert the checkbox to its original state
                    this.checked = !this.checked;
    
                    // Show warning message
                    Swal.fire({
                        title: "Restricted Action",
                        text: "You cannot change the status",
                        icon: "warning",
                        confirmButtonColor: "#3085d6",
                        confirmButtonText: "OK",
                        position: "top-end",
                        toast: true,
                        showConfirmButton: false,
                        timer: 3000,
                        timerProgressBar: true,
                        customClass: {
                            popup: 'swal2-popup-custom',
                            title: 'swal2-title-custom',
                            content: 'swal2-content-custom'
                        }
                    });
                });
            });
        });
    </script>

    <!-- Custom CSS for SweetAlert Styling -->
    <style>
        .swal2-popup-custom {
            border-radius: 8px;
            padding: 15px;
            background: #ffffff;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
        }
        .swal2-title-custom {
            color: #28a745;
            font-weight: bold;
            font-size: 18px;
        }
        .swal2-content-custom {
            font-size: 14px;
            color: #555;
        }
    </style>
@endsection