@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <section class="section">
            <div class="section-body">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4>Roles Management > Edit Staff</h4>
                            </div>
                            <form action="{{ route('mcd-staff.update', $staff->id) }}" method="POST">
                                @csrf
                                @method('PUT')
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                            <label class="block text-base font-semibold text-gray-800">Role <span class="text-red-500">*</span></label>
                                               <input type="hidden" name="role_id" value="{{ $selectedRole->id ?? old('role_id') }}">
                                                <input type="text" class="form-control" value="{{ $selectedRole->name ?? $roles->find(old('role_id'))?->name }}" disabled>

                                                @error('role_id') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                            </div>
                                        </div>
                                         <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Department (Trade) <span class="text-danger">*</span></label>
                                                <select name="department_id" class="form-control @error('department_id') is-invalid @enderror">
                                                    <option value="">Select Department</option>
                                                    @foreach($departments as $department)
                                                        <option value="{{ $department->id }}" {{ old('department_id', $staff->department_id) == $department->id ? 'selected' : '' }}>{{ $department->name }}</option>
                                                    @endforeach
                                                </select>
                                                @error('department_id') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                            </div>
                                        </div>        
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Staff Name <span class="text-danger">*</span></label>
                                                <input type="text" name="staff_name" class="form-control @error('staff_name') is-invalid @enderror" value="{{ old('staff_name', $staff->staff_name) }}">
                                                @error('staff_name') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Email <span class="text-danger">*</span></label>
                                                <input type="email" name="email" class="form-control @error('email') is-invalid @enderror" value="{{ old('email', $staff->email) }}">
                                                @error('email') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Password (Leave blank to keep unchanged)</label>
                                                <input type="password" name="password" class="form-control @error('password') is-invalid @enderror">
                                                @error('password') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                            </div>
                                        </div>
                                       
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Contact Number</label>
                                                <input type="text" name="contact_number" class="form-control @error('contact_number') is-invalid @enderror" value="{{ old('contact_number', $staff->contact_number) }}">
                                                @error('contact_number') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Status</label>
                                                <select name="status" class="form-control @error('status') is-invalid @enderror">
                                                    <option value="1" {{ old('status', $staff->status) ? 'selected' : '' }}>Active</option>
                                                    <option value="0" {{ !old('status', $staff->status) ? 'selected' : '' }}>Inactive</option>
                                                </select>
                                                @error('status') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label>Description</label>
                                                <textarea name="description" class="form-control @error('description') is-invalid @enderror" rows="4">{{ old('description', $staff->description) }}</textarea>
                                                @error('description') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label>Address</label>
                                                <textarea name="address" class="form-control @error('address') is-invalid @enderror" rows="4">{{ old('address', $staff->address) }}</textarea>
                                                @error('address') <div class="invalid-feedback">{{ $message }}</div> @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer text-right">
                                    <button class="btn btn-primary mr-1" type="submit">Update</button>
                                    <a href="{{ route('mcd-staff.index') }}" class="btn btn-secondary">Cancel</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
@endsection