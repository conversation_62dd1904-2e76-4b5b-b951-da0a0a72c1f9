@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4>Communication</h4>
                        <span class="badge bg-primary">
                            {{ $messages->count() }}
                        </span>
                    </div>
                    <div class="card-body">
                        <!-- Tabs for inbox, starred, outbox, and trash -->
                        <div class="d-flex gap-3 mb-4 bg-light rounded p-3 sticky-top">
                            <button class="tab-button btn btn-outline-primary btn-sm active" onclick="showMessages('inbox')" aria-label="Show inbox messages">
                                <i class="fa fa-inbox mr-1"></i> Inbox
                            </button>
                            <button class="tab-button btn btn-outline-primary btn-sm" onclick="showMessages('starred')" aria-label="Show starred messages">
                                <i class="fa fa-star mr-1"></i> Starred
                            </button>
                            <button class="tab-button btn btn-outline-primary btn-sm" onclick="showMessages('outbox')" aria-label="Show outbox messages">
                                <i class="fa fa-paper-plane mr-1"></i> Outbox
                            </button>
                            <button class="tab-button btn btn-outline-primary btn-sm" onclick="showMessages('trash')" aria-label="Show trash messages">
                                <i class="fa fa-trash mr-1"></i> Trash
                            </button>
                        </div>

                        <!-- Message List -->
                        <div class="table-responsive">
                            @if ($messages->count() > 0)
                                <table class="table table-striped table-hover" id="message-table">
                                    <thead class="bg-gray-200 text-gray-800">
                                        <tr>
                                            <th>Sender</th>
                                            <th>Receiver</th>
                                            <th>Message</th>
                                            <th>Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse ($messages as $message)
                                            <tr class="message-card {{ $message->status }}" data-message-status="{{ $message->status }}" data-message-id="{{ $message->id }}">
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="w-8 h-8 rounded-circle bg-gradient-to-br from-orange-400 to-orange-600 d-flex align-items-center justify-content-center text-white font-semibold">
                                                            {{ substr($message->sender_name, 0, 1) }}
                                                        </div>
                                                        <span class="ml-2">{{ $message->sender_name }}</span>
                                                    </div>
                                                </td>
                                                <td>{{ $message->receiver_name }}</td>
                                                <td>
                                                    @if($message->parent_id)
                                                        <span class="text-primary">[RE]</span>
                                                    @endif
                                                    {{ \Illuminate\Support\Str::limit($message->body, 50) }}
                                                </td>
                                                <td>{{ $message->created_at->format('d M Y, H:i') }}</td>
                                                <td>
                                                    <div class="d-flex align-items-center gap-2">
                                                        @if(in_array($message->status, ['inbox', 'outbox']))
                                                            <button class="star-button btn btn-sm btn-outline-warning" onclick="toggleStar('{{ $message->id }}', this)" aria-label="{{ $message->is_starred ? 'Unstar' : 'Star' }} message">
                                                                <i class="fa fa-star {{ $message->is_starred ? 'text-warning' : 'text-muted' }}"></i>
                                                            </button>
                                                        @endif
                                                        @if($message->status === 'inbox')
                                                            <button class="btn btn-sm btn-outline-primary"
                                                                    onclick="openViewModal('{{ $message->id }}', '{{ addslashes($message->sender_name) }}', '{{ addslashes($message->receiver_name) }}', '{{ $message->created_at->format('d M Y, H:i') }}', '{{ addslashes($message->body) }}', '{{ json_encode($message->attachments) }}', '{{ $message->status }}')"
                                                                    aria-label="Reply to message from {{ $message->sender_name }}">
                                                                <i class="fa fa-reply"></i>
                                                            </button>
                                                        @endif
                                                        <button class="btn btn-sm btn-outline-secondary"
                                                                onclick="openViewModal('{{ $message->id }}', '{{ addslashes($message->sender_name) }}', '{{ addslashes($message->receiver_name) }}', '{{ $message->created_at->format('d M Y, H:i') }}', '{{ addslashes($message->body) }}', '{{ json_encode($message->attachments) }}', '{{ $message->status }}')"
                                                                aria-label="View message from {{ $message->sender_name }}">
                                                            <i class="fa fa-eye"></i>
                                                        </button>
                                                        @if($message->status !== 'trash')
                                                            <form action="{{ route('messages.delete', $message->id) }}" method="POST" class="d-inline">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button type="submit" class="btn btn-sm btn-outline-danger" aria-label="Delete message">
                                                                    <i class="fa fa-trash"></i>
                                                                </button>
                                                            </form>
                                                        @endif
                                                    </div>
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="5" class="text-center text-muted">No messages found.</td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            @else
                                <div class="alert alert-info text-center">
                                    <i class="fa fa-info-circle mr-2"></i> No messages found.
                                </div>
                            @endif
                        </div>

                        <!-- View Modal -->
                        <div id="viewModal" class="modal fade" tabindex="-1" aria-hidden="true">
                            <div class="modal-dialog modal-dialog-centered modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">Message Details</h5>
                                        <button type="button" class="btn-close" onclick="closeViewModal()" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="mb-3 d-flex align-items-center">
                                            <div class="w-8 h-8 rounded-circle bg-gradient-to-br from-orange-400 to-orange-600 d-flex align-items-center justify-content-center text-white font-semibold me-2" id="senderInitial"></div>
                                            <div>
                                                <label class="form-label text-muted">Sender</label>
                                                <p id="viewSender" class="font-semibold"></p>
                                            </div>
                                        </div>
                                        <div class="mb-3 d-flex align-items-center">
                                            <div class="w-8 h-8 rounded-circle bg-gradient-to-br from-orange-400 to-orange-600 d-flex align-items-center justify-content-center text-white font-semibold me-2" id="receiverInitial"></div>
                                            <div>
                                                <label class="form-label text-muted">Receiver</label>
                                                <p id="viewReceiver" class="font-semibold"></p>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Date</label>
                                            <p id="viewDate"></p>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Message</label>
                                            <p id="viewContent" class="bg-light p-3 rounded border"></p>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label text-muted">Attachments</label>
                                            <div id="viewAttachment">
                                                <p id="noAttachment" class="text-muted">No attachments</p>
                                                <div id="attachmentList" class="d-flex flex-column gap-2"></div>
                                            </div>
                                        </div>
                                        <!-- Reply Form -->
                                        <div id="replySection" class="border-top pt-3 d-none">
                                            <h6 class="font-semibold mb-3">Reply</h6>
                                            <form id="replyForm" action="{{ route('messages.reply') }}" method="POST" enctype="multipart/form-data">
                                                @csrf
                                                <input type="hidden" id="messageId" name="message_id">
                                                <div class="mb-3">
                                                    <label for="replyContent" class="form-label text-muted">Your Message</label>
                                                    <textarea id="replyContent" name="body" rows="4" class="form-control" required></textarea>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="replyAttachment" class="form-label text-muted">Attachments (Multiple)</label>
                                                    <input type="file" id="replyAttachment" name="attachments[]" multiple accept=".jpg,.jpeg,.png,.pdf" class="form-control">
                                                    <div id="attachmentPreview" class="mt-2 d-flex flex-wrap gap-2"></div>
                                                </div>
                                                <div class="d-flex justify-content-end gap-2">
                                                    <button type="button" onclick="closeViewModal()" class="btn btn-sm btn-outline-secondary">Cancel</button>
                                                    <button type="submit" id="submitReply" class="btn btn-sm btn-primary">
                                                        <i class="fa fa-paper-plane me-1"></i> Send Reply
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('includes')
    <meta name="csrf-token" content="{{ csrf_token() }}">
@endpush

@push('style')
    <style>
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.85rem;
        }
        .badge {
            font-size: 0.9rem;
            padding: 0.4em 0.8em;
        }
        .table th, .table td {
            vertical-align: middle;
        }
        .table-responsive {
            overflow-x: auto;
        }
        .modal-content {
            border-radius: 0.75rem;
        }
        .bg-gradient-to-br {
            background: linear-gradient(to bottom right, #ff6f3c, #fb923c);
        }
        .w-8 {
            width: 32px;
            height: 32px;
        }
        .preview-item {
            width: 80px;
            height: 80px;
        }
        .message-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .message-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .modal {
            background-color: rgba(0, 0, 0, 0.5);
        }
    </style>
@endpush

@push('script')
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    {{-- <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script> --}}
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // Show messages based on tab selection
        function showMessages(status) {
            const cards = document.querySelectorAll('.message-card');
            cards.forEach(card => {
                card.style.display = card.dataset.messageStatus === status || status === 'all' ? '' : 'none';
            });

            // Update active tab styling
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active', 'btn-primary');
                button.classList.add('btn-outline-primary');
                if (button.getAttribute('onclick').includes(status)) {
                    button.classList.add('active', 'btn-primary');
                    button.classList.remove('btn-outline-primary');
                }
            });
        }

        // Open view modal
        function openViewModal(messageId, senderName, receiverName, date, content, attachments, status) {
            document.getElementById('messageId').value = messageId;
            document.getElementById('viewSender').textContent = senderName;
            document.getElementById('viewReceiver').textContent = receiverName;
            document.getElementById('viewDate').textContent = date;
            document.getElementById('viewContent').textContent = content;
            document.getElementById('senderInitial').textContent = senderName.charAt(0).toUpperCase();
            document.getElementById('receiverInitial').textContent = receiverName.charAt(0).toUpperCase();

            const noAttachment = document.getElementById('noAttachment');
            const attachmentList = document.getElementById('attachmentList');
            const replySection = document.getElementById('replySection');

            // Clear previous attachments
            attachmentList.innerHTML = '';
            noAttachment.classList.add('d-none');

            // Parse attachments safely
            let attachmentArray = [];
            try {
                attachmentArray = attachments ? JSON.parse(attachments) : [];
            } catch (e) {
                console.error('Error parsing attachments:', e);
                attachmentArray = [];
            }

            // Display attachments
            if (attachmentArray.length > 0) {
                noAttachment.classList.add('d-none');
                attachmentArray.forEach(attachment => {
                    const attachmentItem = document.createElement('a');
                    attachmentItem.href = attachment.url;
                    attachmentItem.target = '_blank';
                    attachmentItem.className = 'd-flex align-items-center gap-2 text-primary text-decoration-underline';
                    attachmentItem.innerHTML = `
                        <i class="fa fa-file me-1"></i>
                        ${attachment.name}
                    `;
                    attachmentList.appendChild(attachmentItem);
                });
            } else {
                noAttachment.classList.remove('d-none');
            }

            // Show reply section only for inbox messages
            if (status === 'inbox') {
                replySection.classList.remove('d-none');
            } else {
                replySection.classList.add('d-none');
            }

            $('#viewModal').modal('show');
        }

        // Close view modal
        function closeViewModal() {
            $('#viewModal').modal('hide');
            document.getElementById('replyForm').reset();
            document.getElementById('attachmentPreview').innerHTML = '';
        }

        // Attachment preview
        document.getElementById('replyAttachment').addEventListener('change', function(e) {
            const previewContainer = document.getElementById('attachmentPreview');
            previewContainer.innerHTML = '';

            const files = e.target.files;
            for (const file of files) {
                const fileType = file.type;
                const fileName = file.name;
                const reader = new FileReader();

                const previewItem = document.createElement('div');
                previewItem.className = 'preview-item position-relative rounded overflow-hidden shadow-sm';

                if (fileType.startsWith('image/')) {
                    reader.onload = function(event) {
                        previewItem.innerHTML = `
                            <img src="${event.target.result}" alt="${fileName}" class="w-100 h-100 object-fit-cover">
                            <div class="position-absolute top-0 end-0 bg-danger text-white text-xs rounded-bottom-start px-1 cursor-pointer" onclick="removePreview(this)">X</div>
                        `;
                    };
                    reader.readAsDataURL(file);
                } else if (fileType === 'application/pdf') {
                    previewItem.innerHTML = `
                        <div class="w-100 h-100 bg-light d-flex align-items-center justify-content-center text-xs text-muted">PDF: ${fileName}</div>
                        <div class="position-absolute top-0 end-0 bg-danger text-white text-xs rounded-bottom-start px-1 cursor-pointer" onclick="removePreview(this)">X</div>
                    `;
                } else {
                    previewItem.innerHTML = `
                        <div class="w-100 h-100 bg-light d-flex align-items-center justify-content-center text-xs text-muted">${fileName}</div>
                        <div class="position-absolute top-0 end-0 bg-danger text-white text-xs rounded-bottom-start px-1 cursor-pointer" onclick="removePreview(this)">X</div>
                    `;
                }

                previewContainer.appendChild(previewItem);
            }
        });

        // Remove preview item
        function removePreview(element) {
            element.parentElement.remove();
            document.getElementById('replyAttachment').value = '';
        }

        // Handle reply form submission with AJAX
        document.getElementById('replyForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const submitButton = document.getElementById('submitReply');
            submitButton.disabled = true;
            submitButton.innerHTML = `
                <i class="fa fa-spinner fa-spin me-1"></i> Sending...
            `;

            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
            if (!csrfToken) {
                alert('CSRF token not found. Please refresh the page or contact support.');
                submitButton.disabled = false;
                submitButton.innerHTML = `
                    <i class="fa fa-paper-plane me-1"></i> Send Reply
                `;
                return;
            }

            const formData = new FormData(this);
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Success!',
                        text: data.message || 'Reply sent successfully.',
                        confirmButtonText: 'OK',
                        timer: 5000,
                        timerProgressBar: true
                    }).then(() => {
                        closeViewModal();
                        window.location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: data.message || 'Failed to send reply.'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'An error occurred while sending the reply.'
                });
            })
            .finally(() => {
                submitButton.disabled = false;
                submitButton.innerHTML = `
                    <i class="fa fa-paper-plane me-1"></i> Send Reply
                `;
            });
        });

        // Toggle star status
        function toggleStar(messageId, button) {
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
            if (!csrfToken) {
                alert('CSRF token not found. Please refresh the page or contact support.');
                return;
            }

            const icon = button.querySelector('i');
            const isStarred = icon.classList.contains('text-warning');

            fetch('{{ route("messages.star") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json'
                },
                body: JSON.stringify({ message_id: messageId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (isStarred) {
                        icon.classList.remove('text-warning');
                        icon.classList.add('text-muted');
                        button.setAttribute('aria-label', 'Star message');
                    } else {
                        icon.classList.remove('text-muted');
                        icon.classList.add('text-warning');
                        button.setAttribute('aria-label', 'Unstar message');
                    }
                    if (document.querySelector('.tab-button.active').getAttribute('onclick').includes('starred')) {
                        showMessages('starred');
                    }
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error!',
                        text: data.message || 'Failed to toggle star status.'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'An error occurred while toggling the star status.'
                });
            });
        }

        // Initially show inbox messages
        showMessages('inbox');
    </script>
@endpush