@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4>Assign Permit Number</h4>
                        <a href="{{ route('project-department-permits.index') }}" class="btn btn-secondary">Back</a>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('project-department-permits.store') }}" method="POST">
                            @csrf
                            <div class="form-group">
                                <label for="website_builder_project_id">Select Project <span class="text-danger">*</span></label>
                                <select name="website_builder_project_id" id="website_builder_project_id" class="form-control" required>
                                    <option value="">Select a project</option>
                                    @foreach($projects as $project)
                                        <option value="{{ $project->id }}">{{ $project->project_name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="mcd_staff_role_id">Select Department <span class="text-danger">*</span></label>
                                <select name="mcd_staff_role_id" id="mcd_staff_role_id" class="form-control" required>
                                    <option value="">Select a project first</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="permit_number">Permit Number <span class="text-danger">*</span></label>
                                <input type="text" name="permit_number" id="permit_number" class="form-control" required>
                            </div>
                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-primary mr-2">Assign Permit</button>
                                <a href="{{ route('project-department-permits.index') }}" class="btn btn-secondary">Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('JavaScript loaded for AssignPermit create page');
            const projectSelect = document.getElementById('website_builder_project_id');
            const departmentSelect = document.getElementById('mcd_staff_role_id');

            projectSelect.addEventListener('change', function() {
                const projectId = this.value;
                console.log('Selected project ID:', projectId);
                departmentSelect.innerHTML = '<option value="">Loading departments...</option>';

                if (!projectId) {
                    departmentSelect.innerHTML = '<option value="">Select a project first</option>';
                    return;
                }

                const url = "{{ route('project-department-permits.departments', ':projectId') }}".replace(':projectId', projectId);
                console.log('Fetching URL:', url);

                fetch(url, {
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                    .then(response => {
                        console.log('Response status:', response.status);
                        console.log('Response headers:', [...response.headers.entries()]);
                        if (!response.ok) {
                            return response.text().then(text => {
                                throw new Error(`HTTP error! Status: ${response.status}, Response: ${text}`);
                            });
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('Received data:', data);
                        departmentSelect.innerHTML = '<option value="">Select a department</option>';
                        if (data.error) {
                            departmentSelect.innerHTML = `<option value="">Error: ${data.error}</option>`;
                        } else if (data.length === 0) {
                            departmentSelect.innerHTML = `<option value="">No available departments</option>`;
                        } else {
                            data.forEach(dept => {
                                const option = document.createElement('option');
                                option.value = dept.id;
                                option.textContent = dept.name;
                                departmentSelect.appendChild(option);
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching departments:', error);
                        departmentSelect.innerHTML = `<option value="">Error loading departments: ${error.message}</option>`;
                    });
            });
        });
    </script>
@endpush