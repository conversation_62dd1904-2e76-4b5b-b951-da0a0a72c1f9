<?php

namespace App\Http\Controllers;

use App\Models\McdSupportTicket;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class McdSupportTicketController extends Controller
{
    // Display all tickets
    public function index()
    {
        $tickets = McdSupportTicket::all();
        return view('support_tickets.index', compact('tickets'));
    }

    // Show create form
    public function create()
    {
        return view('support_tickets.create');
    }

    // Store new ticket
    public function store(Request $request)
    {
        $validated = $request->validate([
            'subject' => 'required|string|max:255',
            'description' => 'required|string',
            'priority' => 'required|in:low,medium,high',
            'status' => 'required|in:open,in_progress,resolved',
        ]);

        McdSupportTicket::create([
            'subject' => $validated['subject'],
            'description' => $validated['description'],
            'priority' => $validated['priority'],
            'status' => $validated['status'],
            'user_id' => Auth::id(),
        ]);

        return redirect()->route('support_tickets.index')->with('success', 'Ticket created successfully');
    }

    // Show edit form
    public function edit($id)
    {
        $supportTicket = McdSupportTicket::findOrFail($id);
        return view('support_tickets.edit', compact('supportTicket'));
    }

    // Update ticket
    public function update(Request $request, $id)
    {
        $supportTicket = McdSupportTicket::findOrFail($id);
        $validated = $request->validate([
            'subject' => 'required|string|max:255',
            'description' => 'required|string',
            'priority' => 'required|in:low,medium,high',
            'status' => 'required|in:open,in_progress,resolved',
        ]);

        $supportTicket->update($validated);

        return redirect()->route('support_tickets.index')->with('success', 'Ticket updated successfully');
    }

    // Delete ticket
    public function destroy($id)
    {
        $supportTicket = McdSupportTicket::findOrFail($id);
        $supportTicket->delete();
        return redirect()->route('support_tickets.index')->with('success', 'Ticket deleted successfully');
    }

    public function updateStatus(Request $request, $id)
    {
        $supportTicket = McdSupportTicket::findOrFail($id);
        $request->validate([
            'status' => 'required|in:open,in_progress,resolved',
        ]);

        $supportTicket->update(['status' => $request->status]);

        return response()->json([
            'success' => true,
            'message' => 'Status updated successfully',
            'new_status' => $supportTicket->status,
        ]);
    }
    
}
?>