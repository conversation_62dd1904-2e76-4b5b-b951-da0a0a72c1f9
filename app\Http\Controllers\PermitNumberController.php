<?php

namespace App\Http\Controllers;

use App\Models\PermitNumber;
use App\Models\WebsiteBuilderProject;
use Illuminate\Http\Request;

class PermitNumberController extends Controller
{

public function index()
{
    $permits = PermitNumber::with('project')->get();
    return view('permit_numbers.index', compact('permits'));
}

public function assignIndex()
{
    $projects = WebsiteBuilderProject::with('permitNumbers')->get();

    // Attach department names to each project
    foreach ($projects as $project) {
        $departmentIds = json_decode($project->department_trade, true);

        if (!is_array($departmentIds)) {
            $departmentIds = explode(',', $project->department_trade);
        }

        $departmentNames = \App\Models\McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();
        $project->department_names = $departmentNames;
    }

    return view('permit_numbers.assignpermitnum', compact('projects'));
}


public function updatePermitnumber(Request $request, $id)
{
    $request->validate([
        'permit_number' => 'required|string|max:255',
    ]);

    $permit = PermitNumber::findOrFail($id);
    $permit->permit_number = $request->permit_number;
    $permit->save();

    return redirect()->back()->with('success', 'Permit Number updated successfully.');
}

public function assignStore(Request $request, WebsiteBuilderProject $project)
{
    $request->validate(['permit_number' => 'required|string']);

    PermitNumber::create([
        'website_builder_project_id' => $project->id,
        'permit_number' => $request->permit_number,
    ]);

    return back()->with('success', 'Permit number assigned successfully.');
}



public function create()
{
    $projects = WebsiteBuilderProject::all();
    return view('permit_numbers.create', compact('projects'));
}

public function store(Request $request)
{
    $request->validate([
        'website_builder_project_id' => 'required|exists:website_builder_projects,id',
        'permit_number' => 'required|string|max:10',
    ]);

    PermitNumber::create($request->all());
    return redirect()->route('permit-numbers.index')->with('success', 'Permit number assigned successfully.');
}

public function edit($id)
{
    $permit = PermitNumber::findOrFail($id);
    $projects = WebsiteBuilderProject::all(); // Assuming relation
    return view('permit_numbers.edit', compact('permit', 'projects'));
}

public function update(Request $request, $id)
{
    $request->validate([
        'website_builder_project_id' => 'required|exists:website_builder_projects,id',
        'permit_number' => 'required|string|max:10',
    ]);

    $permit = PermitNumber::findOrFail($id);
    $permit->update($request->only('website_builder_project_id', 'permit_number'));

    return redirect()->route('permit-numbers.index')->with('success', 'Permit number updated successfully.');
}

public function destroy($id)
{
    $permit = PermitNumber::findOrFail($id);
    $permit->delete();

    return redirect()->back()->with('success', 'Permit number deleted successfully.');
}


}
