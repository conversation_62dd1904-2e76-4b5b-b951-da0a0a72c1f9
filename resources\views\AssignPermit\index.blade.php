@extends('mcdpanel.layouts.master')
@php
    $user = Auth::guard('staff_mcd')->user();
    $permissions = $user && $user->role ? $user->role->permissions : [];
    $isAdmin = Auth::guard('web')->check();
    $hasWritePermission = $isAdmin || (isset($permissions['assign_permit_number']['write']) && $permissions['assign_permit_number']['write'] == 1);
@endphp

@section('content')
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4>Project Department Permits</h4>
                        {{-- <a href="{{ route('project-department-permits.create') }}" class="btn btn-primary btn-sm">
                            <i class="fa fa-plus"></i> Assign Permit Number
                        </a> --}}
                        @if ($hasWritePermission)
                            <a href="{{ route('project-department-permits.create') }}" class="btn btn-primary btn-sm">
                                <i class="fa fa-plus"></i> Assign Permit Number
                            </a>
                        @else
                            <button type="button" class="btn btn-primary btn-sm" onclick="showAccessDenied()">
                                <i class="fa fa-plus"></i> Assign Permit Number
                            </button>
                        @endif
                    </div>
                    <div class="card-body">
                        @if ($permits->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="table-1">
                                    <thead>
                                        <tr>
                                            <th>Sr No</th>
                                            <th>Project Name</th>
                                            <th>Department</th>
                                            <th>Permit Number</th>
                                            <th>Assigned At</th>
                                            <th class="text-center">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($permits as $key => $permit)
                                            <tr>
                                                <td class="text-center">{{ $key + 1 }}</td>
                                                <td>{{ $permit->project?->project_name ?? 'N/A' }}</td>
                                                <td>{{ $permit->department?->name ?? 'N/A' }}</td>
                                                <td>{{ $permit->permit_number }}</td>
                                                <td>{{ $permit->created_at->format('d-m-Y') }}</td>
                                                <td class="text-center">
    {{-- <a href="{{ route('project-department-permits.edit', $permit->id) }}" 
       class="btn btn-sm btn-primary" 
       title="Edit">
        <i class="fa fa-edit"></i>
    </a> --}}
                                                    @if ($hasWritePermission)
                                                    <a href="{{ route('project-department-permits.edit', $permit->id) }}" 
                                                    class="btn btn-sm btn-primary" 
                                                    title="Edit">
                                                        <i class="fa fa-edit"></i>
                                                    </a>
                                                @else
                                                    <button type="button" class="btn btn-sm btn-primary" onclick="showAccessDenied()" title="Edit">
                                                        <i class="fa fa-edit"></i>
                                                    </button>
                                                @endif
    
                                            </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="alert alert-info text-center">
                                <i class="fa fa-info-circle mr-2"></i>
                                No assigned permit numbers found.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('style')
    <style>
        .btn-sm { 
            padding: 0.25rem 0.5rem; 
        }
        .table th, .table td {
            vertical-align: middle;
        }
        .alert {
            background-color: #e7f1ff;
            border: 1px solid #b8daff;
            color: #004085;
            padding: 0.75rem 1.25rem;
            border-radius: 0.25rem;
            margin-bottom: 1rem;
        }
    </style>
@endpush

@push('script')
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            @if(session('success'))
                Swal.fire({
                    title: 'Success!',
                    text: '{{ session('success') }}',
                    icon: 'success',
                    showConfirmButton: false,
                    timer: 4000,
                    timerProgressBar: true,
                    position: 'top-end',
                    toast: true,
                    customClass: {
                        popup: 'swal2-popup-custom',
                        title: 'swal2-title-custom',
                        content: 'swal2-content-custom'
                    }
                });
            @endif

            document.querySelectorAll('.delete-btn').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const form = this.closest('form');
                    const name = this.getAttribute('data-name');

                    Swal.fire({
                        title: 'Are you sure?',
                        text: `You are about to delete the permit number for "${name}". This action cannot be undone!`,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelBackgroundColor: '#3085d6',
                        confirmButtonText: 'Yes, delete it!',
                        cancelButtonText: 'Cancel'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            form.submit();
                        }
                    });
                });
            });
        });

        // SweetAlert Access Denied Popup
        function showAccessDenied() {
            Swal.fire({
                icon: 'error',
                title: 'Access Denied',
                text: 'You do not have permission to modify the Permit Number.'
            });
        }
    </script>

    <style>
        .swal2-popup-custom {
            border-radius: 8px;
            padding: 15px;
            background: #ffffff;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
        }
        .swal2-title-custom {
            color: #28a745;
            font-weight: bold;
            font-size: 18px;
        }
        .swal2-content-custom {
            font-size: 14px;
            color: #they #555;
        }

         
    </style>
@endpush