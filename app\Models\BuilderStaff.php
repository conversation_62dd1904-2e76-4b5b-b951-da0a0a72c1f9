<?php
 
namespace App\Models;
 
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
 
class BuilderStaff extends Authenticatable
{
    use Notifiable;
    protected $table = 'builder_staff';
 
    protected $fillable = [
        'staff_name',
        'email',
        'password',
        'role_id',
        'status',
        'description',
        'contact_number',
        'address',
        'staff_role_id',
    ];
 
    protected $hidden = [
        'password'
    ];
 
    // protected $casts = [
    //     'status' => 'boolean'
    // ];
 
    protected function casts(): array
    {
        return [
            'status' => 'boolean',
            'password' => 'hashed',
        ];
    }
 
    public function role()
    {
        return $this->belongsTo(BuilderStaffRole::class, 'role_id');
    }
 
    public function getAuthPassword()
    {
        return $this->password;
    }
}