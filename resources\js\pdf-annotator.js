import * as pdfjsLib from 'pdfjs-dist';
import * as fabric from 'fabric';

pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf-annotator/pdf.worker.min.js';

export function renderPDFWithAnnotation(pdfUrl, canvasId) {
    const canvas = document.getElementById(canvasId);
    const fabricCanvas = new fabric.Canvas(canvas);

    pdfjsLib.getDocument(pdfUrl).promise.then(pdfDoc => {
        pdfDoc.getPage(1).then(page => {
            const scale = 1.5;
            const viewport = page.getViewport({ scale });

            canvas.width = viewport.width;
            canvas.height = viewport.height;
            fabricCanvas.setWidth(viewport.width);
            fabricCanvas.setHeight(viewport.height);

            const renderContext = {
                canvasContext: canvas.getContext('2d'),
                viewport: viewport
            };

            page.render(renderContext).promise.then(() => {
                // Once rendered, overlay with fabric canvas
                fabricCanvas.isDrawingMode = true; // Enable drawing
            });
        });
    });
}
