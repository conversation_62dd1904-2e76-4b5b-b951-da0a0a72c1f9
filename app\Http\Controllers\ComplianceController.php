<?php

namespace App\Http\Controllers;
use App\Models\WebsiteBuilderProject;
use App\Models\Compliance;


use Illuminate\Http\Request;

class ComplianceController extends Controller
{

    public function index()
{
    // Load compliances with related project info
    $compliances = Compliance::with('project')->latest()->get();

    return view('compliances.index', compact('compliances'));
}

    public function create()
{
    $projects = WebsiteBuilderProject::all();
    return view('compliances.create', compact('projects'));
}
public function store(Request $request)
{
    $request->validate([
        'website_builder_project_id' => 'required|exists:website_builder_projects,id',
        'compliance_notes' => 'required|string',
    ]);

    Compliance::create($request->all());

    return redirect()->route('compliances.index')->with('success', 'Compliance added successfully.');
}
public function edit($id)
{
    $compliance = Compliance::findOrFail($id);
    $projects = WebsiteBuilderProject::all(); // adjust model name if different
    return view('compliances.edit', compact('compliance', 'projects'));
}
public function update(Request $request, $id)
{
    $request->validate([
        'website_builder_project_id' => 'required|exists:website_builder_projects,id',
        'compliance_notes' => 'required|string',
    ]);

    $compliance = Compliance::findOrFail($id);
    $compliance->update([
        'website_builder_project_id' => $request->website_builder_project_id,
        'compliance_notes' => $request->compliance_notes,
    ]);

    return redirect()->route('compliances.index')->with('success', 'Compliance updated successfully!');
}
public function destroy($id)
{
    $compliance = Compliance::findOrFail($id);
    $compliance->delete();

    return redirect()->route('compliances.index')->with('success', 'Compliance deleted successfully!');
}



}
