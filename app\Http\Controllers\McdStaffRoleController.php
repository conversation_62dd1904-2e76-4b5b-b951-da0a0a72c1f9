<?php
namespace App\Http\Controllers;

use App\Models\McdStaffRole;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use App\Mail\StaffInviteMail;
use Illuminate\Support\Facades\Auth;
use App\Models\StaffInvite;

class McdStaffRoleController extends Controller
{
    public function index()
    {
        $departments = McdStaffRole::all();
        return view('modules.mcd_staff_roles.index', compact('departments'));
    }

    public function create()
    {
        return view('modules.mcd_staff_roles.create');
    }

    public function updateStatus(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:mcd_staff_roles,id',
            'is_active' => 'required|boolean',
        ]);

        $staffRole = McdStaffRole::find($request->id);
        $staffRole->is_active = $request->is_active;
        $staffRole->save();

        return response()->json(['message' => 'Status updated successfully']);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|unique:mcd_staff_roles|max:255',
            'description' => 'nullable|string',
            'is_active' => 'boolean'
        ]);

        McdStaffRole::create($validated);

        return redirect()->route('mcd-staff-roles.index')
            ->with('success', 'Department created successfully');
    }

    public function edit($id)
    {
        $role = McdStaffRole::findOrFail($id);
        return view('modules.mcd_staff_roles.edit', compact('role'));
    }

    public function update(Request $request, $id)
    {
        $role = McdStaffRole::findOrFail($id);

        $validated = $request->validate([
            'name' => 'required|max:255|unique:mcd_staff_roles,name,' . $id,
            'description' => 'nullable|string',
            'is_active' => 'boolean'
        ]);

        $role->update($validated);

        return redirect()->route('mcd-staff-roles.index')
            ->with('success', 'Department updated successfully');
    }

    public function destroy($id)
    {
        $role = McdStaffRole::findOrFail($id);
        $role->delete();

        return redirect()->route('mcd-staff-roles.index')
            ->with('success', 'Department deleted successfully');
    }

    public function assignPermissions($id)
    {
        $user = Auth::guard('web')->user() ?? Auth::guard('mcd')->user();
        if (!$user || $user->role_id !== 2) {
            return redirect()->route('dashboard')->with('error', 'Unauthorized access. Only mcd can assign permissions.');
        }

        $role = McdStaffRole::findOrFail($id);
        $modules = [
            'dashboard' => 'Dashboard',
            'category_management' => 'Category Management',
            'subcategory_management' => 'SubCategory Management',
            'architect_management' => 'Architect Management',
            'project_management' => 'Project Management',
            'my_municipal' => 'My Municipal',
            'complaints' => 'Complaints',
            'communication' => 'Communication',
            'staff_management' => 'Staff Management',
            'settings' => 'Settings'
        ];

        return view('modules.mcd_staff_roles.assign_permissions', compact('role', 'modules'));
    }

    public function storePermissions(Request $request, $id)
    {
        $user = Auth::guard('web')->user() ?? Auth::guard('builder')->user();
        if (!$user || $user->role_id !== 2) {
            return redirect()->route('dashboard')->with('error', 'Unauthorized access. Only builders can assign permissions.');
        }

        $role = McdStaffRole::findOrFail($id);
        $validated = $request->validate(['permissions' => 'array']);
        $role->update(['permissions' => $request->input('permissions', [])]);

        return redirect()->route('mcd-staff-roles.index')
            ->with('success', 'Permissions assigned successfully');
    }

    

    public function sendInvite(Request $request)
    {
        //dd($request->all());
        $validated = $request->validate([
            'role_id' => 'required|exists:mcd_staff_roles,id',
            'email' => 'required|email', // Fixed typo from 'DAY' to 'email'
            'message' => 'nullable|string',
        ]);

        $role = McdStaffRole::findOrFail($validated['role_id']);
        
        // Generate a unique token for the invite
        $token = Str::random(60);

         // Save the invite to DB
            StaffInvite::create([
                'email' => $validated['email'],
                'role_id' => $role->id,
                'token' => $token,
                'message' => $validated['message'],
    ]);
        
        try {
           
            // Send the email
            Mail::to($validated['email'])->send(new StaffInviteMail([
            'role_id' => $role->id,
            'role_name' => $role->name,
            'message' => $validated['message'] ?? null,
            'invite_url' => route('mcd-staff.create', ['role_id' => $role->id, 'token' => $token]),
           ]));
        } catch (\Exception $e) {
            return redirect()->route('mcd-staff-roles.index')
                ->with('error', 'Failed to send invite: ' . $e->getMessage());
        }

        return redirect()->route('mcd-staff-roles.index')
            ->with('success', 'Invite sent successfully');
    }
}