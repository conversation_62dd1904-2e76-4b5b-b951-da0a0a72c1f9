<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AnnotatedPdf extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'department_id',
        'review_step',
        'original_pdf_path',
        'annotated_pdf_path',
        'document_type',
        'uploaded_by',
        'uploaded_by_role'
    ];

    /**
     * Get the project that owns the annotated PDF.
     */
    public function project()
    {
        return $this->belongsTo(WebsiteBuilderProject::class, 'project_id');
    }

    /**
     * Get the department associated with the annotated PDF.
     */
    public function department()
    {
        return $this->belongsTo(McdStaffRole::class, 'department_id');
    }

    /**
     * Get the user who uploaded the annotated PDF.
     * This method attempts to find the user across different user models.
     */
    public function uploader()
    {
        // Try to determine which user model based on role
        if (str_contains($this->uploaded_by_role, 'mcd') || str_contains($this->uploaded_by_role, 'staff')) {
            return $this->belongsTo(McdStaff::class, 'uploaded_by');
        } elseif (str_contains($this->uploaded_by_role, 'builder')) {
            return $this->belongsTo(BuilderStaff::class, 'uploaded_by');
        } else {
            // Default to User model for admin/web users
            return $this->belongsTo(User::class, 'uploaded_by');
        }
    }
}
