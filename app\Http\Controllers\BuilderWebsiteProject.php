<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Storage;
use ZipArchive;
use App\Models\WebsiteBuilderProject;
use App\Models\ArchitectCategory;
use App\Models\BuilderSubCategory;
use Illuminate\Http\Request;
use App\Models\Inspector;
use Illuminate\Support\Facades\DB;


class BuilderWebsiteProject extends Controller
{
    public function index(Request $request)
    {
        $inspectors = Inspector::all();

        $projects = WebsiteBuilderProject::with('inspector')->get();
        return view('BuilderWebsiteDashboard.Projects.index', compact('projects','inspectors'));
    }

    public function nonPendingProjects(Request $request)
    {
        //dd("helk");
        $selectedTrade = $request->query('trade');
        $query = WebsiteBuilderProject::query();

    if (!empty($selectedTrade)) {
        
        $query->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$selectedTrade)]);
    }

    $projects = $query->get();

    // Attach department names
    foreach ($projects as $project) {
        $departmentIds = json_decode($project->department_trade, true);

        if (!is_array($departmentIds)) {
            $departmentIds = explode(',', $project->department_trade);
        }

        $departmentNames = \App\Models\McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();
        $project->department_names = $departmentNames;
    }

      $departments = \App\Models\McdStaffRole::pluck('name', 'id')->toArray();
        return view('BuilderWebsiteDashboard.Projects.non_pending', compact('projects','departments'));
    }


    // public function backlogsAnalysis(Request $request)
    // {
       
    //     $selectedTrade = $request->query('trade');
    //     $query = WebsiteBuilderProject::query();

    //     if (!empty($selectedTrade)) {
            
    //         $query->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$selectedTrade)]);
    //     }

    //     $projects = $query->get();

    
    //     foreach ($projects as $project) {
    //         $departmentIds = json_decode($project->department_trade, true);

    //         if (!is_array($departmentIds)) {
    //             $departmentIds = explode(',', $project->department_trade);
    //         }

    //         $departmentNames = \App\Models\McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();
    //         $project->department_names = $departmentNames;
    //     }

    //     $departments = \App\Models\McdStaffRole::pluck('name', 'id')->toArray();
    //         return view('BuilderWebsiteDashboard.Projects.backlogs', compact('projects','departments'));
    // }

    public function backlogsAnalysis(Request $request)
{
    $selectedTrade = $request->query('trade');
    
    $query = WebsiteBuilderProject::query();

    // ✅ Only Pending (0) or Violation (1) projects
    $query->whereIn('project_status', [0, 1]);

    // ✅ Optional: Filter by Department (Trade)
    if (!empty($selectedTrade)) {
        $query->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$selectedTrade)]);
    }

    $projects = $query->get();

    // ✅ Get Department Names for Each Project
    foreach ($projects as $project) {
        $departmentIds = json_decode($project->department_trade, true);

        if (!is_array($departmentIds)) {
            $departmentIds = explode(',', $project->department_trade);
        }

        $departmentNames = \App\Models\McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();
        $project->department_names = $departmentNames;
    }

    // ✅ Get All Departments for Filter Dropdown
    $departments = \App\Models\McdStaffRole::pluck('name', 'id')->toArray();

    return view('BuilderWebsiteDashboard.Projects.backlogs', compact('projects', 'departments'));
}




    // public function updateStatus(Request $request, $id)
    // {
    //     $request->validate([
    //         'status' => 'required|in:Pending,Assigned,Rejected,Violation',
    //         'objection_comment' => 'required_if:status,Violation|string|nullable',
    //     ]);

    //     $project = WebsiteBuilderProject::findOrFail($id);
    //     $project->status = $request->status;
    //     $project->objection_comment = $request->status === 'Violation' ? $request->objection_comment : null;
    //     $project->save();

    //     return redirect()->route('myprojects.index')->with('success', 'Project status updated successfully.');
    // }
//     public function updateStatus(Request $request, $id)
// {
//     $request->validate([
//         'status' => 'required|in:Pending,Assigned,Rejected,Violation',
//         'objection_comment' => 'required_if:status,Violation|string|nullable',
//     ]);

//     $project = WebsiteBuilderProject::findOrFail($id);
//     $project->status = $request->status;
//     $project->approval_status = $request->status; // ✅ Store in approval_status as well
//     $project->objection_comment = $request->status === 'Violation' ? $request->objection_comment : null;
//     $project->save();

//     return redirect()->route('myprojects.index')->with('success', 'Project status updated successfully.');
// }
public function updateStatus(Request $request, $id)
{
    $request->validate([
        'status' => 'required|in:Pending,Assigned,Rejected,Violation',
        'objection_comment' => 'required_if:status,Violation|string|nullable',
    ]);

    $project = WebsiteBuilderProject::findOrFail($id);
    $project->status = $request->status;

    // Map status to valid approval_status values
    $validApprovalStatus = match ($request->status) {
        'Pending', 'Violation' => $request->status,
        'Assigned', 'Rejected' => 'Pending', // Map to a valid ENUM value
        default => 'Pending',
    };
    $project->approval_status = $validApprovalStatus;
    $project->objection_comment = $request->status === 'Violation' ? $request->objection_comment : null;
    $project->save();

    return redirect()->route('myprojects.index')->with('success', 'Project status updated successfully.');
}
    public function show($id)
    {
        // Fetch project details by ID
        $project = WebsiteBuilderProject::findOrFail($id);
        $inspectors = Inspector::all(); 
        
        return view('BuilderWebsiteDashboard.Projects.show', compact('project', 'inspectors'));
    }
    public function assign(Request $request, $id)
{
    $project = WebsiteBuilderProject::findOrFail($id);
    
    if ($request->isMethod('post')) {
        $request->validate([
            'inspector_id' => 'required|exists:inspectors,id'
        ]);

        $project->inspector_id = $request->inspector_id;
        $project->inspector_assigned_at = now();
        $project->approval_status = 'Approval'; // Use a valid ENUM value
        $project->save();

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Inspector assigned successfully!',
                'redirect' => route('myprojects.index')
            ]);
        }

        return redirect()->route('myprojects.index')->with('success', 'Inspector assigned successfully!');
    }

    $inspectors = Inspector::all();
    return view('BuilderWebsiteDashboard.Projects.assign', compact('project', 'inspectors'));
}
//    public function showStatus($id)
// {
//     $project = WebsiteBuilderProject::with('permitNumbers')->findOrFail($id);
//     // dd($project);
//     //    dd($project->permit_number); 
//     return view('BuilderWebsiteDashboard.Projects.showprojects', compact('project'));
// }
// public function showStatus($id)
// {
//     // Load project with related permit numbers for each department
//     $project = WebsiteBuilderProject::with('permitNumbersDepartment.department')->findOrFail($id);

//     // Existing logic to parse department_trade
//     $departmentIds = json_decode($project->department_trade, true);
//     if (!is_array($departmentIds)) {
//         $departmentIds = explode(',', $project->department_trade);
//     }

//     // Fetch department names
//     $departmentNames = \App\Models\McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();
//     $project->department_names = $departmentNames;

//     // Add permit number info mapped with department name
//     $departmentPermits = [];
//     foreach ($project->permitNumbersDepartment as $permit) {
//         if ($permit->department) {
//             $departmentPermits[] = [
//                 'name' => $permit->department->name,
//                 'permit_number' => $permit->permit_number,
//             ];
//         }
//     }

//     // Attach to project without disturbing existing variables
//     $project->department_permit_info = $departmentPermits;
// // dd($project);
//     return view('BuilderWebsiteDashboard.Projects.showprojects', compact('project'));
// }
public function showStatus($id)
{
    // Load project with related permit numbers for each department
    $project = WebsiteBuilderProject::with('permitNumbersDepartment.department')->findOrFail($id);

    // Existing logic to parse department_trade
    $departmentIds = json_decode($project->department_trade, true);
    if (!is_array($departmentIds)) {
        $departmentIds = explode(',', $project->department_trade);
    }

    // Fetch department names
    $departmentNames = \App\Models\McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();
    $project->department_names = $departmentNames;

    // Add permit number info mapped with department name
    $departmentPermits = [];
    foreach ($project->permitNumbersDepartment as $permit) {
        if ($permit->department) {
            $departmentPermits[] = [
                'name' => $permit->department->name,
                'permit_number' => $permit->permit_number,
            ];
        }
    }

    // Attach to project without disturbing existing variables
    $project->department_permit_info = $departmentPermits;

    // Fetch all documents from project_attachment table
    $allDocuments = DB::table('project_attachment')
        ->where('project_id', $id)
        ->select('file_path', 'file_type', 'created_at', 'updated_at', 'trade_id')
        ->orderBy('created_at', 'desc')
        ->get();

    // Add display name for file types and department name (if applicable)
    foreach ($allDocuments as $doc) {
        $doc->file_type_display = ucwords(str_replace('_', ' ', $doc->file_type));
        // Fetch department name for non-image documents
        $doc->department_name = $doc->trade_id
            ? \App\Models\McdStaffRole::where('id', $doc->trade_id)->value('name') ?? 'N/A'
            : 'All Departments';
    }

    return view('BuilderWebsiteDashboard.Projects.showprojects', compact('project', 'allDocuments'));
}
// public function showStatus($id)
// {
//     $project = WebsiteBuilderProject::with('permitNumbers')->findOrFail($id);
// // dd($project);
//     // Fetch department names
//     $departmentIds = json_decode($project->department_trade, true);

//     if (!is_array($departmentIds)) {
//         $departmentIds = explode(',', $project->department_trade);
//     }

//     $departmentNames = \App\Models\McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();
//     $project->department_names = $departmentNames;

//     return view('BuilderWebsiteDashboard.Projects.showprojects', compact('project'));
// }
// public function assign(Request $request, $id)
    // {
    //     $project = WebsiteBuilderProject::findOrFail($id);
        
    //     if ($request->isMethod('post')) {
    //         $request->validate([
    //             'inspector_id' => 'required|exists:inspectors,id'
    //         ]);
    
    //         // Assign the inspector and set the timestamp
    //         $project->inspector_id = $request->inspector_id;
    //         $project->inspector_assigned_at = now();
    //         $project->approval_status = 'Assigned';
    //         $project->save();
    
    //         // Check if the request expects JSON (from AJAX)
    //         if ($request->expectsJson()) {
    //             return response()->json([
    //                 'success' => true,
    //                 'message' => 'Inspector assigned successfully!',
    //                 'redirect' => route('myprojects.index')
    //             ]);
    //         }
    
    //         // Redirect for non-AJAX requests (e.g., assign.blade.php)
    //         return redirect()->route('myprojects.index')->with('success', 'Inspector assigned successfully!');
    //     }
    
    //     $inspectors = Inspector::all();
    //     return view('BuilderWebsiteDashboard.Projects.assign', compact('project', 'inspectors'));
    // }
    // public function assign(Request $request, $id)
    // {
    //     $project = WebsiteBuilderProject::findOrFail($id);
        
    //     if ($request->isMethod('post')) {
    //         $request->validate([
    //             'inspector_id' => 'required|exists:inspectors,id'
    //         ]);
    
    //         // Assign the inspector and set the timestamp
    //         $project->inspector_id = $request->inspector_id;
    //         $project->inspector_assigned_at = now(); // Set current timestamp
    //         $project->save();
    
    //         // Return JSON response for AJAX
    //         return response()->json([
    //             'success' => true,
    //             'message' => 'Inspector assigned successfully!'
    //         ]);
    //     }
    
    //     $inspectors = Inspector::all();
    //     return view('BuilderWebsiteDashboard.Projects.assign', compact('project', 'inspectors'));
    // }
    // public function assign(Request $request, $id)
    // {
    //     $project = WebsiteBuilderProject::findOrFail($id);
        
    //     if ($request->isMethod('post')) {
    //         $request->validate([
    //             'inspector_id' => 'required|exists:inspectors,id'
    //         ]);
    
    //         // Assign the inspector to the project
    //         $project->inspector_id = $request->inspector_id;
    //         $project->save();
    
    //         return redirect()->route('myprojects.index')->with('success', 'Inspector assigned successfully!');
    //     }
    
    //     $inspectors = Inspector::all();
    //     // dd($inspectors);
    //     return view('BuilderWebsiteDashboard.Projects.assign', compact('project', 'inspectors'));
    // }
   
}