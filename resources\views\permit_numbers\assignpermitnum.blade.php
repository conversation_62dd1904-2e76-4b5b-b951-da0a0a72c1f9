@extends('mcdpanel.layouts.master')
@php
    $user = Auth::guard('staff_mcd')->user();
    $permissions = $user && $user->role ? $user->role->permissions : [];
    $isAdmin = Auth::guard('web')->check();
    $hasWritePermission = $isAdmin || (isset($permissions['assign_puid']['write']) && $permissions['assign_puid']['write'] == 1);
@endphp

@section('content')
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4>Assign PUID</h4>
                    </div>
                    <div class="card-body">

                        @if ($projects->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="table-1">
                                    <thead>
                                        <tr>
                                            <th>Sr No</th>
                                            <th>Project Name</th>
                                            <th>Department(Trade)</th>
                                            <th>PUID</th>
                                            <th>Assigned At</th>
                                            <th class="text-center">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($projects as $key => $project)
                                            <tr>
                                                <td class="text-center">{{ $key + 1 }}</td>
                                                <td>
                                                     <a href="{{ route('myprojects.show.status', ['id' => $project->id]) }}">
                                                        {{ $project->project_name }}
                                                    </a>
                                                </td>
                                                <td>{{ implode(', ', $project->department_names) }}</td>
                                                <td>{{ $project->permitNumbers->first()?->permit_number ?? 'N/A' }}</td>
                                                <td>{{ $project->permitNumbers->first()?->created_at->format('d-m-Y') ?? 'N/A' }}</td>


                                                
                                                {{-- <td class="text-center">
                                                    @if ($project->permitNumbers->first())
                                                        <a 
                                                            class="btn btn-sm btn-primary edit-permit-btn"
                                                            data-id="{{ $project->permitNumbers->first()->id }}"
                                                            data-project="{{ $project->project_name }}"
                                                            data-permit="{{ $project->permitNumbers->first()->permit_number }}"
                                                            data-route="{{ route('update.permit-number', $project->permitNumbers->first()->id) }}"
                                                            data-method="PUT"
                                                            title="Edit">
                                                            <i class="fa fa-edit"></i>
                                                        </a>
                                                    @else
                                                       
                                                        <a 
                                                            class="btn btn-sm btn-success assign-permit-btn"
                                                            data-project="{{ $project->project_name }}"
                                                            data-route="{{ route('assign.permit-number', $project->id) }}"
                                                            data-method="POST"
                                                            title="Assign">
                                                            <i class="fa fa-plus"></i> Assign
                                                        </a>
                                                    @endif
                                                </td> --}}
                                                <td class="text-center">
    @if ($hasWritePermission)
        @if ($project->permitNumbers->first())
            <a 
                class="btn btn-sm btn-primary edit-permit-btn"
                data-id="{{ $project->permitNumbers->first()->id }}"
                data-project="{{ $project->project_name }}"
                data-permit="{{ $project->permitNumbers->first()->permit_number }}"
                data-route="{{ route('update.permit-number', $project->permitNumbers->first()->id) }}"
                data-method="PUT"
                title="Edit">
                <i class="fa fa-edit"></i>
            </a>
        @else
            {{-- Show Assign if no permit number exists --}}
            <a 
                class="btn btn-sm btn-success assign-permit-btn"
                data-project="{{ $project->project_name }}"
                data-route="{{ route('assign.permit-number', $project->id) }}"
                data-method="POST"
                title="Assign">
                <i class="fa fa-plus"></i> Assign
            </a>
        @endif
    @else
        {{-- No permission, show disabled or trigger warning --}}
        <button type="button" class="btn btn-sm btn-secondary" onclick="showAccessDenied()" title="Access Denied">
            <i class="fa fa-lock"></i> PUID
        </button>
    @endif
</td>

                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="alert alert-info text-center">
                                <i class="fa fa-info-circle mr-2"></i>
                                No assigned permit numbers found.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- Edit Permit Number Modal -->
<div class="modal fade" id="editPermitModal" tabindex="-1" aria-labelledby="editPermitModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <form id="editPermitForm" method="POST" action="">
        @csrf
        @method('PUT')
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Edit PUID</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
              <div class="form-group">
                  <label>Project Name</label>
                  <input type="text" class="form-control" id="editProjectName" readonly>
              </div>
              <div class="form-group">
                  <label>PUID </label>
                  <input type="text" class="form-control" name="permit_number" id="editPermitNumber" required>
              </div>
          </div>
          <div class="modal-footer">
            <button type="submit" class="btn btn-primary">Assign</button>
          </div>
        </div>
    </form>
  </div>
</div>

@endsection

@push('style')
    <style>
        .btn-sm { 
            padding: 0.25rem 0.5rem; 
        }
        .badge {
            font-size: 0.8rem;
            padding: 0.3em 0.6em;
        }
        .table th, .table td {
            vertical-align: middle;
        }
        .alert {
            background-color: #e7f1ff;
            border: 1px solid #b8daff;
            color: #004085;
            padding: 0.75rem 1.25rem;
            border-radius: 0.25rem;
            margin-bottom: 1rem;
        }
    </style>
 <script>
    document.addEventListener('DOMContentLoaded', function () {
        // Common logic for opening the modal
        function openPermitModal(projectName, permitNumber, route, method) {
            document.getElementById('editProjectName').value = projectName;
            document.getElementById('editPermitNumber').value = permitNumber || '';
            document.getElementById('editPermitForm').setAttribute('action', route);

            // Set correct method field
            const methodInput = document.querySelector('#editPermitForm input[name="_method"]');
            methodInput.value = method;

            const modal = new bootstrap.Modal(document.getElementById('editPermitModal'));
            modal.show();
        }

        
        // Edit Permit
        document.querySelectorAll('.edit-permit-btn').forEach(function (btn) {
            btn.addEventListener('click', function () {
                const projectName = this.getAttribute('data-project');
                const permitNumber = this.getAttribute('data-permit');
                const actionRoute = this.getAttribute('data-route');
                openPermitModal(projectName, permitNumber, actionRoute, 'PUT');
            });
        });

        // Assign Permit
        document.querySelectorAll('.assign-permit-btn').forEach(function (btn) {
            btn.addEventListener('click', function () {
                const projectName = this.getAttribute('data-project');
                const actionRoute = this.getAttribute('data-route');
                openPermitModal(projectName, '', actionRoute, 'POST');
            });
        });
    });

    

    
</script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        @if(session('success'))
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: "{{ session('success') }}",
                timer: 2500,
                showConfirmButton: false,
            });
        @endif

        @if(session('error'))
            Swal.fire({
                icon: 'error',
                title: 'Oops!',
                text: "{{ session('error') }}",
                timer: 3500,
                showConfirmButton: true,
            });
        @endif

        @if($errors->any())
            let errorMessages = "";
            @foreach ($errors->all() as $error)
                errorMessages += "{{ $error }}\n";
            @endforeach
            Swal.fire({
                icon: 'error',
                title: 'Validation Error',
                text: errorMessages,
                showConfirmButton: true,
            });
        @endif
    });

    // SweetAlert Access Denied Popup
        function showAccessDenied() {
            Swal.fire({
                icon: 'error',
                title: 'Access Denied',
                text: 'You do not have permission to modify the Assign PUID.'
            });
        }
</script>


@endpush


