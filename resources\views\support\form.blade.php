@extends('layouts.app')

@section('content')
<div class="container mx-auto p-4">
    <!-- Support Button -->
    <button id="openSupportModal" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Open Support Ticket</button>

    <!-- Modal for Support Form -->
    <div id="supportModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden">
        <div class="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
            <h2 class="text-2xl font-bold mb-4">Submit Support Ticket</h2>
            <form id="supportForm">
                <div class="mb-4">
                    <label for="subject" class="block text-sm font-medium text-gray-700">Subject</label>
                    <input type="text" id="subject" name="subject" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                </div>
                <div class="mb-4">
                    <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                    <textarea id="description" name="description" rows="4" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required></textarea>
                </div>
                <div class="mb-4">
                    <label for="priority" class="block text-sm font-medium text-gray-700">Priority</label>
                    <select id="priority" name="priority" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                    </select>
                </div>
                <div class="flex justify-end">
                    <button type="button" id="closeModal" class="mr-2 bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400">Cancel</button>
                    <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Submit</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Modal toggle
    const openModalBtn = document.getElementById('openSupportModal');
    const modal = document.getElementById('supportModal');
    const closeModalBtn = document.getElementById('closeModal');
    const supportForm = document.getElementById('supportForm');

    openModalBtn.addEventListener('click', () => {
        modal.classList.remove('hidden');
    });

    closeModalBtn.addEventListener('click', () => {
        modal.classList.add('hidden');
    });

    // Form submission via AJAX
    supportForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const formData = new FormData(supportForm);
        const data = {
            subject: formData.get('subject'),
            description: formData.get('description'),
            priority: formData.get('priority'),
            _token: '{{ csrf_token() }}'
        };

        try {
            const response = await fetch('/api/support-tickets', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify(data),
            });

            if (response.ok) {
                alert('Ticket submitted successfully!');
                modal.classList.add('hidden');
                supportForm.reset();
            } else {
                alert('Error submitting ticket. Please try again.');
            }
        } catch (error) {
            alert('Network error. Please try again later.');
        }
    });
</script>
@endsection