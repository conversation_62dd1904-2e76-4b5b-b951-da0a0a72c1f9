<?php
namespace App\Http\Controllers;

use App\Models\AverageReviewTime;
use App\Models\WebsiteBuilderProject;
use App\Models\McdStaffRole;
use App\Models\ProjectReviewDocument;
use App\Models\ProjectReviewDocumentPc;
use App\Models\PhysicalInspectionReport;
use Illuminate\Http\Request;
use Carbon\Carbon;

class AverageReviewTimeController extends Controller
{
    public function index(Request $request)
    {
        $selectedTrade = $request->query('trade');
        $query = WebsiteBuilderProject::with(['permitNumbers']);

        if (!empty($selectedTrade)) {
            $query->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$selectedTrade)]);
        }

        $projects = $query->get();
        $reviews = collect();

        foreach ($projects as $project) {
            // Parse department_trade IDs
            $departmentIds = is_array($project->department_trade)
                ? $project->department_trade
                : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
            $departmentIds = array_filter($departmentIds, 'is_numeric');

            // Get department names
            $departmentNames = McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();
            $departmentTrade = implode(', ', $departmentNames);

            // Department Review Time
            $deptDocs = ProjectReviewDocument::where('project_id', $project->id)
                ->whereIn('department_id', $departmentIds)
                ->get();
            $deptReviewTime = $deptDocs->count() > 0
                ? $deptDocs->avg(function ($doc) {
                    return Carbon::parse($doc->created_at)->diffInDays($doc->updated_at);
                })
                : 0;

            // Planning Commission Review Time
            $pcDocs = ProjectReviewDocumentPc::where('project_id', $project->id)
                ->whereIn('department_id', $departmentIds) // Fixed: Removed 'Encouraged'
                ->get();
            $pcReviewTime = $pcDocs->count() > 0
                ? $pcDocs->avg(function ($doc) {
                    return Carbon::parse($doc->created_at)->diffInDays($doc->updated_at);
                })
                : 0;

            // Physical Inspection Review Time
            $inspectionReports = PhysicalInspectionReport::where('project_id', $project->id)
                ->whereIn('inspectors', $departmentIds)
                ->get();
            $physicalInspectionTime = $inspectionReports->count() > 0
                ? $inspectionReports->avg(function ($report) {
                    return Carbon::parse($report->created_at)->diffInDays($report->updated_at);
                })
                : 0;

            // Total days
            $totalDays = round($deptReviewTime + $pcReviewTime + $physicalInspectionTime);

            // Get permit number
            $permitNumber = $project->permitNumbers->first()->permit_number ?? 'N/A';

            $reviews->push((object)[
                'id' => $project->id,
                'project_name' => $project->project_name ?? 'Unnamed Project',
                'department_trade' => $departmentTrade,
                'permit_number' => $permitNumber,
                'department_review_time' => round($deptReviewTime, 1) . ' days',
                'pc_review_time' => round($pcReviewTime, 1) . ' days',
                'physical_inspection_review_time' => round($physicalInspectionTime, 1) . ' days',
                'days' => $totalDays,
                'end_date' => $project->updated_at->format('Y-m-d'),
            ]);
        }

        // Fetch all departments for filter dropdown
        $departments = McdStaffRole::pluck('name', 'id')->toArray();

        return view('modules.average_review_times.index', compact('reviews', 'departments', 'selectedTrade'));
    }


  
    public function show(AverageReviewTime $averageReviewTime)
    {
        return view('modules.average_review_times.index', compact('averageReviewTime'));
    }
}