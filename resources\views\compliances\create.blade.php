@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <section class="section">
            <div class="section-body">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4>Add Compliance</h4>
                            </div>

                            <form method="POST" action="{{ route('compliances.store') }}">
                                @csrf
                                <div class="card-body">
                                    <div class="row">
                                        <!-- Project Selection -->
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="website_builder_project_id">Select Project <span class="text-danger">*</span></label>
                                                <select name="website_builder_project_id" id="website_builder_project_id" class="form-control @error('website_builder_project_id') is-invalid @enderror" required>
                                                    <option value="">-- Select Project --</option>
                                                    @foreach($projects as $project)
                                                        <option value="{{ $project->id }}" {{ old('website_builder_project_id') == $project->id ? 'selected' : '' }}>
                                                            {{ $project->project_name }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                @error('website_builder_project_id')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>

                                        <!-- Compliance Notes -->
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label for="compliance_notes">Compliance Notes <span class="text-danger">*</span></label>
                                                <textarea name="compliance_notes" id="compliance_notes" rows="5" class="form-control @error('compliance_notes') is-invalid @enderror" required>{{ old('compliance_notes') }}</textarea>
                                                @error('compliance_notes')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Buttons -->
                                <div class="card-footer text-right">
                                    <button type="submit" class="btn btn-primary">Submit</button>
                                    <a href="{{ route('compliances.index') }}" class="btn btn-secondary">Cancel</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
@endsection
