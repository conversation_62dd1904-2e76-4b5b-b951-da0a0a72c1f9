<?php
namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class StaffInviteMail extends Mailable
{
    use Queueable, SerializesModels;

    public $data;

    public function __construct($data)
    {
        \Log::info('StaffInviteMail Data:', $data); // Log data for debugging
        $this->data = $data;
    }

    public function build()
    {
        // Cast values to strings with fallbacks
        $role_name = (string) ($this->data['role_name'] ?? 'Unknown Role');
        $message = (string) ($this->data['message'] ?? '');
        $invite_url = (string) ($this->data['invite_url'] ?? '');

        return $this->subject('Staff Role Invitation')
                    ->view('emails.staff_invite')
                    ->with([
                        'role_name' => $role_name,
                        'message' => $message,
                        'invite_url' => $invite_url,
                    ]);
    }
}