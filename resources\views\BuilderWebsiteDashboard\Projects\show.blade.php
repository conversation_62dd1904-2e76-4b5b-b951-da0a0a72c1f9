@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">Project Details: {{ $project->project_name }}</h4>
                        <div class="btn-group">
                            <button type="button" class="btn btn-warning" data-toggle="modal" data-target="#assignInspectorModal">
                                Assign Inspector
                            </button>
                            {{-- <button type="button" class="btn btn-danger" data-toggle="modal" data-target="#rejectProjectModal">
                                Reject
                            </button> --}}
                            <button type="button" class="btn btn-info" data-toggle="modal" data-target="#objectionProjectModal">
                                Violation
                            </button>
                            <a href="{{ route('myprojects.index') }}" class="btn btn-primary">Back to Projects</a>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Project Information -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5 class="mb-3">Project Information</h5>
                                <table class="table table-bordered">
                                    <tr>
                                        <th>Project Name</th>
                                        <td>{{ $project->project_name ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Address</th>
                                        <td>{{ $project->project_address ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>City</th>
                                        <td>{{ $project->city ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>State</th>
                                        <td>{{ $project->state ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>ZIP</th>
                                        <td>{{ $project->zip ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Approval Status</th>
                                        <td>
                                            <span class="badge {{ $project->approval_status === 'Pending' ? 'badge-success' : ($project->approval_status === 'Violation' ? 'badge-warning' : ($project->approval_status === 'Approval' && !is_null($project->inspector_id) ? 'badge-info' : 'badge-secondary')) }}">
                                                {{ $project->approval_status ?? 'Pending' }}
                                            </span>
                                            @if ($project->approval_status === 'Violation' && !empty($project->objection_comment))
                                                <br>
                                                <small class="text-muted">
                                                    Comment: {{ $project->objection_comment }}
                                                </small>
                                            @elseif ($project->approval_status === 'Approval' && !is_null($project->inspector_id) && !is_null($project->inspector_assigned_at))
                                                <br>
                                                {{-- <small class="text-muted">
                                                    Assigned on {{ \Carbon\Carbon::parse($project->inspector_assigned_at)->format('d M Y h:i A') }}
                                                </small> --}}
                                                <small class="text-muted">
                                                    Assigned to {{ $project->inspector->first_name }} {{ $project->inspector->last_name }} on {{ \Carbon\Carbon::parse($project->inspector_assigned_at)->format('d M Y h:i A') }}
                                                </small>
                                            @endif
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h5 class="mb-3">Project Scope</h5>
                                <table class="table table-bordered">
                                    {{-- <tr>
                                        <th>Architect Category</th>
                                        <td>{{ $project->architectCategory->name ?? 'N/A' }}</td>
                                    </tr> --}}
                                    {{-- <tr>
                                        <th>Builder Sub-Category</th>
                                        <td>{{ $project->builderSubCategory->name ?? 'N/A' }}</td>
                                    </tr> --}}
                                    <tr>
                                        <th>Scope of Work</th>
                                        <td>{{ $project->scope_of_work ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Permit Number</th>
                                        <td>{{ $project->permit_number ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Trades Involved</th>
                                        <td>
                                            @php
                                                $trades = json_decode($project->trades_involved, true);
                                            @endphp
                                            @if (is_array($trades) && count($trades))
                                                {{ implode(', ', array_filter($trades)) }}
                                            @else
                                                N/A
                                            @endif
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <!-- Timeline -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="mb-3">Timeline</h5>
                                <table class="table table-bordered">
                                    <tr>
                                        <th>Expected Start Date</th>
                                        <td>{{ \Carbon\Carbon::parse($project->expected_start_date)->format('M d, Y') ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <th>Expected End Date</th>
                                        <td>{{ \Carbon\Carbon::parse($project->expected_end_date)->format('M d, Y') ?? 'N/A' }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <!-- Documents -->
                        <div class="row">
                            <div class="col-12">
                                <h5 class="mb-3">Documents (PDFs)</h5>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th>Document Type</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>Project Image</td>
                                                <td>
                                                    @if ($project->project_image)
                                                        <a href="http://localhost/mcdconstructions/public/project_images/{{ basename($project->project_image) }}"
                                                            target="_blank" class="btn btn-sm btn-primary">
                                                            <i class="fa fa-file-image mr-1"></i> View Image
                                                        </a>
                                                    @else
                                                        <span class="text-muted">Not Available</span>
                                                    @endif
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Full Plan Set</td>
                                                <td>
                                                    @if ($project->full_plan_set)
                                                        <a href="http://localhost/mcdconstructions/public/profile_images/{{ basename($project->full_plan_set) }}"
                                                            target="_blank" class="btn btn-sm btn-primary">
                                                            <i class="fa fa-file-pdf mr-1"></i> View PDF
                                                        </a>
                                                    @else
                                                        <span class="text-muted">Not Available</span>
                                                    @endif
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Site Plan</td>
                                                <td>
                                                    @if ($project->site_plan)
                                                        <a href="http://localhost/mcdconstructions/public/profile_images/{{ basename($project->site_plan) }}"
                                                            target="_blank" class="btn btn-sm btn-primary">
                                                            <i class="fa fa-file-pdf mr-1"></i> View PDF
                                                        </a>
                                                    @else
                                                        <span class="text-muted">Not Available</span>
                                                    @endif
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Structural Calculations</td>
                                                <td>
                                                    @if ($project->structural_calculations)
                                                        <a href="http://localhost/mcdconstructions/public/profile_images/{{ basename($project->structural_calculations) }}"
                                                            target="_blank" class="btn btn-sm btn-primary">
                                                            <i class="fa fa-file-pdf mr-1"></i> View PDF
                                                        </a>
                                                    @else
                                                        <span class="text-muted">Not Available</span>
                                                    @endif
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Engineering Reports</td>
                                                <td>
                                                    @if ($project->engineering_reports)
                                                        <a href="http://localhost/mcdconstructions/public/profile_images/{{ basename($project->engineering_reports) }}"
                                                            target="_blank" class="btn btn-sm btn-primary">
                                                            <i class="fa fa-file-pdf mr-1"></i> View PDF
                                                        </a>
                                                    @else
                                                        <span class="text-muted">Not Available</span>
                                                    @endif
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Energy Calculations</td>
                                                <td>
                                                    @if ($project->energy_calculations)
                                                        <a href="http://localhost/mcdconstructions/public/profile_images/{{ basename($project->energy_calculations) }}"
                                                            target="_blank" class="btn btn-sm btn-primary">
                                                            <i class="fa fa-file-pdf mr-1"></i> View PDF
                                                        </a>
                                                    @else
                                                        <span class="text-muted">Not Available</span>
                                                    @endif
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Special Certifications</td>
                                                <td>
                                                    @if ($project->special_certifications)
                                                        <a href="http://localhost/mcdconstructions/public/profile_images/{{ basename($project->special_certifications) }}"
                                                            target="_blank" class="btn btn-sm btn-primary">
                                                            <i class="fa fa-file-pdf mr-1"></i> View PDF
                                                        </a>
                                                    @else
                                                        <span class="text-muted">Not Available</span>
                                                    @endif
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-orange" data-toggle="modal" data-target="#assignInspectorModal">Assign Inspector</button>
                                            {{-- <button type="button" class="btn btn-danger" data-toggle="modal" data-target="#rejectProjectModal">Reject</button> --}}
                                            <button type="button" class="btn btn-info" data-toggle="modal" data-target="#objectionProjectModal">Violation</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Assign Inspector Modal -->
        <div class="modal fade" id="assignInspectorModal" tabindex="-1" role="dialog" aria-labelledby="assignInspectorModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="assignInspectorModalLabel">Assign Inspector to {{ $project->project_name }}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div id="assignFeedback" class="alert" style="display: none;"></div>
                        <form id="assignInspectorForm" method="POST" action="{{ route('myprojects.assign', $project->id) }}">
                            @csrf
                            <div class="form-group">
                                <label for="inspector_id" class="text-dark">Select Inspector</label>
                                <select name="inspector_id" id="inspector_id" class="form-control" required>
                                    <option value="">Select an Inspector</option>
                                    @foreach ($inspectors as $inspector)
                                        {{-- <option value="{{ $inspector->id }}">{{ $project->inspector->first_name }} {{ $project->inspector->last_name }}</option> --}}
                                        <option value="{{ $inspector->id }}">{{ $inspector->first_name }} {{ $inspector->last_name }}</option>

                                        @endforeach
                                </select>
                            </div>
                            <div class="form-group mt-3">
                                <button type="submit" class="btn btn-primary">Assign Inspector</button>
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <!-- Reject Project Modal -->
        <div class="modal fade" id="rejectProjectModal" tabindex="-1" role="dialog" aria-labelledby="rejectProjectModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="rejectProjectModalLabel">Reject Project: {{ $project->project_name }}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div id="rejectFeedback" class="alert" style="display: none;"></div>
                        <form id="rejectProjectForm" method="POST" action="{{ route('builder.projects.updateStatus', $project->id) }}">
                            @csrf
                            @method('PATCH')
                            <input type="hidden" name="status" value="Rejected">
                            <p>Are you sure you want to reject this project?</p>
                            <div class="form-group mt-3">
                                <button type="submit" class="btn btn-danger">Reject Project</button>
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <!-- Objection Project Modal -->
        <div class="modal fade" id="objectionProjectModal" tabindex="-1" role="dialog" aria-labelledby="objectionProjectModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="objectionProjectModalLabel">Raise Violation: {{ $project->project_name }}</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div id="objectionFeedback" class="alert" style="display: none;"></div>
                        <form id="objectionProjectForm" method="POST" action="{{ route('builder.projects.updateStatus', $project->id) }}">
                            @csrf
                            @method('PATCH')
                            <input type="hidden" name="status" value="Violation">
                            <div class="form-group">
                                <label for="objection_comment" class="text-dark">Violation Comment</label>
                                <textarea name="objection_comment" id="objection_comment" class="form-control" rows="4" placeholder="Enter violation details..." required></textarea>
                            </div>
                            <div class="form-group mt-3">
                                <button type="submit" class="btn btn-info">Submit Violation</button>
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('style')
    <style>
        .btn-orange {
            background-color: orange;
            border-color: orange;
            color: white;
        }
        .btn-orange:hover {
            background-color: darkorange;
            border-color: darkorange;
        }
        .card {
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }
        .card-header h4 {
            margin: 0;
            font-weight: 600;
        }
        .table th {
            background-color: #f1f3f5;
            font-weight: 600;
        }
        .table td {
            vertical-align: middle;
        }
        .badge {
            font-size: 0.8rem;
            padding: 0.3em 0.6em;
        }
        .btn-sm {
            padding: 0.25rem 0.5rem;
        }
        h5 {
            font-weight: 600;
            color: #343a40;
        }
        .table-bordered {
            border: 1px solid #dee2e6;
        }
        .fa-file-pdf {
            color: #dc3545;
        }
        @media (max-width: 767px) {
            .table-responsive {
                margin-bottom: 1rem;
            }
        }
        select.form-control,
        select.form-control option {
            color: #000 !important;
            background-color: #fff !important;
        }
        select.form-control option:hover {
            background-color: #f1f1f1 !important;
            color: #000 !important;
        }
        select.form-control {
            border: 1px solid #ced4da !important;
            appearance: auto !important;
        }
        textarea.form-control {
            resize: vertical;
        }
        .btn-group .btn {
            margin-right: 5px;
        }
    </style>
@endpush
@push('scripts')
    <script>
        $(document).ready(function() {
            // Assign Inspector Form Submission
            $('#assignInspectorForm').on('submit', function(e) {
                e.preventDefault();
                var form = $(this);
                var feedback = $('#assignFeedback');
                $.ajax({
                    url: form.attr('action'),
                    type: 'POST',
                    data: form.serialize(),
                    dataType: 'json',
                    success: function(response) {
                        if (response.success && response.redirect) {
                            feedback.removeClass('alert-danger').addClass('alert-success').text(response.message).show();
                            setTimeout(function() {
                                $('#assignInspectorModal').modal('hide');
                                window.location.href = response.redirect;
                            }, 2000);
                        } else {
                            feedback.removeClass('alert-success').addClass('alert-danger').text('Unexpected response from server.').show();
                        }
                    },
                    error: function(xhr) {
                        var errorMsg = 'An error occurred while assigning the inspector.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMsg = xhr.responseJSON.message;
                        } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                            errorMsg = Object.values(xhr.responseJSON.errors).flat().join(' ');
                        }
                        feedback.removeClass('alert-success').addClass('alert-danger').text(errorMsg).show();
                    }
                });
            });
            // Reject Project Form Submission
            $('#rejectProjectForm').on('submit', function(e) {
                e.preventDefault();
                var form = $(this);
                var feedback = $('#rejectFeedback');
                $.ajax({
                    url: form.attr('action'),
                    type: 'POST',
                    data: form.serialize(),
                    dataType: 'json',
                    success: function(response) {
                        if (response.success && response.redirect) {
                            feedback.removeClass('alert-danger').addClass('alert-success').text(response.message).show();
                            setTimeout(function() {
                                $('#rejectProjectModal').modal('hide');
                                window.location.href = response.redirect;
                            }, 2000);
                        } else {
                            feedback.removeClass('alert-success').addClass('alert-danger').text('Unexpected response from server.').show();
                        }
                    },
                    error: function(xhr) {
                        var errorMsg = 'An error occurred while rejecting the project.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMsg = xhr.responseJSON.message;
                        } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                            errorMsg = Object.values(xhr.responseJSON.errors).flat().join(' ');
                        }
                        feedback.removeClass('alert-success').addClass('alert-danger').text(errorMsg).show();
                    }
                });
            });
            // Objection Project Form Submission
            $('#objectionProjectForm').on('submit', function(e) {
                e.preventDefault();
                var form = $(this);
                var feedback = $('#objectionFeedback');
                $.ajax({
                    url: form.attr('action'),
                    type: 'POST',
                    data: form.serialize(),
                    dataType: 'json',
                    success: function(response) {
                        if (response.success && response.redirect) {
                            feedback.removeClass('alert-danger').addClass('alert-success').text(response.message).show();
                            setTimeout(function() {
                                $('#objectionProjectModal').modal('hide');
                                window.location.href = response.redirect;
                            }, 2000);
                        } else {
                            feedback.removeClass('alert-success').addClass('alert-danger').text('Unexpected response from server.').show();
                        }
                    },
                    error: function(xhr) {
                        var errorMsg = 'An error occurred while submitting the violation.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMsg = xhr.responseJSON.message;
                        } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                            errorMsg = Object.values(xhr.responseJSON.errors).flat().join(' ');
                        }
                        feedback.removeClass('alert-success').addClass('alert-danger').text(errorMsg).show();
                    }
                });
            });
            // Clear feedback when modals are closed
            $('#assignInspectorModal, #rejectProjectModal, #objectionProjectModal').on('hidden.bs.modal', function() {
                $(this).find('.alert').hide().removeClass('alert-success alert-danger').text('');
                $(this).find('form')[0].reset();
            });
        });
    </script>
@endpush