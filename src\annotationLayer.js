import { fabric } from 'fabric'

let annotationCanvases = []
let currentTool = ''

export function setupAnnotationLayer(container) {
  const pageContainers = container.querySelectorAll('.page-container')
  annotationCanvases = []

  pageContainers.forEach((pageContainer, index) => {
    const pdfCanvas = pageContainer.querySelector('.pdf-canvas')
    const fabricCanvas = new fabric.Canvas(null, {
      width: pdfCanvas.width,
      height: pdfCanvas.height
    })

    fabricCanvas.wrapperEl.style.position = 'absolute'
    fabricCanvas.wrapperEl.style.top = '0'
    fabricCanvas.wrapperEl.style.left = '0'
    pageContainer.appendChild(fabricCanvas.wrapperEl)

    annotationCanvases.push(fabricCanvas)

    new ResizeObserver(() => {
      fabricCanvas.setDimensions({
        width: pdfCanvas.width,
        height: pdfCanvas.height
      })
    }).observe(pdfCanvas)
  })
}

export function activateAnnotationTool(tool) {
  currentTool = tool
  annotationCanvases.forEach(canvas => {
    if (tool === 'pen') {
      canvas.isDrawingMode = true
      canvas.off('mouse:down')
    } else {
      canvas.isDrawingMode = false
      canvas.off('mouse:down')
      canvas.on('mouse:down', handleMouseDown)
    }
  });
}

function handleMouseDown(event) {
  const canvas = event.target.canvas
  if (!canvas) return

  const pointer = canvas.getPointer(event.e)
  let object

  switch (currentTool) {
    case 'text':
      object = new fabric.Textbox('Type here', {
        left: pointer.x,
        top: pointer.y,
        width: 150,
        fontSize: 20,
        editable: true
      })
      break
    case 'rectangle':
      object = new fabric.Rect({
        left: pointer.x,
        top: pointer.y,
        width: 100,
        height: 100,
        fill: 'rgba(255, 0, 0, 0.3)',
        stroke: 'red',
        strokeWidth: 2
      })
      break
    case 'signature':
      // Placeholder for signature capture
      object = new fabric.Text('Signature', {
        left: pointer.x,
        top: pointer.y,
        fontSize: 20
      })
      break
    default:
      return
  }

  canvas.add(object)
  canvas.setActiveObject(object)
}

export function saveAnnotationsData() {
  const annotations = annotationCanvases.map((canvas, index) => ({
    page: index + 1,
    data: canvas.toJSON(['left', 'top', 'width', 'height'])
  })))

  localStorage.setItem('pdfAnnotations', JSON.stringify(annotations))
  alert('Annotations saved!')
}

export function getAnnotationCanvases() {
  return annotationCanvases;
} 