<?php

namespace App\Models;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use App\Models\RoleMcd;

class McdStaff extends Authenticatable
{
    use Notifiable;
    protected $guard = 'staff_mcd';

    protected $fillable = [
        'staff_name', 'name', 'email', 'password', 'role_id', 'status', 'description',
        'contact_number', 'address', 'department_id'
    ];

    protected $hidden = ['password'];

    protected function casts(): array
    {
        return [
            'status' => 'boolean',
            'password' => 'hashed',
        ];
    }

    public function role()
{
    return $this->belongsTo(RoleMcd::class, 'role_id');
}

    public function roles()
    {
        return $this->belongsToMany(RoleMcd::class, 'staff_roles', 'staff_id', 'role_id')
                    ->withTimestamps();
    }

    public function getAuthPassword()
    {
        return $this->password;
    }

   // app/Models/McdStaff.php
   public function department()
    {
        return $this->belongsTo(McdStaffRole::class, 'department_id');
    }

public function hasPermission($permissionName, $accessType = 'read')
{
    foreach ($this->roles()->with('permissions')->get() as $role) {
        $permission = $role->permissions->firstWhere('name', $permissionName);
        if ($permission) {
            if ($accessType === 'read' && $permission->pivot->can_read) {
                return true;
            }
            if ($accessType === 'write' && $permission->pivot->can_write) {
                return true;
            }
        }
    }
    return false;
}

    /**
     * Get the name attribute (maps to staff_name for profile compatibility)
     */
    public function getNameAttribute()
    {
        return $this->staff_name;
    }

    /**
     * Set the name attribute (maps to staff_name for profile compatibility)
     */
    public function setNameAttribute($value)
    {
        $this->attributes['staff_name'] = $value;
    }

}