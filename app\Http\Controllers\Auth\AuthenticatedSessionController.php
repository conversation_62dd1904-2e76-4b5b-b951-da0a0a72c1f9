<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;
use App\Models\McdStaff;
use App\Models\User;
use Illuminate\Support\Facades\Hash;


class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     */
    public function create(): View
    {
        //dd('hello');
        return view('auth.login');
    }

    /**
     * Handle an incoming authentication request.
     */
    // public function store(LoginRequest $request): RedirectResponse
    // {
    //     $request->authenticate();

    //     $request->session()->regenerate();
    //     return redirect()->intended(route('dashboard', absolute: false));
    // }

//    public function store(LoginRequest $request): RedirectResponse
// {
//     $request->authenticate();
//     $request->session()->regenerate();

//     if (Auth::guard('web')->check()) {
//         //dd('web user logged in');
//         return redirect()->route('dashboard');
//     } elseif (Auth::guard('staff_mcd')->check()) {
//        // dd('staff_mcd user logged in');
//         return redirect()->route('staff.dashboard');
//     }

//     dd('No user authenticated');
//     return redirect('/login');
// }


 public function store(LoginRequest $request): RedirectResponse
{
    $credentials = $request->only('email', 'password');

    // Try 'web' guard (User)
    if (Auth::guard('web')->attempt($credentials)) {
        $user = Auth::guard('web')->user();
        if ($user->role_id === 3) {
            $request->session()->regenerate();
            return redirect()->intended(route('dashboard'));
        }

        Auth::guard('web')->logout();
        return redirect()->route('login')->withErrors(['email' => 'Unauthorized access. Only MCD can login.']);
    }

    // Try 'staff_mcd' guard (McdStaff)
   if (Auth::guard('staff_mcd')->attempt($request->only('email', 'password'))) {
    $staff = Auth::guard('staff_mcd')->user();

    // Final condition with safe checking
    if ($staff->status == 1 && optional($staff->role)->status == 1) {
        $request->session()->regenerate();
        $request->session()->put('permissions', $staff->role->permissions);
        return redirect()->intended(route('dashboard', absolute: false));
    }

    Auth::guard('staff_mcd')->logout();
    return redirect()->route('login')->withErrors([
        'email' => 'Inactive user or role. Please contact admin.'
    ]);
}

    // If both fail
    Auth::guard('web')->logout();
    Auth::guard('staff_mcd')->logout();
    $request->session()->invalidate();
    $request->session()->regenerateToken();

    return redirect()->route('login')->with('error', 'Invalid credentials or unauthorized access.');
}



    /**
     * Destroy an authenticated session.
     */
   

    public function destroy(Request $request): RedirectResponse
{
    Auth::guard('web')->logout();
    Auth::guard('staff_mcd')->logout();

    $request->session()->invalidate();
    $request->session()->regenerateToken();

    return redirect('/login');
}

}
