<?php
namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class InspectorInvitation extends Mailable
{
    use Queueable, SerializesModels;

    public $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function build()
    {
        return $this->subject('Inspector Invitation')
                    ->view('emails.inspector_invitation')
                    ->with([
                        'email' => $this->data['email'],
                        'customMessage' => $this->data['message'],
                        'create_url' => $this->data['create_url'],
                    ]);
    }
}
?>