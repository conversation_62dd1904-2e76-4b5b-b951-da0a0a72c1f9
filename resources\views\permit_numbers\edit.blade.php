@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <section class="section">
            <div class="section-body">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4>Permit Number > Edit Assigned Permit</h4>
                            </div>
                            <form method="POST" action="{{ route('permit-numbers.update', $permit->id) }}">
                                @csrf
                                @method('PUT')
                            
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Select Project <span class="text-danger">*</span></label>
                                                <select name="website_builder_project_id" class="form-control @error('website_builder_project_id') is-invalid @enderror" required>
                                                    <option value="">-- Select Project --</option>
                                                    @foreach($projects as $project)
                                                        <option value="{{ $project->id }}" {{ $permit->website_builder_project_id == $project->id ? 'selected' : '' }}>
                                                            {{ $project->project_name }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                @error('website_builder_project_id')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Permit Number <span class="text-danger">*</span></label>
                                                <input type="text" name="permit_number" class="form-control @error('permit_number') is-invalid @enderror" value="{{ $permit->permit_number }}" required>
                                                @error('permit_number')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card-footer text-right">
                                    <button type="submit" class="btn btn-primary mr-1">Update</button>
                                    <a href="{{ route('permit-numbers.index') }}" class="btn btn-secondary">Cancel</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
@endsection
