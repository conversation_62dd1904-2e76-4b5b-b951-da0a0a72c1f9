<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Form</title>


    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-image: url("{{ asset('assets/img/second_logo.png') }}");
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            overflow: hidden;
            position: relative;
        }
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.1));
            animation: overlayPulse 10s infinite ease-in-out;
            z-index: -1;
        }
        .login-wrapper {
            width: 100%;
            max-width: 400px; / Reduced from 520px for a smaller form /
            text-align: center;
            animation: floatIn 1s ease-out;
            perspective: 1000px;
        }
        .construction-header {
            background: linear-gradient(135deg, #ffffff, #e6e9f0);
            color: #2d3748;
            padding: 12px; / Reduced from 16px /
            border-radius: 16px 16px 0 0; / Slightly smaller radius /
            font-size: 18px; / Reduced from 22px /
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1.5px; / Slightly reduced /
            border-bottom: 1px solid rgba(255, 255, 255, 0.5);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1), inset 0 -1px 4px rgba(255, 255, 255, 0.7); / Adjusted shadow /
        }
        .login-container {
            background: rgba(255, 255, 255, 0.15);
            padding: 30px; / Reduced from 45px /
            border-radius: 0 0 16px 16px; / Matching smaller radius /
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2), inset 0 0 12px rgba(255, 255, 255, 0.3); / Slightly reduced shadow /
            width: 100%;
            text-align: left;
            backdrop-filter: blur(15px) saturate(1.5);
            border: 1px solid rgba(255, 255, 255, 0.4);
            position: relative;
            overflow: hidden;
            transform: rotateX(2deg);
            transition: transform 0.5s ease;
        }
        .login-container:hover {
            transform: rotateX(0deg);
        }
        .login-container::before {
            content: '';
            position: absolute;
            top: -60%;
            left: -60%;
            width: 220%;
            height: 220%;
            background: radial-gradient(circle, rgba(64, 196, 255, 0.2) 0%, transparent 60%);
            animation: glowPulse 12s infinite ease-in-out;
            z-index: -1;
        }
        .login-container h1 {
            text-align: center;
            margin-bottom: 25px; / Reduced from 40px /
            color: #fff;
            font-size: 24px; / Reduced from 32px /
            font-weight: 900;
            letter-spacing: 1.2px; / Slightly reduced /
            text-transform: uppercase;
            text-shadow: 0 2px 10px rgba(64, 196, 255, 0.7), 0 0 4px rgba(255, 255, 255, 0.5); / Adjusted glow /
            animation: textGlow 3s infinite alternate;
        }
        .form-group {
            margin-bottom: 20px; / Reduced from 30px /
           
            position: relative;
            transform: translateZ(0);
        }
        .form-group label {
            display: block;
            font-size: 15px; / Reduced from 17px /
            color: #e6e9f0;
            margin-bottom: 8px; / Reduced from 10px /
            font-weight: 600;
            text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3); / Slightly reduced /
            transition: transform 0.3s ease, color 0.3s ease;
        }
        .form-group .required-star {
            color: #ff8787;
            font-size: 15px; / Reduced from 17px /
            margin-left: 4px; / Slightly reduced /
        }
        .form-group input {
            width: 100%;
            padding: 12px; / Reduced from 16px /
            border: 1px solid rgba(255, 255, 255, 0.4);
            border-radius: 10px; / Slightly smaller /
            font-size: 15px; / Reduced from 17px /
            box-sizing: border-box;
            background: rgba(255, 255, 255, 0.05);
            color: #fff;
            transition: all 0.4s ease;
            box-shadow: inset 0 1px 4px rgba(0, 0, 0, 0.1), 0 1px 8px rgba(64, 196, 255, 0.2); / Adjusted shadow /
        }
        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.6);
            font-style: italic;
        }
        .form-group input:focus {
            border-color: #80deea;
            box-shadow: 0 0 12px rgba(128, 222, 234, 0.6), inset 0 1px 4px rgba(0, 0, 0, 0.1); / Adjusted glow /
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px) scale(1.01); / Slightly reduced scale /
        }
        .form-group .error {
            color: #ff8787;
            font-size: 13px; / Reduced from 15px /
            margin-top: 6px; / Reduced from 8px /
            display: block;
            font-weight: 500;
            text-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
        }
        .submit-btn {
            display: block;
            width: 99%;
            padding: 12px; / Reduced from 16px /
            background: linear-gradient(135deg, #80deea, #0288d1);
            color: black;
            border: none;
            border-radius: 10px; / Slightly smaller /
            font-size: 16px; / Reduced from 18px /
            font-weight: 700;
            cursor: pointer;
            transition: all 0.5s ease;
            box-shadow: 0 6px 20px rgba(128, 222, 234, 0.6), inset 0 -1px 4px rgba(255, 255, 255, 0.4); / Adjusted shadow /
            text-transform: uppercase;
            letter-spacing: 1.2px; / Slightly reduced /
            position: relative;
            overflow: hidden;
            transform: translateZ(8px); / Slightly reduced depth /
        }
        .submit-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.4);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.7s ease, height 0.7s ease;
            z-index: 0;
        }
        .submit-btn:hover::before {
            width: 300px; / Reduced from 400px /
            height: 300px;
        }
        .submit-btn:hover {
            background: linear-gradient(135deg, #4fc3f7, #01579b);
            box-shadow: 0 10px 25px rgba(128, 222, 234, 0.8), inset 0 -1px 4px rgba(255, 255, 255, 0.4);
            transform: translateY(-4px) translateZ(15px); / Slightly reduced lift /
        }
        .submit-btn:active {
            transform: translateY(0) translateZ(5px);
            box-shadow: 0 4px 15px rgba(128, 222, 234, 0.5); / Adjusted shadow /
        }
        .submit-btn span {
            position: relative;
            z-index: 1;s
        }
        / Animations /
        @keyframes floatIn {
            0% {
                opacity: 0;
                transform: translateY(30px) rotateX(10deg); / Slightly reduced translation /
            }
            100% {
                opacity: 1;
                transform: translateY(0) rotateX(0deg);
            }
        }
        @keyframes glowPulse {
            0%, 100% { opacity: 0.5; transform: scale(0.95); }
            50% { opacity: 1; transform: scale(1); }
        }
        @keyframes overlayPulse {
            0%, 100% { opacity: 0.8; }
            50% { opacity: 1; }
        }
        @keyframes textGlow {
            0% { text-shadow: 0 2px 10px rgba(64, 196, 255, 0.7), 0 0 4px rgba(255, 255, 255, 0.5); }
            100% { text-shadow: 0 4px 15px rgba(64, 196, 255, 1), 0 0 6px rgba(255, 255, 255, 0.8); }
        }
    </style>
    
</head>
<body>
    <div class="login-wrapper">
        {{-- <div class="construction-header">Construction</div> --}}
        <form method="POST" action="{{ route('login') }}" class="login-container">
            @csrf
            <h1>LOGIN MCD</h1>

            <!-- Email Address -->
            <div class="form-group" style=" margin-right: 28px !important;">
                <label for="email">Email <span class="required-star">*</span></label>
                <input id="email" 
                       type="email" 
                       name="email" 
                       value="{{ old('email') }}" 
                       required 
                       autofocus 
                       autocomplete="username" />
                @if ($errors->has('email'))
                    <span class="error">{{ $errors->first('email') }}</span>
                @endif
            </div>

            <!-- Password -->
            <div class="form-group" style=" margin-right: 28px !important;">
                <label for="password">Password <span class="required-star">*</span></label>
                <input id="password" 
                       type="password" 
                       name="password" 
                       required 
                       autocomplete="current-password" />
                @if ($errors->has('password'))
                    <span class="error">{{ $errors->first('password') }}</span>
                @endif
            </div>

            <!-- Login Button -->
            <button type="submit" class="submit-btn">Log in</button>
        </form>
    </div>
</body>
</html>