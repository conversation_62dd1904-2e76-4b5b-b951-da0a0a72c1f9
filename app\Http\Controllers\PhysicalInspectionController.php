<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\WebsiteBuilderProject;
use App\Models\McdStaffRole;
use App\Models\PhysicalInspectionReport;
use App\Models\Inspector;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Storage;
use ZipArchive;

class PhysicalInspectionController extends Controller
{
    // public function index(Request $request)
    // {
    //     $selectedTrade = $request->query('trade');
    //     $query = WebsiteBuilderProject::with(['physicalInspectionReportnew' => function ($query) {
    //         $query->latest('inspection_done_at')->first();
    //     }]);

    //     if (!empty($selectedTrade)) {
    //         $query->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$selectedTrade)]);
    //     }

    //     $query->where(function ($q) {
    //         $q->whereDoesntHave('physicalInspectionReportnew')
    //           ->orWhereHas('physicalInspectionReportnew', function ($subQuery) {
    //               $subQuery->where('report_status', '2'); 
    //           });
    //     });

    //     $projects = $query->get();

    //     foreach ($projects as $project) {
    //         $departmentIds = is_array($project->department_trade)
    //             ? $project->department_trade
    //             : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
    //         $departmentIds = array_filter($departmentIds, 'is_numeric');
    //         $project->department_names = McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();
    //     }

    //     $departments = McdStaffRole::pluck('name', 'id')->toArray();

    //     return view('BuilderWebsiteDashboard.physicalinspection.activeinspection', compact('projects', 'departments'));
    // }
    public function index(Request $request)
{
    $selectedTrade = $request->query('trade');
    $query = WebsiteBuilderProject::query();

    if (!empty($selectedTrade)) {
        $query->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$selectedTrade)]);
    }

    // Exclude projects that have any records in physical_inspection_reports
    $query->whereDoesntHave('physicalInspectionReports');

    $projects = $query->get();

    foreach ($projects as $project) {
        $departmentIds = is_array($project->department_trade)
            ? $project->department_trade
            : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
        $departmentIds = array_filter($departmentIds, 'is_numeric');
        $project->department_names = McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();
    }

    $departments = McdStaffRole::pluck('name', 'id')->toArray();

    return view('BuilderWebsiteDashboard.physicalinspection.activeinspection', compact('projects', 'departments'));
}

    public function showreport()
    {
        $departments = McdStaffRole::pluck('name', 'id')->toArray();
        $query = WebsiteBuilderProject::with('physicalInspectionReportnew');

        if (request()->has('department') && request('department') != '') {
            $departmentId = request('department');
            $query->whereJsonContains('department_trade', $departmentId);
        }

        if (request()->has('status') && request('status') != '') {
            $status = request('status');
            if ($status === 'no_report') {
                $query->whereDoesntHave('physicalInspectionReportnew');
            } else {
                $query->whereHas('physicalInspectionReportnew', function($q) use ($status) {
                    $q->where('report_status', $status);
                });
            }
        }

        $projects = $query->paginate(10);

        foreach ($projects as $project) {
            $departmentIds = json_decode($project->department_trade, true);
            if (!is_array($departmentIds)) {
                $departmentIds = explode(',', $project->department_trade);
            }
            $project->department_names = McdStaffRole::whereIn('id', $departmentIds)
                ->pluck('name')
                ->toArray();
            $project->full_address = implode(', ', array_filter([
                $project->project_address,
                $project->city,
                $project->state,
                $project->zip
            ]));
        }

        return view('BuilderWebsiteDashboard.physicalinspection.showreport', compact('projects', 'departments'));
    }

    // public function detailreport($id)
    // {
    //     $project = WebsiteBuilderProject::with(['inspector'])->findOrFail($id);

    //     $departmentIds = is_array($project->department_trade)
    //         ? $project->department_trade
    //         : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
    //     $departmentIds = array_filter($departmentIds, 'is_numeric');
    //     $project->department_names = McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();

    //     $project->full_address = implode(', ', array_filter([
    //         $project->project_address,
    //         $project->city,
    //         $project->state,
    //         $project->zip
    //     ]));

    //     $inspectionReports = PhysicalInspectionReport::where('project_id', $id)->get();

    //     $inspectionReports = $inspectionReports->map(function ($report) {
    //         $inspectorData = [];
    //         if (is_array($report->inspectors)) {
    //             $inspectors = Inspector::with('role')->whereIn('id', $report->inspectors)->get();
    //             $inspectorData = $inspectors->map(function ($inspector) {
    //                 return [
    //                     'name' => $inspector->first_name . ' ' . $inspector->last_name,
    //                     'department' => $inspector->role ? $inspector->role->name : 'N/A'
    //                 ];
    //             })->toArray();
    //         }
    //         $report->inspector_data = $inspectorData;
    //         return $report;
    //     });

    //     return view('BuilderWebsiteDashboard.physicalinspection.detailedreport', compact('project', 'inspectionReports'));
    // }
    public function detailreport($id)
{
    $project = WebsiteBuilderProject::with(['inspector'])->findOrFail($id);

    $departmentIds = is_array($project->department_trade)
        ? $project->department_trade
        : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
    $departmentIds = array_filter($departmentIds, 'is_numeric');
    $project->department_names = McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();

    $project->full_address = implode(', ', array_filter([
        $project->project_address,
        $project->city,
        $project->state,
        $project->zip
    ]));

    $inspectionReports = PhysicalInspectionReport::where('project_id', $id)
        ->orderBy('created_at', 'desc')
        ->get();

    $inspectionReports = $inspectionReports->map(function ($report) {
        $inspectorData = [];
        if (is_array($report->inspectors)) {
            $inspectors = Inspector::with('role')->whereIn('id', $report->inspectors)->get();
            $inspectorData = $inspectors->map(function ($inspector) {
                return [
                    'name' => $inspector->first_name . ' ' . $inspector->last_name,
                    'department' => $inspector->role ? $inspector->role->name : 'N/A'
                ];
            })->toArray();
        }
        $report->inspector_data = $inspectorData;
        return $report;
    });

    return view('BuilderWebsiteDashboard.physicalinspection.detailedreport', compact('project', 'inspectionReports'));
}


    public function complianceflag()
    {
        $departments = McdStaffRole::pluck('name', 'id')->toArray();
        $query = WebsiteBuilderProject::with(['physicalInspectionReportnew' => function ($query) {
            $query->latest('inspection_done_at')->first();
        }]);

        if (request()->has('department') && request('department') != '') {
            $departmentId = request('department');
            $query->whereJsonContains('department_trade', $departmentId);
        }

        $query->whereHas('physicalInspectionReportnew', function ($q) {
            $q->where('report_status', '1');
        });

        $projects = $query->paginate(10);

        foreach ($projects as $project) {
            $departmentIds = is_array($project->department_trade)
                ? $project->department_trade
                : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
            $departmentIds = array_filter($departmentIds, 'is_numeric');
            $project->department_names = McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();
            $project->full_address = implode(', ', array_filter([
                $project->project_address,
                $project->city,
                $project->state,
                $project->zip
            ]));
            $project->compliance_status = 'Violation';
            $project->violation_note = $project->physicalInspectionReportnew->violation_note ?? 'N/A';
        }

        return view('BuilderWebsiteDashboard.physicalinspection.complianceflag', compact('projects', 'departments'));
    }

    public function downloadReport($id)
    {
        try {
            // Get the project with all inspection reports
            $project = WebsiteBuilderProject::with(['inspector'])->findOrFail($id);

            // Fetch all inspection reports for the project
            $inspectionReports = PhysicalInspectionReport::where('project_id', $id)->get();

            // Check if there are any inspection reports
            if ($inspectionReports->isEmpty()) {
                return redirect()->back()->with('error', 'No inspection reports found for this project.');
            }

            // Prepare project data
            $departmentIds = is_array($project->department_trade)
                ? $project->department_trade
                : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
            $departmentIds = array_filter($departmentIds, 'is_numeric');
            $project->department_names = McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();

            $project->full_address = implode(', ', array_filter([
                $project->project_address,
                $project->city,
                $project->state,
                $project->zip
            ]));

            // Create a temporary directory for PDFs
            $tempDir = storage_path('app/temp/reports_' . $project->id);
            if (!file_exists($tempDir)) {
                mkdir($tempDir, 0755, true);
            }

            // Generate PDFs for each inspection report
            $pdfFiles = [];
            foreach ($inspectionReports as $index => $report) {
                // Get inspector names and departments
                $inspectorData = [];
                if (is_array($report->inspectors)) {
                    $inspectors = Inspector::with('role')->whereIn('id', $report->inspectors)->get();
                    $inspectorData = $inspectors->map(function ($inspector) {
                        return [
                            'name' => $inspector->first_name . ' ' . $inspector->last_name,
                            'department' => $inspector->role ? $inspector->role->name : 'N/A'
                        ];
                    })->toArray();
                }

                // Determine report status text
                $reportStatus = $report->report_status ?? null;
                switch ($reportStatus) {
                    case 0:
                        $statusText = 'Approval';
                        break;
                    case 1:
                        $statusText = 'Violation';
                        break;
                    case 2:
                        $statusText = 'Pending';
                        break;
                    default:
                        $statusText = 'N/A';
                        break;
                }

                // Generate PDF for this report
                $pdf = Pdf::loadView('BuilderWebsiteDashboard.physicalinspection.pdfreport', [
                    'project' => $project,
                    'report' => $report,
                    'inspectorData' => $inspectorData,
                    'statusText' => $statusText
                ]);

                // Generate filename for the PDF
                $filename = 'Inspection_Report_' . $project->project_name . '_Report' . ($index + 1) . '_' . date('Y-m-d') . '.pdf';
                $filename = preg_replace('/[^A-Za-z0-9_\-\.]/', '_', $filename);
                $pdfPath = $tempDir . '/' . $filename;

                // Save PDF to temporary directory
                $pdf->save($pdfPath);
                $pdfFiles[] = $pdfPath;
            }

            // Create ZIP archive
            $zipFileName = 'Physical_Inspection_Reports_' . $project->project_name . '_' . date('Y-m-d') . '.zip';
            $zipFileName = preg_replace('/[^A-Za-z0-9_\-\.]/', '_', $zipFileName);
            $zipFilePath = storage_path('app/temp/' . $zipFileName);

            $zip = new ZipArchive();
            if ($zip->open($zipFilePath, ZipArchive::CREATE | ZipArchive::OVERWRITE) === true) {
                foreach ($pdfFiles as $pdfFile) {
                    $zip->addFile($pdfFile, basename($pdfFile));
                }
                $zip->close();
            } else {
                // Clean up temporary PDFs
                foreach ($pdfFiles as $pdfFile) {
                    if (file_exists($pdfFile)) {
                        unlink($pdfFile);
                    }
                }
                if (file_exists($tempDir)) {
                    rmdir($tempDir);
                }
                return redirect()->back()->with('error', 'Failed to create ZIP archive.');
            }

            // Clean up temporary PDFs
            foreach ($pdfFiles as $pdfFile) {
                if (file_exists($pdfFile)) {
                    unlink($pdfFile);
                }
            }
            if (file_exists($tempDir)) {
                rmdir($tempDir);
            }

            // Return ZIP file for download
            return response()->download($zipFilePath, $zipFileName)->deleteFileAfterSend(true);

        } catch (\Exception $e) {
            // Clean up in case of error
            $tempDir = storage_path('app/temp/reports_' . $id);
            if (file_exists($tempDir)) {
                array_map('unlink', glob("$tempDir/*.*"));
                rmdir($tempDir);
            }
            return redirect()->back()->with('error', 'Failed to generate reports: ' . $e->getMessage());
        }
    }
}