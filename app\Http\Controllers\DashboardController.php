<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\McdStaff;
use App\Models\Inspector;
use App\Models\WebsiteBuilderProject;

class DashboardController extends Controller
{
   public function index()
{
    $totalInspectors = Inspector::count();
    $totalMcdStaffs = McdStaff::count();
    $totalUsers = $totalInspectors + $totalMcdStaffs;
    $totalprojects = WebsiteBuilderProject::count();

    return view('welcome', [
        'totalInspectors' => $totalInspectors,
        'totalMcdStaffs' => $totalMcdStaffs,
        'totalUsers' => $totalUsers,
        'totalprojects' => $totalprojects
    ]);
}

}
