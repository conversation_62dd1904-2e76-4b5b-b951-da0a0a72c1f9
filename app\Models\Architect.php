<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Architect extends Model
{
    use HasFactory;

    protected $fillable = [
        'name', 'email', 'password', 'contact_number', 'address', 'city', 'state', 'country',
        'company_name', 'license_number', 'specialization', 'experience_years',
        'portfolio_website', 'image', 'status', 'created_by','role_id','admin_id',
    ];
   
}
