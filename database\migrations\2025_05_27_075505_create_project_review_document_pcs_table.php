<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
   public function up()
    {
        Schema::create('project_review_documents_pc', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('project_id');
            $table->unsignedBigInteger('department_id');
            $table->string('review_step');
            $table->string('document_type');
            $table->text('remarks')->nullable();
            $table->unsignedBigInteger('uploaded_by');
            $table->string('uploaded_by_role');
            $table->timestamps();

            $table->foreign('project_id')->references('id')->on('website_builder_projects')->onDelete('cascade');
            $table->foreign('department_id')->references('id')->on('mcd_staff_roles')->onDelete('cascade');
            $table->foreign('uploaded_by')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::dropIfExists('project_review_documents_pc');
    }
};
