<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class GuidlinesResources extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // return parent::toArray($request);
        return[
            'type' => $this->type,
            'title'=>$this->title,

'description'=>$this->description,
'date_time'=>$this->date_time,
'created_at'=>$this->created_at,

        ];
    }
}
