<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;
use Exception;

class PDFProxyController extends Controller
{
    public function servePDF(Request $request)
    {
        $filePath = $request->query('path');
        $projectId = $request->query('projectId');
        
        // Validate the request
        if (!$filePath) {
            return response()->json(['error' => 'Missing file path parameter'], 400);
        }
        
        // Try to get file from local public directory
        $localPath = public_path($filePath);
        $realPath = realpath($localPath);
        $allowedDir = realpath(public_path());
        
        // Check if file exists locally and is within allowed directory
        if ($realPath && strpos($realPath, $allowedDir) === 0 && file_exists($realPath) && is_file($realPath)) {
            $fileContent = file_get_contents($realPath);
            $fileSize = filesize($realPath);
            $mimeType = mime_content_type($realPath) ?: 'application/pdf';
            
            // Set CORS headers and return file
            return response($fileContent, 200)
                ->header('Content-Type', $mimeType)
                ->header('Content-Length', $fileSize)
                ->header('Access-Control-Allow-Origin', '*')
                ->header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                ->header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
                ->header('Cache-Control', 'public, max-age=3600');
        }
        
        // File not found
        return response()->json(['error' => 'File not found'], 404);
    }
} 