@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <section class="section">
            <div class="section-body">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h4><i class="fas fa-bell"></i> Send Notification to Inspector</h4>
                            </div>

                            @if (session('success'))
                                <div class="alert alert-success">
                                    {{ session('success') }}
                                </div>
                            @endif

                            <form method="POST" action="{{ route('notifications.store') }}">
                                @csrf
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Inspector <span class="text-danger">*</span></label>
                                                <select name="inspector_id" id="inspector_id" class="form-control @error('inspector_id') is-invalid @enderror" required>
                                                    <option value="">Select Inspector</option>
                                                    @foreach ($inspectors as $inspector)
                                                        <option value="{{ $inspector->id }}" {{ old('inspector_id') == $inspector->id ? 'selected' : '' }}>
                                                            {{ $inspector->first_name }} {{ $inspector->last_name }} ({{ $inspector->email }})
                                                        </option>
                                                    @endforeach
                                                </select>
                                                @error('inspector_id')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Project <span class="text-danger">*</span></label>
                                                <select name="project_id" id="project_id" class="form-control @error('project_id') is-invalid @enderror" required>
                                                    <option value="">Select Project</option>
                                                    @foreach ($projects as $project)
                                                        <option value="{{ $project->id }}" {{ old('project_id') == $project->id ? 'selected' : '' }}>
                                                            {{ $project->project_name }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                @error('project_id')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Title <span class="text-danger">*</span></label>
                                                <input type="text" name="title" id="title" value="{{ old('title') }}" class="form-control @error('title') is-invalid @enderror" required>
                                                @error('title')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>

                                        {{-- <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Priority <span class="text-danger">*</span></label>
                                                <select name="priority" id="priority" class="form-control @error('priority') is-invalid @enderror" required>
                                                    <option value="">Select Priority</option>
                                                    <option value="low" {{ old('priority') == 'low' ? 'selected' : '' }}>Low</option>
                                                    <option value="medium" {{ old('priority') == 'medium' ? 'selected' : '' }}>Medium</option>
                                                    <option value="high" {{ old('priority') == 'high' ? 'selected' : '' }}>High</option>
                                                </select>
                                                @error('priority')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div> --}}
                                        <div class="col-md-6">
    <div class="form-group">
        <label>Tag <span class="text-danger">*</span></label>
        <select name="tag" id="tag" class="form-control @error('tag') is-invalid @enderror" required>
            <option value="">Select Tag</option>
            <option value="Approved" {{ old('tag') == 'Approved' ? 'selected' : '' }}>Approved</option>
            <option value="Violation" {{ old('tag') == 'Violation' ? 'selected' : '' }}>Violation</option>
            <option value="Message" {{ old('tag') == 'Message' ? 'selected' : '' }}>Message</option>
        </select>
        @error('tag')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>
</div>


                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Message <span class="text-danger">*</span></label>
                                                <textarea name="message" id="message" class="form-control @error('message') is-invalid @enderror" required>{{ old('message') }}</textarea>
                                                @error('message')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card-footer text-right">
                                    <button class="btn btn-primary" type="submit"><i class="fas fa-paper-plane"></i> Send Notification</button>
                                    <a href="{{ route('notifications.index') }}" class="btn btn-secondary"><i class="fas fa-times-circle"></i> Cancel</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
@endsection