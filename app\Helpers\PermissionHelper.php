<?php

use Illuminate\Support\Facades\Auth;

if (!function_exists('getCurrentPermissions')) {
    function getCurrentPermissions()
    {
        try {
            if (Auth::guard('web')->check()) {
                $user = Auth::guard('web')->user();
                return $user->roles->flatMap(function ($role) {
                    return $role->permissions->map(function ($permission) {
                        return [
                            'name' => $permission->name,
                            'can_read' => $permission->pivot->can_read ?? 0,
                            'can_write' => $permission->pivot->can_write ?? 0,
                        ];
                    });
                })->unique('name')->values()->toArray();
            }

            if (Auth::guard('staff_mcd')->check()) {
                $staff = Auth::guard('staff_mcd')->user();
                return $staff->roles->flatMap(function ($role) {
                    return $role->permissions->map(function ($permission) {
                        return [
                            'name' => $permission->name,
                            'can_read' => $permission->pivot->can_read ?? 0,
                            'can_write' => $permission->pivot->can_write ?? 0,
                        ];
                    });
                })->unique('name')->values()->toArray();
            }
        } catch (\Exception $e) {
            //\Log::error('Error in getCurrentPermissions: ' . $e->getMessage());
            return [];
        }

        return [];
    }
}