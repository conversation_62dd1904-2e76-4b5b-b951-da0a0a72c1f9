@extends('mcdpanel.layouts.master')

@section('content')
    <div class="main-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">Project Details: {{ $project->project_name }}</h4>
                        <div class="btn-group">
                            <a href="{{ route('deptreview.index') }}" class="btn btn-primary">Back to Departmental Review</a>
                        </div>
                    </div>
                    <div class="card-body">
                       
                        <!-- Review Process -->
                        <div class="row">
                            <div class="col-12">
                                <h5 class="mb-3">Review Process</h5>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead>
                                            <tr>
                                                <th>Department</th>
                                                <th>Review Step</th>
                                                <th>Status</th>
                                                <th>Reviewed By</th>
                                                <th>Review</th>
                                                
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {{-- @foreach ($departments as $department) --}}
                                           @foreach ($departments as $department)
                                           

                                                @foreach ($department['steps'] as $index => $step)
                                                    <tr>
                                                        @if ($index === 0)
                                                            <td rowspan="{{ count($department['steps']) }}">{{ $department['name'] }}</td>
                                                        @endif
                                                        <td>{{ $step['step'] }}</td>
                                                        <td>
                                                            <span class="badge 
                                                                @if ($step['status'] === 'Approved') bg-success 
                                                                @elseif ($step['status'] === 'Violation') bg-warning text-dark
                                                                @elseif ($step['status'] === 'Rejected') bg-danger 
                                                                @elseif ($step['status'] === 'Pending') bg-info 
                                                                @else bg-secondary 
                                                                @endif">
                                                                {{ $step['status'] }}
                                                            </span>
                                                        </td>
                                                       <td>
                                                        
                                                        @php $departmentId = $department['id']; @endphp
                                                                <a href="{{ route('review.history.page', [
                                                                    'project_id' => $project->id,
                                                                    'department_id' => $departmentId,
                                                                    'review_step' => $step['step']
                                                                ]) }}" class="btn btn-sm btn-outline-primary">
                                                                    View
                                                                </a>
                                                            
                                                        </td>
                                                    <td>
                                                        <a href="{{ route('process.complete', ['project' => $project->id, 'department' => $department['name'], 'step' => $index]) }}"
                                                            class="btn btn-sm btn-outline-primary">
                                                            Review
                                                        </a>

                                                    </td>
                                                        
                                                    </tr>
                                                @endforeach
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function saveReview(department, index) {
            alert(`Saving review for ${department}, Step ${index}`);
        }
    </script>
@endsection