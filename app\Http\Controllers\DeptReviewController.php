<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Storage;
use ZipArchive;
use App\Models\WebsiteBuilderProject;
use App\Models\ArchitectCategory;
use App\Models\BuilderSubCategory;
use Illuminate\Http\Request;
use App\Models\Inspector;
use Illuminate\Support\Facades\Auth;
use App\Models\McdStaffRole;
use App\Models\ProjectReviewDocumentPc;
use App\Models\ReviewProcess;
use App\Models\AnnotatedPdf;
use App\Models\ProjectReviewDocument;
use App\Models\McdStaff;
use App\Models\BuilderStaff;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;




class DeptReviewController extends Controller
{
   

// public function index(Request $request)
// {
//     $selectedTrade = $request->query('trade');
//     $query = WebsiteBuilderProject::query();

//     if (!empty($selectedTrade)) {
//         // Match as JSON string: "7", not 7
//         $query->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$selectedTrade)]);
//     }

//     $projects = $query->get();

//     // Attach department names
//     foreach ($projects as $project) {
//         $departmentIds = json_decode($project->department_trade, true);

//         if (!is_array($departmentIds)) {
//             $departmentIds = explode(',', $project->department_trade);
//         }

//         $departmentNames = \App\Models\McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();
//         $project->department_names = $departmentNames;
//     }

//     $departments = \App\Models\McdStaffRole::pluck('name', 'id')->toArray();

//     return view('BuilderWebsiteDashboard.DeptReview.index', compact('projects', 'departments'));
// }
// public function index(Request $request)
// {
//     //dd('hello');
//     $selectedTrade = $request->query('trade');
//     $query = WebsiteBuilderProject::query();

//     if (!empty($selectedTrade)) {
//         // Match as JSON string: "7", not 7
//         $query->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$selectedTrade)]);
//     }

//     $projects = $query->get();

//     // Attach department names and review status
//     foreach ($projects as $project) {
//         // Parse department_trade IDs
//         $departmentIds = is_array($project->department_trade)
//             ? $project->department_trade
//             : json_decode($project->department_trade, true);

//         if (!is_array($departmentIds)) {
//             $departmentIds = explode(',', $project->department_trade);
//         }

//         // Get department names
//         $departmentNames = \App\Models\McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();
//         $project->department_names = $departmentNames;

//         // Determine project status
//         $isAllApproved = true;
        
//         // Get ReviewProcesses for departments
//         $reviewProcesses = ReviewProcess::where('review_level', 'Department')->get();

//         foreach ($reviewProcesses as $process) {
//             $processDepartments = is_array($process->department)
//                 ? $process->department
//                 : json_decode($process->department, true);

//             $matchingDepartments = array_intersect($processDepartments, $departmentNames);

//             foreach ($matchingDepartments as $deptName) {
//                 $departmentModel = \App\Models\McdStaffRole::where('name', $deptName)->first();
//                 $department_id = $departmentModel?->id;

//                 foreach ($process->review_steps as $stepName) {
//                     $existingDocument = \App\Models\ProjectReviewDocument::where('project_id', $project->id)
//                         ->where('department_id', $department_id)
//                         ->where('review_step', $stepName)
//                         ->latest()
//                         ->first();

//                     // If any step is not approved (status != 2), mark as not fully approved
//                     if (!$existingDocument || $existingDocument->status != 2) {
//                         $isAllApproved = false;
//                         break 3; // Exit all loops
//                     }
//                 }
//             }
//         }

//         $project->review_status = $isAllApproved ? 'Approved' : 'Pending';
//         $project->badge_class = $isAllApproved ? 'success' : 'warning';
//     }

//     $departments = \App\Models\McdStaffRole::pluck('name', 'id')->toArray();

//     return view('BuilderWebsiteDashboard.DeptReview.index', compact('projects', 'departments'));
// }

public function index(Request $request)
{
    $selectedTrade = $request->query('trade');
    $query = WebsiteBuilderProject::query();

    if (!empty($selectedTrade)) {
        $query->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$selectedTrade)]);
    }

    $projects = $query->get();

    foreach ($projects as $project) {
        // Parse department_trade
        $departmentIds = is_array($project->department_trade)
            ? $project->department_trade
            : json_decode($project->department_trade, true);

        if (!is_array($departmentIds)) {
            $departmentIds = explode(',', $project->department_trade);
        }

        $departmentIds = array_filter($departmentIds, 'is_numeric');

        // Get department names
        $departmentNames = \App\Models\McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();
        $project->department_names = $departmentNames;

        $hasViolation = false;
        $allApproved = true;
        $hasAnyReview = false;

        foreach ($departmentIds as $deptId) {
            $reviewProcesses = \App\Models\ReviewProcess::where('review_level', 'Department')->get();

            foreach ($reviewProcesses as $process) {
                $processDepartments = is_array($process->department)
                    ? $process->department
                    : json_decode($process->department, true) ?? [$process->department];

                $deptModel = \App\Models\McdStaffRole::find($deptId);
                if (!$deptModel || !in_array($deptModel->name, $processDepartments)) {
                    continue;
                }

                foreach ($process->review_steps as $stepName) {
                    // Department Review
                    $latestDeptDoc = \App\Models\ProjectReviewDocument::where('project_id', $project->id)
                        ->where('department_id', $deptId)
                        ->whereRaw('TRIM(LOWER(review_step)) = ?', [strtolower(trim($stepName))])
                        ->latest()
                        ->first();

                    if ($latestDeptDoc) {
                        $hasAnyReview = true;

                        if ($latestDeptDoc->status == 1) { // Violation
                            $hasViolation = true;
                            break 3;
                        }

                        if ($latestDeptDoc->status != 2) { // Not approved
                            $allApproved = false;
                        }
                    } else {
                        $allApproved = false;
                    }

                    
                }
            }
        }

        // Set status based on logic
        if ($hasViolation) {
            $project->review_status = 'Violation';
            $project->badge_class = 'danger';
        } elseif ($allApproved && $hasAnyReview) {
            $project->review_status = 'Approved';
            $project->badge_class = 'success';
        } else {
            $project->review_status = 'Pending';
            $project->badge_class = 'warning';
        }

        // Debug (optional):
        // \Log::info("Project ID {$project->id}: review_status = {$project->review_status}");
    }

    $departments = \App\Models\McdStaffRole::pluck('name', 'id')->toArray();

    return view('BuilderWebsiteDashboard.DeptReview.index', compact('projects', 'departments'));
}



    // public function commissionreview(Request $request)
    // {
    //     // $inspectors = Inspector::all();

    //     // $projects = WebsiteBuilderProject::with('inspector')->get();
    //     return view('BuilderWebsiteDashboard.DeptReview.commissionreview');
    // }
//       public function commissionreview(Request $request)
// {
//     $selectedTrade = $request->query('trade');
//     $query = WebsiteBuilderProject::query();

//     if (!empty($selectedTrade)) {
//         // Match as JSON string: "7", not 7
//         $query->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$selectedTrade)]);
//     }

//     $projects = $query->get();

//     // Attach department names
//     foreach ($projects as $project) {
//         $departmentIds = json_decode($project->department_trade, true);

//         if (!is_array($departmentIds)) {
//             $departmentIds = explode(',', $project->department_trade);
//         }

//         $departmentNames = \App\Models\McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();
//         $project->department_names = $departmentNames;
//     }

//     $departments = \App\Models\McdStaffRole::pluck('name', 'id')->toArray();

//     return view('BuilderWebsiteDashboard.DeptReview.commissionreview', compact('projects', 'departments'));
// }
// public function commissionreview(Request $request)
// {
//     $selectedTrade = $request->query('trade');
//     $query = WebsiteBuilderProject::query();

//     if (!empty($selectedTrade)) {
//         // Match as JSON string: "7", not 7
//         $query->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$selectedTrade)]);
//     }

//     $projects = $query->get();

//     // Attach department names and review status
//     foreach ($projects as $project) {
//         // Parse department_trade IDs
//         $departmentIds = is_array($project->department_trade)
//             ? $project->department_trade
//             : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
//         $departmentIds = array_filter($departmentIds, 'is_numeric');

//         // Get department names
//         $departmentNames = \App\Models\McdStaffRole::whereIn('id', $departmentIds)
//             ->where('is_active', true)
//             ->pluck('name')
//             ->toArray();
//         $project->department_names = $departmentNames;

//         // Determine project status
//         $isAllApproved = true;
        
//         // Get ReviewProcesses for departments
//         $reviewProcesses = ReviewProcess::where('review_level', 'Department')->get();

//         foreach ($reviewProcesses as $process) {
//             $processDepartments = is_array($process->department)
//                 ? $process->department
//                 : json_decode($process->department, true) ?? [$process->department];

//             $matchingDepartments = array_intersect($processDepartments, $departmentNames);

//             foreach ($matchingDepartments as $deptName) {
//                 $departmentModel = \App\Models\McdStaffRole::where('name', $deptName)
//                     ->where('is_active', true)
//                     ->first();

//                 if (!$departmentModel) {
//                     continue;
//                 }

//                 $department_id = $departmentModel->id;

//                 foreach ($process->review_steps as $stepName) {
//                     // Check Department review status
//                     $deptDocument = \App\Models\ProjectReviewDocument::where('project_id', $project->id)
//                         ->where('department_id', $department_id)
//                         ->whereRaw('TRIM(LOWER(review_step)) = ?', [strtolower(trim($stepName))])
//                         ->latest()
//                         ->first();

//                     // Check Planning Commission review status
//                     $pcDocument = \App\Models\ProjectReviewDocumentPc::where('project_id', $project->id)
//                         ->where('department_id', $department_id)
//                         ->whereRaw('TRIM(LOWER(review_step)) = ?', [strtolower(trim($stepName))])
//                         ->latest()
//                         ->first();

//                     // If any step is not approved (status != 2) in either review, mark as not fully approved
//                     if (!$deptDocument || $deptDocument->status != 2 || !$pcDocument || $pcDocument->status != 2) {
//                         $isAllApproved = false;
//                         break 3; // Exit all loops
//                     }
//                 }
//             }
//         }

//         $project->review_status = $isAllApproved ? 'Approved' : 'Pending';
//         $project->badge_class = $isAllApproved ? 'success' : 'warning';
//     }

//     $departments = \App\Models\McdStaffRole::pluck('name', 'id')->toArray();

//     return view('BuilderWebsiteDashboard.DeptReview.commissionreview', compact('projects', 'departments'));
// }

public function commissionreview(Request $request)
{
    $selectedTrade = $request->query('trade');
    $query = WebsiteBuilderProject::query();

    if (!empty($selectedTrade)) {
        $query->whereRaw('JSON_CONTAINS(department_trade, ?)', [json_encode((string)$selectedTrade)]);
    }

    $projects = $query->get();

    foreach ($projects as $project) {
        $departmentIds = is_array($project->department_trade)
            ? $project->department_trade
            : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);

        $departmentIds = array_filter($departmentIds, 'is_numeric');

        // Get department names
        $departmentNames = \App\Models\McdStaffRole::whereIn('id', $departmentIds)
            ->where('is_active', true)
            ->pluck('name')
            ->toArray();
        $project->department_names = $departmentNames;

        $hasViolation = false;
        $allApproved = true;
        $hasAnyReview = false;

        $reviewProcesses = ReviewProcess::where('review_level', 'Department')->get();

        foreach ($reviewProcesses as $process) {
            $processDepartments = is_array($process->department)
                ? $process->department
                : json_decode($process->department, true) ?? [$process->department];

            $matchingDepartments = array_intersect($processDepartments, $departmentNames);

            foreach ($matchingDepartments as $deptName) {
                $departmentModel = \App\Models\McdStaffRole::where('name', $deptName)
                    ->where('is_active', true)
                    ->first();

                if (!$departmentModel) {
                    continue;
                }

                $department_id = $departmentModel->id;

                foreach ($process->review_steps as $stepName) {
                    $stepKey = strtolower(trim($stepName));

                    // Department review check
                    $deptDocument = \App\Models\ProjectReviewDocument::where('project_id', $project->id)
                        ->where('department_id', $department_id)
                        ->whereRaw('TRIM(LOWER(review_step)) = ?', [$stepKey])
                        ->latest()
                        ->first();

                    // PC review check
                    $pcDocument = \App\Models\ProjectReviewDocumentPc::where('project_id', $project->id)
                        ->where('department_id', $department_id)
                        ->whereRaw('TRIM(LOWER(review_step)) = ?', [$stepKey])
                        ->latest()
                        ->first();

                    // Mark as reviewed if any document found
                    if ($deptDocument || $pcDocument) {
                        $hasAnyReview = true;
                    }

                    // Violation check
                    if (
                        ($deptDocument && $deptDocument->status == 1) ||
                        ($pcDocument && $pcDocument->status == 1)
                    ) {
                        $hasViolation = true;
                        break 3; // Exit all loops
                    }

                    // Not approved check
                    if (
                        !$deptDocument || $deptDocument->status != 2 ||
                        !$pcDocument || $pcDocument->status != 2
                    ) {
                        $allApproved = false;
                    }
                }
            }
        }

        // Final review status
        if ($hasViolation) {
            $project->review_status = 'Violation';
            $project->badge_class = 'danger';
        } elseif ($allApproved && $hasAnyReview) {
            $project->review_status = 'Approved';
            $project->badge_class = 'success';
        } else {
            $project->review_status = 'Pending';
            $project->badge_class = 'warning';
        }
    }

    $departments = \App\Models\McdStaffRole::pluck('name', 'id')->toArray();

    return view('BuilderWebsiteDashboard.DeptReview.commissionreview', compact('projects', 'departments'));
}

// public function showreview($id)
// {
//     $project = WebsiteBuilderProject::findOrFail($id);

//     // Step 1: Parse department_trade IDs
//     $departmentIds = is_array($project->department_trade)
//         ? $project->department_trade
//         : json_decode($project->department_trade, true);

//     // Step 2: Get names of departments for this project
//     $projectDepartmentNames = \App\Models\McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();

//     // Step 3: Fetch ReviewProcesses with review_level = 'Department'
//     $reviewProcesses = ReviewProcess::where('review_level', 'Department')->get();

//     $departments = [];

//     foreach ($reviewProcesses as $process) {
//         // Decode department names in ReviewProcess
//         $processDepartments = is_array($process->department)
//             ? $process->department
//             : json_decode($process->department, true);

//         // Filter only departments that match current project
//         $matchingDepartments = array_intersect($processDepartments, $projectDepartmentNames);

//         foreach ($matchingDepartments as $deptName) {
//             $departmentModel = \App\Models\McdStaffRole::where('name', $deptName)->first();
//             $department_id = $departmentModel?->id;

//             $stepsWithStatus = [];

//             foreach ($process->review_steps as $stepName) {
//                 $existingDocument = \App\Models\ProjectReviewDocument::where('project_id', $project->id)
//                     ->where('department_id', $department_id)
//                     ->where('review_step', $stepName)
//                     ->latest()
//                     ->first();

//                 $statusLabel = match(optional($existingDocument)->status) {
//                     0 => 'Pending',
//                     1 => 'Violation',
//                     2 => 'Approved',
//                     3 => 'Rejected',
//                     default => 'Not Started',
//                 };

//                 $stepsWithStatus[] = [
//                     'step' => $stepName,
//                     'status' => $statusLabel,
//                 ];
//             }

//             $departments[] = [
//                 'name' => $deptName,
//                 'steps' => $stepsWithStatus,
//             ];
//         }
//     }

//     return view('BuilderWebsiteDashboard.DeptReview.showreview', compact('project', 'departments'));
// }

public function showreview($id)
{
    //dd("heelo");
    $project = WebsiteBuilderProject::findOrFail($id);

    // Step 1: Parse department_trade IDs
    $departmentIds = is_array($project->department_trade)
        ? $project->department_trade
        : json_decode($project->department_trade, true);

    // Step 2: Get names of departments for this project
    $projectDepartmentNames = \App\Models\McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();

    // Step 3: Fetch ReviewProcesses with review_level = 'Department'
    $reviewProcesses = ReviewProcess::where('review_level', 'Department')->get();

    $departments = [];

    // Step 4: Check if the user is logged in via staff_mcd or web guard
    if (Auth::guard('web')->check()) {
        //dd('web');
       $allowedDepartments = $projectDepartmentNames;
       
    } elseif (Auth::guard('staff_mcd')->check()) {
        // For admin (web guard), allow all departments
        //dd('staff_mcd');
        $userDepartment = Auth::guard('staff_mcd')->user()->department; // Assuming department is a field in the staff_mcd user model
        $allowedDepartments = is_array($userDepartment)
            ? $userDepartment
            : json_decode($userDepartment, true);
    }

    foreach ($reviewProcesses as $process) {
        // Decode department names in ReviewProcess
        $processDepartments = is_array($process->department)
            ? $process->department
            : json_decode($process->department, true);

        // Filter only departments that match current project and allowed departments
        $matchingDepartments = array_intersect($processDepartments, $projectDepartmentNames, $allowedDepartments);

        foreach ($matchingDepartments as $deptName) {
            $departmentModel = \App\Models\McdStaffRole::where('name', $deptName)->first();
            $department_id = $departmentModel?->id;

            $stepsWithStatus = [];

            foreach ($process->review_steps as $stepName) {
                $existingDocument = \App\Models\ProjectReviewDocument::where('project_id', $project->id)
                    ->where('department_id', $department_id)
                    ->where('review_step', $stepName)
                    ->latest()
                    ->first();

                $statusLabel = match (optional($existingDocument)->status) {
                    0 => 'Pending',
                    1 => 'Violation',
                    2 => 'Approved',
                    3 => 'Rejected',
                    default => 'Not Started',
                };

                $stepsWithStatus[] = [
                    'step' => $stepName,
                    'status' => $statusLabel,
                ];
            }

            $departments[] = [
                'id' => $department_id, // ✅ FIXED HERE
                'name' => $deptName,
                'steps' => $stepsWithStatus,
            ];
        }
    }

    return view('BuilderWebsiteDashboard.DeptReview.showreview', compact('project', 'departments'));
}

// public function process_complete($id, $department, $step)
// {
//     //dd('hh');
    
//     $project = WebsiteBuilderProject::findOrFail($id);

//     // Decode department IDs
//     $departmentIds = json_decode($project->department_trade, true);
//     if (!is_array($departmentIds)) {
//         $departmentIds = explode(',', $project->department_trade);
//     }
    
//     $departmentNames = \App\Models\McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();
//     $project->department_names = $departmentNames;

//     // Get department ID
//     $departmentModel = \App\Models\McdStaffRole::where('name', $department)->first();
//     $department_id = $departmentModel?->id;

//     // Fetch ReviewProcess
//     $reviewProcess = ReviewProcess::where('review_level', 'Department')
//         ->whereJsonContains('department', $department)
//         ->first();

//     // Safety check
//     if (!$reviewProcess || empty($reviewProcess->review_steps)) {
//         return abort(404, 'Review steps not found');
//     }

//     // Loop all steps and get remarks
//     $stepRemarks = [];

//     foreach ($reviewProcess->review_steps as $reviewStep) {
//         $doc = ProjectReviewDocument::where('project_id', $project->id)
//             ->where('department_id', $department_id)
//             ->whereRaw('TRIM(LOWER(review_step)) = ?', [strtolower(trim($reviewStep))])
//             ->latest()
//             ->first();

//         $stepRemarks[] = [
//             'ste p' => $reviewStep,
//             'remarks' => $doc->remarks ?? '',
//             'status' => match ($doc->status ?? null) {
//                 0 => 'Pending',
//                 1 => 'Violation',
//                 2 => 'Approved',
//                 3 => 'Rejected',
//                 default => 'Not Started',
//             },
//             'attachment' => $doc->file_path ?? null,
//         ];
//     }

//     // Dump result for debugging
//     // dd($stepRemarks);

//     // --- optional view return if dd is removed ---
//     $stepName = $reviewProcess->review_steps[$step] ?? null;
//     $existingDocument = ProjectReviewDocument::where('project_id', $project->id)
//         ->where('department_id', $department_id)
//         ->where('review_step', $stepName)
//         ->latest()
//         ->first();

//     // Query annotated PDFs for this project and department
//     $annotatedPdfs = \App\Models\AnnotatedPdf::where('project_id', $project->id)
//         ->where('department_id', $department_id)
//         ->where('review_step', $stepName)
//         ->orderBy('created_at', 'desc')
//         ->get();

//     // Resolve user names for each annotated PDF
//     foreach ($annotatedPdfs as $pdf) {
//         $pdf->uploader_name = 'Unknown User';
//         $pdf->uploader_role_display = $pdf->uploaded_by_role ?? 'Unknown Role';

//         if ($pdf->uploaded_by && $pdf->uploaded_by_role) {
//             // Try to get user name based on role
//             if (str_contains(strtolower($pdf->uploaded_by_role), 'mcd') ||
//                 str_contains(strtolower($pdf->uploaded_by_role), 'staff') ||
//                 str_contains(strtolower($pdf->uploaded_by_role), 'manager') ||
//                 str_contains(strtolower($pdf->uploaded_by_role), 'admin')) {

//                 $mcdUser = \App\Models\McdStaff::find($pdf->uploaded_by);
//                 if ($mcdUser) {
//                     $pdf->uploader_name = $mcdUser->staff_name ?? 'MCD Staff';
//                 }
//             } elseif (str_contains(strtolower($pdf->uploaded_by_role), 'builder')) {
//                 $builderUser = \App\Models\BuilderStaff::find($pdf->uploaded_by);
//                 if ($builderUser) {
//                     $pdf->uploader_name = $builderUser->staff_name ?? 'Builder Staff';
//                 }
//             } else {
//                 // Default to User model for admin/web users
//                 $webUser = \App\Models\User::find($pdf->uploaded_by);
//                 if ($webUser) {
//                     $pdf->uploader_name = $webUser->name ?? 'Admin User';
//                 }
//             }
//         }
//     }

//     return view('BuilderWebsiteDashboard.DeptReview.processcomplete', compact(
//         'project', 'department', 'step', 'stepName', 'department_id', 'existingDocument', 'annotatedPdfs'
//     ));
// }
public function process_complete($id, $department, $step)
{
    // Fetch the project
    $project = WebsiteBuilderProject::findOrFail($id);

    // Decode department IDs
    $departmentIds = json_decode($project->department_trade, true);
    if (!is_array($departmentIds)) {
        $departmentIds = explode(',', $project->department_trade);
    }
    
    $departmentNames = \App\Models\McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();
    $project->department_names = $departmentNames;

    // Get department ID
    $departmentModel = \App\Models\McdStaffRole::where('name', $department)->first();
    $department_id = $departmentModel?->id;

    // Fetch ReviewProcess
    $reviewProcess = ReviewProcess::where('review_level', 'Department')
        ->whereJsonContains('department', $department)
        ->first();

    // Safety check
    if (!$reviewProcess || empty($reviewProcess->review_steps)) {
        return abort(404, 'Review steps not found');
    }

    // Loop all steps and get remarks
    $stepRemarks = [];
    foreach ($reviewProcess->review_steps as $reviewStep) {
        $doc = ProjectReviewDocument::where('project_id', $project->id)
            ->where('department_id', $department_id)
            ->whereRaw('TRIM(LOWER(review_step)) = ?', [strtolower(trim($reviewStep))])
            ->latest()
            ->first();

        $stepRemarks[] = [
            'step' => $reviewStep,
            'remarks' => $doc->remarks ?? '',
            'status' => match ($doc->status ?? null) {
                0 => 'Pending',
                1 => 'Violation',
                2 => 'Approved',
                3 => 'Rejected',
                default => 'Not Started',
            },
            'attachment' => $doc->file_path ?? null,
        ];
    }

    // Fetch the current step name
    $stepName = $reviewProcess->review_steps[$step] ?? null;

    // Fetch existing document for the current step
    $existingDocument = ProjectReviewDocument::where('project_id', $project->id)
        ->where('department_id', $department_id)
        ->where('review_step', $stepName)
        ->latest()
        ->first();

    // Fetch annotated PDFs for this project and department
    $annotatedPdfs = \App\Models\AnnotatedPdf::where('project_id', $project->id)
        ->where('department_id', $department_id)
        ->where('review_step', $stepName)
        ->orderBy('created_at', 'desc')
        ->get();

    // Resolve user names for each annotated PDF
    foreach ($annotatedPdfs as $pdf) {
        $pdf->uploader_name = 'Unknown User';
        $pdf->uploader_role_display = $pdf->uploaded_by_role ?? 'Unknown Role';

        if ($pdf->uploaded_by && $pdf->uploaded_by_role) {
            if (str_contains(strtolower($pdf->uploaded_by_role), 'mcd') ||
                str_contains(strtolower($pdf->uploaded_by_role), 'staff') ||
                str_contains(strtolower($pdf->uploaded_by_role), 'manager') ||
                str_contains(strtolower($pdf->uploaded_by_role), 'admin')) {
                $mcdUser = \App\Models\McdStaff::find($pdf->uploaded_by);
                if ($mcdUser) {
                    $pdf->uploader_name = $mcdUser->staff_name ?? 'MCD Staff';
                }
            } elseif (str_contains(strtolower($pdf->uploaded_by_role), 'builder')) {
                $builderUser = \App\Models\BuilderStaff::find($pdf->uploaded_by);
                if ($builderUser) {
                    $pdf->uploader_name = $builderUser->staff_name ?? 'Builder Staff';
                }
            } else {
                $webUser = \App\Models\User::find($pdf->uploaded_by);
                if ($webUser) {
                    $pdf->uploader_name = $webUser->name ?? 'Admin User';
                }
            }
        }
    }

    // Fetch PDFs and images from project_attachment table
    $departmentPdfs = DB::table('project_attachment')
        ->where('project_id', $project->id)
        ->where('trade_id', $department_id)
        ->whereIn('file_type', [
            'full_plan_set',
            'site_plan',
            'structural_calculations',
            'engineering_reports',
            'energy_calculations',
            'special_certifications'
        ])
        ->select('file_path', 'file_type', 'created_at', 'updated_at')
        ->orderBy('created_at', 'desc')
        ->get();

    // Fetch project images (trade_id is NULL)
    $projectImages = DB::table('project_attachment')
        ->where('project_id', $project->id)
        ->whereNull('trade_id')
        ->where('file_type', 'project_image')
        ->select('file_path', 'file_type', 'created_at', 'updated_at')
        ->orderBy('created_at', 'desc')
        ->get();

    // Combine department PDFs and project images
    $allDocuments = $departmentPdfs->merge($projectImages);

    // Add display name for file types
    foreach ($allDocuments as $doc) {
        $doc->file_type_display = ucwords(str_replace('_', ' ', $doc->file_type));
    }

    return view('BuilderWebsiteDashboard.DeptReview.processcomplete', compact(
        'project',
        'department',
        'step',
        'stepName',
        'department_id',
        'existingDocument',
        'annotatedPdfs',
        'allDocuments' // Pass combined documents to the view
    ));
}
public function showReviewHistoryPage(Request $request)
{
    $request->validate([
        'project_id' => 'required|integer',
        'department_id' => 'required|integer',
        'review_step' => 'required|string',
    ]);

    $project = WebsiteBuilderProject::findOrFail($request->project_id);

    $departmentIds = is_array($project->department_trade)
        ? $project->department_trade
        : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
    $departmentIds = array_filter($departmentIds, 'is_numeric');
    $project->department_names = McdStaffRole::whereIn('id', $departmentIds)->pluck('name', 'id')->toArray();

    $project->full_address = implode(', ', array_filter([
        $project->project_address,
        $project->city,
        $project->state,
        $project->zip
    ]));

    // Load review history
    $reviews = ProjectReviewDocument::with('reviewer')
        ->where('project_id', $request->project_id)
        ->where('department_id', $request->department_id)
        ->where('review_step', $request->review_step)
        ->orderBy('created_at', 'desc')
        ->get();

    return view('BuilderWebsiteDashboard.DeptReview.review-history-page', compact('project', 'reviews'));
}



public function showReviewHistoryPagePc(Request $request)
{
    $request->validate([
        'project_id' => 'required|integer',
        'department_id' => 'required|integer',
        'review_step' => 'required|string',
    ]);

    $project = WebsiteBuilderProject::findOrFail($request->project_id);

    $departmentIds = is_array($project->department_trade)
        ? $project->department_trade
        : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
    $departmentIds = array_filter($departmentIds, 'is_numeric');
    $project->department_names = McdStaffRole::whereIn('id', $departmentIds)->pluck('name', 'id')->toArray();

    $project->full_address = implode(', ', array_filter([
        $project->project_address,
        $project->city,
        $project->state,
        $project->zip
    ]));

    // Load review history
    $reviews = ProjectReviewDocumentPc::with('reviewer')
        ->where('project_id', $request->project_id)
        ->where('department_id', $request->department_id)
        ->where('review_step', $request->review_step)
        ->orderBy('created_at', 'desc')
        ->get();

    return view('BuilderWebsiteDashboard.DeptReview.review-history-page-pc', compact('project', 'reviews'));
}



public function process_complete_pc($id, $department, $step)
{
    $project = WebsiteBuilderProject::findOrFail($id);

    $departmentIds = json_decode($project->department_trade, true);
    if (!is_array($departmentIds)) {
        $departmentIds = explode(',', $project->department_trade);
    }
    dd($departmentIds);
    $departmentNames = \App\Models\McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();
    $project->department_names = $departmentNames;

    $departmentModel = \App\Models\McdStaffRole::where('name', $department)->first();
    if (!$departmentModel) {
        // \Log::warning("Department not found: {$department}");
        return abort(404, 'Department not found');
    }
    $department_id = $departmentModel->id;

    $reviewProcess = ReviewProcess::where('review_level', 'PlanningCommission')
        ->whereJsonContains('department', $department)
        ->first();

    if (!$reviewProcess || empty($reviewProcess->review_steps)) {
        return abort(404, 'Review steps not found');
    }

    $stepRemarks = [];

    foreach ($reviewProcess->review_steps as $reviewStep) {
        $doc = \App\Models\ProjectReviewDocumentPc::where('project_id', $project->id)
            ->where('department_id', $department_id)
            ->whereRaw('TRIM(LOWER(review_step)) = ?', [strtolower(trim($reviewStep))])
            ->latest()
            ->first();

        $stepRemarks[] = [
            'step' => $reviewStep,
            'remarks' => $doc->remarks ?? '',
            'status' => match ($doc->status ?? null) {
                0 => 'Pending',
                1 => 'Violation',
                2 => 'Approved',
                3 => 'Rejected',
                default => 'Not Started',
            },
            'attachment' => $doc->file_path ?? null,
        ];
    }

    $stepName = $reviewProcess->review_steps[$step] ?? null;
    if (!$stepName) {
        // \Log::warning("Step not found for index: {$step}");
        return abort(404, 'Step not found');
    }

    $existingDocument = \App\Models\ProjectReviewDocumentPc::where('project_id', $project->id)
        ->where('department_id', $department_id)
        ->where('review_step', $stepName)
        ->latest()
        ->first();

    return view('BuilderWebsiteDashboard.DeptReview.processcompletepc', compact(
        'project', 'department', 'step', 'stepName', 'department_id', 'existingDocument', 'stepRemarks'
    ));
}



public function updateProjectStatus(Request $request, $id)
{
    $request->validate([
        'project_status' => 'required|in:0,1,2', // 0: Pending, 1: Violation, 2: Approved
    ]);

    $project = WebsiteBuilderProject::findOrFail($id);
    $project->project_status = $request->project_status;
    $project->save();

    return redirect()->back()->with('success', 'Project status updated successfully.');
}

// public function updateProStatus(Request $request)
// {
    
//     $request->validate([
//         'project_id' => 'required|integer|exists:website_builder_projects,id',
//         'department_id' => 'required|integer|exists:mcd_staff_roles,id',
//         'review_step' => 'required|string',
//         'status' => 'required|in:0,1,2,3',
//     ]);

//     $document = ProjectReviewDocument::where('project_id', $request->project_id)
//         ->where('department_id', $request->department_id)
//         ->where('review_step', $request->review_step)
//         ->latest() // Get the most recent one
//         ->first();

//     if ($document) {
//         $document->status = $request->status;
//         $document->save();
//         return back()->with('success', 'Document review status updated successfully.');
//     }

//     return back()->with('error', 'No document found to update.');
// }

public function updateProStatus(Request $request)
{
    if (Auth::guard('web')->check()) {
        abort(403, 'Admins are not allowed to update review status or remarks.');
    }
    $request->validate([
        'project_id' => 'required|integer|exists:website_builder_projects,id',
        'department_id' => 'required|integer|exists:mcd_staff_roles,id',
        'review_step' => 'required|string',
        'status' => 'required|in:0,1,2,3',
    ]);

    // Logged in MCD staff
    $user = Auth::guard('staff_mcd')->user();
    if (!$user) {
        return back()->with('error', 'Unauthorized: MCD staff login required.');
    }

    // Check if review document already exists
    $document = ProjectReviewDocument::where('project_id', $request->project_id)
        ->where('department_id', $request->department_id)
        ->where('review_step', $request->review_step)
        ->latest()
        ->first();

    if ($document) {
        // Update existing document
        $document->status = $request->status;
        $document->reviewed_by = $user->id;
        $document->save();

        return back()->with('success', 'Review status updated successfully.');
    } else {
        // Create new document
        ProjectReviewDocument::create([
            'project_id' => $request->project_id,
            'department_id' => $request->department_id,
            'review_step' => $request->review_step,
            'status' => $request->status,
            'reviewed_by' => $user->id,
            'uploaded_by' => $user->id,
            'uploaded_by_role' => 'staff_mcd', // or use role name dynamically
            'document_type' => 'status-only', // Or pass via request if needed
        ]);

        return back()->with('success', 'New review document created with status.');
    }
}







public function updateProStatusPc(Request $request)
{
    if (Auth::guard('web')->check()) {
        abort(403, 'Admins are not allowed to update review status or remarks.');
    }
    $request->validate([
        'project_id' => 'required|integer|exists:website_builder_projects,id',
        'department_id' => 'required|integer|exists:mcd_staff_roles,id',
        'review_step' => 'required|string',
        'status' => 'required|in:0,1,2,3',
    ]);

     // Logged in MCD staff
    $user = Auth::guard('staff_mcd')->user();
    if (!$user) {
        return back()->with('error', 'Unauthorized: MCD staff login required.');
    }


    $document =ProjectReviewDocumentPc::where('project_id', $request->project_id)
        ->where('department_id', $request->department_id)
        ->where('review_step', $request->review_step)
        ->latest() // Get the most recent one
        ->first();

    // if ($document) {
    //     $document->status = $request->status;
    //     $document->save();
    //     return back()->with('success', 'Document review status updated successfully.');
    // }
    if ($document) {
        // Update existing document
        $document->status = $request->status;
        $document->reviewed_by = $user->id;
        $document->save();
        return back()->with('success', 'Review status updated successfully.');
    }else {
        // Create new document
        ProjectReviewDocumentPc::create([
            'project_id' => $request->project_id,
            'department_id' => $request->department_id,
            'review_step' => $request->review_step,
            'status' => $request->status,
            'reviewed_by' => $user->id,
            'uploaded_by' => $user->id,
            'uploaded_by_role' => 'staff_mcd', // or use role name dynamically
            'document_type' => 'status-only', // Or pass via request if needed
        ]);

        return back()->with('success', 'New review document created with status.');
    }
}







public function uploadDocuments(Request $request, $projectId)
{
    $request->validate([
        'department_id' => 'required|integer|exists:mcd_staff_roles,id',
        'review_step' => 'required|string',
        'attachments.*' => 'nullable|file|mimes:jpg,jpeg,png,pdf|max:2048',
        'remarks.*' => 'nullable|string|max:255',
    ]);

    $user = Auth::user();

    foreach ($request->attachments as $docType => $file) {
        if ($file) {
            // Define destination path
            $destinationPath = public_path('project_docs');

            // Create folder if not exists
            if (!file_exists($destinationPath)) {
                mkdir($destinationPath, 0755, true);
            }

            // Create a unique filename
            $fileName = time() . '_' . $file->getClientOriginalName();

            // Move file to public/project_docs
            $file->move($destinationPath, $fileName);

            // Store file path relative to public folder
            $relativePath = 'project_docs/' . $fileName;

            // Save to DB
            ProjectReviewDocument::create([
                'project_id' => $projectId,
                'department_id' => $request->department_id,
                'review_step' => $request->review_step,
                'document_type' => $docType,
                'file_path' => $relativePath,
                'remarks' => $request->remarks[$docType] ?? null,
                'uploaded_by' => $user->id,
                'uploaded_by_role' => $user->role_id === null ? $user->name : ($user->role->name ?? 'unknown'),
            ]);
        }
    }

    return back()->with('success', 'Review documents uploaded successfully.');
}



 

public function showprojectdocument($id, $department = null, $step_index = null)
{
    //dd('hh');
    $project = WebsiteBuilderProject::with([
        'architectCategory',
        'builderSubCategory',
        'permitNumbers',
        'inspector'
    ])->findOrFail($id);

    // Fetch review processes for Planning Commission
    $reviewProcesses = ReviewProcess::where('review_level', 'Planning Commission')->get();
// dd($reviewProcesses );
    // Prepare departments array
    $departments = [];
    foreach ($reviewProcesses as $process) {
        $departmentNames = \App\Models\ReviewProcess::whereIn('department', $process->department)->pluck('department')->toArray();
        foreach ($departmentNames as $deptName) {
            $steps = array_map(function ($step, $index) use ($deptName, $department, $step_index) {
                // Initialize review data
                $reviewData = [
                    'step' => $step,
                    'status' => 'Not Started',
                    'remarks' => '',
                    'attachment' => null,
                ];

                // If this is the selected department and step, fetch existing review data
                if ($deptName === $department && $index == $step_index) {
                    $review = \App\Models\ReviewProcess::where([
                        'project_id' => $id,
                        'department' => $deptName,
                        'step_index' => $index,
                    ])->first();
                    if ($review) {
                        $reviewData['status'] = $review->status;
                        $reviewData['remarks'] = $review->remarks;
                        $reviewData['attachment'] = $review->attachment;
                    }
                }

                return $reviewData;
            }, $process->review_steps, array_keys($process->review_steps));

            // Only include the department if it matches the selected department or no department is specified
            if (is_null($department) || $deptName === $department) {
                $departments[] = [
                    'name' => $deptName,
                    'steps' => $steps,
                ];
            }
        }
    }
$existingRemarks = ProjectReviewDocument::where('project_id', $id)
    ->where('review_step', 'Document Review')
    ->pluck('remarks', 'document_type')
    ->toArray();
    // If department and step_index are provided, filter to show only the specific step
    $selectedReview = null;
    if ($department && !is_null($step_index)) {
        foreach ($departments as $dept) {
            if ($dept['name'] === $department) {
                $selectedReview = [
                    'department' => $dept['name'],
                    'step' => $dept['steps'][$step_index] ?? null,
                ];
                break;
            }
        }
    }

    return view('BuilderWebsiteDashboard.DeptReview.projectdocument', compact('project', 'departments', 'selectedReview'));
}
// public function showprojectdocumentpc($id, $department = null, $step_index = null)
// {
//     //dd('mm');
//     $project = WebsiteBuilderProject::with([
//         'architectCategory',
//         'builderSubCategory',
//         'permitNumbers',
//         'inspector'
//     ])->findOrFail($id);

//     // Handle department_trade (string or array)
//     $departmentIds = is_string($project->department_trade)
//         ? json_decode($project->department_trade, true) ?? explode(',', $project->department_trade)
//         : (is_array($project->department_trade) ? $project->department_trade : explode(',', $project->department_trade));

//     // Ensure $departmentIds is an array
//     $departmentIds = array_filter($departmentIds, 'is_numeric'); // Filter to keep only numeric IDs
//     $departmentNames = McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();
//     $project->department_names = $departmentNames;

//     // Get department ID
//     $department_id = null;
//     if ($department) {
//         $departmentModel = McdStaffRole::where('name', $department)->first();
//         $department_id = $departmentModel?->id;
//     }

//     // Fetch review processes for Department
//     $reviewProcesses = ReviewProcess::where('review_level', 'Department')->get();

//     // Prepare departments array
//     $departments = [];
//     $stepName = 'Document Review'; // Default step name
//     foreach ($reviewProcesses as $process) {
//         $departmentNames = is_string($process->department)
//             ? json_decode($process->department, true) ?? [$process->department]
//             : (is_array($process->department) ? $process->department : [$process->department]);

//         foreach ($departmentNames as $deptName) {
//             $steps = array_map(function ($step, $index) use ($deptName, $department, $step_index, $id) {
//                 $reviewData = [
//                     'step' => $step,
//                     'status' => 'Not Started',
//                     'remarks' => '',
//                     'attachment' => null,
//                 ];

//                 if ($deptName === $department && $index == $step_index) {
//                     $departmentModel = McdStaffRole::where('name', $deptName)->first();
//                     $review = ProjectReviewDocumentPc::where('project_id', $id)
//                         ->where('department_id', $departmentModel?->id)
//                         ->whereRaw('TRIM(LOWER(review_step)) = ?', [strtolower(trim($step))])
//                         ->latest()
//                         ->first();
//                     if ($review) {
//                         $reviewData['status'] = match ($review->status ?? null) {
//                             0 => 'Pending',
//                             1 => 'Violation',
//                             2 => 'Approved',
//                             3 => 'Rejected',
//                             default => 'Not Started',
//                         };
//                         $reviewData['remarks'] = $review->remarks;
//                         $reviewData['attachment'] = $review->file_path ?? null;
//                     }
//                 }

//                 return $reviewData;
//             }, $process->review_steps, array_keys($process->review_steps));

//             if (is_null($department) || $deptName === $department) {
//                 $departments[] = [
//                     'name' => $deptName,
//                     'steps' => $steps,
//                 ];
//             }
//         }
//     }

//     // Get step name and existing document
//     $existingDocument = null;
//     if ($department && is_numeric($step_index) && $step_index >= 0) {
//         $reviewProcess = ReviewProcess::where('review_level', 'Department')
//             ->whereJsonContains('department', $department)
//             ->first();
//         if ($reviewProcess && isset($reviewProcess->review_steps[$step_index])) {
//             $stepName = $reviewProcess->review_steps[$step_index];
//             $existingDocument = ProjectReviewDocumentPc::where('project_id', $id)
//                 ->where('department_id', $department_id)
//                 ->whereRaw('TRIM(LOWER(review_step)) = ?', [strtolower(trim($stepName))])
//                 ->latest()
//                 ->first();
//         } else {
//             \Log::warning("Invalid step_index or review process not found for department: {$department}, step_index: {$step_index}");
//         }
//     }

//     $existingRemarks = ProjectReviewDocumentPc::where('project_id', $id)
//         ->whereRaw('TRIM(LOWER(review_step)) = ?', [strtolower(trim($stepName))])
//         ->pluck('remarks', 'document_type')
//         ->toArray();

//     $selectedReview = null;
//     if ($department && is_numeric($step_index) && $step_index >= 0) {
//         foreach ($departments as $dept) {
//             if ($dept['name'] === $department && isset($dept['steps'][$step_index])) {
//                 $selectedReview = [
//                     'department' => $dept['name'],
//                     'step' => $dept['steps'][$step_index],
//                 ];
//                 break;
//             }
//         }
//     }

//     // Query annotated PDFs for this project and department
//     $annotatedPdfs = \App\Models\AnnotatedPdf::where('project_id', $project->id)
//         ->where('department_id', $department_id)
//         ->orderBy('created_at', 'desc')
//         ->get();

//     // Resolve user names for each annotated PDF
//     foreach ($annotatedPdfs as $pdf) {
//         $pdf->uploader_name = 'Unknown User';
//         $pdf->uploader_role_display = $pdf->uploaded_by_role ?? 'Unknown Role';

//         if ($pdf->uploaded_by && $pdf->uploaded_by_role) {
//             // Try to get user name based on role
//             if (str_contains(strtolower($pdf->uploaded_by_role), 'mcd') ||
//                 str_contains(strtolower($pdf->uploaded_by_role), 'staff') ||
//                 str_contains(strtolower($pdf->uploaded_by_role), 'manager') ||
//                 str_contains(strtolower($pdf->uploaded_by_role), 'admin')) {

//                 $mcdUser = \App\Models\McdStaff::find($pdf->uploaded_by);
//                 if ($mcdUser) {
//                     $pdf->uploader_name = $mcdUser->staff_name ?? 'MCD Staff';
//                 }
//             } elseif (str_contains(strtolower($pdf->uploaded_by_role), 'builder')) {
//                 $builderUser = \App\Models\BuilderStaff::find($pdf->uploaded_by);
//                 if ($builderUser) {
//                     $pdf->uploader_name = $builderUser->staff_name ?? 'Builder Staff';
//                 }
//             } else {
//                 // Default to User model for admin/web users
//                 $webUser = \App\Models\User::find($pdf->uploaded_by);
//                 if ($webUser) {
//                     $pdf->uploader_name = $webUser->name ?? 'Admin User';
//                 }
//             }
//         }
//     }

//     return view('BuilderWebsiteDashboard.DeptReview.processcompletepc', compact(
//         'project',
//         'departments',
//         'selectedReview',
//         'existingRemarks',
//         'department',
//         'step_index',
//         'stepName',
//         'department_id',
//         'existingDocument',
//         'annotatedPdfs'
//     ));
// }
public function showprojectdocumentpc($id, $department = null, $step_index = null)
{
    // Fetch the project with related data
    $project = WebsiteBuilderProject::with([
        'architectCategory',
        'builderSubCategory',
        'permitNumbers',
        'inspector'
    ])->findOrFail($id);

    // Handle department_trade (string or array)
    $departmentIds = is_string($project->department_trade)
        ? json_decode($project->department_trade, true) ?? explode(',', $project->department_trade)
        : (is_array($project->department_trade) ? $project->department_trade : explode(',', $project->department_trade));

    // Ensure $departmentIds is an array
    $departmentIds = array_filter($departmentIds, 'is_numeric'); // Filter to keep only numeric IDs
    $departmentNames = McdStaffRole::whereIn('id', $departmentIds)->pluck('name')->toArray();
    $project->department_names = $departmentNames;

    // Get department ID
    $department_id = null;
    if ($department) {
        $departmentModel = McdStaffRole::where('name', $department)->first();
        $department_id = $departmentModel?->id;
    }

    // Fetch review processes for Department
    $reviewProcesses = ReviewProcess::where('review_level', 'Department')->get();

    // Prepare departments array
    $departments = [];
    $stepName = 'Document Review'; // Default step name
    foreach ($reviewProcesses as $process) {
        $departmentNames = is_string($process->department)
            ? json_decode($process->department, true) ?? [$process->department]
            : (is_array($process->department) ? $process->department : [$process->department]);

        foreach ($departmentNames as $deptName) {
            $steps = array_map(function ($step, $index) use ($deptName, $department, $step_index, $id) {
                $reviewData = [
                    'step' => $step,
                    'status' => 'Not Started',
                    'remarks' => '',
                    'attachment' => null,
                ];

                if ($deptName === $department && $index == $step_index) {
                    $departmentModel = McdStaffRole::where('name', $deptName)->first();
                    $review = ProjectReviewDocumentPc::where('project_id', $id)
                        ->where('department_id', $departmentModel?->id)
                        ->whereRaw('TRIM(LOWER(review_step)) = ?', [strtolower(trim($step))])
                        ->latest()
                        ->first();
                    if ($review) {
                        $reviewData['status'] = match ($review->status ?? null) {
                            0 => 'Pending',
                            1 => 'Violation',
                            2 => 'Approved',
                            3 => 'Rejected',
                            default => 'Not Started',
                        };
                        $reviewData['remarks'] = $review->remarks;
                        $reviewData['attachment'] = $review->file_path ?? null;
                    }
                }

                return $reviewData;
            }, $process->review_steps, array_keys($process->review_steps));

            if (is_null($department) || $deptName === $department) {
                $departments[] = [
                    'name' => $deptName,
                    'steps' => $steps,
                ];
            }
        }
    }

    // Get step name and existing document
    $existingDocument = null;
    if ($department && is_numeric($step_index) && $step_index >= 0) {
        $reviewProcess = ReviewProcess::where('review_level', 'Department')
            ->whereJsonContains('department', $department)
            ->first();
        if ($reviewProcess && isset($reviewProcess->review_steps[$step_index])) {
            $stepName = $reviewProcess->review_steps[$step_index];
            $existingDocument = ProjectReviewDocumentPc::where('project_id', $id)
                ->where('department_id', $department_id)
                ->whereRaw('TRIM(LOWER(review_step)) = ?', [strtolower(trim($stepName))])
                ->latest()
                ->first();
        } else {
            // \Log::warning("Invalid step_index or review process not found for department: {$department}, step_index: {$step_index}");
        }
    }

    $existingRemarks = ProjectReviewDocumentPc::where('project_id', $id)
        ->whereRaw('TRIM(LOWER(review_step)) = ?', [strtolower(trim($stepName))])
        ->pluck('remarks', 'document_type')
        ->toArray();

    $selectedReview = null;
    if ($department && is_numeric($step_index) && $step_index >= 0) {
        foreach ($departments as $dept) {
            if ($dept['name'] === $department && isset($dept['steps'][$step_index])) {
                $selectedReview = [
                    'department' => $dept['name'],
                    'step' => $dept['steps'][$step_index],
                ];
                break;
            }
        }
    }

    // Query annotated PDFs for this project and department
    $annotatedPdfs = \App\Models\AnnotatedPdf::where('project_id', $project->id)
        ->where('department_id', $department_id)
        ->orderBy('created_at', 'desc')
        ->get();

    // Resolve user names for each annotated PDF
    foreach ($annotatedPdfs as $pdf) {
        $pdf->uploader_name = 'Unknown User';
        $pdf->uploader_role_display = $pdf->uploaded_by_role ?? 'Unknown Role';

        if ($pdf->uploaded_by && $pdf->uploaded_by_role) {
            if (str_contains(strtolower($pdf->uploaded_by_role), 'mcd') ||
                str_contains(strtolower($pdf->uploaded_by_role), 'staff') ||
                str_contains(strtolower($pdf->uploaded_by_role), 'manager') ||
                str_contains(strtolower($pdf->uploaded_by_role), 'admin')) {
                $mcdUser = \App\Models\McdStaff::find($pdf->uploaded_by);
                if ($mcdUser) {
                    $pdf->uploader_name = $mcdUser->staff_name ?? 'MCD Staff';
                }
            } elseif (str_contains(strtolower($pdf->uploaded_by_role), 'builder')) {
                $builderUser = \App\Models\BuilderStaff::find($pdf->uploaded_by);
                if ($builderUser) {
                    $pdf->uploader_name = $builderUser->staff_name ?? 'Builder Staff';
                }
            } else {
                $webUser = \App\Models\User::find($pdf->uploaded_by);
                if ($webUser) {
                    $pdf->uploader_name = $webUser->name ?? 'Admin User';
                }
            }
        }
    }

    // Fetch PDFs and images from project_attachment table
    $departmentPdfs = DB::table('project_attachment')
        ->where('project_id', $project->id)
        ->where('trade_id', $department_id)
        ->whereIn('file_type', [
            'full_plan_set',
            'site_plan',
            'structural_calculations',
            'engineering_reports',
            'energy_calculations',
            'special_certifications'
        ])
        ->select('file_path', 'file_type', 'created_at', 'updated_at')
        ->orderBy('created_at', 'desc')
        ->get();

    // Fetch project images (trade_id is NULL)
    $projectImages = DB::table('project_attachment')
        ->where('project_id', $project->id)
        ->whereNull('trade_id')
        ->where('file_type', 'project_image')
        ->select('file_path', 'file_type', 'created_at', 'updated_at')
        ->orderBy('created_at', 'desc')
        ->get();

    // Combine department PDFs and project images
    $allDocuments = $departmentPdfs->merge($projectImages);

    // Add display name for file types
    foreach ($allDocuments as $doc) {
        $doc->file_type_display = ucwords(str_replace('_', ' ', $doc->file_type));
    }

    return view('BuilderWebsiteDashboard.DeptReview.processcompletepc', compact(
        'project',
        'departments',
        'selectedReview',
        'existingRemarks',
        'department',
        'step_index',
        'stepName',
        'department_id',
        'existingDocument',
        'annotatedPdfs',
        'allDocuments' // Pass combined documents to the view
    ));
}
//new
public function showReviewPlanning($id)
{
    //dd('hwello');
    $project = WebsiteBuilderProject::findOrFail($id);

    // Step 1: Parse department_trade IDs
    $departmentIds = is_array($project->department_trade)
        ? $project->department_trade
        : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
    $departmentIds = array_filter($departmentIds, 'is_numeric');

    // Step 2: Get department names for the project
    $projectDepartmentNames = \App\Models\McdStaffRole::whereIn('id', $departmentIds)
        ->where('is_active', true)
        ->pluck('name')
        ->toArray();

    // Step 3: Determine allowed departments based on user type
    if (Auth::guard('web')->check()) {
        // Admin can view all project-related departments
        $allowedDepartments = $projectDepartmentNames;
    } elseif (Auth::guard('staff_mcd')->check()) {
        // Staff can only view their assigned departments
        $userDepartment = Auth::guard('staff_mcd')->user()->department;
        $allowedDepartments = is_array($userDepartment)
            ? $userDepartment
            : json_decode($userDepartment, true) ?? [$userDepartment];
    } else {
        abort(403, 'Unauthorized access.');
    }

    // Step 4: Fetch all ReviewProcesses for Department level
    $reviewProcesses = ReviewProcess::where('review_level', 'Department')->get();

    // Step 5: Collect all departments used in processes (optional for view dropdown)
    $allDepartments = [];
    foreach ($reviewProcesses as $process) {
        $processDepartments = is_array($process->department)
            ? $process->department
            : json_decode($process->department, true) ?? [$process->department];

        $allDepartments = array_merge($allDepartments, $processDepartments);
    }
    $allDepartments = array_unique($allDepartments);

    // Step 6: Filter and collect review steps for each department
    $departments = [];

    foreach ($reviewProcesses as $process) {
        $processDepartments = is_array($process->department)
            ? $process->department
            : json_decode($process->department, true) ?? [$process->department];

        $matchingDepartments = array_intersect($processDepartments, $projectDepartmentNames, $allowedDepartments);

        foreach ($matchingDepartments as $deptName) {
            $departmentModel = \App\Models\McdStaffRole::where('name', $deptName)
                ->where('is_active', true)
                ->first();

            if (!$departmentModel) {
                // \Log::warning("Department not found or inactive: {$deptName}");
                continue;
            }

            $department_id = $departmentModel->id;
            $stepsWithStatus = [];

            foreach ($process->review_steps as $index => $stepName) {
                // Department review data (ProjectReviewDocument)
                $deptDocuments = ProjectReviewDocument::where('project_id', $project->id)
                    ->where('department_id', $department_id)
                    ->whereRaw('TRIM(LOWER(review_step)) = ?', [strtolower(trim($stepName))])
                    ->latest()
                    ->get();

                $deptRemarks = $deptDocuments->pluck('remarks')->filter()->implode('; ');
                $latestDeptDocument = $deptDocuments->first();

                // PC review data (ProjectReviewDocumentPc)
                $pcDocuments = ProjectReviewDocumentPc::where('project_id', $project->id)
                    ->where('department_id', $department_id)
                    ->whereRaw('TRIM(LOWER(review_step)) = ?', [strtolower(trim($stepName))])
                    ->latest()
                    ->get();

                $pcRemarks = $pcDocuments->pluck('remarks')->filter()->implode('; ');
                $latestPcDocument = $pcDocuments->first();

                $stepsWithStatus[] = [
                    'step' => $stepName,
                    'remarks' => $deptRemarks ?: '-',
                    'status' => $latestDeptDocument ? match ($latestDeptDocument->status) {
                        0 => 'Pending',
                        1 => 'Violation',
                        2 => 'Approved',
                        3 => 'Rejected',
                        default => 'Not Started',
                    } : 'Not Started',
                    'attachment' => $latestDeptDocument?->file_path,
                    'pc_status' => $latestPcDocument ? match ($latestPcDocument->status) {
                        0 => 'Pending',
                        1 => 'Violation',
                        2 => 'Approved',
                        3 => 'Rejected',
                        default => 'Not Started',
                    } : 'Not Started',
                    'pc_remarks' => $pcRemarks ?: '-',
                    'pc_attachment' => $latestPcDocument?->file_path,
                ];
            }

            $departments[] = [
                'id' => $department_id,
                'name' => $deptName,
                'steps' => $stepsWithStatus,
            ];
        }
    }

    return view('BuilderWebsiteDashboard.DeptReview.commissionreviewshow', compact('project', 'departments', 'allDepartments'));
}




    // Add a new method to handle department assignment
    // public function assignDepartment(Request $request, $projectId)
    // {
    //     $request->validate([
    //         'department_id' => 'required|integer|exists:mcd_staff_roles,id',
    //         'remark' => 'nullable|string|max:255',
    //     ]);

    //     $project = WebsiteBuilderProject::findOrFail($projectId);
    //     $departmentIds = is_array($project->department_trade)
    //         ? $project->department_trade
    //         : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
    //     $departmentIds = array_filter($departmentIds, 'is_numeric');

    //     // Add the new department if not already present
    //     if (!in_array($request->department_id, $departmentIds)) {
    //         $departmentIds[] = $request->department_id;
    //         $project->department_trade = json_encode($departmentIds);
    //         $project->save();
    //     }

    //     // Optionally save a remark
    //     if ($request->filled('remark')) {
    //         ProjectReviewDocument::create([
    //             'project_id' => $projectId,
    //             'department_id' => $request->department_id,
    //             'review_step' => 'Department Assignment',
    //             'document_type' => 'assignment',
    //             'remarks' => $request->remark,
    //             'uploaded_by' => Auth::user()->id,
    //             'uploaded_by_role' => Auth::user()->role->name ?? 'unknown',
    //         ]);
    //     }

    //     return response()->json(['success' => 'Department assigned successfully.']);
    // }
     public function assignDepartment(Request $request, $projectId)
    {
        $request->validate([
            'department_id' => 'required|integer|exists:mcd_staff_roles,id',
            'remark' => 'nullable|string|max:255',
        ]);

        $project = WebsiteBuilderProject::findOrFail($projectId);
        $departmentIds = is_array($project->department_trade)
            ? $project->department_trade
            : json_decode($project->department_trade, true) ?? explode(',', $project->department_trade);
        $departmentIds = array_filter($departmentIds, 'is_numeric');

        // Add the new department if not already present
        if (!in_array($request->department_id, $departmentIds)) {
            $departmentIds[] = $request->department_id;
            $project->department_trade = json_encode($departmentIds);
            $project->save();
        }

        // Optionally save a remark
        if ($request->filled('remark')) {
            ProjectReviewDocument::create([
                'project_id' => $projectId,
                'department_id' => $request->department_id,
                'review_step' => 'Department Assignment',
                'document_type' => 'assignment',
                'remarks' => $request->remark,
                'uploaded_by' => Auth::user()->id,
                'uploaded_by_role' => Auth::user()->role->name ?? 'unknown',
            ]);
        }

        // Redirect to the showReviewPlanning route
        return redirect()->route('planningcommission', ['id' => $projectId])
            ->with('success', 'Department assigned successfully.');
    }
public function saveAnnotations(Request $request, $projectId)
{
    $request->validate([
        'annotations' => 'required|string',
        'pdf' => 'required|file|mimes:pdf|max:50000', // 50MB max
        'department_id' => 'required|integer',
        'review_step' => 'required|string',
    ]);

    // Save PDF file
    $pdfFileName = 'pdf_' . $projectId . '_' . time() . '.pdf';
    $pdfPath = $request->file('pdf')->storeAs('pdfs', $pdfFileName, 'public');

    // Save annotations
    $annotationsFileName = 'annotations_' . $projectId . '_' . time() . '.json';
    Storage::disk('public')->put('annotations/' . $annotationsFileName, $request->annotations);

    // Create document record
    $document = ProjectReviewDocument::create([
        'project_id' => $projectId,
        'department_id' => $request->department_id,
        'review_step' => $request->review_step,
        'document_type' => 'annotated_pdf',
        'file_path' => $pdfPath,
        'annotations_path' => 'annotations/' . $annotationsFileName,
        'remarks' => 'PDF with annotations saved',
        'uploaded_by' => auth()->id(),
        'uploaded_by_role' => auth()->user()->role->name ?? 'unknown',
    ]);

    return response()->json([
        'message' => 'Annotations and PDF saved successfully',
        'fileId' => $document->id
    ]);
}
public function addRemark(Request $request, $projectId)
{
    if (Auth::guard('web')->check()) {
    abort(403, 'Admins cannot modify remarks.');
}

    $request->validate([
        'department_id' => 'required|integer|exists:mcd_staff_roles,id',
        'review_step' => 'required|string',
        'document_type' => 'required|string',
        'remark' => 'required|string|max:255',
     
    ]);
 
    $user = Auth::user();
 
    // Save remark to DB
    ProjectReviewDocument::create([
        'project_id' => $projectId,
        'department_id' => $request->department_id,
        'review_step' => $request->review_step,
        'document_type' => $request->document_type,
        'remarks' => $request->remark,
        'uploaded_by' => $user->id,
        'uploaded_by_role' => $user->role_id === null ? $user->name : ($user->role->name ?? 'unknown'),
    ]);
    return back()->with('success', 'Remark added and project status updated successfully.');
}
public function addRemarkPc(Request $request, $projectId)
    {
        if (Auth::guard('web')->check()) {
         abort(403, 'Admins cannot modify remarks.');
        }

        $request->validate([
            'department_id' => 'required|integer|exists:mcd_staff_roles,id',
            'review_step' => 'required|string',
            'document_type' => 'required|string',
            'remark' => 'required|string|max:255',
        ]);

        $user = Auth::user();

        ProjectReviewDocumentPc::create([
            'project_id' => $projectId,
            'department_id' => $request->department_id,
            'review_step' => $request->review_step,
            'document_type' => $request->document_type,
            'remarks' => $request->remark,
            'uploaded_by' => $user->id,
            'uploaded_by_role' => $user->role_id === null ? $user->name : ($user->role->name ?? 'unknown'),
        ]);

        return back()->with('success', 'Remark added successfully.');
    }


}