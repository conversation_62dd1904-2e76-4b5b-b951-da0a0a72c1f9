import { Canvas, PencilBrush, Image as FabricImage, IText, Rect, Path } from 'fabric'

export class AnnotationTools {
  constructor() {
    this.currentTool = null
    this.canvases = new Map()
    this.activeCanvas = null
    this.brushThickness = 2 // Default brush thickness
    this.brushColor = '#000000' // Default brush color
    this.textColor = '#000000' // Default text color
    this.textSize = 20 // Default text size
    this.undoStack = []
    this.redoStack = []
    this.isRestoring = false
    this.stateChangeListeners = new Set() // Add state change listeners
    this.currentZoom = 100 // Default zoom level
    this.currentArrowType = 'right' // Default arrow direction
  }

  addCanvas(pageNumber, pdfCanvas) {
    // In Fabric 5.5.0, we need to create a canvas element first
    const canvasEl = document.createElement('canvas')
    canvasEl.width = pdfCanvas.width
    canvasEl.height = pdfCanvas.height
    
    const fabricCanvas = new Canvas(canvasEl, {
      width: pdfCanvas.width,
      height: pdfCanvas.height
    })

    // Position the fabric canvas absolutely over the PDF canvas
    fabricCanvas.wrapperEl.style.position = 'absolute'
    fabricCanvas.wrapperEl.style.top = '0'
    fabricCanvas.wrapperEl.style.left = '0'
    fabricCanvas.wrapperEl.style.zIndex = '10'
    fabricCanvas.wrapperEl.style.pointerEvents = 'auto'

    // Add the fabric canvas to the PDF page container
    pdfCanvas.parentElement.appendChild(fabricCanvas.wrapperEl)

    // Patch the context to fix 'alphabetical' bug
    const ctx = fabricCanvas.getContext();
    Object.defineProperty(ctx, 'textBaseline', {
      set(value) {
        if (value === 'alphabetical') value = 'alphabetic';
        this._textBaseline = value;
      },
      get() {
        return this._textBaseline || 'alphabetic';
      }
    });

    // Initialize brush settings
    if (this.currentTool === 'pen') {
      fabricCanvas.isDrawingMode = true
      if (!fabricCanvas.freeDrawingBrush) {
        fabricCanvas.freeDrawingBrush = new PencilBrush(fabricCanvas)
      }
      fabricCanvas.freeDrawingBrush.width = this.brushThickness
      fabricCanvas.freeDrawingBrush.color = this.brushColor
    }

    // Store the canvas reference
    this.canvases.set(pageNumber, fabricCanvas)

    // Set up event listeners
    this._setupCanvasEvents(fabricCanvas)

    // Save initial empty state
    if (this.undoStack.length === 0) {
      this._saveState()
    }
    return fabricCanvas
  }

  setActiveTool(tool) {
    this.currentTool = tool
    this.canvases.forEach(canvas => {
      if (tool === 'pen') {
        canvas.isDrawingMode = true
        canvas.selection = false // Disable selection for drawing
        if (!canvas.freeDrawingBrush) {
          canvas.freeDrawingBrush = new PencilBrush(canvas)
        }
        canvas.freeDrawingBrush.width = this.brushThickness
        canvas.freeDrawingBrush.color = this.brushColor
      } else {
        canvas.isDrawingMode = false
        canvas.selection = true // Enable selection for other tools
      }

      // Set cursor based on tool - but allow object-specific cursors to override
      switch (tool) {
        case 'pen':
          canvas.defaultCursor = 'crosshair'
          canvas.hoverCursor = 'crosshair'
          canvas.moveCursor = 'crosshair'
          break
        case 'text':
          canvas.defaultCursor = 'text'
          canvas.hoverCursor = 'move' // Allow move cursor when hovering over objects
          canvas.moveCursor = 'move'
          break
        case 'rectangle':
          canvas.defaultCursor = 'crosshair'
          canvas.hoverCursor = 'move' // Allow move cursor when hovering over objects
          canvas.moveCursor = 'move'
          break
        case 'arrow':
          canvas.defaultCursor = 'crosshair'
          canvas.hoverCursor = 'move' // Allow move cursor when hovering over objects
          canvas.moveCursor = 'move'
          break
        default:
          canvas.defaultCursor = 'default'
          canvas.hoverCursor = 'move'
          canvas.moveCursor = 'move'
      }
    })
  }

  setBrushThickness(thickness) {
    this.brushThickness = thickness
    if (this.currentTool === 'pen') {
      this.canvases.forEach(canvas => {
        canvas.freeDrawingBrush.width = thickness
      })
    }
  }

  setBrushColor(color) {
    this.brushColor = color
    if (this.currentTool === 'pen') {
      this.canvases.forEach(canvas => {
        canvas.freeDrawingBrush.color = color
      })
    }
  }

  setTextColor(color) {
    this.textColor = color

    // Apply color to currently selected text objects
    this.canvases.forEach(canvas => {
      const activeObject = canvas.getActiveObject()
      if (activeObject instanceof IText) {
        activeObject.set('fill', color)
        canvas.renderAll()
        this._saveState()
      }
    })
  }

  setTextSize(size) {
    this.textSize = size

    // Apply size to currently selected text objects
    this.canvases.forEach(canvas => {
      const activeObject = canvas.getActiveObject()
      if (activeObject instanceof IText) {
        activeObject.set('fontSize', size)
        canvas.renderAll()
        this._saveState()
      }
    })
  }

  _setupCanvasEvents(canvas) {
    let isDrawingArrow = false;
    let arrowStartPoint = null;
    let tempArrow = null;

    canvas.on('mouse:down', (event) => {
      if (!this.currentTool) return

      // Check if user clicked on an existing object first
      const clickedObject = canvas.findTarget(event.e, false)

      // If user clicked on an existing object, just select it (no edit mode, no new object)
      if (clickedObject) {
        console.log('Selecting existing object for drag/drop:', clickedObject.type, 'isArrow:', clickedObject.isArrow)

        // Force restore drag controls for ANY object type
        clickedObject.set({
          hasControls: true,
          hasBorders: true,
          selectable: true,
          borderColor: '#2196F3',
          cornerColor: '#2196F3',
          cornerSize: clickedObject.isArrow ? 10 : 8, // Larger corners for arrows
          transparentCorners: false,
          cornerStyle: 'rect'
        })

        // For arrows, ensure they don't enter edit mode
        if (clickedObject.isArrow && clickedObject instanceof IText) {
          clickedObject.editable = false
          clickedObject.selectable = true
          console.log('Arrow clicked - drag mode enabled, edit mode disabled')
        }

        canvas.setActiveObject(clickedObject)
        canvas.renderAll()
        console.log('Drag controls force-restored for', clickedObject.isArrow ? 'arrow' : clickedObject.type)
        return // Let Fabric.js handle existing object interactions (drag/drop)
      }

      // Only create new objects if clicking on empty space
      this.activeCanvas = canvas
      const pointer = canvas.getPointer(event.e)
      let object

      switch (this.currentTool) {
        case 'pen':
          // Let fabric handle pen drawing
          return

        case 'text':
          // User clicked on empty space - create new text and enter edit mode
          console.log('Creating new text object at', pointer)
          object = new IText('Type here...', {
            left: pointer.x,
            top: pointer.y,
            fontSize: this.textSize,
            fill: this.textColor,
            width: 150,
            fontFamily: 'Arial',
            selectable: true,
            editable: true,
            hasControls: true,
            hasBorders: true,
            borderColor: '#2196F3',
            borderScaleFactor: 2,
            cornerColor: '#2196F3',
            cornerSize: 8,
            transparentCorners: false,
            cornerStyle: 'rect'
          })
          break

        case 'rectangle':
          object = new Rect({
            left: pointer.x,
            top: pointer.y,
            width: 100,
            height: 50,
            fill: 'rgba(255, 0, 0, 0.3)',
            stroke: '#000000',
            strokeWidth: 1,
            selectable: true,
            hasControls: true,
            hasBorders: true,
            borderColor: '#2196F3',
            borderScaleFactor: 2,
            cornerColor: '#2196F3',
            cornerSize: 8,
            transparentCorners: false,
            cornerStyle: 'rect'
          })
          break

        case 'arrow':
          // Create Unicode arrow using IText
          const arrowSymbol = this.getArrowSymbol()
          object = new IText(arrowSymbol, {
            left: pointer.x,
            top: pointer.y,
            fontSize: 32,
            fill: this.textColor,
            fontFamily: 'Arial Unicode MS, Arial, sans-serif',
            selectable: true,
            editable: false, // Disable text editing for arrows
            hasControls: true,
            hasBorders: true,
            borderColor: '#2196F3',
            borderScaleFactor: 2,
            cornerColor: '#2196F3',
            cornerSize: 10, // Slightly larger for better visibility
            transparentCorners: false,
            cornerStyle: 'rect',
            hoverCursor: 'move',
            moveCursor: 'move',
            isArrow: true // Custom property to identify arrows
          })
          console.log('Created arrow:', arrowSymbol, 'at', pointer)
          break;
      }

      if (object) {
        canvas.add(object)
        canvas.setActiveObject(object)

        // Only enter edit mode for NEW text objects
        if (object instanceof IText) {
          setTimeout(() => {
            object.enterEditing()
            if (object.hiddenTextarea) {
              object.hiddenTextarea.focus()
            }
          }, 100)
        }

        // Save state after adding new object
        this._saveState()
      }
    })

    canvas.on('mouse:move', (event) => {
      if (!isDrawingArrow || !arrowStartPoint || this.currentTool !== 'arrow') return

      const pointer = canvas.getPointer(event.e)
      
      // Temporarily disable state saving for temp arrow operations
      this.isRestoring = true
      
      // Remove previous temp arrow
      if (tempArrow) {
        canvas.remove(tempArrow)
      }

      // Create new arrow from start to current position
      tempArrow = this._createArrow(
        arrowStartPoint.x, 
        arrowStartPoint.y, 
        pointer.x, 
        pointer.y
      )
      
      canvas.add(tempArrow)
      
      // Re-enable state saving
      this.isRestoring = false
      
      canvas.renderAll()
    })

    canvas.on('mouse:up', (event) => {
      if (!isDrawingArrow || !arrowStartPoint || this.currentTool !== 'arrow') return

      const pointer = canvas.getPointer(event.e)
      
      // Temporarily disable state saving to avoid saving temp arrow removal
      this.isRestoring = true
      
      // Remove temp arrow
      if (tempArrow) {
        canvas.remove(tempArrow)
      }
      
      // Re-enable state saving
      this.isRestoring = false

      // Create final arrow if there's enough distance
      const distance = Math.sqrt(
        Math.pow(pointer.x - arrowStartPoint.x, 2) + 
        Math.pow(pointer.y - arrowStartPoint.y, 2)
      )

      if (distance > 10) { // Minimum distance to create arrow
        const finalArrow = this._createArrow(
          arrowStartPoint.x, 
          arrowStartPoint.y, 
          pointer.x, 
          pointer.y
        )
        canvas.add(finalArrow)
        // State will be saved automatically by object:added event
      }

      // Reset arrow drawing state
      isDrawingArrow = false
      arrowStartPoint = null
      tempArrow = null
      canvas.selection = true // Re-enable selection
      canvas.renderAll()
    })

    // Add text editing event handlers
    canvas.on('text:editing:entered', (event) => {
      console.log('Text editing started')
      // Disable canvas selection while editing text
      canvas.selection = false
    })

    canvas.on('text:editing:exited', (event) => {
      console.log('Text editing finished')
      // Re-enable canvas selection after editing
      canvas.selection = true

      // Restore selection controls for the text object
      const textObject = event.target
      if (textObject) {
        textObject.set({
          hasControls: true,
          hasBorders: true,
          selectable: true,
          borderColor: '#2196F3',
          cornerColor: '#2196F3',
          cornerSize: 8,
          transparentCorners: false,
          cornerStyle: 'rect'
        })

        // Keep the object selected to show drag handles
        canvas.setActiveObject(textObject)
        canvas.renderAll()
        console.log('Drag handles restored after text editing')
      }

      // Save state after text editing
      this._saveState()
    })

    // Handle object selection for better UX
    canvas.on('selection:created', (event) => {
      const activeObject = event.selected[0]

      // Ensure the selected object shows controls for drag/drop
      if (activeObject) {
        activeObject.set({
          hasControls: true,
          hasBorders: true,
          selectable: true,
          borderColor: '#2196F3',
          cornerColor: '#2196F3',
          cornerSize: 8,
          transparentCorners: false
        })
        canvas.renderAll()
        console.log('Object selected - drag/drop enabled')
      }

      // NO automatic edit mode - only drag/drop selection
    })

    // Handle when objects are added to ensure they have proper controls
    canvas.on('object:added', (event) => {
      const obj = event.target
      if (obj && !this.isRestoring) {
        obj.set({
          hasControls: true,
          hasBorders: true,
          selectable: true,
          borderColor: '#2196F3',
          cornerColor: '#2196F3',
          cornerSize: obj.isArrow ? 10 : 8, // Larger corners for arrows
          transparentCorners: false,
          cornerStyle: 'rect'
        })

        // Ensure arrows are set up for drag mode, not edit mode
        if (obj.isArrow && obj instanceof IText) {
          obj.editable = false
          obj.selectable = true
          console.log('Arrow added - configured for drag mode')
        }

        canvas.renderAll()
        console.log('Object added with drag controls enabled:', obj.isArrow ? 'arrow' : obj.type)
      }
    })

    // Ensure controls are always visible when objects are selected
    canvas.on('selection:updated', (event) => {
      const activeObject = canvas.getActiveObject()
      if (activeObject) {
        activeObject.set({
          hasControls: true,
          hasBorders: true,
          borderColor: '#2196F3',
          cornerColor: '#2196F3',
          cornerSize: activeObject.isArrow ? 10 : 8, // Larger corners for arrows
          transparentCorners: false
        })

        // Ensure arrows stay in drag mode, not edit mode
        if (activeObject.isArrow && activeObject instanceof IText) {
          activeObject.editable = false
          activeObject.selectable = true
        }

        canvas.renderAll()
        console.log('Selection updated - drag controls restored for', activeObject.isArrow ? 'arrow' : activeObject.type)
      }
    })

    // Handle double-click on text objects for editing
    canvas.on('mouse:dblclick', (event) => {
      const clickedObject = canvas.findTarget(event.e, false)
      if (clickedObject instanceof IText) {
        console.log('Double-click detected - entering text edit mode')
        canvas.setActiveObject(clickedObject)
        clickedObject.enterEditing()
        if (clickedObject.hiddenTextarea) {
          clickedObject.hiddenTextarea.focus()
        }
      }
    })

    // Handle object selection to show controls when not in text editing mode
    canvas.on('object:selected', (event) => {
      const obj = event.target
      if (obj && !(obj instanceof IText && obj.isEditing)) {
        // Show selection controls for non-editing objects
        obj.set({
          hasControls: true,
          hasBorders: true,
          borderColor: '#2196F3',
          cornerColor: '#2196F3',
          cornerSize: obj.isArrow ? 10 : 8, // Larger corners for arrows
          transparentCorners: false
        })
        canvas.renderAll()
        console.log('Object selected - showing controls for:', obj.isArrow ? 'arrow' : obj.type)
      }
    })

    // Handle mouse over for arrows to show controls
    canvas.on('mouse:over', (event) => {
      const obj = event.target
      if (obj && obj.isArrow) {
        obj.set({
          hasControls: true,
          hasBorders: true,
          borderColor: '#2196F3',
          cornerColor: '#2196F3',
          cornerSize: 10,
          transparentCorners: false
        })
        canvas.renderAll()
        console.log('Arrow hovered - showing controls')
      }
    })

    // Track object movement to prevent edit mode during drag
    canvas.on('object:moving', (event) => {
      const obj = event.target
      if (obj) {
        obj._isMoving = true
      }
    })

    canvas.on('object:moved', (event) => {
      const obj = event.target
      if (obj) {
        obj._isMoving = false
        this._saveState() // Save state after moving
      }
    })

    // Track object scaling/rotating
    canvas.on('object:scaling', (event) => {
      const obj = event.target
      if (obj) {
        obj._isMoving = true
      }
    })

    canvas.on('object:scaled', (event) => {
      const obj = event.target
      if (obj) {
        obj._isMoving = false
        this._saveState() // Save state after scaling
      }
    })

    // Save state after drawing is complete
    canvas.on('path:created', (event) => {
      console.log('Drawing completed - saving state')
      this._saveState()
    })

    // Save state when objects are removed
    canvas.on('object:removed', (event) => {
      if (!this.isRestoring) {
        console.log('Object removed - saving state')
        this._saveState()
      }
    })
  }

  addSignature(signatureData) {
    // If no active canvas, use the first available canvas
    if (!this.activeCanvas) {
      const firstCanvas = this.canvases.values().next().value;
      if (!firstCanvas) return;
      this.activeCanvas = firstCanvas;
    }

    // Create an image from the signature data
    const img = new Image();
    img.onload = () => {
      // Calculate position to center the signature
      const x = (this.activeCanvas.width - img.width) / 2;
      const y = (this.activeCanvas.height - img.height) / 2;

      // Create a fabric image object
      const fabricImage = new FabricImage(img, {
        left: x,
        top: y,
        selectable: true,
        hasControls: true,
        scaleX: 0.5,
        scaleY: 0.5
      });

      // Add to canvas
      this.activeCanvas.add(fabricImage);
      this.activeCanvas.renderAll();
      this._saveState();
    };
    img.src = signatureData;
  }

  saveAnnotations() {
    const annotations = {}
    this.canvases.forEach((canvas, pageNumber) => {
      const canvasWidth = canvas.getWidth()
      const canvasHeight = canvas.getHeight()
      const objects = canvas.getObjects().map(obj => {
        const json = obj.toObject()
        // Convert coordinates to percentages
        json.left = (obj.left / canvasWidth) * 100
        json.top = (obj.top / canvasHeight) * 100
        if (obj.width) {
          json.width = (obj.width * obj.scaleX / canvasWidth) * 100
        }
        if (obj.height) {
          json.height = (obj.height * obj.scaleY / canvasHeight) * 100
        }
        // Ensure path, stroke, and strokeWidth are present for pen/free drawing
        if (json.type === 'path') {
          json.path = obj.path;
          json.stroke = obj.stroke;
          json.strokeWidth = obj.strokeWidth;
          
          // Convert path coordinates to percentages
          if (typeof json.path === 'string') {
            // Parse SVG path string and convert coordinates
            try {
              // Simple regex to find coordinate pairs in the path string
              json.path = json.path.replace(/([0-9.-]+)\s+([0-9.-]+)/g, (match, x, y) => {
                const newX = (parseFloat(x) / canvasWidth) * 100;
                const newY = (parseFloat(y) / canvasHeight) * 100;
                return `${newX} ${newY}`;
              });
            } catch (e) {
              console.warn('Failed to convert path coordinates:', e);
            }
          } else if (Array.isArray(json.path)) {
            json.path = json.path.map(cmd => {
              if (Array.isArray(cmd) && cmd.length >= 3) {
                const newCmd = [...cmd];
                // Convert x coordinate (index 1)
                if (typeof newCmd[1] === 'number') {
                  newCmd[1] = (newCmd[1] / canvasWidth) * 100;
                }
                // Convert y coordinate (index 2)
                if (typeof newCmd[2] === 'number') {
                  newCmd[2] = (newCmd[2] / canvasHeight) * 100;
                }
                // Handle additional coordinates for curves (Q commands have 4 more coordinates)
                if (cmd[0] === 'Q' && cmd.length >= 6) {
                  if (typeof newCmd[3] === 'number') {
                    newCmd[3] = (newCmd[3] / canvasWidth) * 100;
                  }
                  if (typeof newCmd[4] === 'number') {
                    newCmd[4] = (newCmd[4] / canvasHeight) * 100;
                  }
                }
                return newCmd;
              }
              return cmd;
            });
          }
        }
        return json
      })
      annotations[pageNumber] = objects
    })
    return annotations
  }

  loadAnnotations(annotations) {
    Object.entries(annotations).forEach(([pageNumber, objects]) => {
      const canvas = this.canvases.get(parseInt(pageNumber))
      if (!canvas) return

      const canvasWidth = canvas.getWidth()
      const canvasHeight = canvas.getHeight()

      objects.forEach(obj => {
        // Convert percentages back to pixels
        obj.left = (obj.left * canvasWidth) / 100
        obj.top = (obj.top * canvasHeight) / 100
        if (obj.width) {
          obj.width = (obj.width * canvasWidth) / 100
        }
        if (obj.height) {
          obj.height = (obj.height * canvasHeight) / 100
        }

        // Create and add the object based on its type
        let fabricObj
        switch (obj.type) {
          case 'i-text':
            fabricObj = new fabric.IText(obj.text, obj)
            break
          case 'rect':
            fabricObj = new fabric.Rect(obj)
            break
          case 'path':
            fabricObj = new fabric.Path(obj.path, obj)
            break
          case 'group':
            // Handle arrow groups and other groups
            fabric.util.enlivenObjects(obj.objects, (objects) => {
              const group = new fabric.Group(objects, obj)
              canvas.add(group)
              canvas.renderAll()
            })
            return
          case 'image':
            fabric.Image.fromURL(obj.src, (img) => {
              Object.assign(img, obj)
              canvas.add(img)
              canvas.renderAll()
            })
            return
        }

        if (fabricObj) {
          canvas.add(fabricObj)
        }
      })

      canvas.renderAll()
    })
  }

  onStateChange(callback) {
    this.stateChangeListeners.add(callback)
  }

  offStateChange(callback) {
    this.stateChangeListeners.delete(callback)
  }

  _notifyStateChange() {
    const state = {
      canUndo: this.undoStack.length > 1,
      canRedo: this.redoStack.length > 0
    }
    this.stateChangeListeners.forEach(callback => callback(state))
  }

  _saveState(clearRedo = true) {
    if (this.isRestoring) {
      console.log('Skipping save state - currently restoring')
      return
    }

    const state = this._serializeCanvases()
    this.undoStack.push(state)
    console.log('State saved - undoStack now has:', this.undoStack.length, 'states')

    // Limit stack size to prevent memory issues
    if (this.undoStack.length > 50) {
      this.undoStack.shift()
    }

    if (clearRedo) {
      this.redoStack = []
      console.log('Redo stack cleared')
    }

    this._notifyStateChange()
  }

  undo() {
    console.log('Undo called - undoStack length:', this.undoStack.length, 'redoStack length:', this.redoStack.length)

    if (this.undoStack.length <= 1) {
      console.log('Cannot undo - not enough states in stack')
      return false
    }

    const currentState = this.undoStack.pop()
    this.redoStack.push(currentState)

    const previousState = this.undoStack[this.undoStack.length - 1]
    console.log('Restoring previous state, undoStack now has:', this.undoStack.length, 'states')

    this.isRestoring = true
    this._loadState(previousState)
    this.isRestoring = false

    this._notifyStateChange()
    return true
  }

  redo() {
    console.log('Redo called - undoStack length:', this.undoStack.length, 'redoStack length:', this.redoStack.length)

    if (this.redoStack.length === 0) {
      console.log('Cannot redo - no states in redo stack')
      return false
    }

    const nextState = this.redoStack.pop()
    this.undoStack.push(nextState)

    console.log('Restoring next state, redoStack now has:', this.redoStack.length, 'states')

    this.isRestoring = true
    this._loadState(nextState)
    this.isRestoring = false

    this._notifyStateChange()
    return true
  }

  _loadState(stateStr) {
    try {
      const state = JSON.parse(stateStr)
      console.log('Loading state with', Object.keys(state).length, 'pages')

      // Clear canvases WITHOUT resetting undo/redo stacks
      this.canvases.forEach((canvas, pageNum) => {
        const objectCount = canvas.getObjects().length
        console.log('Clearing page', pageNum, 'which had', objectCount, 'objects')
        canvas.clear()
        canvas.renderAll()
      })

      Object.entries(state).forEach(([pageNum, objects]) => {
        const canvas = this.canvases.get(parseInt(pageNum))
        if (!canvas) return

        objects.forEach(objData => {
          let fabricObj

          // Create objects based on type
          switch (objData.type) {
            case 'i-text':
              fabricObj = new IText(objData.text || '', objData)
              break
            case 'rect':
              fabricObj = new Rect(objData)
              break
            case 'path':
              fabricObj = new Path(objData.path, objData)
              break
            case 'image':
              // Handle image objects
              const img = new Image()
              img.onload = () => {
                fabricObj = new FabricImage(img, objData)
                canvas.add(fabricObj)
                canvas.renderAll()
              }
              img.src = objData.src
              return // Skip adding to canvas here, will be added in onload
            default:
              console.warn('Unknown object type:', objData.type)
              return
          }

          if (fabricObj) {
            // Restore visual properties for drag/drop
            fabricObj.set({
              hasControls: true,
              hasBorders: true,
              selectable: true,
              borderColor: '#2196F3',
              cornerColor: '#2196F3',
              cornerSize: 8,
              transparentCorners: false,
              cornerStyle: 'rect'
            })

            canvas.add(fabricObj)
          }
        })
        canvas.renderAll()
      })
    } catch (error) {
      console.error('Error loading state:', error)
    }
  }

  clearAnnotations() {
    this.canvases.forEach(canvas => {
      canvas.clear()
      canvas.renderAll()
    })

    // Save the cleared state for undo/redo
    this._saveState()
  }

  _serializeCanvases() {
    const state = {}
    this.canvases.forEach((canvas, pageNumber) => {
      const objects = canvas.getObjects().map(obj => {
        // Clone the object to avoid reference issues
        const objData = obj.toObject(['type', 'version'])
        
        // Handle special properties for different object types
        if (obj.type === 'i-text') {
          objData.text = obj.text
          objData.fontSize = obj.fontSize
          objData.fill = obj.fill
        } else if (obj.type === 'path') {
          objData.path = obj.path
          objData.stroke = obj.stroke
          objData.strokeWidth = obj.strokeWidth
        } else if (obj.type === 'image') {
          objData.src = obj.getSrc()
          objData.crossOrigin = 'anonymous'
        }
        
        // Common properties
        objData.left = obj.left
        objData.top = obj.top
        objData.scaleX = obj.scaleX
        objData.scaleY = obj.scaleY
        objData.angle = obj.angle
        
        return objData
      })
      state[pageNumber] = objects
    })
    return JSON.stringify(state)
  }

  // Get all canvases as an array for PDF generation
  getCanvases() {
    const canvasArray = []
    // Sort by page number to maintain order
    const sortedEntries = Array.from(this.canvases.entries()).sort(([a], [b]) => a - b)

    for (const [pageNumber, canvas] of sortedEntries) {
      canvasArray[pageNumber - 1] = canvas // Convert to 0-based index
    }

    return canvasArray
  }

  // Zoom functionality
  setZoom(zoomLevel) {
    const zoomFactor = zoomLevel / 100
    console.log('Setting zoom to:', zoomLevel + '%', 'Factor:', zoomFactor)

    // Find all PDF page containers and apply zoom
    const pdfPages = document.querySelectorAll('.pdf-page')

    if (pdfPages.length > 0) {
      // Apply zoom to all PDF page containers
      pdfPages.forEach((pageContainer, index) => {
        pageContainer.style.transform = `scale(${zoomFactor})`
        pageContainer.style.transformOrigin = 'top center'

        // Adjust spacing between pages
        if (zoomFactor !== 1) {
          const originalHeight = pageContainer.offsetHeight / (pageContainer.style.transform ? parseFloat(pageContainer.style.transform.match(/scale\(([^)]+)\)/)?.[1] || 1) : 1)
          const scaledHeight = originalHeight * zoomFactor
          const extraHeight = scaledHeight - originalHeight
          pageContainer.style.marginBottom = `${extraHeight / 2}px`
        } else {
          pageContainer.style.marginBottom = ''
        }

        console.log(`PDF page ${index + 1} zoomed to ${zoomLevel}%`)
      })
    } else {
      // Fallback: zoom individual Fabric canvas containers
      console.log('PDF pages not found, using fallback zoom method')
      this.canvases.forEach((canvas, pageNumber) => {
        const fabricCanvasElement = canvas.getElement()
        const fabricContainer = fabricCanvasElement.parentElement

        if (fabricContainer) {
          fabricContainer.style.transform = `scale(${zoomFactor})`
          fabricContainer.style.transformOrigin = 'top center'
          console.log(`Fabric canvas ${pageNumber} zoomed to ${zoomLevel}%`)
        }
      })
    }

    // Store current zoom level
    this.currentZoom = zoomLevel
  }

  getCurrentZoom() {
    return this.currentZoom || 100
  }

  // Arrow functionality
  setArrowType(direction) {
    this.currentArrowType = direction
    console.log('Arrow type set to:', direction)
  }

  getArrowSymbol() {
    const arrowTypes = {
      'right': '→',
      'left': '←',
      'up': '↑',
      'down': '↓',
      'up-right': '↗',
      'down-right': '↘',
      'down-left': '↙',
      'up-left': '↖'
    }
    return arrowTypes[this.currentArrowType] || '→'
  }
}